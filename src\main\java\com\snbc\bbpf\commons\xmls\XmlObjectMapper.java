/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.xmls;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * # 【XML模块】XML字符串和对象之间的转换处理
 * <p>
 * 实现XML字符串和对象的互相转换，可指定对象，可处理CDATA数据。
 * 已经兼融各种日期格式与空属性。
 * 属性注解请参考 Jackson下的annotation
 * 例如@JsonFormat：格式化
 * @JacksonXmlProperty自定义节点属性，里面属性是否可以为节点等
 * @JacksonXmlCData 定义cdata
 * @JsonRootName 定义跟节点
 *
 *
 *
 * <AUTHOR>
 * @module XML模块
 * @date 2023-09-05 14:59
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class XmlObjectMapper {

    private static final XmlMapper xmlMapper = new XmlMapper();

    static {
        xmlMapper.registerModule(new JavaTimeModule());
    }

    /**
     * # 将对象转化为xml
     *
     * @param object 要转化为xml的对象，不允许为null
     * @return
     * @throws RuntimeException
     * <AUTHOR>
     * @date 2023/9/11
     * @since 1.2.0
     */
    public static String marshall(Object object) {
        Objects.requireNonNull(object, "object is not allowed null");
        try {
            return xmlMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * # 将xml转化在对象
     *
     * @param xmlStr 要转化的xml文本
     * @param clazz  目标类
     * @return 目标格式
     * @throws RuntimeException 参数为空
     * <AUTHOR>
     * @date 2023/9/11
     * @since 1.2.0
     */
    public static <T> T unmarshall(String xmlStr, Class<T> clazz) {
        try {
            return xmlMapper.readValue(xmlStr, clazz);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }


}
