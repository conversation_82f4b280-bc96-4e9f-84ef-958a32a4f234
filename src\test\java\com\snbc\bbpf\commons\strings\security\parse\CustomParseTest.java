/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.strings.security.parse;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 自定义脱敏解析器测试类
 *
 * <AUTHOR>
 * @module 字符脱敏
 * @date 2025/07/28
 * @copyright 2025 山东新北洋信息技术股份有限公司. All rights reserved
 * @since 1.7.0
 */
class CustomParseTest {

    @Test
    @DisplayName("默认构造函数测试")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/28")
    })
    void testDefaultConstructor() {
        CustomParse parser = new CustomParse();
        assertEquals(3, parser.getPrefixKeep());
        assertEquals(4, parser.getSuffixKeep());
        assertEquals("*", parser.getMaskSymbol());
    }

    @Test
    @DisplayName("自定义构造函数测试")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/28")
    })
    void testCustomConstructor() {
        CustomParse parser = new CustomParse(2, 3, "#");
        assertEquals(2, parser.getPrefixKeep());
        assertEquals(3, parser.getSuffixKeep());
        assertEquals("#", parser.getMaskSymbol());
    }

    @Test
    @DisplayName("构造函数参数验证-负数处理")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/28")
    })
    void testConstructorValidation_NegativeNumbers() {
        CustomParse parser = new CustomParse(-1, -2, "#");
        assertEquals(0, parser.getPrefixKeep()); // 负数转为0
        assertEquals(0, parser.getSuffixKeep()); // 负数转为0
        assertEquals("#", parser.getMaskSymbol());
    }

    @Test
    @DisplayName("构造函数参数验证-空符号处理")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/28")
    })
    void testConstructorValidation_EmptySymbol() {
        CustomParse parser1 = new CustomParse(2, 3, "");
        assertEquals("*", parser1.getMaskSymbol()); // 空字符串转为*

        CustomParse parser2 = new CustomParse(2, 3, null);
        assertEquals("*", parser2.getMaskSymbol()); // null转为*
    }

    @Test
    @DisplayName("正常脱敏测试-默认参数")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/28")
    })
    void testParseString_Normal_Default() {
        CustomParse parser = new CustomParse();
        String result = parser.parseString("1234567890123");
        assertEquals("123******0123", result);
    }

    @Test
    @DisplayName("正常脱敏测试-自定义参数")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/28")
    })
    void testParseString_Normal_Custom() {
        CustomParse parser = new CustomParse(2, 3, "#");
        String result = parser.parseString("abcdefghijk");
        assertEquals("ab######ijk", result);
    }

    @Test
    @DisplayName("脱敏测试-只保留前缀")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/28")
    })
    void testParseString_PrefixOnly() {
        CustomParse parser = new CustomParse(3, 0, "*");
        String result = parser.parseString("1234567890");
        assertEquals("123*******", result);
    }

    @Test
    @DisplayName("脱敏测试-只保留后缀")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/28")
    })
    void testParseString_SuffixOnly() {
        CustomParse parser = new CustomParse(0, 3, "*");
        String result = parser.parseString("1234567890");
        assertEquals("*******890", result);
    }

    @Test
    @DisplayName("脱敏测试-字符串长度不足")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/28")
    })
    void testParseString_ShortString() {
        CustomParse parser = new CustomParse(3, 4, "*");
        
        // 长度等于前缀+后缀，不脱敏
        String result1 = parser.parseString("1234567");
        assertEquals("1234567", result1);
        
        // 长度小于前缀+后缀，不脱敏
        String result2 = parser.parseString("123456");
        assertEquals("123456", result2);
    }

    @Test
    @DisplayName("脱敏测试-边界情况")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/28")
    })
    void testParseString_EdgeCases() {
        CustomParse parser = new CustomParse(3, 4, "*");
        
        // 长度刚好大于前缀+后缀1位
        String result = parser.parseString("12345678");
        assertEquals("123*5678", result);
    }

    @Test
    @DisplayName("脱敏测试-null输入")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/28")
    })
    void testParseString_NullInput() {
        CustomParse parser = new CustomParse();
        String result = parser.parseString(null);
        assertNull(result);
    }

    @Test
    @DisplayName("脱敏测试-空字符串输入")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/28")
    })
    void testParseString_EmptyInput() {
        CustomParse parser = new CustomParse();
        String result = parser.parseString("");
        assertEquals("", result);
    }

    @Test
    @DisplayName("脱敏测试-中文字符")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/28")
    })
    void testParseString_ChineseCharacters() {
        CustomParse parser = new CustomParse(2, 2, "●");
        String result = parser.parseString("张三李四王五赵六");
        assertEquals("张三●●●●赵六", result);
    }

    @Test
    @DisplayName("脱敏测试-特殊字符")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/28")
    })
    void testParseString_SpecialCharacters() {
        CustomParse parser = new CustomParse(2, 2, "X");
        String result = parser.parseString("a@#$%^&*()b");
        assertEquals("a@XXXXXXX)b", result); // 字符串长度11，前2后2，中间7个X
    }

    @Test
    @DisplayName("脱敏测试-多字符脱敏符号")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/28")
    })
    void testParseString_MultiCharMaskSymbol() {
        CustomParse parser = new CustomParse(2, 2, "**");
        String result = parser.parseString("1234567890");
        assertEquals("12************90", result);
    }

    @Test
    @DisplayName("toString方法测试")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/28")
    })
    void testToString() {
        CustomParse parser = new CustomParse(2, 3, "#");
        String result = parser.toString();
        assertEquals("CustomParse{prefixKeep=2, suffixKeep=3, maskSymbol='#'}", result);
    }

    @Test
    @DisplayName("脱敏测试-极端情况：前缀后缀都为0")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/28")
    })
    void testParseString_ZeroPrefixSuffix() {
        CustomParse parser = new CustomParse(0, 0, "*");
        String result = parser.parseString("1234567890");
        assertEquals("**********", result);
    }

    @Test
    @DisplayName("脱敏测试-单字符字符串")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/28")
    })
    void testParseString_SingleCharacter() {
        CustomParse parser = new CustomParse(1, 1, "*");
        String result = parser.parseString("A");
        assertEquals("A", result); // 长度不足，不脱敏
    }
}
