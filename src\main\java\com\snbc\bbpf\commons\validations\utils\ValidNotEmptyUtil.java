/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.validations.utils;

import com.snbc.bbpf.commons.objs.ObjectEmptyCheck;
import com.snbc.bbpf.commons.reflects.ReflectUtils;
import com.snbc.bbpf.commons.strings.StringEmptyCheck;
import com.snbc.bbpf.commons.validations.annotation.ValidNotEmpty;

import java.lang.reflect.Field;

/**
 * 【验证模块】非空验证
 *
 * <p>要实现有非空的注解</p>
 * <p>参考之前开发的判断空的方法。</p>
 *
 * <AUTHOR>
 * @module
 * @date 2023/7/13 14:34
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class ValidNotEmptyUtil {

    private ValidNotEmptyUtil() {
        throw new IllegalStateException("Utility class");
    }

    public static final int GETMETHOD_PRE_LENGTH = 3;

    /**
     * 用于校验配置ValidNotEmpty非空验证注解的对象是否为空
     * 适合验证javabean中属性为任意类型的对象的空值验证
     * 若数组、list存在空值或map中存在空的key或value则校验不通过
     * 对非javabean类型的参数不进行校验
     *
     * @param object javabean类型的对象参数
     * @return void
     * @throws IllegalArgumentException 对象值为空或字段值为空时抛出异常
     * @since 1.1.0
     * <AUTHOR>
     * @date 2023/7/13
     */
    public static void emptyFieldValidate(Object object) throws IllegalArgumentException {
        if (ObjectEmptyCheck.isEmpty(object)) {
            throw new IllegalArgumentException("object can't be null");
        }

        Field[] fields = object.getClass().getDeclaredFields();
        for (Field field : fields) {
            //获取对应注解
            ValidNotEmpty validNotEmpty = field.getAnnotation(ValidNotEmpty.class);
            if (null == validNotEmpty) {
                continue;
            }
            //根据get方法取出属性值
            Object fieldValue = ReflectUtils.getPropertyValue(object,field.getName(),Object.class);
            //非空校验
            if (ObjectEmptyCheck.isEmpty(fieldValue)) {

                throw new IllegalArgumentException(StringEmptyCheck.isEmpty(validNotEmpty.message()) ?
                        (field.getName() + " can't be empty") : validNotEmpty.message());
            }
        }
    }
}
