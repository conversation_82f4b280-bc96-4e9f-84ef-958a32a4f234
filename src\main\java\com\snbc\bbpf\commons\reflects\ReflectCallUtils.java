/*
 * 版权所有 2009-2023 山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.reflects;

import com.snbc.bbpf.commons.strings.StringEmptyCheck;
import java.lang.reflect.Method;


/**
 * 通过反射调用方法
 * <p>1、调用指定类方法</p>
 * <p>2、要求支持带返回值和不带返回值，带参数和不带参数，带返回值需要返回结果</p>
 *
 * 不支持对私有方法调用，理由：不应使用反射来增加类、方法或字段的可访问性，
 * 更改或绕过类、方法或字段的可访问性违反了封装原则，并可能导致运行时错误。
 *
 * <AUTHOR>
 * @module 对象模块
 * @date 2023/11/27 20:10
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class ReflectCallUtils {

	private static final String PARAMETER_EMPTY = "Input parameter is empty";
    private ReflectCallUtils() {
        throw new IllegalStateException("Utility class");
    }


    /**
     * <p>调用指定对象方法</p>
     *
     * @param targetObject 对象实体
     * @param methodName 指定方法
     * @param paramclasses 参数类型
     * @param args 参数
     * @return T 返回值(无返回值，则返回null)
     * <AUTHOR>
     * @date 2023/11/27 20:10
     * @since 1.4.0
     */
    public static  <T> T call(Object targetObject, String methodName, Class<?>[] paramclasses, Object... args) {
		if (targetObject == null || StringEmptyCheck.isEmpty(methodName)) {
			throw new IllegalArgumentException(PARAMETER_EMPTY);
		}
		
		try {
			//反射得到类 包名+类名
			Class<?> clazz = targetObject.getClass();

			//直接java反射得到方法
			Method method= clazz.getMethod(methodName, paramclasses);
			Object objResult = method.invoke(clazz.getDeclaredConstructor().newInstance(), args);
			if (null!= objResult){
                return (T)objResult;
            }
            return null;
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
    }
	
    /**
     * <p>调用指定类方法</p>
     *
     * @param className 包名+类名
     * @param methodName 指定方法
     * @param paramclasses 参数类型
     * @param args 参数
     * @return T 返回值(无返回值，则返回null)
     * <AUTHOR>
     * @date 2023/11/27 20:10
     * @since 1.4.0
     */
    public static  <T> T call(String className, String methodName, Class<?>[] paramclasses, Object... args) {
		if (StringEmptyCheck.isEmpty(className) || StringEmptyCheck.isEmpty(methodName)) {
			throw new IllegalArgumentException(PARAMETER_EMPTY);
		}
		
		try {
			//反射得到类 包名+类名
			Class<?> clazz = Class.forName(className);

			//直接java反射得到方法
			Method method= clazz.getMethod(methodName, paramclasses);
			Object objResult = method.invoke(clazz.getDeclaredConstructor().newInstance(), args);
			if (null!= objResult){
                return (T)objResult;
            }
            return null;
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
    }
	

    /**
     * <p>调用指定类无参数方法</p>
     *
     * @param className 包名+类名
     * @param methodName 指定方法
     * @return T 返回值(无返回值，则返回null)
     * <AUTHOR>
     * @date 2023/11/27 20:10
     * @since 1.4.0
     */
    public static  <T> T call(String className, String methodName) {
		return call(className, methodName, null);
    }
	

    /**
     * <p>调用指定对象无参数方法</p>
     *
     * @param targetObject 对象实体
     * @param methodName 指定方法
     * @return T 返回值(无返回值，则返回null)
     * <AUTHOR>
     * @date 2023/11/27 20:10
     * @since 1.4.0
     */
    public static  <T> T call(Object targetObject, String methodName) {
		return call(targetObject, methodName, null);
    }

	
}
