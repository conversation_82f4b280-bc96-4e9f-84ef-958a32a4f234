package com.snbc.bbpf.commons.collects;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.function.Predicate;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 类描述:    .
 *
 * <AUTHOR>
 * 创建时间:  [2023/11/7 09:45]
 */
class CollectFinderTest {

    @DisplayName("检查对象是否存在")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping"),
            @Tag("@date:2023/11/7")
    })
    @Test
    void testExistsExactMatch() {
        Collection<String> collection = new ArrayList<>();
        String item = "test";
        collection.add(item);
        assertTrue(CollectFinder.existsExactMatch(collection, item));
    }

    @DisplayName("检查对象null")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping"),
            @Tag("@date:2023/11/7")
    })
    @Test
    void testExistsExactMatch_null() {

        assertFalse(CollectFinder.existsExactMatch(null, "item"));
    }

    @DisplayName("existsRegexMatch")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping"),
            @Tag("@date:2023/11/7")
    })
    @Test
    void testExistsRegexMatch() {
        Collection<String> collection = new ArrayList<>();
        String item = "test";
        collection.add(item);
        assertTrue(CollectFinder.existsRegexMatch(collection, item));
    }

    @DisplayName("existsRegexMatch")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping"),
            @Tag("@date:2023/11/7")
    })
    @Test
    void testExistsRegexMatch_null() {
        Collection<String> collection = new ArrayList<>();
        String item = "test";
        collection.add(item);
        assertFalse(CollectFinder.existsRegexMatch(null, item));
    }

    @DisplayName("existsRegexMatch")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping"),
            @Tag("@date:2023/11/7")
    })
    @Test
    void testExistsRegexMatch_Regex() {
        Collection<String> collection = new ArrayList<>();
        String item = "123123";
        collection.add(item);
        assertTrue(CollectFinder.existsRegexMatch(collection, "^\\d+$"));
    }

    @DisplayName("测试对象属性匹配")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping"),
            @Tag("@date:2023/11/7")
    })
    @Test
    public void testFindPartialMatches() {
        // 创建测试集合
        List<Person> people = new ArrayList<>();
        people.add(new Person("Alice", 25));
        people.add(new Person("Bob", 30));
        people.add(new Person("Charlie", 35));


        // 调用方法进行查找
        List<Person> matches = CollectFinder.findPartialMatches(people, person -> person.getName().equals("Bob"));

        // 验证结果
        Assertions.assertEquals(1, matches.size());
        Assertions.assertEquals("Bob", matches.get(0).getName());
    }

    @DisplayName("测试对象属性匹配")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping"),
            @Tag("@date:2023/11/7")
    })
    @Test
    public void testFindPartialMatches_null() {

        // 创建部分匹配的条件
        Predicate<Person> predicate = person -> person.getName().equals("Bob");
        // 调用方法进行查找
        List<Person> matches = CollectFinder.findPartialMatches(null, predicate);
        // 验证结果
        Assertions.assertEquals(Collections.emptyList(), matches);
    }

}

