package com.snbc.bbpf.commons.crypt;

import com.snbc.bbpf.commons.crypt.Base64Utils;
import com.snbc.bbpf.commons.randoms.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.nio.charset.StandardCharsets;

/**
 * Base算法工具类测试类
 *
 * <AUTHOR>
 * @date 2023/11/28 18:23
 */
class Base64UtilsTest {

    private String plainText = "zhangyegong";

    private String cipherText = "emhhbmd5ZWdvbmc=";

    private static String plainTextBig = null;

    @BeforeAll
    @DisplayName("生成1M大字符串，以便性能测试")
    @Tags({
            @Tag("@id:84"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/12/11")
    })
    private static void genBigData () {
        int iCount = 1024 * 1024;
        plainTextBig = RandomStringUtils.randomAlphabetic(iCount, false);
    }


    @Test
    @DisplayName("加密方法参数为Null异常校验")
    @Tags({
            @Tag("@id:84"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/11/28")
    })
    void testEncode_argsNull() {
        byte[] bytes = null;
        RuntimeException runtimeException = Assertions.assertThrows(RuntimeException.class, () -> Base64Utils.encode(null));
        Assertions.assertEquals("The argument plainTextByteArray is Blank",runtimeException.getMessage());
    }

    @Test
    @DisplayName("加密方法参数为空字节数组异常校验")
    @Tags({
            @Tag("@id:84"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/11/28")
    })
    void testEncode_argsBlank() {
        byte[] bytes = new byte[1];
        RuntimeException runtimeException = Assertions.assertThrows(RuntimeException.class, () -> Base64Utils.encode(null));
        Assertions.assertEquals("The argument plainTextByteArray is Blank",runtimeException.getMessage());
    }

    @Test
    @DisplayName("加密成功")
    @Tags({
            @Tag("@id:84"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/11/28")
    })
    void testEncode_success() {
        Assertions.assertEquals(cipherText,Base64Utils.encode(plainText.getBytes(StandardCharsets.UTF_8)));
        System.out.println(Base64Utils.encode(plainText.getBytes(StandardCharsets.UTF_8)));
    }

    @Test
    @DisplayName("解密参数异常校验")
    @Tags({
            @Tag("@id:84"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/11/28")
    })
    void testDecode_argsNull() {
        RuntimeException runtimeException = Assertions.assertThrows(RuntimeException.class, () -> Base64Utils.decode(null));
        Assertions.assertEquals("The cipherTextStr is Blank",runtimeException.getMessage());
    }

    @Test
    @DisplayName("解密参数异常校验")
    @Tags({
            @Tag("@id:84"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/11/28")
    })
    void testDecode_argsBlank() {
        RuntimeException runtimeException = Assertions.assertThrows(RuntimeException.class, () -> Base64Utils.decode(""));
        Assertions.assertEquals("The cipherTextStr is Blank",runtimeException.getMessage());
    }

    @Test
    @DisplayName("解密成功")
    @Tags({
            @Tag("@id:84"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/11/28")
    })
    void testDecode_success() {
        byte[] decode = Base64Utils.decode("emhhbmd5ZWdvbmc=");
        Assertions.assertEquals(plainText,new String(decode,StandardCharsets.UTF_8));
    }


    @Test
    @DisplayName("大报文数据解密成功")
    @Tags({
            @Tag("@id:84"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/11/28")
    })
    void testDecode_bigData_success() {
        byte[] decode = Base64Utils.decode(Base64Utils.encode(plainTextBig.getBytes(StandardCharsets.UTF_8)));
        Assertions.assertEquals(plainTextBig,new String(decode,StandardCharsets.UTF_8));
    }
}