/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.bytes.protocol;

/**
 * 协议消息数据结构
 * <p>定义完整的协议消息格式，包括固定消息头、扩展消息头和消息体</p>
 * 
 * <AUTHOR>
 * @module 字节处理
 * @date 2025/07/25 15:00
 * @copyright 2024 山东新北洋信息技术股份有限公司. All rights reserved
 * @since 1.7.0
 */
public class ProtocolMessage {
    
    // 固定消息头字段
    /** 消息标识，固定为"STUM" */
    private final byte[] messageId = {'S', 'T', 'U', 'M'};
    /** 协议版本，当前版本为1 */
    private byte protocolVersion = 1;
    /** 消息类型 */
    private short messageType;
    /** 消息序号 */
    private long sequenceNumber;
    /** 时间戳（13位毫秒级） */
    private long timestamp;
    /** 扩展消息头数据长度 */
    private int extendedHeaderLength;
    /** 消息体数据长度 */
    private int bodyLength;
    
    // 扩展消息头字段
    /** 商户编号 */
    private String tenantId;
    /** 产品编号 */
    private String productId;
    /** 应用编号 */
    private String appKey;
    /** 保留信息 */
    private byte[] reservedInfo;
    
    // 消息体
    /** 消息体数据 */
    private byte[] messageBody;
    
    // 校验码（可选）
    /** 校验码 */
    private byte[] checksum;
    
    /**
     * 默认构造函数
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public ProtocolMessage() {
        this.tenantId = "";
        this.productId = "";
        this.appKey = "";
        this.reservedInfo = new byte[0];
        this.messageBody = new byte[0];
    }
    
    /**
     * 构造函数
     * @param messageType 消息类型
     * @param sequenceNumber 消息序号
     * @param timestamp 时间戳
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public ProtocolMessage(short messageType, long sequenceNumber, long timestamp) {
        this();
        this.messageType = messageType;
        this.sequenceNumber = sequenceNumber;
        this.timestamp = timestamp;
    }
    
    /**
     * 获取消息标识
     * @return 消息标识字节数组
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public byte[] getMessageId() {
        return messageId.clone();
    }

    /**
     * 获取协议版本
     * @return 协议版本
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public byte getProtocolVersion() {
        return protocolVersion;
    }

    /**
     * 设置协议版本
     * @param protocolVersion 协议版本
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public void setProtocolVersion(byte protocolVersion) {
        this.protocolVersion = protocolVersion;
    }

    /**
     * 获取消息类型
     * @return 消息类型
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public short getMessageType() {
        return messageType;
    }

    /**
     * 设置消息类型
     * @param messageType 消息类型
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public void setMessageType(short messageType) {
        this.messageType = messageType;
    }

    /**
     * 获取消息序号
     * @return 消息序号
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public long getSequenceNumber() {
        return sequenceNumber;
    }

    /**
     * 设置消息序号
     * @param sequenceNumber 消息序号
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public void setSequenceNumber(long sequenceNumber) {
        this.sequenceNumber = sequenceNumber;
    }

    /**
     * 获取时间戳
     * @return 时间戳
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public long getTimestamp() {
        return timestamp;
    }

    /**
     * 设置时间戳
     * @param timestamp 时间戳
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    /**
     * 获取扩展消息头长度
     * @return 扩展消息头长度
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public int getExtendedHeaderLength() {
        return extendedHeaderLength;
    }

    /**
     * 获取消息体长度
     * @return 消息体长度
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public int getBodyLength() {
        return bodyLength;
    }
    
    /**
     * 获取商户编号
     * @return 商户编号
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public String getTenantId() {
        return tenantId;
    }

    /**
     * 设置商户编号
     * @param tenantId 商户编号
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId != null ? tenantId : "";
    }

    /**
     * 获取产品编号
     * @return 产品编号
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public String getProductId() {
        return productId;
    }

    /**
     * 设置产品编号
     * @param productId 产品编号
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public void setProductId(String productId) {
        this.productId = productId != null ? productId : "";
    }

    /**
     * 获取应用编号
     * @return 应用编号
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public String getAppKey() {
        return appKey;
    }

    /**
     * 设置应用编号
     * @param appKey 应用编号
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public void setAppKey(String appKey) {
        this.appKey = appKey != null ? appKey : "";
    }

    /**
     * 获取保留信息
     * @return 保留信息字节数组
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public byte[] getReservedInfo() {
        return reservedInfo != null ? reservedInfo.clone() : new byte[0];
    }

    /**
     * 设置保留信息
     * @param reservedInfo 保留信息字节数组
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public void setReservedInfo(byte[] reservedInfo) {
        this.reservedInfo = reservedInfo != null ? reservedInfo.clone() : new byte[0];
    }

    /**
     * 获取消息体
     * @return 消息体字节数组
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public byte[] getMessageBody() {
        return messageBody != null ? messageBody.clone() : new byte[0];
    }

    /**
     * 设置消息体
     * @param messageBody 消息体字节数组
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public void setMessageBody(byte[] messageBody) {
        this.messageBody = messageBody != null ? messageBody.clone() : new byte[0];
        this.bodyLength = this.messageBody.length;
    }

    /**
     * 获取校验码
     * @return 校验码字节数组
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public byte[] getChecksum() {
        return checksum != null ? checksum.clone() : new byte[0];
    }

    /**
     * 设置校验码
     * @param checksum 校验码字节数组
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public void setChecksum(byte[] checksum) {
        this.checksum = checksum != null ? checksum.clone() : null;
    }
    
    /**
     * 计算扩展消息头长度
     * @return 扩展消息头的字节长度
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public int calculateExtendedHeaderLength() {
        int length = 0;
        
        // 商户编号：4字节长度 + 内容字节数
        byte[] tenantIdBytes = tenantId.getBytes(java.nio.charset.StandardCharsets.UTF_8);
        length += 4 + tenantIdBytes.length;
        
        // 产品编号：4字节长度 + 内容字节数
        byte[] productIdBytes = productId.getBytes(java.nio.charset.StandardCharsets.UTF_8);
        length += 4 + productIdBytes.length;
        
        // 应用编号：4字节长度 + 内容字节数
        byte[] appKeyBytes = appKey.getBytes(java.nio.charset.StandardCharsets.UTF_8);
        length += 4 + appKeyBytes.length;
        
        // 保留信息：4字节长度 + 内容字节数
        length += 4 + reservedInfo.length;
        
        this.extendedHeaderLength = length;
        return length;
    }
    
    /**
     * 获取完整消息的总长度
     * @return 消息总字节长度
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public int getTotalLength() {
        int totalLength = 31; // 固定消息头长度
        totalLength += calculateExtendedHeaderLength(); // 扩展消息头长度
        totalLength += bodyLength; // 消息体长度
        if (checksum != null) {
            totalLength += checksum.length; // 校验码长度
        }
        return totalLength;
    }
    
    @Override
    public String toString() {
        return "ProtocolMessage{" +
                "protocolVersion=" + protocolVersion +
                ", messageType=" + messageType +
                ", sequenceNumber=" + sequenceNumber +
                ", timestamp=" + timestamp +
                ", tenantId='" + tenantId + '\'' +
                ", productId='" + productId + '\'' +
                ", appKey='" + appKey + '\'' +
                ", bodyLength=" + bodyLength +
                ", extendedHeaderLength=" + extendedHeaderLength +
                '}';
    }
}
