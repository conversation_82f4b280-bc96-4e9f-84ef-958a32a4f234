/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.dates;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 对ZonedDateTime进行时间计算  单元测试类
 * <p>计算当前时间一定时间（天，小时，分钟，秒，毫秒）之前或之后的时间</p>
 * <p>计算两个时间之间间隔时差（天，小时，分钟，秒，毫秒）<p>
 * <p>计算当前时间一定天数之前或之后的00点00分00秒或23点59分59秒的时间</p>
 * <p>计算当前时间所在当前年/季/月/周第一天的起始时间和最后一天的起始时间，可区分00点00分00秒或23点59分59秒</p>
 * <p>计算当前时间的（天，小时，分钟，秒，毫秒）数</p>
 * <p>根据（天，小时，分钟，秒，毫秒）数转换成时间</p>
 *
 * <AUTHOR>
 * @module 日期处理
 * @date 2023-05-16 13:04
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class ZoneDateTimeUtilsTest {

    ZonedDateTime zonedDateTime;
    ZonedDateTime targetDateTime;

    @BeforeEach
    void testSetUp() {
        zonedDateTime = ZonedDateTime.of(2023, 5, 16, 17, 18, 19, 20000000, ZoneId.systemDefault());
        targetDateTime = ZonedDateTime.of(2024, 5, 16, 17, 18, 19, 20000000, ZoneId.systemDefault());
    }

    @Test
    @DisplayName("测试ZonedDateTime的加任意年月日时分秒毫秒")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testPlus() {
        final ZonedDateTime plusReslut = ZoneDateTimeUtils.plus(zonedDateTime, 1, 1, 1, 1, 1, 1, 1);
        assertEquals(2024, ZoneDateTimeUtils.year(plusReslut));
        assertEquals(6, ZoneDateTimeUtils.month(plusReslut));
        assertEquals(17, ZoneDateTimeUtils.day(plusReslut));
        assertEquals(18, ZoneDateTimeUtils.hour(plusReslut));
        assertEquals(19, ZoneDateTimeUtils.minute(plusReslut));
        assertEquals(20, ZoneDateTimeUtils.second(plusReslut));
        assertEquals(21, ZoneDateTimeUtils.millisecond(plusReslut));
    }

    @Test
    @DisplayName("测试ZonedDateTime的diff")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testDiff() {
        assertEquals(366, ZoneDateTimeUtils.diff(zonedDateTime, targetDateTime, TimeUnit.DAYS));
        assertEquals(8784, ZoneDateTimeUtils.diff(zonedDateTime, targetDateTime, TimeUnit.HOURS));
        assertEquals(527040, ZoneDateTimeUtils.diff(zonedDateTime, targetDateTime, TimeUnit.MINUTES));
    }

    @Test
    @DisplayName("测试ZoneDateTime的加任意年")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void plusYear() {
        assertEquals(2024, ZoneDateTimeUtils.year(ZoneDateTimeUtils.plusYears(zonedDateTime, 1)));
    }

    @Test
    @DisplayName("测试ZoneDateTime的加任意月")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void plusMonth() {
        assertEquals(6, ZoneDateTimeUtils.month(ZoneDateTimeUtils.plusMonths(zonedDateTime, 1)));
    }

    @Test
    @DisplayName("测试ZoneDateTime的加任意日")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void plusDays() {
        assertEquals(17, ZoneDateTimeUtils.day(ZoneDateTimeUtils.plusDays(zonedDateTime, 1)));
    }


    @Test
    @DisplayName("测试ZoneDateTime的加任意小时")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testPlusHours() {
        assertEquals(18, ZoneDateTimeUtils.hour(ZoneDateTimeUtils.plusHours(zonedDateTime, 1)));
    }

    @Test
    @DisplayName("测试ZoneDateTime的加任意分钟")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testPlusMinutes() {
        assertEquals(19, ZoneDateTimeUtils.minute(ZoneDateTimeUtils.plusMinutes(zonedDateTime, 1)));
    }

    @Test
    @DisplayName("测试ZoneDateTime的加任意秒")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testPlusSeconds() {
        assertEquals(20, ZoneDateTimeUtils.second(ZoneDateTimeUtils.plusSeconds(zonedDateTime, 1)));
    }

    @Test
    @DisplayName("测试ZoneDateTime的加任意毫秒")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testPlusMilliseconds() {
        assertEquals(21, ZoneDateTimeUtils.millisecond(ZoneDateTimeUtils.plusMilliseconds(zonedDateTime, 1)));
    }


    @Test
    @DisplayName("获取ZonedDateTime的年份")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testYear() {
        assertEquals(2023, ZoneDateTimeUtils.year(zonedDateTime));
    }

    @Test
    @DisplayName("获取ZonedDateTime的月份")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testMonth() {
        assertEquals(5, ZoneDateTimeUtils.month(zonedDateTime));
    }

    @Test
    @DisplayName("获取ZonedDateTime的天数")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testDay() {
        assertEquals(16, ZoneDateTimeUtils.day(zonedDateTime));
    }

    @Test
    @DisplayName("获取ZonedDateTime的小时")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testHour() {
        assertEquals(17, ZoneDateTimeUtils.hour(zonedDateTime));
    }

    @Test
    @DisplayName("获取ZonedDateTime的分钟")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testMinute() {
        assertEquals(18, ZoneDateTimeUtils.minute(zonedDateTime));
    }

    @Test
    @DisplayName("获取ZonedDateTime的秒")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testSecond() {
        assertEquals(19, ZoneDateTimeUtils.second(zonedDateTime));
    }

    @Test
    @DisplayName("获取ZonedDateTime的毫秒")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testMillisecond() {
        assertEquals(20, ZoneDateTimeUtils.millisecond(zonedDateTime));
    }


    @Test
    @DisplayName("获取ZoneDateTime的starOfDay")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void starOfDay() {
        final ZonedDateTime reslut = ZoneDateTimeUtils.starOfDay(zonedDateTime);
        assertEquals(0, ZoneDateTimeUtils.hour(reslut));
        assertEquals(0, ZoneDateTimeUtils.minute(reslut));
        assertEquals(0, ZoneDateTimeUtils.second(reslut));
        assertEquals(0, ZoneDateTimeUtils.millisecond(reslut));
    }

    @Test
    @DisplayName("获取ZoneDateTime的endOfDay")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void endOfDay() {
        final ZonedDateTime reslut = ZoneDateTimeUtils.endOfDay(zonedDateTime);
        assertEquals(23, ZoneDateTimeUtils.hour(reslut));
        assertEquals(59, ZoneDateTimeUtils.minute(reslut));
        assertEquals(59, ZoneDateTimeUtils.second(reslut));
    }

    @Test
    @DisplayName("获取ZoneDateTime的firstDayOfYear")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void firstDayOfYear() {
        final ZonedDateTime reslut = ZoneDateTimeUtils.firstDayOfYear(zonedDateTime);
        assertEquals(2023, ZoneDateTimeUtils.year(reslut));
        assertEquals(1, ZoneDateTimeUtils.month(reslut));
        assertEquals(1, ZoneDateTimeUtils.day(reslut));
        assertEquals(0, ZoneDateTimeUtils.hour(reslut));
        assertEquals(0, ZoneDateTimeUtils.minute(reslut));
        assertEquals(0, ZoneDateTimeUtils.second(reslut));
    }

    @Test
    @DisplayName("获取ZoneDateTime的firstDayOfYearWithOption")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void firstDayOfYear_with_option() {
        final ZonedDateTime reslut = ZoneDateTimeUtils.firstDayOfYear(zonedDateTime, false);
        assertEquals(2023, ZoneDateTimeUtils.year(reslut));
        assertEquals(1, ZoneDateTimeUtils.month(reslut));
        assertEquals(1, ZoneDateTimeUtils.day(reslut));
        assertEquals(23, ZoneDateTimeUtils.hour(reslut));
        assertEquals(59, ZoneDateTimeUtils.minute(reslut));
        assertEquals(59, ZoneDateTimeUtils.second(reslut));
    }

    @Test
    @DisplayName("获取ZoneDateTime的lastDayOfYear")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void lastDayOfYear() {
        final ZonedDateTime reslut = ZoneDateTimeUtils.lastDayOfYear(zonedDateTime);
        assertEquals(2023, ZoneDateTimeUtils.year(reslut));
        assertEquals(12, ZoneDateTimeUtils.month(reslut));
        assertEquals(31, ZoneDateTimeUtils.day(reslut));
        assertEquals(0, ZoneDateTimeUtils.hour(reslut));
        assertEquals(0, ZoneDateTimeUtils.minute(reslut));
        assertEquals(0, ZoneDateTimeUtils.second(reslut));
    }

    @Test
    @DisplayName("获取ZoneDateTime的lastDayOfYearWithOption")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void lastDayOfYear_with_option() {
        final ZonedDateTime reslut = ZoneDateTimeUtils.lastDayOfYear(zonedDateTime, false);
        assertEquals(2023, ZoneDateTimeUtils.year(reslut));
        assertEquals(12, ZoneDateTimeUtils.month(reslut));
        assertEquals(31, ZoneDateTimeUtils.day(reslut));
        assertEquals(23, ZoneDateTimeUtils.hour(reslut));
        assertEquals(59, ZoneDateTimeUtils.minute(reslut));
        assertEquals(59, ZoneDateTimeUtils.second(reslut));
    }

    @Test
    @DisplayName("获取ZoneDateTime的firstDayOfMonth")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void firstDayOfMonth() {
        final ZonedDateTime reslut = ZoneDateTimeUtils.firstDayOfMonth(zonedDateTime);
        assertEquals(2023, ZoneDateTimeUtils.year(reslut));
        assertEquals(5, ZoneDateTimeUtils.month(reslut));
        assertEquals(1, ZoneDateTimeUtils.day(reslut));
        assertEquals(0, ZoneDateTimeUtils.hour(reslut));
        assertEquals(0, ZoneDateTimeUtils.minute(reslut));
        assertEquals(0, ZoneDateTimeUtils.second(reslut));
    }

    @Test
    @DisplayName("获取ZoneDateTime的firstDayOfMonthWithOption")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void firstDayOfMonth_with_option() {
        final ZonedDateTime reslut = ZoneDateTimeUtils.firstDayOfMonth(zonedDateTime, false);
        assertEquals(2023, ZoneDateTimeUtils.year(reslut));
        assertEquals(5, ZoneDateTimeUtils.month(reslut));
        assertEquals(1, ZoneDateTimeUtils.day(reslut));
        assertEquals(23, ZoneDateTimeUtils.hour(reslut));
        assertEquals(59, ZoneDateTimeUtils.minute(reslut));
        assertEquals(59, ZoneDateTimeUtils.second(reslut));
    }

    @Test
    @DisplayName("获取ZoneDateTime的lastDayOfMonth")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void lastDayOfMonth() {
        final ZonedDateTime reslut = ZoneDateTimeUtils.lastDayOfMonth(zonedDateTime);
        assertEquals(2023, ZoneDateTimeUtils.year(reslut));
        assertEquals(5, ZoneDateTimeUtils.month(reslut));
        assertEquals(31, ZoneDateTimeUtils.day(reslut));
        assertEquals(0, ZoneDateTimeUtils.hour(reslut));
        assertEquals(0, ZoneDateTimeUtils.minute(reslut));
        assertEquals(0, ZoneDateTimeUtils.second(reslut));

    }

    @Test
    @DisplayName("获取ZoneDateTime的lastDayOfMonthWithOption")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void lastDayOfMonth_with_option() {
        final ZonedDateTime reslut = ZoneDateTimeUtils.lastDayOfMonth(zonedDateTime, false);
        assertEquals(2023, ZoneDateTimeUtils.year(reslut));
        assertEquals(5, ZoneDateTimeUtils.month(reslut));
        assertEquals(31, ZoneDateTimeUtils.day(reslut));
        assertEquals(23, ZoneDateTimeUtils.hour(reslut));
        assertEquals(59, ZoneDateTimeUtils.minute(reslut));
        assertEquals(59, ZoneDateTimeUtils.second(reslut));
    }

    @Test
    @DisplayName("获取ZonedDateTime的ofLocalDate")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testOfZonedDateTime() {
        final ZonedDateTime result = ZoneDateTimeUtils.ofZonedDateTime(2023, 5, 16, 17, 18, 19, 20000000);
        assertEquals(2023, ZoneDateTimeUtils.year(result));
        assertEquals(5, ZoneDateTimeUtils.month(result));
        assertEquals(16, ZoneDateTimeUtils.day(result));
        assertEquals(17, ZoneDateTimeUtils.hour(result));
        assertEquals(18, ZoneDateTimeUtils.minute(result));
        assertEquals(19, ZoneDateTimeUtils.second(result));
        assertEquals(20, ZoneDateTimeUtils.millisecond(result));
    }

    @Test
    @DisplayName("判断时间是否处于start和end时间之间")
    @Tags({
            @Tag("@id:23"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/5/24")
    })
    void test_isEffectiveDate(){
        ZonedDateTime nowDate = ZoneDateTimeUtils.ofZonedDateTime(2023, 5, 16, 16, 18, 19, 20000000);
        ZonedDateTime startDate = ZoneDateTimeUtils.ofZonedDateTime(2023, 5, 16, 15, 18, 19, 20000000);
        ZonedDateTime endDate = ZoneDateTimeUtils.ofZonedDateTime(2023, 5, 16, 18, 18, 19, 20000000);
        assertTrue(ZoneDateTimeUtils.isEffectiveDate(nowDate, startDate, endDate));
        startDate = ZoneDateTimeUtils.ofZonedDateTime(2023, 5, 16, 17, 18, 19, 20000000);
        endDate = ZoneDateTimeUtils.ofZonedDateTime(2023, 5, 16, 17, 19, 19, 20000000);
        assertFalse(ZoneDateTimeUtils.isEffectiveDate(nowDate, startDate, endDate));
    }

    @Test
    @DisplayName("ZonedDateTime转换为Date")
    @Tags({
            @Tag("@id:20"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/5/24")
    })
    void test_toDate(){
        ZonedDateTime nowDate = ZoneDateTimeUtils.ofZonedDateTime(2023, 5, 16, 17, 18, 19, 20000000);
        Date date = ZoneDateTimeUtils.toDate(nowDate);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int year = calendar.get(Calendar.YEAR);
        int monthValue = calendar.get(Calendar.MONTH)+1;
        int dayOfMonth = calendar.get(Calendar.DAY_OF_MONTH);
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int minute = calendar.get(Calendar.MINUTE);
        int second = calendar.get(Calendar.SECOND);
        assertEquals(year,2023);
        assertEquals(monthValue,5);
        assertEquals(dayOfMonth,16);
        assertEquals(hour,17);
        assertEquals(minute,18);
        assertEquals(second,19);
    }

    @Test
    @DisplayName("ZonedDateTime转换为LocalDateTime")
    @Tags({
            @Tag("@id:20"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/5/24")
    })
    void test_toLocalDateTime(){
        ZonedDateTime nowDate = ZoneDateTimeUtils.ofZonedDateTime(2023, 5, 16, 17, 18, 19, 20000000);
        LocalDateTime localDateTime = ZoneDateTimeUtils.toLoacalDateTime(nowDate);
        int year = localDateTime.getYear();
        int monthValue = localDateTime.getMonthValue();
        int dayOfMonth = localDateTime.getDayOfMonth();
        int hour = localDateTime.getHour();
        int minute = localDateTime.getMinute();
        int second = localDateTime.getSecond();
        assertEquals(year,2023);
        assertEquals(monthValue,5);
        assertEquals(dayOfMonth,16);
        assertEquals(hour,17);
        assertEquals(minute,18);
        assertEquals(second,19);
    }

    @Test
    @DisplayName("ZonedDateTime转换为LocalDate")
    @Tags({
            @Tag("@id:20"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/5/24")
    })
    void test_toLocalDate(){
        ZonedDateTime nowDate = ZoneDateTimeUtils.ofZonedDateTime(2023, 5, 16, 17, 18, 19, 20000000);
        LocalDate localDateTime = ZoneDateTimeUtils.toLoacalDate(nowDate);
        int year = localDateTime.getYear();
        int monthValue = localDateTime.getMonthValue();
        int dayOfMonth = localDateTime.getDayOfMonth();
        assertEquals(year,2023);
        assertEquals(monthValue,5);
        assertEquals(dayOfMonth,16);
    }

    @Test
    @DisplayName("获取两个时间之间的日期")
    @Tags({
            @Tag("@id:104"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/6/20")
    })
    void testGetBetweenDate_tenDay(){
        ZonedDateTime start = ZoneDateTimeUtils.ofZonedDateTime(2024,6,21,0,0,0,0);
        ZonedDateTime end = ZoneDateTimeUtils.ofZonedDateTime(2024,7,1,0,0,0,0);
        List<ZonedDateTime> betweenDate = ZoneDateTimeUtils.getBetweenDate(start, end, ChronoUnit.DAYS, 1);
        assertEquals(betweenDate.size(),11);
    }

    @Test
    @DisplayName("获取两个时间之间的日期,不满足条件测试")
    @Tags({
            @Tag("@id:104"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/6/20")
    })
    void testGetBetweenDate_parmError(){
        ZonedDateTime start = ZoneDateTimeUtils.ofZonedDateTime(2024,6,21,0,0,0,0);
        ZonedDateTime end = ZoneDateTimeUtils.ofZonedDateTime(2024,7,1,0,0,0,0);

        assertThrows(IllegalArgumentException.class,
                () -> ZoneDateTimeUtils.getBetweenDate(null, end, ChronoUnit.DAYS, 1));
        assertThrows(IllegalArgumentException.class,
                () -> ZoneDateTimeUtils.getBetweenDate(start, null, ChronoUnit.DAYS, 1));
        assertThrows(IllegalArgumentException.class,
                () -> ZoneDateTimeUtils.getBetweenDate(start, end, ChronoUnit.DAYS, 0));
    }

    @Test
    @DisplayName("获取某个时间之前或之后的时间")
    @Tags({
            @Tag("@id:104"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/6/20")
    })
    void testGetNextDates_tenDay(){
        ZonedDateTime start = ZoneDateTimeUtils.ofZonedDateTime(2024,6,21,0,0,0,0);
        List<ZonedDateTime> nextAfterDates = ZoneDateTimeUtils.getNextDates(start, ChronoUnit.DAYS, 10, false);
        assertEquals(nextAfterDates.size(),10);
        List<ZonedDateTime> nextBeforDates = ZoneDateTimeUtils.getNextDates(start, ChronoUnit.DAYS, -10, false);
        assertEquals(nextBeforDates.size(),10);
    }

    @Test
    @DisplayName("获取某个时间之前或之后的时间,错误参数")
    @Tags({
            @Tag("@id:104"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/6/20")
    })
    void testGetNextDates_parmError(){
        ZonedDateTime start = ZoneDateTimeUtils.ofZonedDateTime(2024,6,21,0,0,0,0);
        assertThrows(IllegalArgumentException.class,
                () -> ZoneDateTimeUtils.getNextDates(null, ChronoUnit.DAYS, 10, false));
        assertThrows(IllegalArgumentException.class,
                () -> ZoneDateTimeUtils.getNextDates(start, null, 10, false));
    }

    @Test
    @DisplayName("获取两个时间之间的日期,不包括开始和结束时间")
    @Tags({
            @Tag("@id:104"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/6/20")
    })
    void testGetBetweenDate_noIncludeStartAndEnd(){
        ZonedDateTime start = ZoneDateTimeUtils.ofZonedDateTime(2024,6,21,0,0,0,0);
        ZonedDateTime end = ZoneDateTimeUtils.ofZonedDateTime(2024,7,1,0,0,0,0);
        List<ZonedDateTime> betweenDate = ZoneDateTimeUtils.getBetweenDate(start, end, ChronoUnit.DAYS, 1,false,false);
        assertEquals(betweenDate.size(),9);
        assertNotEquals(betweenDate.get(0),start);
        assertNotEquals(betweenDate.get(betweenDate.size()-1),end);
    }

    @Test
    @DisplayName("获取两个时间之间的日期,倒序列表")
    @Tags({
            @Tag("@id:104"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/6/20")
    })
    void testGetBetweenDate_reverse(){
        ZonedDateTime start = ZoneDateTimeUtils.ofZonedDateTime(2024,6,21,0,0,0,0);
        ZonedDateTime end = ZoneDateTimeUtils.ofZonedDateTime(2024,7,1,0,0,0,0);
        List<ZonedDateTime> betweenDate = ZoneDateTimeUtils.getBetweenDate(start, end, ChronoUnit.DAYS, -1);
        assertEquals(betweenDate.size(),11);
        assertEquals(betweenDate.get(0),end);
        assertEquals(betweenDate.get(betweenDate.size()-1),start);
    }
}