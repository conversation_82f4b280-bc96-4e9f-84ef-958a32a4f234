/*
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */

package com.snbc.bbpf.commons.strings.security.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @ClassName:  Desensitized
 * 脱敏注解
 * @module:    字符脱敏
 * @Author:    yangweipeng
 * @date:      2023/05/06
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
@Target({ElementType.FIELD, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
public @interface Desensitized {

    /**
     * 脱敏类型(规则)
     * @return 脱敏类型枚举值
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/6
     */
    SensitiveType type();

    /**
     * 判断注解是否生效的方法

     * @return 方法名（需无参且返回boolean类型）
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/6
     */
    String isEffictiveMethod() default "";

    /**
     * 自定义脱敏保留前几位（仅当type为CUSTOM时生效）
     * @return 保留前几位数字，默认为3
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/28
     */
    int prefixKeep() default 3;

    /**
     * 自定义脱敏保留后几位（仅当type为CUSTOM时生效）
     * @return 保留后几位数字，默认为4
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/28
     */
    int suffixKeep() default 4;

    /**
     * 自定义脱敏符号（仅当type为CUSTOM时生效）
     * @return 脱敏符号，默认为*
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/28
     */
    String maskSymbol() default "*";

}
