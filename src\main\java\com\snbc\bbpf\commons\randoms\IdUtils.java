/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.randoms;

import com.github.f4b6a3.ulid.Ulid;
import com.github.f4b6a3.ulid.UlidCreator;
import com.snbc.bbpf.commons.dates.DateFormatUtil;
import com.snbc.bbpf.commons.strings.StringEmptyCheck;

import java.time.LocalDate;
import java.util.UUID;

/**
 * 类描述:指定格式随机字符串
 * 1.返回前缀+年月日+n位随机数的字符串
 * 2.返回前缀+UUID的字符串（去掉-或不去掉-）
 * 3.返回UUID （去掉-或不去掉-） ，注释需要说明返回的长度。
 * 4.返回指定数量的UUID的String数组
 * 5.使用ULID生成有序的ID
 * 6.看之前的随机字符串功能是否满足，满足利用之前的方法。
 * <p>
 * ULID（Universally Unique Lexicographically Sortable Identifier）是一种可排序、唯一的标识符，
 * 由Alizain Feerasta在2016年提出，它结合了时间戳和随机数生成器来生成一个32位的标识符，
 * 适用于分布式系统中标识数据实体和事件等场景。
 *
 * <AUTHOR>
 * 创建时间:  [2023/6/8 11:45]
 */
public final class IdUtils {


    private IdUtils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 生成带有前缀、年月日和随机数的字符串
     * @param prefix 前缀字符串（若为null或空字符串，视为空）
     * @param randomLength 随机数的长度
     * @return 组合后的字符串（格式：前缀+年月日+随机数）
     * <AUTHOR>
     * @date 2023/6/6
     * @since 1.1.0
     */
    public static String generateString(String prefix, int randomLength) {
        if (StringEmptyCheck.isEmpty(prefix)) {
            prefix = "";
        }
        // 生成当前日期
        String currentDate = getCurrentDate();
        // 生成随机数
        String randomString = RandomNumberUtils.randomNumeric(randomLength);
        // 组合生成的字符串
        return prefix + currentDate + randomString;
    }

    /*
     * 获取当前日期，格式为年月日
     * <AUTHOR>
     * @date 2023/6/6
     * @since 1.1.0
     */
    private static String getCurrentDate() {
        return DateFormatUtil.format(LocalDate.now(), DateFormatUtil.PURE_DATE_FORMATTER);
    }


    /**
     * 生成带有前缀和UUID的字符串（不含连字符）
     * @param prefix 前缀字符串（若为null或空字符串，视为空）
     * @return 组合后的字符串（格式：前缀+无连字符UUID）
     * <AUTHOR>
     * @date 2023/6/6
     * @since 1.1.0
     */

    public static String generateStringWithoutHyphen(String prefix) {
        if (StringEmptyCheck.isEmpty(prefix)) {
            prefix = "";
        }
        String uuid = UUID.randomUUID().toString().replace("-", "");
        return prefix + uuid;
    }

    /**
     * 生成带有前缀和UUID的字符串（保留-）
     * @param prefix 前缀字符串（若为null或空字符串，视为空）
     * @return 组合后的字符串（格式：前缀+带连字符UUID）
     * <AUTHOR>
     * @date 2023/6/6
     * @since 1.1.0
     */
    public static String generateStringWithHyphen(String prefix) {
        if (StringEmptyCheck.isEmpty(prefix)) {
            prefix = "";
        }
        String uuid = UUID.randomUUID().toString();
        return prefix + uuid;
    }

    /**
     * 生成UUID字符串（带有或不带有连字符-）
     *
     * @param removeHyphens 指定是否去除连字符
     * @return 生成的UUID字符串
     * <AUTHOR>
     * @date 2023/6/6
     * @since 1.1.0
     */
    public static String generateUUID(boolean removeHyphens) {
        UUID uuid = UUID.randomUUID();
        String uuidString = uuid.toString();
        if (removeHyphens) {
            uuidString = uuidString.replace("-", "");
        }
        return uuidString;
    }

    /**
     * 生成指定数量的UUID字符串数组（带连字符）
     *
     * @param count 数量
     * @return UUID字符串数组
     * <AUTHOR>
     * @date 2023/6/6
     * @since 1.1.0
     */
    public static String[] generateUUIDsWithHyphens(int count) {
        String[] uuids = new String[count];
        for (int i = 0; i < count; i++) {
            uuids[i] = UUID.randomUUID().toString();
        }
        return uuids;
    }

    /**
     * 生成指定数量的UUID字符串数组（不带连字符）
     *
     * @param count 数量
     * @return UUID字符串数组
     * <AUTHOR>
     * @date 2023/6/6
     * @since 1.1.0
     */
    public static String[] generateUUIDsWithoutHyphens(int count) {
        String[] uuids = new String[count];
        for (int i = 0; i < count; i++) {
            uuids[i] = UUID.randomUUID().toString().replace("-", "");
        }
        return uuids;
    }

    /**
     * 生成有序的ULID字符串
     *
     * @return 有序的ULID字符串
     * <AUTHOR>
     * @date 2023/6/6
     * @since 1.1.0
     */
    public static String generateULID() {
        //// 单调排序
        Ulid ulid = UlidCreator.getMonotonicUlid();
        return ulid.toLowerCase();
    }


}
