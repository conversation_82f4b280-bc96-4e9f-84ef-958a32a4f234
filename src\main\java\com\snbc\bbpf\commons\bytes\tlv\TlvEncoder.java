/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.bytes.tlv;

import com.snbc.bbpf.commons.bytes.ByteOrderUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * TLV格式数据编码器
 * <p>实现Type-Length-Value格式数据的编码功能</p>
 * <p>支持多种数据类型的自动识别和序列化</p>
 * <p>支持基本类型、字符串类型、复杂类型和二进制数据的编码</p>
 * 
 * <AUTHOR>
 * @module 字节处理
 * @date 2025/07/25 10:30
 * @copyright 2024 山东新北洋信息技术股份有限公司. All rights reserved
 * @since 1.7.0
 */
public class TlvEncoder {
    
    /** 默认字节序 */
    private final ByteOrder byteOrder;
    /** 字符串编码方式 */
    private final Charset defaultCharset;
    /** Type字段长度（字节数） */
    private final int typeLength;
    /** Length字段长度（字节数） */
    private final int lengthFieldSize;
    
    /**
     * 使用默认配置创建TLV编码器
     * 默认配置：大端字节序，UTF-8编码，Type字段1字节，Length字段4字节
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public TlvEncoder() {
        this(ByteOrder.BIG_ENDIAN, StandardCharsets.UTF_8, 1, 4);
    }
    
    /**
     * 创建TLV编码器
     * @param byteOrder 字节序
     * @param defaultCharset 默认字符编码
     * @param typeLength Type字段长度（字节数）
     * @param lengthFieldSize Length字段长度（字节数）
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public TlvEncoder(ByteOrder byteOrder, Charset defaultCharset, int typeLength, int lengthFieldSize) {
        if (byteOrder == null) {
            throw new IllegalArgumentException("Byte order cannot be null");
        }
        if (defaultCharset == null) {
            throw new IllegalArgumentException("Default charset cannot be null");
        }
        if (typeLength <= 0 || typeLength > 4) {
            throw new IllegalArgumentException("Type length must be between 1 and 4 bytes");
        }
        if (lengthFieldSize <= 0 || lengthFieldSize > 8) {
            throw new IllegalArgumentException("Length field size must be between 1 and 8 bytes");
        }
        
        this.byteOrder = byteOrder;
        this.defaultCharset = defaultCharset;
        this.typeLength = typeLength;
        this.lengthFieldSize = lengthFieldSize;
    }
    
    /**
     * 编码数据为TLV格式
     * @param data 要编码的数据
     * @return TLV格式的字节数组
     * @throws IllegalArgumentException 如果数据类型不支持
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public byte[] encode(Object data) {
        if (data == null) {
            return encodeNull();
        }
        
        // 自动识别数据类型
        TlvDataType dataType = TlvDataType.fromJavaType(data.getClass());
        if (dataType == null) {
            throw new IllegalArgumentException("Unsupported data type: " + data.getClass().getName());
        }
        
        return encode(data, dataType);
    }
    
    /**
     * 编码数据为TLV格式，指定数据类型
     * @param data 要编码的数据
     * @param dataType TLV数据类型
     * @return TLV格式的字节数组
     * @throws IllegalArgumentException 如果数据类型不匹配或不支持
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public byte[] encode(Object data, TlvDataType dataType) {
        if (dataType == null) {
            throw new IllegalArgumentException("Data type cannot be null");
        }
        
        // 编码Value部分
        byte[] valueBytes = encodeValue(data, dataType);
        
        // 计算总长度
        int totalLength = typeLength + lengthFieldSize + valueBytes.length;
        
        // 创建结果缓冲区
        ByteBuffer buffer = ByteBuffer.allocate(totalLength);
        buffer.order(byteOrder);
        
        // 写入Type字段
        writeTypeField(buffer, dataType.getTypeValue());
        
        // 写入Length字段
        writeLengthField(buffer, valueBytes.length);
        
        // 写入Value字段
        buffer.put(valueBytes);
        
        return buffer.array();
    }
    
    /**
     * 编码空值
     * @return TLV格式的字节数组
     */
    private byte[] encodeNull() {
        ByteBuffer buffer = ByteBuffer.allocate(typeLength + lengthFieldSize);
        buffer.order(byteOrder);
        
        writeTypeField(buffer, TlvDataType.NULL.getTypeValue());
        writeLengthField(buffer, 0);
        
        return buffer.array();
    }
    
    /**
     * 编码Value部分
     * @param data 要编码的数据
     * @param dataType TLV数据类型
     * @return Value部分的字节数组
     */
    private byte[] encodeValue(Object data, TlvDataType dataType) {
        if (data == null) {
            return new byte[0];
        }
        
        switch (dataType) {
            case BYTE:
                return encodeByteValue((Byte) data);
            case SHORT:
                return encodeShortValue((Short) data);
            case INTEGER:
                return encodeIntegerValue((Integer) data);
            case LONG:
                return encodeLongValue((Long) data);
            case BIG_INTEGER:
                return encodeBigIntegerValue((BigInteger) data);
            case FLOAT:
                return encodeFloatValue((Float) data);
            case DOUBLE:
                return encodeDoubleValue((Double) data);
            case BIG_DECIMAL:
                return encodeBigDecimalValue((BigDecimal) data);
            case BOOLEAN:
                return encodeBooleanValue((Boolean) data);
            case STRING_UTF8:
                return encodeStringValue((String) data, StandardCharsets.UTF_8);
            case STRING_ASCII:
                return encodeStringValue((String) data, StandardCharsets.US_ASCII);
            case BYTE_ARRAY:
                return encodeByteArrayValue((byte[]) data);
            case MAP:
                return encodeMapValue((Map<?, ?>) data);
            case ARRAY:
                return encodeArrayValue((Iterable<?>) data);
            default:
                throw new IllegalArgumentException("Unsupported data type: " + dataType);
        }
    }
    
    /**
     * 写入Type字段
     * @param buffer 字节缓冲区
     * @param typeValue Type值
     */
    private void writeTypeField(ByteBuffer buffer, int typeValue) {
        switch (typeLength) {
            case 1:
                buffer.put((byte) typeValue);
                break;
            case 2:
                buffer.putShort((short) typeValue);
                break;
            case 4:
                buffer.putInt(typeValue);
                break;
            default:
                throw new IllegalArgumentException("Unsupported type length: " + typeLength);
        }
    }
    
    /**
     * 写入Length字段
     * @param buffer 字节缓冲区
     * @param length 长度值
     */
    private void writeLengthField(ByteBuffer buffer, int length) {
        switch (lengthFieldSize) {
            case 1:
                if (length > 255) {
                    throw new IllegalArgumentException("Length too large for 1-byte field: " + length);
                }
                buffer.put((byte) length);
                break;
            case 2:
                if (length > 65535) {
                    throw new IllegalArgumentException("Length too large for 2-byte field: " + length);
                }
                buffer.putShort((short) length);
                break;
            case 4:
                buffer.putInt(length);
                break;
            case 8:
                buffer.putLong(length);
                break;
            default:
                throw new IllegalArgumentException("Unsupported length field size: " + lengthFieldSize);
        }
    }
    
    // 基本数据类型编码方法
    private byte[] encodeByteValue(Byte value) {
        return new byte[]{value};
    }
    
    private byte[] encodeShortValue(Short value) {
        return ByteOrderUtils.toByteArray(value, byteOrder);
    }
    
    private byte[] encodeIntegerValue(Integer value) {
        return ByteOrderUtils.toByteArray(value, byteOrder);
    }
    
    private byte[] encodeLongValue(Long value) {
        return ByteOrderUtils.toByteArray(value, byteOrder);
    }
    
    private byte[] encodeBigIntegerValue(BigInteger value) {
        return value.toByteArray();
    }
    
    private byte[] encodeFloatValue(Float value) {
        return ByteOrderUtils.toByteArray(Float.floatToIntBits(value), byteOrder);
    }
    
    private byte[] encodeDoubleValue(Double value) {
        return ByteOrderUtils.toByteArray(Double.doubleToLongBits(value), byteOrder);
    }
    
    private byte[] encodeBigDecimalValue(BigDecimal value) {
        return value.toString().getBytes(defaultCharset);
    }
    
    private byte[] encodeBooleanValue(Boolean value) {
        return new byte[]{value ? (byte) 1 : (byte) 0};
    }
    
    private byte[] encodeStringValue(String value, Charset charset) {
        return value.getBytes(charset);
    }
    
    private byte[] encodeByteArrayValue(byte[] value) {
        return value.clone();
    }

    /**
     * 编码Map类型数据
     * Map编码格式：[元素数量(4字节)] + [Key1的TLV] + [Value1的TLV] + [Key2的TLV] + [Value2的TLV] + ...
     * @param value Map对象
     * @return 编码后的字节数组
     */
    private byte[] encodeMapValue(Map<?, ?> value) {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            // 写入元素数量
            byte[] sizeBytes = ByteOrderUtils.toByteArray(value.size(), byteOrder);
            baos.write(sizeBytes);

            // 编码每个键值对
            for (Map.Entry<?, ?> entry : value.entrySet()) {
                // 编码Key
                byte[] keyTlv = encode(entry.getKey());
                baos.write(keyTlv);

                // 编码Value
                byte[] valueTlv = encode(entry.getValue());
                baos.write(valueTlv);
            }

            return baos.toByteArray();
        } catch (IOException e) {
            throw new RuntimeException("Failed to encode Map value", e);
        }
    }

    /**
     * 编码数组/集合类型数据
     * 数组编码格式：[元素数量(4字节)] + [Element1的TLV] + [Element2的TLV] + ...
     * @param value Iterable对象
     * @return 编码后的字节数组
     */
    private byte[] encodeArrayValue(Iterable<?> value) {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            // 先收集所有元素的TLV编码
            ByteArrayOutputStream elementsStream = new ByteArrayOutputStream();
            int elementCount = 0;

            for (Object element : value) {
                byte[] elementTlv = encode(element);
                elementsStream.write(elementTlv);
                elementCount++;
            }

            // 写入元素数量
            byte[] sizeBytes = ByteOrderUtils.toByteArray(elementCount, byteOrder);
            baos.write(sizeBytes);

            // 写入所有元素的TLV编码
            baos.write(elementsStream.toByteArray());

            return baos.toByteArray();
        } catch (IOException e) {
            throw new RuntimeException("Failed to encode Array value", e);
        }
    }

    /**
     * 获取字节序
     * @return 字节序
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public ByteOrder getByteOrder() {
        return byteOrder;
    }

    /**
     * 获取默认字符编码
     * @return 默认字符编码
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public Charset getDefaultCharset() {
        return defaultCharset;
    }

    /**
     * 获取Type字段长度
     * @return Type字段长度（字节数）
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public int getTypeLength() {
        return typeLength;
    }

    /**
     * 获取Length字段长度
     * @return Length字段长度（字节数）
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public int getLengthFieldSize() {
        return lengthFieldSize;
    }
}
