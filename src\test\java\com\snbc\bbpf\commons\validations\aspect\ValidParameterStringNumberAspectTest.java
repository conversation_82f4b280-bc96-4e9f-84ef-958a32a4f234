/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.validations.aspect;

import com.snbc.bbpf.commons.strings.StringDigitCheck;
import com.snbc.bbpf.commons.validations.annotation.ValidStringNumber;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 字符串是否为整数或浮点数注解方法参数的测试类
 *
 * <AUTHOR>
 * @module
 * @date 2023/10/30 13:59
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class ValidParameterStringNumberAspectTest {

    @Test
    @DisplayName("未添加注解，不进行参数校验")
    @Tags({
            @Tag("@id:67"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/10/30")
    })
    void testNoAnnotation_noAnnotation() {
        assertTrue(noAnnotation("aaa"));
    }

    @Test
    @DisplayName("方法未添加注解，不进行参数校验")
    @Tags({
            @Tag("@id:67"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/10/30")
    })
    void testNoMethodAnno_noMethodAnno() {
        assertTrue(noMethodAnno("aaa"));
    }

    @Test
    @DisplayName("方法参数未添加注解，不进行参数校验")
    @Tags({
            @Tag("@id:67"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/10/30")
    })
    void testNoParameterAnno_noParameterAnno() {
        assertTrue(noParameterAnno("aaa"));
    }

    @Test
    @DisplayName("方法参数添加注解，参数校验失败")
    @Tags({
            @Tag("@id:67"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/10/30")
    })
    void testAnnoString_annoString() {
        IllegalArgumentException thrown1 = assertThrows(IllegalArgumentException.class,
                () -> annoString("aaa"));
        assertEquals("method[annoString]parameter[arg0]number check error", thrown1.getMessage());
    }

    @Test
    @DisplayName("方法参数添加注解，参数为空")
    @Tags({
            @Tag("@id:67"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/10/30")
    })
    void testAnnoString_annoStringEmpty() {
        IllegalArgumentException thrown1 = assertThrows(IllegalArgumentException.class,
                () -> annoString(null));
        assertEquals("method[annoString]parameter[arg0]can't be null", thrown1.getMessage());
    }


    public Boolean noAnnotation(String str) {
        return !StringDigitCheck.isFloat(str);
    }

    public Boolean noMethodAnno(@ValidStringNumber String str) {
        return !StringDigitCheck.isFloat(str);
    }

    @ValidStringNumber
    public Boolean noParameterAnno(String str) {
        return !StringDigitCheck.isFloat(str);
    }

    @ValidStringNumber
    public Boolean annoString(@ValidStringNumber String str) {
        return !StringDigitCheck.isFloat(str);
    }

}
