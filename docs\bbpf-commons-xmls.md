# XML处理 (com.snbc.bbpf.commons.xmls)

## 类概览 

| 类名 | 功能描述 |
|------|----------|
| XPathUtils | 提供XML XPath操作功能，包括查找、修改、删除XML节点和属性 |
| XmlValidateUtils | 提供XML格式验证功能，支持基于DTD验证XML |
| XmlObjectMapper | 提供Java对象与XML之间的转换功能 |
| XmlJsonMapper | 提供JSON与XML之间的转换功能 |

## XPathUtils - XML XPath操作工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| verfyXmlFormat(String xmlStr) | 校验XML字符串格式是否正确 | xmlStr: XML字符串 | void |
| getChildStringByXpath(String xmlStr, String xpath) | 从XML字符串中查找满足XPath条件的节点 | xmlStr: XML字符串<br>xpath: XPath表达式 | List<String>: 匹配的节点XML字符串列表 |
| getFirstNodeValue(String xmlStr, String nodeName, String attributeName) | 获取第一个指定名称且包含指定属性的节点的值 | xmlStr: XML字符串<br>nodeName: 节点名称<br>attributeName: 属性名称 | String: 节点值 |
| getAllNodeValue(String xmlStr, String nodeName, String attributeName) | 获取所有指定名称且包含指定属性的节点的值 | xmlStr: XML字符串<br>nodeName: 节点名称<br>attributeName: 属性名称 | List<String>: 节点值列表 |
| getFirstNodeXmlStr(String xmlStr, String nodeName, String attributeName) | 获取第一个指定名称且包含指定属性的节点XML字符串 | xmlStr: XML字符串<br>nodeName: 节点名称<br>attributeName: 属性名称 | String: 节点XML字符串 |
| getAllNodeXmlStr(String xmlStr, String nodeName, String attributeName) | 获取所有指定名称且包含指定属性的节点XML字符串 | xmlStr: XML字符串<br>nodeName: 节点名称<br>attributeName: 属性名称 | List<String>: 节点XML字符串列表 |
| getFirstNodeAttributeValue(String xmlStr, String nodeName, String attributeName) | 获取第一个指定名称且包含指定属性的节点的该属性值 | xmlStr: XML字符串<br>nodeName: 节点名称<br>attributeName: 属性名称 | String: 属性值 |
| getAllNodeAttributeValue(String xmlStr, String nodeName, String attributeName) | 获取所有指定名称且包含指定属性的节点的该属性值 | xmlStr: XML字符串<br>nodeName: 节点名称<br>attributeName: 属性名称 | List<String>: 属性值列表 |
| removeNode(String xml, String nodeXpathExpr) | 删除满足条件的节点 | xml: XML字符串<br>nodeXpathExpr: 节点XPath表达式 | String: 修改后的XML字符串 |
| removeAttribute(String xml, String nodeXpathExpr, String attributeName) | 删除满足条件的节点的指定属性 | xml: XML字符串<br>nodeXpathExpr: 节点XPath表达式<br>attributeName: 属性名称 | String: 修改后的XML字符串 |
| removeNodeText(String xml, String nodeXpathExpr) | 删除满足条件的节点的文本内容 | xml: XML字符串<br>nodeXpathExpr: 节点XPath表达式 | String: 修改后的XML字符串 |
| getNodeText(String xml, String nodeXpathExpr, Boolean onlyFirstFlag) | 获取满足条件的节点的文本内容 | xml: XML字符串<br>nodeXpathExpr: 节点XPath表达式<br>onlyFirstFlag: 是否只返回第一个匹配节点 | List<String>: 节点文本内容列表 |
| updateAttribute(String xml, String nodeXpathExpr, String attributeName, String attributeValueNew, boolean onlyFirstFlag) | 更新满足条件的节点的属性值 | xml: XML字符串<br>nodeXpathExpr: 节点XPath表达式<br>attributeName: 属性名称<br>attributeValueNew: 新属性值<br>onlyFirstFlag: 是否只更新第一个匹配节点 | String: 修改后的XML字符串 |
| updateNodeText(String xml, String nodeXpathExpr, String textValueNew, boolean onlyFirstFlag) | 更新满足条件的节点的文本内容 | xml: XML字符串<br>nodeXpathExpr: 节点XPath表达式<br>textValueNew: 新文本内容<br>onlyFirstFlag: 是否只更新第一个匹配节点 | String: 修改后的XML字符串 |
| addChildNode(String xml, String xpathExpr, String newNodeName, String newNodeValue, Map<String,String> newNodeAttributeMap, boolean addForFirst) | 为满足条件的父节点添加子节点 | xml: XML字符串<br>xpathExpr: 父节点XPath表达式<br>newNodeName: 新节点名称<br>newNodeValue: 新节点值<br>newNodeAttributeMap: 新节点属性映射<br>addForFirst: 是否只为第一个匹配节点添加 | String: 修改后的XML字符串 |
| insertNode(String xml, String xpathExpr, String newNnodeName, String newNodeValue, Map<String,String> newNodeAttributeMap, boolean beforeOrAfter, boolean insertForFirst) | 在满足条件的节点前后插入节点 | xml: XML字符串<br>xpathExpr: 节点XPath表达式<br>newNnodeName: 新节点名称<br>newNodeValue: 新节点值<br>newNodeAttributeMap: 新节点属性映射<br>beforeOrAfter: 是否在节点前插入(true前/false后)<br>insertForFirst: 是否只为第一个匹配节点插入 | String: 修改后的XML字符串 |

### 使用注意事项

1. **参数校验**：所有方法对参数进行严格校验，如果传入空参数将抛出`IllegalArgumentException`异常。
2. **XPath表达式**：使用前请确保XPath表达式格式正确，否则会抛出`RuntimeException`异常。
3. **返回值处理**：操作查找类方法可能返回空列表，使用前建议进行非空检查。
4. **异常处理**：方法可能抛出运行时异常，建议使用try-catch进行异常捕获处理。
5. **性能考虑**：对于大型XML文档，XPath查询可能影响性能，建议对重复使用的Document对象进行缓存。
6. **修改操作**：所有修改方法都不会改变原始XML字符串，而是返回修改后的新XML字符串。
7. **节点属性查询**：查询节点属性时，如果指定的节点或属性不存在，方法将抛出异常或返回空值。
8. **多节点操作**：多个节点操作时，`onlyFirstFlag`参数决定是否只操作第一个匹配节点。

### 使用示例

```java
// XML字符串示例
String xmlStr = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>"
        + "<root>"
        + "  <person id=\"1\">"
        + "    <name>张三</name>"
        + "    <age>25</age>"
        + "  </person>"
        + "  <person id=\"2\">"
        + "    <name>李四</name>"
        + "    <age>30</age>"
        + "  </person>"
        + "</root>";

// 验证XML格式
XPathUtils.verfyXmlFormat(xmlStr);  // 无异常表示格式正确

// 通过XPath表达式查找节点
List<String> persons = XPathUtils.getChildStringByXpath(xmlStr, "/root/person");
System.out.println("查找到的person节点数量: " + persons.size());  // 输出: 查找到的person节点数量: 2

// 获取节点值
String name = XPathUtils.getFirstNodeValue(xmlStr, "name", "id");
System.out.println("第一个name节点值: " + name);  // 如果name节点有id属性，输出对应的值

// 获取所有节点值
List<String> ages = XPathUtils.getAllNodeValue(xmlStr, "age", "id");
System.out.println("所有age节点值: " + ages);  // 如果age节点有id属性，输出所有匹配节点的值列表

// 通过XPath获取节点文本
List<String> nameTexts = XPathUtils.getNodeText(xmlStr, "//name", false);
System.out.println("所有name节点文本: " + nameTexts);  // 输出: 所有name节点文本: [张三, 李四]

// 修改节点属性
String modifiedXml = XPathUtils.updateAttribute(xmlStr, "//person[@id='1']", "id", "100", true);
System.out.println("修改后XML中id='100'的person节点: " + 
    XPathUtils.getChildStringByXpath(modifiedXml, "//person[@id='100']").get(0));

// 添加子节点
Map<String, String> attributes = new HashMap<>();
attributes.put("type", "mobile");
String addedXml = XPathUtils.addChildNode(xmlStr, "//person[@id='1']", "phone", "12345678", attributes, true);
System.out.println("添加phone节点后的XML: " + addedXml);
```

## XmlValidateUtils - XML验证工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| validateDTD(String xmlFilePath, String dtdFileContent) | 基于DTD文件内容验证XML文件 | xmlFilePath: XML文件路径<br>dtdFileContent: DTD文件内容 | boolean: 验证是否通过 |
| validate(String xmlFilePath, String dtdFilePath) | 基于DTD文件路径验证XML文件 | xmlFilePath: XML文件路径<br>dtdFilePath: DTD文件路径 | boolean: 验证是否通过 |
| validate(String xmlFilePath) | 验证XML文件格式是否正确 | xmlFilePath: XML文件路径 | boolean: 验证是否通过 |

### 使用注意事项

1. **文件路径**：所有方法使用的文件路径必须是有效的，否则将抛出异常。
2. **参数验证**：如果输入参数为空，方法将抛出`IllegalArgumentException`异常。
3. **安全限制**：为防止XML外部实体注入攻击，验证过程中已禁用外部实体解析。
4. **返回值解释**：验证方法返回布尔值，`true`表示验证通过，`false`表示验证失败。
5. **DTD内容处理**：验证时会尝试从XML中提取DOCTYPE，或者插入指定的DTD内容。
6. **文件编码**：默认使用UTF-8编码读取XML和DTD文件。
7. **性能考虑**：验证大型XML文件可能需要较长时间，特别是当DTD复杂时。
8. **异常处理**：验证过程中的SAX解析异常会被捕获并返回`false`，而不是直接抛出。

### 使用示例

```java
// 验证XML文件格式
boolean isValid = XmlValidateUtils.validate("example.xml");
System.out.println("XML格式是否正确: " + isValid);  // 输出验证结果

// 使用DTD文件验证XML
boolean isValidWithDtd = XmlValidateUtils.validate("example.xml", "rules.dtd");
System.out.println("XML是否符合DTD: " + isValidWithDtd);  // 输出验证结果

// 使用DTD内容验证XML
String dtdContent = "<!ELEMENT root (person*)>\n"
    + "<!ELEMENT person (name, age)>\n"
    + "<!ATTLIST person id CDATA #REQUIRED>\n"
    + "<!ELEMENT name (#PCDATA)>\n"
    + "<!ELEMENT age (#PCDATA)>";
boolean isValidWithDtdContent = XmlValidateUtils.validateDTD("example.xml", dtdContent);
System.out.println("XML是否符合DTD内容: " + isValidWithDtdContent);  // 输出验证结果
```

## XmlObjectMapper - XML与对象转换工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| marshall(Object object) | 将对象转换为XML字符串 | object: 要转换的对象 | String: XML字符串 |
| unmarshall(String xmlStr, Class<T> clazz) | 将XML字符串转换为对象 | xmlStr: XML字符串<br>clazz: 目标类型 | <T>: 转换后的对象 |

### 使用注意事项

1. **依赖关系**：该工具基于Jackson XML模块实现，确保项目中已引入相关依赖。
2. **空值处理**：转换对象时不允许传入`null`值，否则将抛出`NullPointerException`异常。
3. **日期支持**：内置了JavaTimeModule以支持Java 8日期时间类型的转换。
4. **注解支持**：支持Jackson注解定制XML结构，包括：
   - `@JsonFormat`：格式化日期等特殊类型
   - `@JacksonXmlProperty`：自定义节点属性
   - `@JacksonXmlCData`：定义CDATA内容
   - `@JsonRootName`：定义根节点名称
5. **异常处理**：转换过程中的Jackson异常会被封装为`RuntimeException`抛出。
6. **性能考虑**：使用静态初始化的XmlMapper实例，避免重复创建实例带来的性能开销。
7. **字符编码**：使用默认的UTF-8编码处理XML字符串。

### 使用示例

```java
// 创建一个简单的用户类
public class User {
    private int id;
    private String name;
    private int age;
    
    // getter和setter方法省略
}

// 实例化用户对象
User user = new User();
user.setId(1);
user.setName("张三");
user.setAge(25);

// 将对象转换为XML
String xml = XmlObjectMapper.marshall(user);
System.out.println("转换后的XML: " + xml);
// 输出类似: <User><id>1</id><name>张三</name><age>25</age></User>

// 将XML转换回对象
User parsedUser = XmlObjectMapper.unmarshall(xml, User.class);
System.out.println("解析后的用户ID: " + parsedUser.getId()); // 输出: 解析后的用户ID: 1
System.out.println("解析后的用户名: " + parsedUser.getName()); // 输出: 解析后的用户名: 张三
System.out.println("解析后的用户年龄: " + parsedUser.getAge()); // 输出: 解析后的用户年龄: 25
```

## XmlJsonMapper - XML与JSON转换工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| marshall(String jsonStrStr, Class<T> clazz) | 将JSON字符串转换为XML字符串 | jsonStrStr: JSON字符串<br>clazz: 中间对象类型 | String: XML字符串 |

### 使用注意事项

1. **转换流程**：该工具先将JSON转换为Java对象，再将对象转换为XML，是一个两步转换过程。
2. **依赖关系**：依赖`JsonObjectMapper`和`XmlObjectMapper`，确保这两个类正常工作。
3. **类型要求**：需要提供正确的中间对象类型(`clazz`)，该类必须能够从JSON正确反序列化，否则会抛出异常。
4. **异常处理**：转换过程中的任何异常都会被封装为`RuntimeException`抛出。
5. **字段映射**：JSON和XML字段映射依赖于中间对象类的定义，确保字段名称匹配。
6. **方法限制**：目前只支持JSON到XML的转换，不支持XML到JSON的转换。
7. **特性支持**：继承了`JsonObjectMapper`和`XmlObjectMapper`的特性，包括日期格式支持和注解处理。

### 使用示例

```java
// JSON字符串
String json = "{\"id\":1,\"name\":\"张三\",\"age\":25}";

// 将JSON转换为XML (通过User类作为中间对象)
String xml = XmlJsonMapper.marshall(json, User.class);
System.out.println("转换后的XML: " + xml);
// 输出类似: <User><id>1</id><name>张三</name><age>25</age></User>
``` 