/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.crypt;


import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * SM3散列算法工具类单元测试类
 *
 * <AUTHOR>
 * @module 加解密
 * @date 2023-12-04 20:30
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class SM3UtilsTest {

    private static Map<String, String> sm3Map = new HashMap<>();

    @BeforeAll
    @DisplayName("生成用于测试的数据")
    @Tags({
            @Tag("@id:82"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/12/04")
    })
    static void setUp() {
        sm3Map.put("admin", "dc1fd00e3eeeb940ff46f457bf97d66ba7fcc36e0b20802383de142860e76ae6");
        sm3Map.put("123456", "207cf410532f92a47dee245ce9b11ff71f578ebd763eb3bbea44ebd043d018fb");

    }

    @Test
    @DisplayName("使用BouncyCastle计算字符串SM3正常")
    @Tags({
            @Tag("@id:82"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/12/04")
    })
    void testSm3_string() {
        assertEquals(sm3Map.get("admin"), SM3Utils.sm3("admin"));
        assertEquals(sm3Map.get("123456"), SM3Utils.sm3("123456"));
    }

    @Test
    @DisplayName("使用BouncyCastle计算字节数组SM3正常")
    @Tags({
            @Tag("@id:82"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/12/04")
    })
    void testSm3_bytes() {
        assertEquals(sm3Map.get("admin"), SM3Utils.sm3("admin".getBytes(StandardCharsets.UTF_8)));
        assertEquals(sm3Map.get("123456"), SM3Utils.sm3("123456".getBytes(StandardCharsets.UTF_8)));
    }

    @Test
    @DisplayName("测试计算入参为null的异常情况")
    @Tags({
            @Tag("@id:82"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/12/04")
    })
    void testSM3Utils_param_is_null() {
        assertThrows(IllegalArgumentException.class,
                () -> SM3Utils.sm3((String) null),
                "parameter is empty");
        assertThrows(NullPointerException.class,
                () -> SM3Utils.sm3((byte[]) null),
                "parameter is null");
    }

    @Test
    @DisplayName("测试计算入参为空的异常情况")
    @Tags({
            @Tag("@id:82"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/12/04")
    })
    void testSM3Utils_param_is_empty() {
		assertThrows(IllegalArgumentException.class,
				() -> SM3Utils.sm3(""),
				"parameter is empty");
    }


}
