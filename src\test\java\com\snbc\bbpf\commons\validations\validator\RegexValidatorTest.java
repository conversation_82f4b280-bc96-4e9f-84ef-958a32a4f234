package com.snbc.bbpf.commons.validations.validator;

import com.snbc.bbpf.commons.validations.ValidException;
import org.junit.jupiter.api.Test;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import static org.junit.jupiter.api.Assertions.*;
class RegexValidatorTest {
    private final String validRegex = "[a-zA-Z0-9]+";
    private final String invalidRegex = "\\d+";
    private final RegexValidator validator = new RegexValidator(validRegex);
    @Test
    @DisplayName("输入非空")
    @Tags({
            @Tag("@id:36"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/6/28")
    })
    void testValidateWithValidString() {
        String validString = "abc123";
        String validMessage = "Valid string";
        assertDoesNotThrow(() -> validator.validate(validString, validMessage));
    }
    @Test
    @DisplayName("输入为空")
    @Tags({
            @Tag("@id:36"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/6/28")
    })
    void testValidateWithNullString() {
        String nullString = null;
        String validMessage = "Valid string";
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                validator.validate(nullString, validMessage));
        assertEquals("String cannot be empty", exception.getMessage());
    }
    @Test
    @DisplayName("字符串不满足正则表达式")
    @Tags({
            @Tag("@id:36"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/6/28")
    })
    void testValidateWithInvalidStringFormat() {
        String invalidString = "sdfsdf";
        RegexValidator validator1 = new RegexValidator(invalidRegex);
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                validator1.validate(invalidString, null));
        assertEquals("String does not meet regular expression", exception.getMessage());
    }
}
