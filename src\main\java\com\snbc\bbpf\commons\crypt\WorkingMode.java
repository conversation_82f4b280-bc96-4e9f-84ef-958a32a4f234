/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.crypt;

/**
 * 算法工作模式
 *
 * <AUTHOR>
 * @module
 * @date 2023/11/26 21:19
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public enum WorkingMode {
    ECB("ECB"), // ECB
    CBC("CBC"); //CBC

    private String value;

    /**
     * @param value
     */
    WorkingMode(String value) {
        this.value = value;
    }

    /**
     * 根据值取对应枚举
     *
     * @param type
     * @return WorkingMode
     * @throws
     * <AUTHOR>
     * @date 2023/11/27
     * @since 1.4.0
     */
    public static WorkingMode getByValue(String type) {

        for (WorkingMode val : values()) {

            if (val.getValue().equals(type)) {
                return val;
            }
        }
        return null;
    }

    public String getValue() {
        return value;
    }
}
