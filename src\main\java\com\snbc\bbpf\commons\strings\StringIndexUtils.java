/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.strings;


/**
 * 指定字符或字符串，返回出现次数
 * <p>
 * <p>
 * 指定字符或字符串，开始位置开始查找，返回出现次数
 * <p>
 * <p>
 * 指定字符或字符串，开始和结束位置开始查找，返回出现次数
 * <p>
 * <p>
 * 指定字符或字符串，次数，返回对应索引位置
 * <p>
 * <p>
 * 指定字符或字符串，次数，开始位置开始查找，返回对应索引位置
 * <p>
 * <p>
 * 指定字符或字符串，次数，开始位置开始和结束位置查找，返回对应索引位置
 *
 * <AUTHOR>
 * @module 字符串处理
 * @date 2023/06/06
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class StringIndexUtils {

    /**
     * 计算指定字符在字符串中出现的次数
     *
     * @param str     给定字符串
     * @param pattern 指定字符
     * @return 出现次数
     * @since 1.0.1
     */
    public static int countOccurrences(String str, char pattern) {
        int count = 0;
        for (int i = 0; i < str.length(); i++) {
            if (str.charAt(i) == pattern) {
                count++;
            }
        }
        return count;
    }

    /**
     * 计算指定字符串在字符串中出现的次数
     *
     * @param str        给定字符串
     * @param searchText 指定字符串
     * @return 出现次数
     */
    public static int countOccurrences(String str, String searchText) {
        int count = 0;
        int index = 0;
        while (index != -1) {
            index = str.indexOf(searchText, index);
            if (index != -1) {
                count++;
                index += searchText.length();
            }
        }
        return count;
    }


    /**
     * 计算指定字符在字符串中出现的次数
     *
     * @param str        给定字符串
     * @param pattern    指定字符
     * @param startIndex 起始位置
     * @return 出现次数
     */
    public static int countOccurrences(String str, char pattern, int startIndex) {
        int count = 0;
        for (int i = startIndex; i < str.length(); i++) {
            if (str.charAt(i) == pattern) {
                count++;
            }
        }
        return count;
    }

    /**
     * 计算指定字符串在字符串中出现的次数
     *
     * @param str        给定字符串
     * @param searchText 指定字符串
     * @param startIndex 起始位置
     * @return 出现次数
     */
    public static int countOccurrences(String str, String searchText, int startIndex) {
        int count = 0;
        int index = startIndex;
        while (index != -1 && index < str.length()) {
            index = str.indexOf(searchText, index);
            if (index != -1) {
                count++;
                index += searchText.length();
            }
        }
        return count;
    }

    /**
     * 计算指定字符串在字符串中出现的次数
     *
     * @param str        给定字符串
     * @param pattern    指定字符或字符串
     * @param startIndex 起始位置（包含）
     * @param endIndex   结束位置（不包含）
     * @return 出现次数
     */
    public static int countOccurrences(String str, String pattern, int startIndex, int endIndex) {
        int count = 0;
        int index = startIndex;
        while (index != -1 && index < endIndex) {
            index = str.indexOf(pattern, index);
            if (index != -1) {
                count++;
                index += pattern.length();
            }
        }
        return count;
    }

    /**
     * 计算指定字符中出现的次数
     *
     * @param str        给定字符串
     * @param pattern    指定字符
     * @param startIndex 起始位置（包含）
     * @param endIndex   结束位置（不包含）
     * @return 出现次数
     */
    public static int countOccurrences(String str, char pattern, int startIndex, int endIndex) {
        int count = 0;
        for (int i = startIndex; i < endIndex; i++) {
            if (str.charAt(i) == pattern) {
                count++;
            }
        }
        return count;
    }

    /**
     * 返回指定字符出现指定次数后的索引位置
     *
     * @param str        给定字符串
     * @param pattern    指定字符
     * @param occurrence 指定次数
     * @param startIndex 起始位置
     * @return 索引位置，若未找到或指定次数未达到则返回 -1
     */
    public static int getIndexAfterOccurrences(String str, char pattern, int occurrence, int startIndex) {
        int count = 0;
        int index = startIndex;
        while (count < occurrence && index != -1 && index < str.length()) {
            index = str.indexOf(pattern, index);
            if (index != -1) {
                count++;
                index++;
            }
        }
        if (count == occurrence) {
            return index - 1;
        } else {
            return -1;
        }
    }

    /**
     * 返回指定字符串出现指定次数后的索引位置
     *
     * @param str        给定字符串
     * @param searchText 指定字符串
     * @param occurrence 指定次数
     * @param startIndex 起始位置
     * @return 索引位置，若未找到或指定次数未达到则返回 -1
     */
    public static int getIndexAfterOccurrences(String str, String searchText, int occurrence, int startIndex) {
        int count = 0;
        int index = startIndex;
        while (count < occurrence && index != -1 && index < str.length()) {
            index = str.indexOf(searchText, index);
            if (index != -1) {
                count++;
                index += searchText.length();
            }
        }
        if (count == occurrence) {
            return index - searchText.length();
        } else {
            return -1;
        }
    }

    /**
     * 查找指定字符在字符串中指定次数后的索引位置
     *
     * @param str        给定字符串
     * @param pattern    指定字符
     * @param count      指定字符出现次数
     * @param startIndex 开始查找的起始位置
     * @param endIndex   结束查找的位置
     * @return 指定字符出现次数后的索引位置，如果未找到返回 -1
     */
    public static int getIndexAfterOccurrences(String str, char pattern, int count, int startIndex, int endIndex) {
        int occurrences = 0;
        int index = startIndex;

        while (occurrences < count && index < endIndex) {
            if (str.charAt(index) == pattern) {
                occurrences++;
            }
            index++;
        }

        if (occurrences == count) {
            return index;
        } else {
            return -1;
        }
    }

    /**
     * 查找指定字符串在字符串中指定次数后的索引位置
     *
     * @param str        给定字符串
     * @param searchText 指定字符串
     * @param count      指定字符串出现次数
     * @param startIndex 开始查找的起始位置
     * @param endIndex   结束查找的位置
     * @return 指定字符串出现次数后的索引位置，如果未找到返回 -1
     */
    public static int getIndexAfterOccurrences(String str, String searchText, int count, int startIndex, int endIndex) {
        int occurrences = 0;
        int index = startIndex;

        while (occurrences < count && index < endIndex) {
            index = str.indexOf(searchText, index);
            if (index == -1) {
                break;
            }
            occurrences++;
            index += searchText.length();
        }

        if (occurrences == count) {
            return index;
        } else {
            return -1;
        }
    }


}
