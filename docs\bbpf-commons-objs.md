# 对象处理 (com.snbc.bbpf.commons.objs)

## 类概览 

| 类名 | 功能描述 |
|------|----------|
| IConvert | 提供对象之间的转换功能，通常用于VO和DO、BO和DO等对象之间的互转 |
| ObjectDeepCopy | 提供对象深拷贝功能，使用Gson实现无需实现额外接口 |
| ObjectEmptyCheck | 提供对象空值检查功能，支持多种类型对象的空值判断 |

## IConvert - 对象转换接口

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| to(S source) | 将源对象转换为目标类型对象 | source: 源对象 | T: 目标类型对象 |
| from(T source) | 将目标类型对象转换回源对象 | source: 目标类型对象 | S: 源对象 |

### 注意事项

1. 此接口需要结合MapStruct框架使用，实现类需添加@Mapper注解
2. 需要为转换器创建静态INSTANCE实例以优化性能和减少对象创建
3. 属性名不同时，必须使用@Mapping注解进行显式映射
4. 默认情况下不会处理null值，如需特殊处理请使用nullValuePropertyMappingStrategy配置
5. 复杂类型转换(如日期格式化)需使用expression属性或自定义方法实现
6. 集合类型会自动转换，但嵌套对象需要定义额外的转换器
7. 转换器在编译时生成实现代码，运行时几乎没有性能损失

### 使用示例

```java
// 1. 首先定义一个接口实现IConvert
@Mapper
interface UserConverter extends IConvert<User, UserVO> {
    UserConverter INSTANCE = Mappers.getMapper(UserConverter.class);
}

// 2. 将User对象转换为UserVO对象
User user = new User("John", 25, "<EMAIL>");
UserVO userVO = UserConverter.INSTANCE.to(user);
System.out.println(userVO);  // 输出：UserVO{name='John', age=25, email='<EMAIL>'}

// 3. 将UserVO对象转换回User对象
User convertedUser = UserConverter.INSTANCE.from(userVO);
System.out.println(convertedUser);  // 输出：User{name='John', age=25, email='<EMAIL>'}

// 4. 处理属性名不同的情况，在实现接口时使用@Mappings注解
@Mapper
interface CustomerConverter extends IConvert<Customer, CustomerDTO> {
    @Mappings({
        @Mapping(source = "customerName", target = "name"),
        @Mapping(target = "createTime", expression = "java(java.time.format.DateTimeFormatter.ofPattern(\"yyyy-MM-dd HH:mm:ss\").format(source.getCreateTime()))")
    })
    @Override
    CustomerDTO to(Customer source);
    
    @Mappings({
        @Mapping(source = "name", target = "customerName")
    })
    @Override
    Customer from(CustomerDTO source);
    
    CustomerConverter INSTANCE = Mappers.getMapper(CustomerConverter.class);
}

// 5. 使用带有属性映射的转换器
Customer customer = new Customer();
customer.setCustomerName("Alice");
customer.setCreateTime(LocalDateTime.now());

CustomerDTO customerDTO = CustomerConverter.INSTANCE.to(customer);
System.out.println(customerDTO.getName());  // 输出: Alice
```

## ObjectDeepCopy - 对象深拷贝工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| deepCopy(Object obj) | 使用默认限制(8M)进行对象深拷贝 | obj: 待拷贝的对象 | Object: 拷贝后的新对象 |
| deepCopy(Object obj, long lLimitObjSize) | 将对象进行深拷贝，可设置对象大小限制 | obj: 待拷贝的对象<br>lLimitObjSize: 限制对象大小(M)，为0时不限制 | Object: 拷贝后的新对象 |

### 注意事项

1. 基于Gson实现，待拷贝对象无需实现Serializable接口
2. 默认大小限制为8MB，超出限制将抛出IllegalArgumentException异常
3. 无法正确处理包含循环引用的对象，可能导致StackOverflowError
4. 对特殊类型(如线程、数据库连接等)可能无法正确拷贝
5. 不适用于包含无法序列化的字段的对象
6. 性能考量：大对象的深拷贝会消耗较多CPU和内存资源
7. 传入null值会抛出IllegalArgumentException异常
8. 对于超大对象，建议使用第二个方法并将limitObjSize设为0
9. 不保留transient关键字标记的字段

### 使用示例

```java
// 使用默认大小限制(8M)进行对象深拷贝
User user = new User("John", 25, "<EMAIL>");
User clonedUser = (User) ObjectDeepCopy.deepCopy(user);
System.out.println("Cloned user: " + clonedUser);
// 输出：Cloned user: User{name='John', age=25, email='<EMAIL>'}

// 修改原对象，验证深拷贝效果
user.setName("John Modified");
System.out.println("Original user after modification: " + user);
// 输出：Original user after modification: User{name='John Modified', age=25, email='<EMAIL>'}
System.out.println("Cloned user after modification: " + clonedUser);
// 输出：Cloned user after modification: User{name='John', age=25, email='<EMAIL>'}

// 使用自定义大小限制进行对象深拷贝
Department dept = new Department("IT", Arrays.asList(user));
// 限制对象大小为4MB
Department deepClonedDept = (Department) ObjectDeepCopy.deepCopy(dept, 4);
System.out.println("Deep cloned department: " + deepClonedDept);
// 输出：Deep cloned department: Department{name='IT', employees=[User{name='John Modified', age=25, email='<EMAIL>'}]}

// 不限制对象大小进行深拷贝
User unlimitedUser = (User) ObjectDeepCopy.deepCopy(user, 0);
System.out.println("Unlimited deep copied user: " + unlimitedUser);
// 输出：Unlimited deep copied user: User{name='John Modified', age=25, email='<EMAIL>'}

// 抛出异常示例 - 对象为null
try {
    Object copy = ObjectDeepCopy.deepCopy(null);
} catch (IllegalArgumentException e) {
    System.out.println("Exception: " + e.getMessage());  // 输出：Exception: Object is null!
}

// 抛出异常示例 - 对象大小超过限制
try {
    // 假设user对象超过1KB，限制为1KB
    Object copy = ObjectDeepCopy.deepCopy(user, 0.001);
} catch (IllegalArgumentException e) {
    System.out.println("Exception: " + e.getMessage());  // 输出：Exception: Object size is too big!
}
```

## ObjectEmptyCheck - 对象空值检查工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| isEmpty(Object obj) | 判断对象是否为空 | obj: 要判断的对象 | boolean: 如果对象为空则返回true，否则返回false |

### 注意事项

1. 对不同类型对象有不同的空值判断标准：
   - null值始终被视为空
   - 字符串类型：空字符串("")被视为空
   - 集合类型：size为0的集合被视为空
   - 数组类型：length为0的数组被视为空
   - Map类型：size为0的Map被视为空
   - Optional类型：isPresent()为false的Optional被视为空
2. 对于集合和数组，会递归检查内部元素，只要有一个元素为空，整个集合/数组就被视为空
3. 对于Map，会递归检查所有value，只要有一个value为空，整个Map就被视为空
4. 对于普通对象，如无特殊处理则非null对象视为非空
5. 该方法是线程安全的，可在并发环境下使用
6. 检查逻辑较为严格，可能与常规理解有差异，尤其是对集合内元素的递归检查

### 使用示例

```java
// 检查null值
boolean isNullEmpty = ObjectEmptyCheck.isEmpty(null);
System.out.println("Null is empty: " + isNullEmpty);  // 输出：Null is empty: true

// 检查Optional
Optional<String> emptyOptional = Optional.empty();
Optional<String> fullOptional = Optional.of("test");
System.out.println("Empty Optional is empty: " + ObjectEmptyCheck.isEmpty(emptyOptional));  // 输出：Empty Optional is empty: true
System.out.println("Full Optional is empty: " + ObjectEmptyCheck.isEmpty(fullOptional));    // 输出：Full Optional is empty: false

// 检查字符串
String emptyString = "";
String nonEmptyString = "Hello";
System.out.println("Empty string is empty: " + ObjectEmptyCheck.isEmpty(emptyString));      // 输出：Empty string is empty: true
System.out.println("Non-empty string is empty: " + ObjectEmptyCheck.isEmpty(nonEmptyString)); // 输出：Non-empty string is empty: false

// 检查数组
String[] emptyArray = {};
String[] nonEmptyArray = {"a", "b"};
String[] arrayWithEmptyItem = {"a", "", "c"};
System.out.println("Empty array is empty: " + ObjectEmptyCheck.isEmpty(emptyArray));          // 输出：Empty array is empty: true
System.out.println("Non-empty array is empty: " + ObjectEmptyCheck.isEmpty(nonEmptyArray));   // 输出：Non-empty array is empty: false
System.out.println("Array with empty item is empty: " + ObjectEmptyCheck.isEmpty(arrayWithEmptyItem)); // 输出：Array with empty item is empty: true

// 检查集合
List<String> emptyList = new ArrayList<>();
List<String> nonEmptyList = Arrays.asList("a", "b");
List<String> listWithEmptyItem = Arrays.asList("a", "", "c");
System.out.println("Empty list is empty: " + ObjectEmptyCheck.isEmpty(emptyList));          // 输出：Empty list is empty: true
System.out.println("Non-empty list is empty: " + ObjectEmptyCheck.isEmpty(nonEmptyList));   // 输出：Non-empty list is empty: false
System.out.println("List with empty item is empty: " + ObjectEmptyCheck.isEmpty(listWithEmptyItem)); // 输出：List with empty item is empty: true

// 检查Map
Map<String, String> emptyMap = new HashMap<>();
Map<String, String> nonEmptyMap = Map.of("key1", "value1", "key2", "value2");
Map<String, String> mapWithEmptyValue = new HashMap<>();
mapWithEmptyValue.put("key1", "value1");
mapWithEmptyValue.put("key2", "");
System.out.println("Empty map is empty: " + ObjectEmptyCheck.isEmpty(emptyMap));            // 输出：Empty map is empty: true
System.out.println("Non-empty map is empty: " + ObjectEmptyCheck.isEmpty(nonEmptyMap));     // 输出：Non-empty map is empty: false
System.out.println("Map with empty value is empty: " + ObjectEmptyCheck.isEmpty(mapWithEmptyValue)); // 输出：Map with empty value is empty: true

// 检查其他类型对象
User user = new User("John", 25, "<EMAIL>");
System.out.println("User object is empty: " + ObjectEmptyCheck.isEmpty(user));              // 输出：User object is empty: false
```