/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.strings;

import java.io.ByteArrayOutputStream;
import java.util.Base64;
import java.util.zip.Deflater;
import java.util.zip.Inflater;
import java.nio.charset.StandardCharsets;

/**
 * 对字符串进行压缩和解压处理类
 * <p>由于数据传输频率高，对流量有较大影响，因此需要对大字符串进行压缩后传输，在接收到数据后再进行解压</p>
 * <p>功能列表</p>
 * <p>1.对大字符串进行压缩</p>
 * <p>2.对被压缩过的大字符串进行解压</p>
 *
 * <p>注意项</p>
 * <p>1.字符串压缩到字符串，经过Base64后添加一些额外信息，实际输出字符串不一定比输入字符串短，目前经过比较zlib压缩和解压占用资源较小
 * 比如输入：“1234567890” 经压缩输出 “eNozNDI2MTUzt7A0AAALLAIO”，长度变成长
 * 比如输入："1234567890123456789012345678901234567890123456789012345" 经压缩输出 “eNozNDI2MTUzt7A0MCSFBQA7PAtB”，长度就大大缩短
 * 所以越长的数据压缩效果越明显。</p>
 * <p>2.本类没有对压缩字符串长度做限制，主要用于大字符串的压缩起到减少流量的目的，如果使用长度较小的字符串虽然能正常工作，但可能达不到压缩变短的目的且会浪费一定性能。</p>
 * <p>3.压缩与解压使用了UTF-8字符集进行相关内部处理。</p>
 * 
 * <AUTHOR>
 * @module 字符串处理
 * @date 2023/8/16 20:10
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class StringZipUtils {

    public static final String PARAMETER_NULL = "Input parameter is null";
    public static final String PARAMETER_INVALID = "Input parameter is invalid";
    public static final int SIZE_256 = 0xff;
    public static final int LEVEL_9 = 9;

    private StringZipUtils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 对大字符串进行压缩,使用UTF-8字符集
     * @param str 待压缩的字符串
     * @param level 压缩级别 (范围：0-9，数值越大压缩效果越好)
     * @return 压缩后的字节数组
     * @throws IllegalArgumentException 如果输入字符串为null
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/8/16
     */
	public static byte[] compressToByteArray(String str, int level) {
		if (str == null) {
			throw new IllegalArgumentException(PARAMETER_NULL);
		} 
		if (0 > level || LEVEL_9 < level) {
			throw new IllegalArgumentException(PARAMETER_INVALID);		
		}
		
		//使用指定的压缩级别创建一个新的压缩器。
		Deflater deflater = new Deflater(level);
		//设置压缩输入数据。
		deflater.setInput(str.getBytes(StandardCharsets.UTF_8));
		//当被调用时，表示压缩应该以输入缓冲区的当前内容结束。
		deflater.finish();

		final byte[] bytes = new byte[SIZE_256];
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream(SIZE_256);

		int length = 0;
		while (!deflater.finished()) {
		   //压缩输入数据并用压缩数据填充指定的缓冲区。
			length = deflater.deflate(bytes);
			outputStream.write(bytes, 0, length);
		}
		//关闭压缩器
		deflater.end();
		return outputStream.toByteArray();
	}

    /**
     * 对大字符串进行压缩,默认使用最佳压缩级别
     * @param str 待压缩的字符串
     * @return 压缩后的字节数组
     * @throws IllegalArgumentException 如果输入字符串为null
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/8/16
     */
	public static byte[] compressToByteArray(String str) {
		return compressToByteArray(str, Deflater.BEST_COMPRESSION);
	}

    /**
     * 对大字符串进行压缩
     * @param str 待压缩的字符串
	 * @param level 压缩级别 (范围：0-9，数值越大压缩效果越好)
     * @return 压缩后的字符串(由于经过Base64后添加一些额外信息，实际输出字符串长度不一定比输入字符串短)
     * @throws IllegalArgumentException 如果输入字符串为null
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/8/16
     */
	public static String compressToString(String str, int level) {
		return Base64.getEncoder().encodeToString(compressToByteArray(str, level));
	}

    /**
     * 对大字符串进行压缩,默认使用最佳压缩级别
     * @param str 待压缩的字符串
     * @return 压缩后的字符串(由于经过Base64后添加一些额外信息，实际输出字符串长度不一定比输入字符串短)
     * @throws IllegalArgumentException 如果输入字符串为null
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/8/16
     */
	public static String compressToString(String str) {
		return Base64.getEncoder().encodeToString(compressToByteArray(str));
	}

    /**
     * 对被压缩过的大字符串进行解压,使用UTF-8字符集
     * @param byData 待解压的字节数组
     * @return 解压后的字符
     * @throws IllegalArgumentException 如果输入字节数组为null或无效
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/8/16
     */
	public static String uncompress(byte[] byData) throws Exception {
		if (byData == null) {
			throw new IllegalArgumentException(PARAMETER_NULL);
		}
				
		//创建一个新的解压缩器	
		Inflater inflater = new Inflater();
		//设置解压缩的输入数据。
		inflater.setInput(byData);
		final byte[] bytes = new byte[SIZE_256];
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream(SIZE_256);
		int length = 0;
		try {
			//判断是否到达压缩数据流的末尾
			while (!inflater.finished()) {
				//将字节解压缩到指定的缓冲区中。
				length = inflater.inflate(bytes);
				outputStream.write(bytes, 0, length);
			}
		} catch (Exception e) {
			throw new RuntimeException(e);
		} finally {
			//关闭解压缩器
			inflater.end();
		}

		return outputStream.toString("UTF-8");
	} 

    /**
     * 对被压缩过的大字符串进行解压
     * @param str 待解压的字符串
     * @return 解压后的字符
     * @throws IllegalArgumentException 如果输入字符串为null或无效
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/8/16
     */
	public static String uncompress(String str) throws Exception {
		if (str == null) {
			throw new IllegalArgumentException(PARAMETER_NULL);
		}

		return uncompress(Base64.getDecoder().decode(str));	
	} 
}
