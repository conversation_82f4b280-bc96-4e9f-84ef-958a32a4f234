/*
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.commons.strings.security.annotation;
/**
 * @ClassName:  SensitiveType
 * 脱敏枚举
 * @module:    字符脱敏
 * @Author:    yangweipeng
 * @date:      2023/05/06
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public enum SensitiveType {
    /**
     * 中文名
     */
    CHINESE_NAME,
    /**
     * 身份证号
     */
    ID_CARD,
    /**
     * 座机号
     */
    FIXED_PHONE,
    /**
     * 手机号
     */
    MOBILE_PHONE,
    /**
     * 地址
     */
    ADDRESS,
    /**
     * 电子邮件
     */
    EMAIL,
    /**
     * 银行卡
     */
    BANK_CARD,
    /**
     * 密码
     */
    PASSWORD,
    /**
     * 自定义脱敏（保留前几位和后几位）
     */
    CUSTOM;
}
