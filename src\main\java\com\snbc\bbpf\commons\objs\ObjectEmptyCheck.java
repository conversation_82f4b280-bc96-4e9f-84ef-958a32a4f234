/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.objs;

import com.snbc.bbpf.commons.collects.ObjectArrayCheck;

import java.lang.reflect.Array;
import java.util.Collection;
import java.util.Map;
import java.util.Optional;

/**
 * 【对象处理】检查对象是否为空
 *
 * <AUTHOR>
 * @module
 * @date 2023/6/4 20:41
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class ObjectEmptyCheck {
    private ObjectEmptyCheck() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 判断对象是否为空。
     *
     * @param obj 要判断的对象
     * @return 如果对象为空则返回true，否则返回false
     * @since 1.1.0
     * @date 2023/6/3
     * <AUTHOR>
     */
    public static boolean isEmpty(Object obj) {
        if (obj == null) {
            return true;
        } else if (obj instanceof Optional) {
            return !((Optional)obj).isPresent();
        } else if (obj instanceof CharSequence) {
            return ((CharSequence)obj).length() == 0;
        } else if (obj.getClass().isArray()) {
            return Array.getLength(obj) == 0 || ObjectArrayCheck.hasEmptyItem((Object[])obj);
        } else {
            return isEmptyByCollects(obj);
        }
    }

    /**
     * 判定是否集合对象中有空
     * @param obj 要判断的对象
     * @return 如果对象为空则返回true，否则返回false
     * @throws
     * @since 1.1.0
     * @date 2023/6/3
     * <AUTHOR>
     */
    private static boolean isEmptyByCollects(Object obj) {
        if (obj instanceof Collection) {
            return ((Collection) obj).isEmpty() || ObjectArrayCheck.hasEmptyItem(((Collection) obj).toArray());
        } else if (obj instanceof Map) {
            return ((Map) obj).isEmpty() ||
                    ObjectArrayCheck.hasEmptyItem(((Map) obj).values().toArray()) ||
                    ObjectArrayCheck.hasEmptyItem(((Map) obj).keySet().toArray());
        } else {
            return false;
        }
    }
}
