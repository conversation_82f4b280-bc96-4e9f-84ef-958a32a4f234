package com.snbc.bbpf.commons.files;

import com.snbc.bbpf.commons.strings.StringEmptyCheck;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * ini文件处理帮助类
 * 1. 可对指定分组修改key的值，如果key不存在则是新增key和值
 * 2. 可对指定key修改值，如果key在多个分组中，则都进行修改。
 * 3. 可修改一个分组，如果分组不存在则新增分组。
 * 4. 可删除一个分组（正则），同时删除该分组下的所有key和value
 * 5. 可以在修改分组的同时修改key和对应的值列表 如果不存在则新增。
 * 6. 可以删除一个key，如果指定分组，则只删除该分组下的key，如果不指定分组，则删除所有分组下的key。
 * <p>
 * iniFileUtils = new IniFileUtils();
 * iniFileUtils.load("/files/z.ini");
 * iniFileUtils.setValue("Section1","Key1","NewValue");
 * iniFileUtils.setValue("Section2","Key2","NewValue2");
 * iniFileUtils.setValue("Section2","Key2","NewValue2");
 * iniFileUtils.save("/files/z.ini");
 * INI 文件操作工具类
 * 实现对 INI 文件的增删改查操作
 */
public class IniFileUtils {
    private Map<String, Map<String, String>> data;
    private String filePath;

    /**
     * 构造函数，初始化数据存储结构
     *
     * <AUTHOR>
     * @date 2023/11/24
     * @since 1.4.0
     */


    public IniFileUtils(String filePath) {
        if (StringEmptyCheck.isBlank(filePath)) {
            throw new IllegalArgumentException("filePath must not be empty");
        }
        data = new HashMap<>();
        this.filePath = filePath;
        load(filePath);
    }

    /**
     * @param path
     * @return
     * <AUTHOR>
     * @date 2023/11/24
     * @since 1.4.0
     */
    public static List<IniSectionDomain> findAll(String path) {
        if (StringEmptyCheck.isBlank(path)) {
            throw new RuntimeException("找不文件");
        }
        IniFileUtils iniFileUtils = new IniFileUtils(path);
        return iniFileUtils.findAll();
    }

    /**
     * 加载 INI 文件
     *
     * @param filePath 文件路径
     * <AUTHOR>
     * @date 2023/11/24
     * @since 1.4.0
     */
    public final void load(String filePath) {
        try (BufferedReader reader = Files.newBufferedReader(Paths.get(filePath))) {
            String line;
            String currentSection = null;

            while ((line = reader.readLine()) != null) {
                line = line.trim();

                if (!(line.isEmpty() || line.startsWith(";"))) {
                    if (line.startsWith("[")) {
                        currentSection = line.substring(1, line.indexOf("]"));
                        data.put(currentSection, new HashMap<>());
                    } else if (currentSection != null) {
                        int equalsIndex = line.indexOf("=");
                        if (equalsIndex > 0) {
                            String key = line.substring(0, equalsIndex).trim();
                            String value = line.substring(equalsIndex + 1).trim();
                            data.get(currentSection).put(key, value);
                        }
                    }
                }
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 保存修改后的 INI 文件
     *
     * @param filePath 文件路径
     * <AUTHOR>
     * @date 2023/11/24
     * @since 1.4.0 *
     */
    public void save(String filePath) {
        try (BufferedWriter writer = Files.newBufferedWriter(Paths.get(filePath))) {
            for (Map.Entry<String, Map<String, String>> section : data.entrySet()) {
                writer.write("[" + section.getKey() + "]");
                writer.newLine();

                for (Map.Entry<String, String> entry : section.getValue().entrySet()) {
                    writer.write(entry.getKey() + "=" + entry.getValue());
                    writer.newLine();
                }
                writer.newLine();
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    public void save() {
        save(this.filePath);
    }

    /**
     * 获取指定分组和键的值
     *
     * @param section 分组名称
     * @param key     键名称
     * @return 键对应的值，如果不存在则返回 null
     * <AUTHOR>
     * @date 2023/11/24
     * @since 1.4.0
     */
    public String getValue(String section, String key) {
        Map<String, String> sectionData = data.get(section);
        if (sectionData != null) {
            return sectionData.get(key);
        }
        return null;
    }

    /**
     * 设置指定分组和键的值
     * 如果键不存在，则新增键和值
     *
     * @param section 分组名称
     * @param key     键名称
     * @param value   键对应的值
     * <AUTHOR>
     * @date 2023/11/24
     * @since 1.4.0
     */
    public void setValue(String section, String key, String value) {
        data.computeIfAbsent(section, k -> new HashMap<>()).put(key, value);
        save();
    }

    /**
     * 删除指定分组和键的值
     *
     * @param section 分组名称
     * @param key     键名称
     * <AUTHOR>
     * @date 2023/11/24
     * @since 1.4.0
     */

    public void removeValue(String section, String key) {
        data.computeIfPresent(section, (k, sectionData) -> {
            sectionData.remove(key);
            save();
            return sectionData.isEmpty() ? null : sectionData;
        });
    }

    /**
     * 修改指定 key 的值
     * 如果 key 在多个分组中存在，则都进行修改
     *
     * @param key   键名称
     * @param value 新的值
     * <AUTHOR>
     * @date 2023/11/24
     * @since 1.4.0
     */
    public void modifyValue(String key, String value) {
        for (Map<String, String> sectionData : data.values()) {
            sectionData.computeIfPresent(key, (k, oldValue) -> oldValue == null ? value : oldValue);
        }
        save();
    }

    /**
     * 修改指定分组的值
     * 如果分组不存在，则新增分组并设置值
     *
     * @param section 分组名称
     * @param values  键值对
     * <AUTHOR>
     * @date 2023/11/24
     * @since 1.4.0
     */
    public void modifySection(String section, Map<String, String> values) {
        data.put(section, values);
        save();
    }

    /**
     * 删除指定分组及其下的所有键值对
     *
     * @param sectionRegex 分组名称的正则表达式
     * <AUTHOR>
     * @date 2023/11/24
     * @since 1.4.0
     */
    public void removeSection(String sectionRegex) {
        Iterator<Map.Entry<String, Map<String, String>>> iterator = data.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, Map<String, String>> entry = iterator.next();
            String section = entry.getKey();
            if (Pattern.matches(sectionRegex, section)) {
                iterator.remove();
            }
        }

        save();
    }

    /**
     * 修改指定分组的键值对
     * 如果分组不存在，则新增分组
     *
     * @param section 分组名称
     * @param values  键值对
     * <AUTHOR>
     * @date 2023/11/24
     * @since 1.4.0
     */
    public void modifySectionWithValues(String section, Map<String, String> values) {
        data.computeIfAbsent(section, k -> new HashMap<>()).putAll(values);
        save();
    }

    /**
     * 删除指定键
     * 如果指定了分组，则只删除该分组下的键
     * 如果未指定分组，则删除所有分组下的键
     *
     * @param key     键名称
     * @param section 分组名称（可选）
     * <AUTHOR>
     * @date 2023/11/24
     * @since 1.4.0
     */
    public void removeKey(String key, String section) {
        if (section != null) {
            Map<String, String> sectionData = data.get(section);
            if (sectionData != null) {
                sectionData.remove(key);
            }
        } else {
            for (Map<String, String> sectionData : data.values()) {
                sectionData.remove(key);
            }
        }
        save();
    }


    /**
     * 返回所有的值
     *
     * @return
     * <AUTHOR>
     * @date 2023/11/24
     * @since 1.4.0
     */
    public List<IniSectionDomain> findAll() {
        List<IniSectionDomain> re = new ArrayList<>();
        for (Map.Entry<String, Map<String, String>> section : data.entrySet()) {
            Map<String, String> sectionData = data.get(section.getKey());
            sectionData.forEach((k, v) ->
                re.add(new IniSectionDomain(section.getKey(), k, v))
            );
        }
        return re;
    }
}
