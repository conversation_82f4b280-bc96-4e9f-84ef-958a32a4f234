<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.snbc.bbpf</groupId>
    <artifactId>bbpf-commons</artifactId>
    <version>1.6.0</version>
    <name>bbpf-commons</name>
    <description>commons class library for BBPF V2.0</description>

    <!--maven deploy repository-->
    <distributionManagement>
        <repository>
            <id>releases</id>
            <url>http://*************:8081/repository/bbpf-platform/</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <url>http://*************:8081/repository/bbpf-platform-snapshot/</url>
        </snapshotRepository>
    </distributionManagement>

    <properties>
        <!-- JDK version -->
        <java.version>1.8</java.version>
        <!-- encoding setting -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <!-- maven plugin list -->
        <!-- maven plugin : compiler -->
        <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
        <!-- maven plugin : resources -->
        <maven-resources-plugin.version>3.1.0</maven-resources-plugin.version>
        <!-- maven plugin : surefire -->
        <maven-surefire-plugin.version>2.22.2</maven-surefire-plugin.version>
        <!-- maven plugin : failsafe -->
        <maven-failsafe-plugin.version>2.22.2</maven-failsafe-plugin.version>
        <!-- maven plugin : aspectj -->
        <aspectj-maven-plugin.version>1.7</aspectj-maven-plugin.version>
        <!-- package dependency -->
        <apache.lang3.version>3.12.0</apache.lang3.version>
        <apache.text.version>1.10.0</apache.text.version>
        <apache.codec.version>1.16.0</apache.codec.version>
        <!-- ulid creator -->
        <ulid-creator.version>5.2.0</ulid-creator.version>
        <gson.version>2.9.0</gson.version>
        <!-- annotation auto find -->
        <auto-service.version>1.1.1</auto-service.version>
        <!-- mapstruct -->
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <!-- json path -->
        <json-path.version>2.8.0</json-path.version>
        <!-- expression -->
        <aviator.version>5.3.3</aviator.version>
        <!--   json/xml marshall     -->
        <jackson.version>2.15.2</jackson.version>
        <!--aop aspectjweaver-->
        <aspectjweaver.version>1.8.2</aspectjweaver.version>
        <!-- ftp -->
        <commons.net.version>3.9.0</commons.net.version>
        <!-- sftp -->
        <jsch.version>0.2.11</jsch.version>
        <!-- xml parse -->
        <dom4j.version>2.1.4</dom4j.version>
        <!-- xpath -->
        <jaxen.version>2.0.0</jaxen.version>
        <jwt.version>0.12.3</jwt.version>
        <!-- bouncycastle -->
        <bouncycastle.version>1.77</bouncycastle.version>
        <!-- Apache HttpClient -->
        <httpclient.version>4.5.14</httpclient.version>
        <!-- jasypt -->
        <jasypt.version>1.9.3</jasypt.version>
        <!-- unit test list -->
        <!-- unit test : junit -->
        <junit.version>5.7.2</junit.version>
        <mockito.version>3.6.28</mockito.version>
        <opentest4j.version>1.2.0</opentest4j.version>
        <!-- unit test : jacoco -->
        <jacoco.version>0.8.8</jacoco.version>
        <!-- unit test : compile-testing -->
        <compile-testing.version>0.20</compile-testing.version>
    </properties>

    <dependencies>
        <!-- Jwt工具类加密属性配置依赖 -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
            <version>${jwt.version}</version>
        </dependency>
        <!-- Jwt工具类加密属性配置依赖 -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <!-- or jjwt-gson if Gson is preferred -->
            <artifactId>jjwt-jackson</artifactId>
            <version>${jwt.version}</version>
        </dependency>
        <!-- Jwt工具类加密属性配置依赖 -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
            <version>${jwt.version}</version>
        </dependency>
        <!--JWT测试end-->
        <!-- uto-service -->
        <dependency>
            <groupId>com.google.auto.service</groupId>
            <artifactId>auto-service</artifactId>
            <version>${auto-service.version}</version>
        </dependency>


        <!-- aviator-->
        <dependency>
            <groupId>com.googlecode.aviator</groupId>
            <artifactId>aviator</artifactId>
            <version>${aviator.version}</version>
        </dependency>

        <!-- commons-codec-->
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>${apache.codec.version}</version>
        </dependency>

        <!-- StringEscapeUtils-->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>${apache.text.version}</version>
        </dependency>

        <!-- Gson-->
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>${gson.version}</version>
        </dependency>

        <!-- apache common 通用类库 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${apache.lang3.version}</version>
        </dependency>
        <!-- add mapstruct class convert -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <!--ulid-->
        <!-- https://search.maven.org/artifact/com.github.f4b6a3/ulid-creator -->
        <dependency>
            <groupId>com.github.f4b6a3</groupId>
            <artifactId>ulid-creator</artifactId>
            <version>${ulid-creator.version}</version>
        </dependency>
        <!-- ftp功能依赖 -->
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>${commons.net.version}</version>
        </dependency>
        <!-- sftp功能依赖 -->
        <dependency>
            <groupId>com.github.mwiede</groupId>
            <artifactId>jsch</artifactId>
            <version>${jsch.version}</version>
        </dependency>
        <!-- XML / JSON 序列化依赖 -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <!-- XML / JSON 序列化依赖 -->
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <!-- XML / JSON 序列化依赖 -->
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <!-- json path -->
        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
            <version>${json-path.version}</version>
        </dependency>

        <!--aop的依赖  aspectjweaver-->
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
            <version>${aspectjweaver.version}</version>
            <scope>compile</scope>
        </dependency>
        <!--aop的依赖  aspectjweaver-->
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjrt</artifactId>
            <version>${aspectjweaver.version}</version>
            <scope>compile</scope>
        </dependency>
        <!-- dom4j -->
        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>${dom4j.version}</version>
        </dependency>
        <!-- xpath -->
        <dependency>
            <groupId>jaxen</groupId>
            <artifactId>jaxen</artifactId>
            <version>${jaxen.version}</version>
        </dependency>
        <!-- 加解密库 bouncycastle -->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk18on</artifactId>
            <version>${bouncycastle.version}</version>
        </dependency>
        <!-- Apache HttpClient -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>${httpclient.version}</version>
        </dependency>

        <!-- Jasypt encryption library -->
        <dependency>
            <groupId>org.jasypt</groupId>
            <artifactId>jasypt</artifactId>
            <version>${jasypt.version}</version>
        </dependency>

        <!-- unit test : junit -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
        <!-- unit test : junit -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
        <!-- unit test : junit -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-params</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>


        <!-- unit test : mockito -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>
        <!-- unit test : mockito -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>
        <!--unit test : mockito Junit5-->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>
        <!-- unit test : opentest4j -->
        <dependency>
            <groupId>org.opentest4j</groupId>
            <artifactId>opentest4j</artifactId>
            <scope>test</scope>
            <version>${opentest4j.version}</version>
        </dependency>
        <!-- unit test : jacoco -->
        <dependency>
            <groupId>org.jacoco</groupId>
            <artifactId>org.jacoco.agent</artifactId>
            <version>${jacoco.version}</version>
            <classifier>runtime</classifier>
            <scope>test</scope>
        </dependency>
        <!-- unit test : testing -->
        <dependency>
            <groupId>com.google.testing.compile</groupId>
            <artifactId>compile-testing</artifactId>
            <version>${compile-testing.version}</version> <!-- 请使用适当的版本 -->
            <scope>test</scope>
        </dependency>

    </dependencies>

    <build>
        <!--插件版本-->
        <plugins>
            <!-- maven plugin list -->
            <!-- maven plugin resources  -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <!--修改版本-->
                <version>${maven-resources-plugin.version}</version>
            </plugin>
            <!-- maven plugin surefire  -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
                <configuration>
                    <systemPropertyVariables>
                        <jacoco-agent.destfile>${project.build.directory}/jacoco.exec</jacoco-agent.destfile>
                    </systemPropertyVariables>
                </configuration>
            </plugin>
            <!-- maven plugin compiler  -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                        <!-- other annotation processors -->
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <!-- maven plugin failsafe  -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
                <version>2.22.2</version>
            </plugin>

            <!-- unit test plugin : jacoco -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco.version}</version>
                <executions>
                    <execution>
                        <id>default-instrument</id>
                        <goals>
                            <goal>instrument</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-restore-instrumented-classes</id>
                        <goals>
                            <goal>restore-instrumented-classes</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>pre-unit-test</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                        <configuration>
                            <append>false</append>
                            <destFile>${basedir}/target/jacoco.exec</destFile>
                            <propertyName>surefireArgLine</propertyName>
                        </configuration>
                    </execution>
                    <execution>
                        <id>default-report</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                        <configuration>
                            <dataFile>${basedir}/target/jacoco.exec</dataFile>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!--编译期织入-->
            <!-- maven plugin aspectj  -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>aspectj-maven-plugin</artifactId>
                <version>${aspectj-maven-plugin.version}</version>
                <configuration>
                    <complianceLevel>${java.version}</complianceLevel>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <showWeaveInfo>true</showWeaveInfo>
                    <verbose>true</verbose>
                    <Xlint>ignore</Xlint>
                    <encoding>${project.reporting.outputEncoding}</encoding>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <!-- 在编译期织入 -->
                            <goal>compile</goal>
                            <goal>test-compile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
