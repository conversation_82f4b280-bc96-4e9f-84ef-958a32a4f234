/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.crypt;

import javax.crypto.Cipher;

/**
 * 【加解密】SM4对称加密算法
 * <p>1.实现SM4的加密和解密算法</p>
 * <p>2.使用BouncyCastleProvider（简称BC）扩展可用算法</p>
 *
 * <AUTHOR>
 * @module
 * @date 2023/11/26 20:49
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class Sm4Utils {

    private Sm4Utils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * SM4加密CBC模式
     *
     * @param key     加密密钥 密钥长度只能为16位
     * @param keyIv   初始向量 keyIv长度必须等于16
     * @param data    加密数据
     * @param padding Padding枚举
     *                NoPadding待加密原文长度必须是 16 的倍数
     *                PKCS5Padding待加密原文长度不限制
     * @return byte
     * @throws
     * <AUTHOR>
     * @date 2023/11/27
     * @since 1.4.0
     */
    public static byte[] encryptCbc(byte[] key, byte[] keyIv, byte[] data, Padding padding) {
        return CipherUtils.doFinal(Cipher.ENCRYPT_MODE, SymmetricAlgorithm.SM4, WorkingMode.CBC, padding, key, keyIv, data);
    }

    /**
     * SM4解密CBC模式
     *
     * @param key     加密密钥 密钥长度只能为16位
     * @param keyIv   初始向量 keyIv长度必须等于16
     * @param data    解密数据
     * @param padding Padding枚举
     * @return byte
     * @throws
     * <AUTHOR>
     * @date 2023/11/27
     * @since 1.4.0
     */
    public static byte[] decryptCbc(byte[] key, byte[] keyIv, byte[] data, Padding padding) {
        return CipherUtils.doFinal(Cipher.DECRYPT_MODE, SymmetricAlgorithm.SM4, WorkingMode.CBC, padding, key, keyIv, data);
    }

    /**
     * SM4加密ECB模式
     *
     * @param key     加密密钥 密钥长度只能为16位
     * @param data    加密数据
     * @param padding Padding枚举
     *                NoPadding待加密原文长度必须是 16 的倍数
     *                PKCS5Padding待加密原文长度不限制
     * @return byte
     * @throws
     * <AUTHOR>
     * @date 2023/11/27
     * @since 1.4.0
     */
    public static byte[] encryptEcb(byte[] key, byte[] data, Padding padding) {
        return CipherUtils.doFinal(Cipher.ENCRYPT_MODE, SymmetricAlgorithm.SM4, WorkingMode.ECB, padding, key, data);
    }

    /**
     * SM4解密ECB模式
     *
     * @param key     加密密钥 密钥长度只能为16位
     * @param data    解密数据
     * @param padding Padding枚举（NoPadding或PKCS5Padding、PKCS7Padding）
     * @return byte
     * @throws
     * <AUTHOR>
     * @date 2023/11/27
     * @since 1.4.0
     */
    public static byte[] decryptEcb(byte[] key, byte[] data, Padding padding) {
        return CipherUtils.doFinal(Cipher.DECRYPT_MODE, SymmetricAlgorithm.SM4, WorkingMode.ECB, padding, key, data);
    }


}
