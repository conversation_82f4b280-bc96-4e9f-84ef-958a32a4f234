package com.snbc.bbpf.commons.strings.security.processor;

import com.google.testing.compile.Compilation;
import com.google.testing.compile.JavaFileObjects;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;

import javax.tools.JavaFileObject;

import static com.google.testing.compile.CompilationSubject.assertThat;
import static com.google.testing.compile.Compiler.javac;


@DisplayName("ValidDesensitizedProcessor单元测试")
@Tags({
        @Tag("@id:46"),
        @Tag("@author:yangweipeng"),
        @Tag("@date:2023/6/28")
})
public class ValidDesensitizedProcessorTest {

    @Test
    @DisplayName("输入非法字段")
    @Tags({
            @Tag("@id:46"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/9/21")
    })
    void testValidDesensitizedProcessor_WithInvalidField() {
        JavaFileObject source = JavaFileObjects.forSourceString("TestClass",
                "import com.snbc.bbpf.commons.strings.security.annotation.Desensitized;\n" +
                        "import com.snbc.bbpf.commons.strings.security.annotation.SensitiveType;\n" +
                        "public class TestClass {\n" +
                        "    @Desensitized(type = SensitiveType.CHINESE_NAME)\n" +
                        "    private String sensitiveData;\n" +
                        "    @Desensitized(type = SensitiveType.CHINESE_NAME)\n" +
                        "    private String getString(){" +
                        " return \"aaa\"; }\n" +
                        "}\n");

        Compilation compilation = javac().withProcessors(new ValidDesensitizedProcessor())
                .withOptions("-s", "./")  // 指定输出目录
                .compile(source);
        assertThat(compilation).failed();
    }
    @Test
    @DisplayName("正常测试")
    @Tags({
            @Tag("@id:46"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/9/21")
    })
    void testValidDesensitizedProcessor_nomarl() {
        JavaFileObject source = JavaFileObjects.forSourceString("TestClass",
                "import com.snbc.bbpf.commons.strings.security.annotation.Desensitized;\n" +
                        "import com.snbc.bbpf.commons.strings.security.annotation.SensitiveType;\n" +
                        "public class TestClass {\n" +
                        "    @Desensitized(type = SensitiveType.CHINESE_NAME)\n" +
                        "    private String sensitiveData;\n" +
                        "    @Desensitized(type = SensitiveType.CHINESE_NAME)\n" +
                        "    private String name;" +
                        "}\n");

        Compilation compilation = javac().withProcessors(new ValidDesensitizedProcessor())
                .withOptions("-s", "./")  // 指定输出目录
                .compile(source);
        assertThat(compilation).succeeded();
    }
    @Test
    @DisplayName("输入非String类型")
    @Tags({
            @Tag("@id:46"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/9/21")
    })
    void testValidDesensitizedProcessor_WithNonStringType() {
        JavaFileObject source = JavaFileObjects.forSourceString("TestClass",
                "import com.snbc.bbpf.commons.strings.security.annotation.Desensitized;\n" +
                        "import com.snbc.bbpf.commons.strings.security.annotation.SensitiveType;\n" +
                        "public class TestClass {\n" +
                        "    @Desensitized(type = SensitiveType.CHINESE_NAME)\n" +
                        "    private int sensitiveData;\n" +
                        "}\n");

        Compilation compilation = javac().withProcessors(new ValidDesensitizedProcessor())
                .withOptions("-s", "./")  // 指定输出目录
                .compile(source);
        assertThat(compilation).failed();
    }
}