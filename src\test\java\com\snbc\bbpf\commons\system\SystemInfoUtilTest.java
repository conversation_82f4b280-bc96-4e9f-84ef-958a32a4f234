package com.snbc.bbpf.commons.system;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledOnOs;
import org.junit.jupiter.api.condition.OS;
import org.mockito.Mockito;

import static com.snbc.bbpf.commons.system.SystemInfoUtil.getSystemInfo;
import static org.junit.jupiter.api.Assertions.*;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;

class SystemInfoUtilTest {

    @Test
    @DisplayName("测试获取操作系统名称")
    @Tags({
            @Tag("@id:106"),
            @Tag("@auth:yangweipeng"),
            @Tag("@date:2024/5/30")
    })
    void testGetOsName() {
        String osName = SystemInfoUtil.getOsName();
        assertNotNull(osName, "Operating System name should not be null");
        System.out.println("OS Name: " + osName);
    }
    @Test
    @DisplayName("测试获取操作系统版本")
    @Tags({
            @Tag("@id:106"),
            @Tag("@auth:yangweipeng"),
            @Tag("@date:2024/6/11")
    })
    void testGetOsVersion() {
        String osVersion = SystemInfoUtil.getOsVersion();
        assertNotNull(osVersion, "Operating System version should not be null");
        System.out.println("OS Version: " + osVersion);
    }

    @Test
    @DisplayName("测试获取系统架构")
    @Tags({
            @Tag("@id:106"),
            @Tag("@auth:yangweipeng"),
            @Tag("@date:2024/6/11")
    })
    void testGetOsArchitecture() {
        String osArchitecture = SystemInfoUtil.getOsArchitecture();
        assertNotNull(osArchitecture, "Operating System architecture should not be null");
        System.out.println("OS Architecture: " + osArchitecture);
    }
    @Test
    @DisplayName("测试获取总内存（MB）")
    @Tags({
            @Tag("@id:106"),
            @Tag("@auth:yangweipeng"),
            @Tag("@date:2024/5/30")
    })
    void testGetTotalMemoryInMB() {
        long totalMemory = SystemInfoUtil.getTotalMemoryInMB();
        assertTrue(totalMemory > 0, "Total memory should be a positive number");
        System.out.println("Total Memory (MB): " + totalMemory);
    }
    @Test
    @DisplayName("测试获取Java虚拟机最大可分配内存（MB）")
    @Tags({
            @Tag("@id:106"),
            @Tag("@auth:yangweipeng"),
            @Tag("@date:2024/6/11")
    })
    void testGetMaxMemoryInMB() {
        long maxMemoryInMB = SystemInfoUtil.getMaxMemoryInMB();
        assertTrue(maxMemoryInMB > 0, "Max JVM memory should be a positive number");
        System.out.println("Max JVM Memory (MB): " + maxMemoryInMB);
    }

    @Test
    @DisplayName("测试获取CPU使用率 - 正常情况")
    @Tags({
            @Tag("@id:106"),
            @Tag("@auth:yangweipeng"),
            @Tag("@date:2024/6/11")
    })
    void testGetCpuUsage_NormalScenario() {
        double cpuUsage = SystemInfoUtil.getCpuUsage();
        if (cpuUsage != -1) {
            assertTrue(cpuUsage >= 0.0 && cpuUsage <= 1.0, "CPU usage should be between 0.0 and 1.0");
            System.out.println("CPU Usage: " + cpuUsage);
        } else {
            fail("Unable to retrieve CPU usage, returned -1");
        }
    }

    @Test
    @DisplayName("测试获取空闲内存（MB）")
    @Tags({
            @Tag("@id:106"),
            @Tag("@auth:yangweipeng"),
            @Tag("@date:2024/5/30")
    })
    void testGetFreeMemoryInMB() {
        long freeMemory = SystemInfoUtil.getFreeMemoryInMB();
        assertTrue(freeMemory > 0, "Free memory should be a positive number");
        System.out.println("Free Memory (MB): " + freeMemory);
    }

    @Test
    @DisplayName("测试获取CPU核心数")
    @Tags({
            @Tag("@id:106"),
            @Tag("@auth:yangweipeng"),
            @Tag("@date:2024/5/30")
    })
    void testGetCpuCoreCount() {
        int coreCount = SystemInfoUtil.getCpuCoreCount();
        assertTrue(coreCount >= 1, "CPU core count should be at least 1");
        System.out.println("CPU Core Count: " + coreCount);
    }

    @Test
    @DisplayName("测试获取主机名")
    @Tags({
            @Tag("@id:106"),
            @Tag("@auth:yangweipeng"),
            @Tag("@date:2024/5/30")
    })
    void testGetHostName_NormalScenario() {
        try {
            String hostName = SystemInfoUtil.getHostName();
            assertNotNull(hostName, "Host name should not be null");
            System.out.println("Host Name: " + hostName);
        } catch (Exception e) {
            fail("Exception occurred while getting host name: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("测试获取网络接口信息")
    @Tags({
            @Tag("@id:106"),
            @Tag("@auth:yangweipeng"),
            @Tag("@date:2024/5/30")
    })
    void testGetNetworkInterfaces_NormalScenario() {
        try {
            List<NetworkInterfaceInfo> networkInterfaces = SystemInfoUtil.getNetworkInterfaces();
            assertNotNull(networkInterfaces, "Network interfaces list should not be null");
            assertFalse(networkInterfaces.isEmpty(), "Network interfaces list should not be empty");

            for (NetworkInterfaceInfo info : networkInterfaces) {
                assertNotNull(info.getName(), "Interface name should not be null");
                assertNotNull(info.getMacAddress(), "MAC address should not be null");
                assertNotNull(info.getIpAddresses(), "IP addresses list should not be null");
                assertFalse(info.getIpAddresses().isEmpty(), "IP addresses list should not be empty");

                System.out.println("Interface: " + info.getName());
                System.out.println("MAC Address: " + info.getMacAddress());
                System.out.println("IP Addresses: " + info.getIpAddresses());
            }
        } catch (Exception e) {
            fail("Exception occurred while getting network interfaces: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("测试获取系统运行时间")
    @Tags({
            @Tag("@id:106"),
            @Tag("@auth:yangweipeng"),
            @Tag("@date:2024/5/30")
    })
    void testGetUptime_NormalScenario() {
        try {
            long uptime = SystemInfoUtil.getUptime();
            assertTrue(uptime > 0, "Uptime should be greater than zero");
            System.out.println("Uptime (seconds): " + uptime);
        } catch (Exception e) {
            fail("Exception occurred while getting uptime: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("测试获取系统开机时间")
    @Tags({
            @Tag("@id:106"),
            @Tag("@auth:yangweipeng"),
            @Tag("@date:2024/5/30")
    })
    void testGetBootTime_NormalScenario() {
        LocalDateTime currentTime = LocalDateTime.now();
        try {
            LocalDateTime bootTime = SystemInfoUtil.getBootTime();

            // 确保获取到的启动时间不为null
            assertNotNull(bootTime, "Boot time should not be null");

            // 比较启动时间和当前时间，确保启动时间早于当前时间
            assertTrue(bootTime.isBefore(currentTime), "Boot time should be before the current time");

            System.out.println("Boot Time: " + bootTime);
        } catch (Exception e) {
            fail("An exception should not occur while getting boot time: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("测试获取系统类型")
    @Tags({
            @Tag("@id:106"),
            @Tag("@auth:yangweipeng"),
            @Tag("@date:2024/5/30")
    })
    void testGetSystemType_NormalScenario() {
        OSType systemType = SystemInfoUtil.getSystemType();
        assertNotNull(systemType, "System type should not be null");
        assertTrue(systemType.equals(OSType.WINDOWS) || systemType.equals(OSType.LINUX) ||
                        systemType.equals(OSType.OTHER),
                "System type should be one of 'Windows', 'Unix/Linux/MacOS', or 'Other'");
        System.out.println("Operating System: " + systemType);
    }
    @Test
    @DisplayName("测试获取系统信息——window环境")
    @Tags({
            @Tag("@id:106"),
            @Tag("@auth:yangweipeng"),
            @Tag("@date:2024/5/30")
    })
    @EnabledOnOs(OS.WINDOWS)
    void testGetSystemInfo_Windows()  {
        String result = SystemInfoUtil.getSystemInfo();
        System.out.println("System Info for Windows:\n" + result);
        // Assert expected behavior for Windows
        // Example assertion:
        assertTrue(result.contains("Windows"));
    }
    @Test
    @DisplayName("测试获取系统信息——linux环境")
    @Tags({
            @Tag("@id:106"),
            @Tag("@auth:yangweipeng"),
            @Tag("@date:2024/5/30")
    })
    @EnabledOnOs(OS.LINUX)
    void testGetSystemInfo_Linux()  {
        String result = SystemInfoUtil.getSystemInfo();
        System.out.println("System Info for Linux:\n" + result);
        // Assert expected behavior for Linux
        // Example assertion:
        assertTrue(result.contains("Linux"));
    }

    @Test
    @DisplayName("测试获取环境变量")
    @Tags({
            @Tag("@id:106"),
            @Tag("@auth:yangweipeng"),
            @Tag("@date:2024/5/30")
    })
    void testGetEnvironmentVariable_NormalScenario() {
        String envVar = "PATH"; // 可以更改为你想获取的环境变量名称
        String envValue = SystemInfoUtil.getEnvironmentVariable(envVar);
        assertNotNull(envValue, "Environment variable value should not be null");
        System.out.println(envVar + ": " + envValue);
    }

    // 异常场景测试

    @Test
    @DisplayName("测试获取主机名异常场景")
    @Tags({
            @Tag("@id:106"),
            @Tag("@auth:yangweipeng"),
            @Tag("@date:2024/5/30")
    })
    void testGetHostName_ExceptionScenario() {
        try {
            // 模拟异常场景
            SystemInfoUtil.getHostName();
        } catch (Exception e) {
            assertNotNull(e.getMessage(), "Exception message should not be null");
        }
    }

    @Test
    @DisplayName("测试获取网络接口信息异常场景")
    @Tags({
            @Tag("@id:106"),
            @Tag("@auth:yangweipeng"),
            @Tag("@date:2024/5/30")
    })
    void testGetNetworkInterfaces_ExceptionScenario() {
        try {
            // 模拟异常场景
            SystemInfoUtil.getNetworkInterfaces();
        } catch (Exception e) {
            assertNotNull(e.getMessage(), "Exception message should not be null");
        }
    }

    @Test
    @DisplayName("测试获取系统运行时间异常场景")
    @Tags({
            @Tag("@id:106"),
            @Tag("@auth:yangweipeng"),
            @Tag("@date:2024/5/30")
    })
    void testGetUptime_ExceptionScenario() {
        try {
            // 模拟异常场景
            SystemInfoUtil.getUptime();
        } catch (Exception e) {
            assertNotNull(e.getMessage(), "Exception message should not be null");
        }
    }

    @Test
    @DisplayName("测试获取系统开机时间异常场景")
    @Tags({
            @Tag("@id:106"),
            @Tag("@auth:yangweipeng"),
            @Tag("@date:2024/5/30")
    })
    void testGetBootTime_ExceptionScenario() {
        try {
            // 模拟异常场景
            SystemInfoUtil.getBootTime();
        } catch (Exception e) {
            assertNotNull(e.getMessage(), "Exception message should not be null");
        }
    }
}
