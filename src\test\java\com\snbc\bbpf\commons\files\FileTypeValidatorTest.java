/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.files;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

/**
 * 类描述:    .
 *
 * <AUTHOR>
 * 创建时间:  [2023/8/17 14:14]
 */
class FileTypeValidatorTest {

    @Test
    @DisplayName("testIsValidFileType")
    @Tags({
            @Tag("@id:56"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/6/4")
    })
    void testIsValidFileType() {
        List<String> validFileTypes = Arrays.asList("jpg", "png", "txt");

        // 测试一个有效的文件类型
        Assertions.assertTrue(FileTypeValidator.isValidFileType("example.jpg", validFileTypes));
        // 测试一个无效的文件类型
        Assertions.assertFalse(FileTypeValidator.isValidFileType("example.jpg1", validFileTypes));

        // 测试一个有效的文件类型
        Assertions.assertTrue(FileTypeValidator.isValidFileType("example.png", validFileTypes));
        // 测试一个无效的文件类型
        Assertions.assertFalse(FileTypeValidator.isValidFileType("example.png1", validFileTypes));

        // 测试一个有效的文件类型
        Assertions.assertTrue(FileTypeValidator.isValidFileType("example.txt", validFileTypes));
        // 测试一个无效的文件类型
        Assertions.assertFalse(FileTypeValidator.isValidFileType("example.txt1", validFileTypes));
        Assertions.assertFalse(FileTypeValidator.isValidFileType(null, validFileTypes));
    }


}
