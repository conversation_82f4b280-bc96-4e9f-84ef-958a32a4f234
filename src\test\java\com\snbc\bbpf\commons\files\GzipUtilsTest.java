/*
 * 版权所有 2009-2024 山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.files;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.AfterAll;

import java.io.File;
import java.nio.charset.StandardCharsets;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
/**
 * Gzip文件解压和压缩单元测试类
 *
 * <AUTHOR>
 * @module 文件处理模块
 * @date 2024/6/6 20:30
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class GzipUtilsTest {

    public static String sourceFilePath = "./test/idea_settings.txt";
    public static String sourcGZipFile = "./test/idea_settings.txt.gz";
    public final String errorTypeFile = "./test/test.7z";
    public static String destDir = "./test";
    public final String destZipFilePath = "./test/new.gz";
    public final String destZipFileTypeError = "./test/new.rar";


    @BeforeAll
    @DisplayName("生成用于测试的文件")
    @Tags({
            @Tag("@id:99"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2024/6/6")
    })
    static void beforeAll_creatFile() throws Exception {
		FileUtils.write(sourceFilePath, "用于测试的文件", StandardCharsets.UTF_8);		
		GzipUtils.compress(sourceFilePath,sourcGZipFile);
    }
    @AfterAll
    @DisplayName("释放资源")
    @Tags({
            @Tag("@id:99"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2024/6/6")
    })
    static void afterAll_deleteFile() throws Exception {
		FileUtils.delete(destDir);
    }

    @Test
    @DisplayName("解压源文件地址为空")
    @Tags({
            @Tag("@id:99"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2024/6/6")
    })
    void testDecompress_zipFilePathBlank() {
        assertThrows(IllegalArgumentException.class,() ->  GzipUtils.decompress("", destDir),
                "The Source GZip File Path is Blank");
    }

    @Test
    @DisplayName("解压目的地址为空")
    @Tags({
            @Tag("@id:99"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2024/6/6")
    })
    void testDecompress_destDirBlank() {
        IllegalArgumentException illegalArgumentException = assertThrows(IllegalArgumentException.class, () -> GzipUtils.decompress(sourcGZipFile, ""));
        assertEquals("The Destination Directory is Blank",illegalArgumentException.getLocalizedMessage());
    }

    @Test
    @DisplayName("待解压文件后缀名错误")
    @Tags({
            @Tag("@id:99"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2024/6/6")
    })
    void testDecompress_zipFileTypeError() {
        assertThrows(IllegalArgumentException.class,
                () ->  GzipUtils.decompress(errorTypeFile, destDir),
                "The Source File Suffix Name is Error ,Only Support .gz Type File");
    }

    @Test
    @DisplayName("解压目的地址不存在，则创建")
    @Tags({
            @Tag("@id:99"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2024/6/6")
    })
    void testDecompress_destDirNotExists() {
        String destDirNotExists = "./test/123";
        GzipUtils.decompress(sourcGZipFile, destDirNotExists);
        assertTrue(new File("./test/123/idea_settings.txt").exists());
    }

    @Test
    @DisplayName("解压成功")
    @Tags({
            @Tag("@id:99"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2024/6/6")
    })
    void testDecompress_success() {
        GzipUtils.decompress( sourcGZipFile, destDir);
        File file = new File(sourceFilePath);
        assertTrue(file.exists());
    }

    /***************************************************************************/

    @Test
    @DisplayName("待压缩路径为空")
    @Tags({
            @Tag("@id:99"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2024/6/6")
    })
    void testCompress_sourceFilePathBlank() {
        assertThrows(IllegalArgumentException.class,
                () ->  GzipUtils.compress("", destZipFilePath),
                "The Source File is Blank");
    }

    @Test
    @DisplayName("压缩目标文件路径为空")
    @Tags({
            @Tag("@id:99"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2024/6/6")
    })
    void testCompress_destFilePathBlank() {
        IllegalArgumentException illegalArgumentException = assertThrows(IllegalArgumentException.class, () -> GzipUtils.compress(
                destDir, ""));
        assertEquals("The Destination File Path is Blank",illegalArgumentException.getLocalizedMessage());
    }

    @Test
    @DisplayName("压缩目标文件后缀名错误")
    @Tags({
            @Tag("@id:99"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2024/6/6")
    })
    void testCompress_destFileTypeError() {
        assertThrows(IllegalArgumentException.class,
                () ->  GzipUtils.compress(destDir, destZipFileTypeError),
                "The Destination File Suffix Name is Error ,Only Support .gz Type File");
    }

    @Test
    @DisplayName("待压缩文件不存在")
    @Tags({
            @Tag("@id:99"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2024/6/6")
    })
    void testCompress_sourcFileNotExist() {
        String sourceDirPathNotExists = "/123.gz";
        assertThrows(IllegalArgumentException.class,
                () ->  GzipUtils.compress(sourceDirPathNotExists, destZipFilePath),
                "The Source File is Not Exists");
    }
	
    @Test
    @DisplayName("待压缩的是目录")
    @Tags({
            @Tag("@id:99"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2024/6/6")
    })
    void testCompress_isDirectory() {
        assertThrows(IllegalArgumentException.class,
                () ->  GzipUtils.compress(destDir, destZipFilePath),
                "The Directory can Not compress gz");
    }

    @Test
    @DisplayName("压缩文件成功")
    @Tags({
            @Tag("@id:99"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2024/6/6")
    })
    void testCompress_success() {
        GzipUtils.compress(sourceFilePath,destZipFilePath);
        File file = new File(destZipFilePath);
        assertTrue(file.exists());
    }


}

