/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.strings;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeAll;

import java.util.List;

import static java.time.Duration.ofSeconds;
import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTimeoutPreemptively;
/**
 * 字符串分隔单元测试类
 *
 * <AUTHOR>
 * @module 字符串分隔处理
 * @date 2023/4/25 19:21
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class SplitHandlerTest {

	public static String strBigString;
	@BeforeAll
	@DisplayName("生成1M [12<456>67890] 大字符串，以便性能测试")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	static void beforeAll_generateBigString () {
		int iCount = 1024 * 1024;
		String str = "12<3456>67890";
		StringBuilder strBld = new StringBuilder(str.length() * iCount);
		for (int i = 0; i < iCount; i++) {
			strBld.append(str);
		}
		strBigString = strBld.toString();
	}

    @Test
    @DisplayName("将[12,345,67890,0987,654,321]返回第一个出现分隔符之后的子字符串[345,67890,0987,654,321]")
    @Tags({
            @Tag("@id:16"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testFirstAfter() {
        assertEquals("345,67890,0987,654,321", SplitHandler.firstAfter("12,345,67890,0987,654,321", ","));
    }

    @Test
    @DisplayName("将[12,345,67890,0987,654,321]查找不存在的分隔符")
    @Tags({
            @Tag("@id:16"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testFirstAfter_notFindseparator() {
        assertEquals("", SplitHandler.firstAfter("12,345,67890,0987,654,321", ";"));
    }


    @Test
    @DisplayName("输入null字符串应该抛出提示参数是空的IllegalArgumentException异常")
    @Tags({
            @Tag("@id:16"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testFirstAfter_strNullShouldThrowException() {
        assertThrows(IllegalArgumentException.class,
                () -> SplitHandler.firstAfter(null, ","),
                "Input parameter is empty");
    }

    @Test
    @DisplayName("输入空字符串应该抛出提示参数是空的IllegalArgumentException异常")
    @Tags({
            @Tag("@id:16"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testFirstAfter_strEmptyShouldThrowException() {
        assertThrows(IllegalArgumentException.class,
                () -> SplitHandler.firstAfter("", ","),
                "Input parameter is empty");
    }

    @Test
    @DisplayName("输入null分隔符应该抛出提示参数是空的IllegalArgumentException异常")
    @Tags({
            @Tag("@id:16"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testFirstAfter_separatorNullShouldThrowException() {
        assertThrows(IllegalArgumentException.class,
                () -> SplitHandler.firstAfter("12,345,67890,0987,654,321", null),
                "Input parameter is empty");
    }

    @Test
    @DisplayName("输入空分隔符应该抛出提示参数是空的IllegalArgumentException异常")
    @Tags({
            @Tag("@id:16"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testFirstAfter_separatorEmptyShouldThrowException() {
        assertThrows(IllegalArgumentException.class,
                () -> SplitHandler.firstAfter("12,345,67890,0987,654,321", ""),
                "Input parameter is empty");
    }

    @Test
    @DisplayName("将[12,345,67890,0987,654,321]返回第一个出现分隔符之前的子字符串[12]")
    @Tags({
            @Tag("@id:16"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testFirstBefore() {
        assertEquals("12", SplitHandler.firstBefore("12,345,67890,0987,654,321", ","));
    }

	@Test
	@DisplayName("将[12,345,67890,0987,654,321]查找不存在的分隔符")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstBefore_notFindseparator() {
		assertEquals("", SplitHandler.firstBefore("12,345,67890,0987,654,321", ";"));
	}


	@Test
	@DisplayName("输入null字符串应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstBefore_strNullShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.firstBefore(null, ","),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入空字符串应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstBefore_strEmptyShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.firstBefore("", ","),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入null分隔符应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstBefore_separatorNullShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.firstBefore("12,345,67890,0987,654,321", null),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入空分隔符应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstBefore_separatorEmptyShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.firstBefore("12,345,67890,0987,654,321", ""),
				"Input parameter is empty");
	}

    @Test
    @DisplayName("将[12,345,67890,0987,654,321]返回最后一个出现分隔符之前的子字符串[12,345,67890,0987,654]")
    @Tags({
            @Tag("@id:16"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testLastBefore() {
        assertEquals("12,345,67890,0987,654", SplitHandler.lastBefore("12,345,67890,0987,654,321", ","));
    }

	@Test
	@DisplayName("将[12,345,67890,0987,654,321]查找不存在的分隔符")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testLastBefore_notFindseparator() {
		assertEquals("", SplitHandler.lastBefore("12,345,67890,0987,654,321", ";"));
	}


	@Test
	@DisplayName("输入null字符串应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testLastBefore_strNullShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.lastBefore(null, ","),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入空字符串应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testLastBefore_strEmptyShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.lastBefore("", ","),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入null分隔符应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testLastBefore_separatorNullShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.lastBefore("12,345,67890,0987,654,321", null),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入空分隔符应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testLastBefore_separatorEmptyShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.lastBefore("12,345,67890,0987,654,321", ""),
				"Input parameter is empty");
	}

    @Test
    @DisplayName("将[12,345,67890,0987,654,321]返回最后一个出现分隔符之后的子字符串[321]")
    @Tags({
            @Tag("@id:16"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testLastAfter() {
        assertEquals("321", SplitHandler.lastAfter("12,345,67890,0987,654,321", ","));
    }

	@Test
	@DisplayName("将[12,345,67890,0987,654,321]查找不存在的分隔符")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testLastAfter_notFindseparator() {
		assertEquals("", SplitHandler.lastAfter("12,345,67890,0987,654,321", ";"));
	}


	@Test
	@DisplayName("输入null字符串应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testLastAfter_strNullShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.lastAfter(null, ","),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入空字符串应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testLastAfter_strEmptyShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.lastAfter("", ","),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入null分隔符应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testLastAfter_separatorNullShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.lastAfter("12,345,67890,0987,654,321", null),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入空分隔符应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testLastAfter_separatorEmptyShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.lastAfter("12,345,67890,0987,654,321", ""),
				"Input parameter is empty");
	}
	
	
    @Test
    @DisplayName("将[12<345>67890<0987>654<321>]获取首次两个分隔符（可以相同也可以不相同）成对出现之间的子字符串[345]")
    @Tags({
            @Tag("@id:16"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testFirstMiddle() {
        assertEquals("345", SplitHandler.firstMiddle("12<345>67890<0987>654<321>", "<", ">"));
    }
	
	@Test
	@DisplayName("将[12<345>67890<0987>654<321>]查找不存在的左分隔符")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstMiddle_notFindSeparatorLeft() {
		assertEquals("", SplitHandler.firstMiddle("12<345>67890<0987>654<321>", ";", ">"));
	}

	@Test
	@DisplayName("将[12<345>67890<0987>654<321>]查找不存在的右分隔符")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstMiddle_notFindSeparatorRight() {
		assertEquals("", SplitHandler.firstMiddle("12<345>67890<0987>654<321>", "<", ";"));
	}


	@Test
	@DisplayName("输入null字符串应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstMiddle_strNullShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.firstMiddle(null, "<", ">"),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入空字符串应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstMiddle_strEmptyShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.firstMiddle("", "<", ">"),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入null左分隔符应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstMiddle_separatorLeftNullShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.firstMiddle("12<345>67890<0987>654<321>", null, ">"),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入null右分隔符应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstMiddle_separatorRightNullShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.firstMiddle("12<345>67890<0987>654<321>", "<", null),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入空左分隔符应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstMiddle_separatorLeftEmptyShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.firstMiddle("12<345>67890<0987>654<321>", "", ">"),
				"Input parameter is empty");
	}
	
	
	@Test
	@DisplayName("输入空右分隔符应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstMiddle_separatorRightEmptyShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.firstMiddle("12<345>67890<0987>654<321>", "<", ""),
				"Input parameter is empty");
	}
	
    @Test
    @DisplayName("将[12<345>67890<0987>654<321>]从指定位置开始获取首次两个分隔符（可以相同也可以不相同）成对出现之间的子字符串[0987]")
    @Tags({
            @Tag("@id:16"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testFirstMiddle_indexBegin() {
        assertEquals("0987", SplitHandler.firstMiddle("12<345>67890<0987>654<321>", 4, "<", ">"));
    }
	
	@Test
	@DisplayName("将[12<345>67890<0987>654<321>]指定开始位置小于0应该抛出提示位置无效的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstMiddle_indexBegin_lessZero() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.firstMiddle("12<345>67890<0987>654<321>", -1, "<", ">"),
				"Index parameter invalid");
	}

	@Test
	@DisplayName("将[12<345>67890<0987>654<321>]指定开始位置大于字符串长度应该抛出提示位置无效的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstMiddle_indexBegin_overLong() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.firstMiddle("12<345>67890<0987>654<321>", 100, "<", ">"),
				"Index parameter invalid");
	}

	@Test
	@DisplayName("将[12<345>67890<0987>654<321>]查找不存在的左分隔符")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstMiddle_indexBegin_notFindSeparatorLeft() {
		assertEquals("", SplitHandler.firstMiddle("12<345>67890<0987>654<321>", 4, ";", ">"));
	}

	@Test
	@DisplayName("将[12<345>67890<0987>654<321>]查找不存在的右分隔符")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstMiddle_indexBegin_notFindSeparatorRight() {
		assertEquals("", SplitHandler.firstMiddle("12<345>67890<0987>654<321>", 4, "<", ";"));
	}


	@Test
	@DisplayName("输入null字符串应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstMiddle_indexBegin_strNullShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.firstMiddle(null, 4, "<", ">"),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入空字符串应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstMiddle_indexBegin_strEmptyShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.firstMiddle("", 4, "<", ">"),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入null左分隔符应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstMiddle_indexBegin_separatorLeftNullShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.firstMiddle("12<345>67890<0987>654<321>", 4, null, ">"),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入null右分隔符应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstMiddle_indexBegin_separatorRightNullShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.firstMiddle("12<345>67890<0987>654<321>", 4, "<", null),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入空左分隔符应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstMiddle_indexBegin_separatorLeftEmptyShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.firstMiddle("12<345>67890<0987>654<321>", 4, "", ">"),
				"Input parameter is empty");
	}
	
	
	@Test
	@DisplayName("输入空右分隔符应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstMiddle_indexBegin_separatorRightEmptyShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.firstMiddle("12<345>67890<0987>654<321>", 4, "<", ""),
				"Input parameter is empty");
	}
	
    @Test
    @DisplayName("将[12<345>67890<0987>654<321>]从指定开始和结束位置获取首次两个分隔符（可以相同也可以不相同）成对出现之间的子字符串[345]")
    @Tags({
            @Tag("@id:16"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testFirstMiddle_indexBeginEnd() {
        assertEquals("345", SplitHandler.firstMiddle("12<345>67890<0987>654<321>", 1, 8, "<", ">"));
    }

	@Test
	@DisplayName("将[12<345>67890<0987>654<321>]指定开始位置小于0应该抛出提示位置无效的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstMiddle_indexBeginEnd_beginLessZero() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.firstMiddle("12<345>67890<0987>654<321>", -1, 8, "<", ">"),
				"Index parameter invalid");
	}

	@Test
	@DisplayName("将[12<345>67890<0987>654<321>]指定开始位置大于字符串长度应该抛出提示位置无效的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstMiddle_indexBeginEnd_beginOverLong() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.firstMiddle("12<345>67890<0987>654<321>", 100, 8, "<", ">"),
				"Index parameter invalid");
	}

	@Test
	@DisplayName("将[12<345>67890<0987>654<321>]指定结束位置小于0应该抛出提示位置无效的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstMiddle_indexBeginEnd_endLessZero() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.firstMiddle("12<345>67890<0987>654<321>", 1, -1, "<", ">"),
				"Index parameter invalid");
	}

	@Test
	@DisplayName("将[12<345>67890<0987>654<321>]指定结束位置大于字符串长度应该抛出提示位置无效的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstMiddle_indexBeginEnd_endOverLong() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.firstMiddle("12<345>67890<0987>654<321>", 1, 100, "<", ">"),
				"Index parameter invalid");
	}

	@Test
	@DisplayName("将[12<345>67890<0987>654<321>]指定开始位置大于结束位置应该抛出提示位置无效的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstMiddle_indexBeginEnd_beginOverEnd() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.firstMiddle("12<345>67890<0987>654<321>", 8, 1, "<", ">"),
				"Index parameter invalid");
	}

	@Test
	@DisplayName("将[12<345>67890<0987>654<321>]查找不存在的左分隔符")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstMiddle_indexBeginEnd_notFindSeparatorLeft() {
		assertEquals("", SplitHandler.firstMiddle("12<345>67890<0987>654<321>", 1, 8, ";", ">"));
	}

	@Test
	@DisplayName("将[12<345>67890<0987>654<321>]查找不存在的右分隔符")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstMiddle_indexBeginEnd_notFindSeparatorRight() {
		assertEquals("", SplitHandler.firstMiddle("12<345>67890<0987>654<321>", 1, 8, "<", ";"));
	}


	@Test
	@DisplayName("输入null字符串应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstMiddle_indexBeginEnd_strNullShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.firstMiddle(null, 1, 8, "<", ">"),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入空字符串应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstMiddle_indexBeginEnd_strEmptyShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.firstMiddle("", 1, 8, "<", ">"),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入null左分隔符应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstMiddle_indexBeginEnd_separatorLeftNullShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.firstMiddle("12<345>67890<0987>654<321>", 1, 8, null, ">"),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入null右分隔符应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstMiddle_indexBeginEnd_separatorRightNullShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.firstMiddle("12<345>67890<0987>654<321>", 1, 8, "<", null),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入空左分隔符应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstMiddle_indexBeginEnd_separatorLeftEmptyShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.firstMiddle("12<345>67890<0987>654<321>", 1, 8, "", ">"),
				"Input parameter is empty");
	}
	
	
	@Test
	@DisplayName("输入空右分隔符应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstMiddle_indexBeginEnd_separatorRightEmptyShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.firstMiddle("12<345>67890<0987>654<321>", 1, 8, "<", ""),
				"Input parameter is empty");
	}
	
    @Test
    @DisplayName("将[12<345>67890<0987>654<321>]获取两个分隔符（可以相同也可以不相同）成对出现之间的子字符串数组[345,0987,321]")
    @Tags({
            @Tag("@id:16"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testMiddleAr() {
        assertArrayEquals(new String[]{"345","0987","321"}, SplitHandler.middleAr("12<345>67890<0987>654<321>", "<", ">"));
    }

	@Test
	@DisplayName("将[12<345>67890<>654<321>]获取两个分隔符（可以相同也可以不相同）成对出现之间的子字符串数组,不要空元素[345,321]")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_removeEmptyElements() {
		assertArrayEquals(new String[]{"345","321"}, SplitHandler.middleAr("12<345>67890<>654<321>", "<", ">"));
	}

	@Test
	@DisplayName("将[12<345>67890<0987>654<321>]查找不存在的左分隔符")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_notFindSeparatorLeft() {
		assertArrayEquals(new String[0], SplitHandler.middleAr("12<345>67890<0987>654<321>", ";", ">"));
	}

	@Test
	@DisplayName("将[12<345>67890<0987>654<321>]查找不存在的右分隔符")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_notFindSeparatorRight() {
		assertArrayEquals(new String[0], SplitHandler.middleAr("12<345>67890<0987>654<321>", "<", ";"));
	}


	@Test
	@DisplayName("输入null字符串应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_strNullShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.middleAr(null, "<", ">"),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入空字符串应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_strEmptyShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.middleAr("", "<", ">"),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入null左分隔符应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_separatorLeftNullShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.middleAr("12<345>67890<0987>654<321>", null, ">"),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入null右分隔符应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_separatorRightNullShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.middleAr("12<345>67890<0987>654<321>", "<", null),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入空左分隔符应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_separatorLeftEmptyShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.middleAr("12<345>67890<0987>654<321>", "", ">"),
				"Input parameter is empty");
	}


	@Test
	@DisplayName("输入空右分隔符应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_separatorRightEmptyShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.middleAr("12<345>67890<0987>654<321>", "<", ""),
				"Input parameter is empty");
	}

    @Test
    @DisplayName("将[12<345>67890<0987>654<321>]从指定位置开始获取两个分隔符（可以相同也可以不相同）成对出现之间的子字符串数组[0987,321]")
    @Tags({
            @Tag("@id:16"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testMiddleAr_indexBegin() {
        assertArrayEquals(new String[]{"0987","321"}, SplitHandler.middleAr("12<345>67890<0987>654<321>", 8, "<", ">"));
    }

	@Test
	@DisplayName("将[12<345>67890<0987>654<321>]指定开始位置小于0应该抛出提示位置无效的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_indexBegin_lessZero() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.middleAr("12<345>67890<0987>654<321>", -1, "<", ">"),
				"Index parameter invalid");
	}

	@Test
	@DisplayName("将[12<345>67890<0987>654<321>]指定开始位置大于字符串长度应该抛出提示位置无效的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_indexBegin_overLong() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.middleAr("12<345>67890<0987>654<321>", 100, "<", ">"),
				"Index parameter invalid");
	}

	@Test
	@DisplayName("将[12<345>67890<0987>654<321>]查找不存在的左分隔符")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_indexBegin_notFindSeparatorLeft() {
		assertArrayEquals(new String[0], SplitHandler.middleAr("12<345>67890<0987>654<321>", 4, ";", ">"));
	}

	@Test
	@DisplayName("将[12<345>67890<0987>654<321>]查找不存在的右分隔符")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_indexBegin_notFindSeparatorRight() {
		assertArrayEquals(new String[0], SplitHandler.middleAr("12<345>67890<0987>654<321>", 4, "<", ";"));
	}


	@Test
	@DisplayName("输入null字符串应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_indexBegin_strNullShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.middleAr(null, 4, "<", ">"),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入空字符串应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_indexBegin_strEmptyShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.middleAr("", 4, "<", ">"),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入null左分隔符应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_indexBegin_separatorLeftNullShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.middleAr("12<345>67890<0987>654<321>", 4, null, ">"),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入null右分隔符应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_indexBegin_separatorRightNullShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.middleAr("12<345>67890<0987>654<321>", 4, "<", null),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入空左分隔符应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_indexBegin_separatorLeftEmptyShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.middleAr("12<345>67890<0987>654<321>", 4, "", ">"),
				"Input parameter is empty");
	}


	@Test
	@DisplayName("输入空右分隔符应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_indexBegin_separatorRightEmptyShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.middleAr("12<345>67890<0987>654<321>", 4, "<", ""),
				"Input parameter is empty");
	}

    @Test
    @DisplayName("将[12<345>67890<0987>654<321>]从指定开始和结束位置获取两个分隔符（可以相同也可以不相同）成对出现之间的子字符串数组[0987]")
    @Tags({
            @Tag("@id:16"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testMiddleAr_indexBeginEnd() {
        assertArrayEquals(new String[]{"0987"}, SplitHandler.middleAr("12<345>67890<0987>654<321>", 8, 19, "<", ">"));
    }

	@Test
	@DisplayName("将[12<345>67890<0987>654<321>]指定开始位置小于0应该抛出提示位置无效的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_indexBeginEnd_beginLessZero() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.middleAr("12<345>67890<0987>654<321>", -1, 8, "<", ">"),
				"Index parameter invalid");
	}

	@Test
	@DisplayName("将[12<345>67890<0987>654<321>]指定开始位置大于字符串长度应该抛出提示位置无效的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_indexBeginEnd_beginOverLong() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.middleAr("12<345>67890<0987>654<321>", 100, 8, "<", ">"),
				"Index parameter invalid");
	}

	@Test
	@DisplayName("将[12<345>67890<0987>654<321>]指定结束位置小于0应该抛出提示位置无效的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_indexBeginEnd_endLessZero() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.middleAr("12<345>67890<0987>654<321>", 1, -1, "<", ">"),
				"Index parameter invalid");
	}

	@Test
	@DisplayName("将[12<345>67890<0987>654<321>]指定结束位置大于字符串长度应该抛出提示位置无效的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_indexBeginEnd_endOverLong() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.middleAr("12<345>67890<0987>654<321>", 1, 100, "<", ">"),
				"Index parameter invalid");
	}

	@Test
	@DisplayName("将[12<345>67890<0987>654<321>]指定开始位置大于结束位置应该抛出提示位置无效的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_indexBeginEnd_beginOverEnd() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.middleAr("12<345>67890<0987>654<321>", 8, 1, "<", ">"),
				"Index parameter invalid");
	}

	@Test
	@DisplayName("将[12<345>67890<0987>654<321>]查找不存在的左分隔符")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_indexBeginEnd_notFindSeparatorLeft() {
		assertArrayEquals(new String[0], SplitHandler.middleAr("12<345>67890<0987>654<321>", 1, 8, ";", ">"));
	}

	@Test
	@DisplayName("将[12<345>67890<0987>654<321>]查找不存在的右分隔符")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_indexBeginEnd_notFindSeparatorRight() {
		assertArrayEquals(new String[0], SplitHandler.middleAr("12<345>67890<0987>654<321>", 1, 8, "<", ";"));
	}


	@Test
	@DisplayName("输入null字符串应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_indexBeginEnd_strNullShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.middleAr(null, 1, 8, "<", ">"),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入空字符串应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_indexBeginEnd_strEmptyShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.middleAr("", 1, 8, "<", ">"),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入null左分隔符应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_indexBeginEnd_separatorLeftNullShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.middleAr("12<345>67890<0987>654<321>", 1, 8, null, ">"),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入null右分隔符应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_indexBeginEnd_separatorRightNullShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.middleAr("12<345>67890<0987>654<321>", 1, 8, "<", null),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("输入空左分隔符应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_indexBeginEnd_separatorLeftEmptyShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.middleAr("12<345>67890<0987>654<321>", 1, 8, "", ">"),
				"Input parameter is empty");
	}


	@Test
	@DisplayName("输入空右分隔符应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_indexBeginEnd_separatorRightEmptyShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.middleAr("12<345>67890<0987>654<321>", 1, 8, "<", ""),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("大字符串性能测试(与CPU性能有关),返回第一个出现分隔符之后的子字符串")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstAfter_bigStringPerformance () {
		assertTimeoutPreemptively(ofSeconds(10), ()->SplitHandler.firstAfter(strBigString, "<"));
	}

	@Test
	@DisplayName("大字符串性能测试(与CPU性能有关),指定开始和结束位置获取首次两个分隔符（可以相同也可以不相同）成对出现之间的子字符串")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testFirstMiddle_bigStringPerformance () {
		assertTimeoutPreemptively(ofSeconds(5), ()->SplitHandler.firstMiddle(strBigString, 20, 12000000, "<", ">"));
	}

	@Test
	@DisplayName("大字符串性能测试(与CPU性能有关),获取两个分隔符（可以相同也可以不相同）成对出现之间的子字符串数组")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_bigStringPerformance () {
		assertTimeoutPreemptively(ofSeconds(5), ()->SplitHandler.middleAr(strBigString, "<", ">"));
	}

	@Test
	@DisplayName("大字符串性能测试(与CPU性能有关), 从指定位置开始获取两个分隔符（可以相同也可以不相同）成对出现之间的子字符串数组")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_indexBegin_bigStringPerformance () {
		assertTimeoutPreemptively(ofSeconds(5), ()->SplitHandler.middleAr(strBigString, 20,"<", ">"));
	}


	@Test
	@DisplayName("大字符串性能测试(与CPU性能有关), 从指定开始和结束位置获取两个分隔符（可以相同也可以不相同）成对出现之间的子字符串数组")
	@Tags({
			@Tag("@id:16"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testMiddleAr_indexBeginEnd_bigStringPerformance () {
		assertTimeoutPreemptively(ofSeconds(5), ()->SplitHandler.middleAr(strBigString, 20, 12000000,"<", ">"));
	}

	@Test
	@DisplayName("通过分隔符分隔字符串返回集合,字符串为空抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:103"),
			@Tag("@author:wangxiaoji"),
			@Tag("@date:2023/5/21")
	})
	void testSplitToList_strEmptyShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> SplitHandler.splitToList("1,2,3,4,5,6", null),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("通过分隔符分隔字符串返回集合,分隔符为空抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:103"),
			@Tag("@author:wangxiaoji"),
			@Tag("@date:2023/5/21")
	})
	void testSplitToList_separatorEmptyShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() ->SplitHandler.splitToList(null, null),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("通过分隔符分隔字符串返回集合,数据正常")
	@Tags({
			@Tag("@id:103"),
			@Tag("@author:wangxiaoji"),
			@Tag("@date:2023/5/21")
	})
	void testSplitToList_strToList() {
		List<String> list = SplitHandler.splitToList("1,2,3,4,5,6", ",");
		assertEquals(6, list.size());
	}

}

