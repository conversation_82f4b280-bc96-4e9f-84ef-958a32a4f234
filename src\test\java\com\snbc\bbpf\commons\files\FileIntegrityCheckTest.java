/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.files;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.AfterAll;

import java.io.File;
import java.io.FileWriter;
import java.io.FileInputStream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * 对文件完整性检查单元测试类
 *
 * <AUTHOR>
 * @module 文件处理模块
 * @date 2023/8/26 20:30
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class FileIntegrityCheckTest {

    public static String FILE_NAME = "./FileIntegrityCheckTest.txt";
    public static File testFile = null;
    @BeforeAll
    @DisplayName("生成1M 1234567890 大字符串，以便性能测试")
    @Tags({
            @Tag("@id:61"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
    })
    static void beforeAll_creatFile() throws Exception {
        testFile = new File(FILE_NAME);
		boolean  bCreate = true;
        if (!testFile.exists()) {
            bCreate = testFile.createNewFile();
        }
		if (bCreate) {
			
			try (FileWriter writer = new FileWriter(testFile, false)) {
				int iCount = 1024 * 1024;
				for (int i = 0; i < iCount; ++i) {
	            	writer.write("1234567890");					
				}
			} catch (Exception e) {
				throw new RuntimeException(e);
			}
					
	        testFile.deleteOnExit();
		}
    }

    @AfterAll
    @DisplayName("释放资源")
    @Tags({
            @Tag("@id:61"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
    })
    static void afterAll_creatFile() throws Exception {
        if (testFile.exists()) {
            testFile.delete();
        }
    }

    @Test
	@DisplayName("传入文件流生成文件完整性的校验码MD5")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testGenerateCheckCode_streamMD5() throws Exception {
        try (FileInputStream fis = new FileInputStream(testFile)) {
			assertEquals("56432bfd166c3a2fcc541b31389000ef",
				FileIntegrityCheck.generateCheckCode(fis, FileIntegrityCheck.CheckAlgorithm.MD5));
        } catch (Exception e) {
			throw new RuntimeException(e);
        }    	
    }

    @Test
	@DisplayName("传入文件对象生成文件完整性的校验码MD5")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testGenerateCheckCode_fileMD5() throws Exception {
        assertEquals("56432bfd166c3a2fcc541b31389000ef",
			FileIntegrityCheck.generateCheckCode(testFile, FileIntegrityCheck.CheckAlgorithm.MD5));
    }

    @Test
	@DisplayName("传入文件路径生成文件完整性的校验码MD5")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testGenerateCheckCode_nameMD5() throws Exception {
        assertEquals("56432bfd166c3a2fcc541b31389000ef",
			FileIntegrityCheck.generateCheckCode(FILE_NAME, FileIntegrityCheck.CheckAlgorithm.MD5));
    }


    @Test
	@DisplayName("传入文件流生成文件完整性的校验码SHA1")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testGenerateCheckCode_streamSHA1() throws Exception {
        try (FileInputStream fis = new FileInputStream(testFile)) {
			assertEquals("a4d073fe0191dcb9fba7d8e75a8eae8063732d04",
				FileIntegrityCheck.generateCheckCode(fis, FileIntegrityCheck.CheckAlgorithm.SHA1));
        } catch (Exception e) {
			throw new RuntimeException(e);
        }
    }

    @Test
	@DisplayName("传入文件对象生成文件完整性的校验码SHA1")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testGenerateCheckCode_fileSHA1() throws Exception {
        assertEquals("a4d073fe0191dcb9fba7d8e75a8eae8063732d04",
			FileIntegrityCheck.generateCheckCode(testFile, FileIntegrityCheck.CheckAlgorithm.SHA1));
    }

    @Test
	@DisplayName("传入文件路径生成文件完整性的校验码SHA1")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testGenerateCheckCode_nameSHA1() throws Exception {
        assertEquals("a4d073fe0191dcb9fba7d8e75a8eae8063732d04",
			FileIntegrityCheck.generateCheckCode(FILE_NAME, FileIntegrityCheck.CheckAlgorithm.SHA1));
    }


    @Test
	@DisplayName("传入文件流生成文件完整性的校验码SHA256")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testGenerateCheckCode_streamSHA256() throws Exception {
        try (FileInputStream fis = new FileInputStream(testFile)) {
			assertEquals("1f1b37b41c5da95e86c515c8d94e376ac22ad2cd1b5d0e7795b5b68f77b341e5",
				FileIntegrityCheck.generateCheckCode(fis, FileIntegrityCheck.CheckAlgorithm.SHA256));
        } catch (Exception e) {
			throw new RuntimeException(e);
        }
    }

    @Test
	@DisplayName("传入文件对象生成文件完整性的校验码SHA256")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testGenerateCheckCode_fileSHA256() throws Exception {
        assertEquals("1f1b37b41c5da95e86c515c8d94e376ac22ad2cd1b5d0e7795b5b68f77b341e5",
			FileIntegrityCheck.generateCheckCode(testFile, FileIntegrityCheck.CheckAlgorithm.SHA256));
    }

    @Test
	@DisplayName("传入文件路径生成文件完整性的校验码SHA256")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testGenerateCheckCode_nameSHA256() throws Exception {
        assertEquals("1f1b37b41c5da95e86c515c8d94e376ac22ad2cd1b5d0e7795b5b68f77b341e5",
			FileIntegrityCheck.generateCheckCode(FILE_NAME, FileIntegrityCheck.CheckAlgorithm.SHA256));
    }


    @Test
	@DisplayName("传入文件流生成文件完整性的校验码SHA384")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testGenerateCheckCode_streamSHA384() throws Exception {
        try (FileInputStream fis = new FileInputStream(testFile)) {
			assertEquals("6c16c6b9b4b3d18f138908b22ef3fa54efd2be9e9fbff1d8a00d4add647337cb95dfce8000645c2e5cba4638db55c13c",
            	FileIntegrityCheck.generateCheckCode(fis, FileIntegrityCheck.CheckAlgorithm.SHA384));
        } catch (Exception e) {
			throw new RuntimeException(e);
        }
    }

    @Test
	@DisplayName("传入文件对象生成文件完整性的校验码SHA384")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testGenerateCheckCode_fileSHA384() throws Exception {
        assertEquals("6c16c6b9b4b3d18f138908b22ef3fa54efd2be9e9fbff1d8a00d4add647337cb95dfce8000645c2e5cba4638db55c13c",
                FileIntegrityCheck.generateCheckCode(testFile, FileIntegrityCheck.CheckAlgorithm.SHA384));
    }

    @Test
	@DisplayName("传入文件路径生成文件完整性的校验码SHA384")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testGenerateCheckCode_nameSHA384() throws Exception {
        assertEquals("6c16c6b9b4b3d18f138908b22ef3fa54efd2be9e9fbff1d8a00d4add647337cb95dfce8000645c2e5cba4638db55c13c",
                FileIntegrityCheck.generateCheckCode(FILE_NAME, FileIntegrityCheck.CheckAlgorithm.SHA384));
    }



    @Test
	@DisplayName("传入文件流生成文件完整性的校验码SHA512")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testGenerateCheckCode_streamSHA512() throws Exception {
        try (FileInputStream fis = new FileInputStream(testFile)) {
			assertEquals("4a5c840f6f8626a9b2a067ef09fd5afa794ecc2997a9db5d100656f009349b5ab25d163fb5027ff6602b2b880b5beb4fc26383ce8eafd54976629d334d3dd739",
            	FileIntegrityCheck.generateCheckCode(fis, FileIntegrityCheck.CheckAlgorithm.SHA512));
        } catch (Exception e) {
			throw new RuntimeException(e);
        }
    }

    @Test
	@DisplayName("传入文件对象生成文件完整性的校验码SHA512")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testGenerateCheckCode_fileSHA512() throws Exception {
        assertEquals("4a5c840f6f8626a9b2a067ef09fd5afa794ecc2997a9db5d100656f009349b5ab25d163fb5027ff6602b2b880b5beb4fc26383ce8eafd54976629d334d3dd739",
                FileIntegrityCheck.generateCheckCode(testFile, FileIntegrityCheck.CheckAlgorithm.SHA512));
    }

    @Test
	@DisplayName("传入文件路径生成文件完整性的校验码SHA512")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testGenerateCheckCode_nameSHA512() throws Exception {
        assertEquals("4a5c840f6f8626a9b2a067ef09fd5afa794ecc2997a9db5d100656f009349b5ab25d163fb5027ff6602b2b880b5beb4fc26383ce8eafd54976629d334d3dd739",
                FileIntegrityCheck.generateCheckCode(FILE_NAME, FileIntegrityCheck.CheckAlgorithm.SHA512));
    }



    @Test
	@DisplayName("传入null文件流应该抛出IllegalArgumentException异常")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testGenerateCheckCode_streamNullShouldThrowException() throws Exception {
        assertThrows(IllegalArgumentException.class,
                () -> FileIntegrityCheck.generateCheckCode((FileInputStream)null, FileIntegrityCheck.CheckAlgorithm.SHA512),
                "Input parameter is null");    	
    }

    @Test
	@DisplayName("传入null文件对象应该抛出IllegalArgumentException异常")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testGenerateCheckCode_fileNullShouldThrowException() throws Exception {
		assertThrows(IllegalArgumentException.class,
				() -> FileIntegrityCheck.generateCheckCode((File)null, FileIntegrityCheck.CheckAlgorithm.SHA512),
				"Input parameter is null"); 	
    }

    @Test
	@DisplayName("传入null文件路径应该抛出IllegalArgumentException异常")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testGenerateCheckCode_nameNullShouldThrowException() throws Exception {
		assertThrows(IllegalArgumentException.class,
				() -> FileIntegrityCheck.generateCheckCode((String)null, FileIntegrityCheck.CheckAlgorithm.SHA512),
				"Input parameter is null"); 	
    }

    @Test
	@DisplayName("根据MD5校验码与传入文件流检查文件完整性")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testCheck_streamMD5() throws Exception {
        try (FileInputStream fis = new FileInputStream(testFile)) {
			assertTrue(FileIntegrityCheck.check(fis, FileIntegrityCheck.CheckAlgorithm.MD5, "56432bfd166c3a2fcc541b31389000ef"));
        } catch (Exception e) {
			throw new RuntimeException(e);
        }
    }

    @Test
	@DisplayName("根据大小写混合MD5校验码与传入文件流检查文件完整性")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testCheck_streamUpperLowerMD5() throws Exception {
        try (FileInputStream fis = new FileInputStream(testFile)) {
			assertTrue(FileIntegrityCheck.check(fis, FileIntegrityCheck.CheckAlgorithm.MD5, "56432BFD166C3a2fcc541b31389000ef"));
        } catch (Exception e) {
			throw new RuntimeException(e);
        }
    }

    @Test
	@DisplayName("传入不合适的MD5校验码与传入文件流检查文件完整性不成功")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testCheck_streamMD5False() throws Exception {
        try (FileInputStream fis = new FileInputStream(testFile)) {
			assertFalse(FileIntegrityCheck.check(fis, FileIntegrityCheck.CheckAlgorithm.MD5, "57432bfd166c3a2fcc541b31389000ef"));
        } catch (Exception e) {
			throw new RuntimeException(e);
        }
    }

    @Test
	@DisplayName("根据MD5校验码与传入文件对象检查文件完整性")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testCheck_fileMD5() throws Exception {
        assertTrue(FileIntegrityCheck.check(testFile, FileIntegrityCheck.CheckAlgorithm.MD5, "56432bfd166c3a2fcc541b31389000ef"));
    }

    @Test
	@DisplayName("根据大小写混合MD5校验码与传入文件对象检查文件完整性")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testCheck_fileUpperLowerMD5() throws Exception {
        assertTrue(FileIntegrityCheck.check(testFile, FileIntegrityCheck.CheckAlgorithm.MD5, "56432BFd166c3A2FCC541B31389000ef"));
    }

    @Test
	@DisplayName("根据MD5校验码与传入文件路径检查文件完整性")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testCheck_nameMD5() throws Exception {
        assertTrue(FileIntegrityCheck.check(FILE_NAME, FileIntegrityCheck.CheckAlgorithm.MD5, "56432bfd166c3a2fcc541b31389000ef"));
    }

    @Test
	@DisplayName("根据大小写混合MD5校验码与传入文件路径检查文件完整性")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testCheck_nameUpperLowerMD5() throws Exception {
        assertTrue(FileIntegrityCheck.check(FILE_NAME, FileIntegrityCheck.CheckAlgorithm.MD5, "56432bfd166C3A2FCc541B31389000Ef"));
    }



    @Test
	@DisplayName("根据SHA1校验码与传入文件流检查文件完整性")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testCheck_streamSHA1() throws Exception {
        try (FileInputStream fis = new FileInputStream(testFile)) {
			assertTrue(FileIntegrityCheck.check(fis, FileIntegrityCheck.CheckAlgorithm.SHA1,
				"a4d073fe0191dcb9fba7d8e75a8eae8063732d04"));
        } catch (Exception e) {
			throw new RuntimeException(e);
        }
    }

    @Test
	@DisplayName("根据SHA1校验码与传入文件对象检查文件完整性")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testCheck_fileSHA1() throws Exception {
        assertTrue(FileIntegrityCheck.check(testFile, FileIntegrityCheck.CheckAlgorithm.SHA1,
			"a4d073fe0191dcb9fba7d8e75a8eae8063732d04"));
    }

    @Test
	@DisplayName("根据SHA1校验码与传入文件路径检查文件完整性")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testCheck_nameSHA1() throws Exception {
        assertTrue(FileIntegrityCheck.check(FILE_NAME, FileIntegrityCheck.CheckAlgorithm.SHA1,
			"a4d073fe0191dcb9fba7d8e75a8eae8063732d04"));
    }


    @Test
	@DisplayName("根据SHA256校验码与传入文件流检查文件完整性")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testCheck_streamSHA256() throws Exception {
        try (FileInputStream fis = new FileInputStream(testFile)) {
			assertTrue(FileIntegrityCheck.check(fis, FileIntegrityCheck.CheckAlgorithm.SHA256,
					"1f1b37b41c5da95e86c515c8d94e376ac22ad2cd1b5d0e7795b5b68f77b341e5"));
        } catch (Exception e) {
			throw new RuntimeException(e);
        }
    }

    @Test
	@DisplayName("根据SHA256校验码与传入文件对象检查文件完整性")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testCheck_fileSHA256() throws Exception {
        assertTrue(FileIntegrityCheck.check(testFile, FileIntegrityCheck.CheckAlgorithm.SHA256,
			"1f1b37b41c5da95e86c515c8d94e376ac22ad2cd1b5d0e7795b5b68f77b341e5"));
    }

    @Test
	@DisplayName("根据SHA256校验码与传入文件路径检查文件完整性")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testCheck_nameSHA256() throws Exception {
        assertTrue(FileIntegrityCheck.check(FILE_NAME, FileIntegrityCheck.CheckAlgorithm.SHA256,
			"1f1b37b41c5da95e86c515c8d94e376ac22ad2cd1b5d0e7795b5b68f77b341e5"));
    }


    @Test
	@DisplayName("根据SHA384校验码与传入文件流检查文件完整性")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testCheck_streamSHA384() throws Exception {
        try (FileInputStream fis = new FileInputStream(testFile)) {
			assertTrue(FileIntegrityCheck.check(fis, FileIntegrityCheck.CheckAlgorithm.SHA384,
					"6c16c6b9b4b3d18f138908b22ef3fa54efd2be9e9fbff1d8a00d4add647337cb95dfce8000645c2e5cba4638db55c13c"));
        } catch (Exception e) {
			throw new RuntimeException(e);
        }
    }

    @Test
	@DisplayName("根据SHA384校验码与传入文件对象检查文件完整性")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testCheck_fileSHA384() throws Exception {
        assertTrue(FileIntegrityCheck.check(testFile, FileIntegrityCheck.CheckAlgorithm.SHA384,
			"6c16c6b9b4b3d18f138908b22ef3fa54efd2be9e9fbff1d8a00d4add647337cb95dfce8000645c2e5cba4638db55c13c"));
    }

    @Test
	@DisplayName("根据SHA384校验码与传入文件路径检查文件完整性")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testCheck_nameSHA384() throws Exception {
        assertTrue(FileIntegrityCheck.check(FILE_NAME, FileIntegrityCheck.CheckAlgorithm.SHA384,
			"6c16c6b9b4b3d18f138908b22ef3fa54efd2be9e9fbff1d8a00d4add647337cb95dfce8000645c2e5cba4638db55c13c"));
    }



    @Test
	@DisplayName("根据SHA512校验码与传入文件流检查文件完整性")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testCheck_streamSHA512() throws Exception {
        try (FileInputStream fis = new FileInputStream(testFile)) {
			assertTrue(FileIntegrityCheck.check(fis, FileIntegrityCheck.CheckAlgorithm.SHA512,
					"4a5c840f6f8626a9b2a067ef09fd5afa794ecc2997a9db5d100656f009349b5ab25d163fb5027ff6602b2b880b5beb4fc26383ce8eafd54976629d334d3dd739"));
        } catch (Exception e) {
			throw new RuntimeException(e);
        }
    }

    @Test
	@DisplayName("根据SHA512校验码与传入文件对象检查文件完整性")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testCheck_fileSHA512() throws Exception {
        assertTrue(FileIntegrityCheck.check(testFile, FileIntegrityCheck.CheckAlgorithm.SHA512,
			"4a5c840f6f8626a9b2a067ef09fd5afa794ecc2997a9db5d100656f009349b5ab25d163fb5027ff6602b2b880b5beb4fc26383ce8eafd54976629d334d3dd739"));
    }

    @Test
	@DisplayName("根据SHA512校验码与传入文件路径检查文件完整性")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testCheck_nameSHA512() throws Exception {
        assertTrue(FileIntegrityCheck.check(FILE_NAME, FileIntegrityCheck.CheckAlgorithm.SHA512,
			"4a5c840f6f8626a9b2a067ef09fd5afa794ecc2997a9db5d100656f009349b5ab25d163fb5027ff6602b2b880b5beb4fc26383ce8eafd54976629d334d3dd739"));
    }



    @Test
	@DisplayName("传入null文件流检查文件完整性应该抛出IllegalArgumentException异常")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testCheck_streamNullShouldThrowException() throws Exception {
        assertThrows(IllegalArgumentException.class,
                () -> FileIntegrityCheck.check((FileInputStream)null, FileIntegrityCheck.CheckAlgorithm.SHA512, ""),
                "Input parameter is null");    	
    }

    @Test
	@DisplayName("传入null文件对象检查文件完整性应该抛出IllegalArgumentException异常")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testCheck_fileNullShouldThrowException() throws Exception {
        assertThrows(IllegalArgumentException.class,
                () -> FileIntegrityCheck.check((File)null, FileIntegrityCheck.CheckAlgorithm.SHA512, ""),
                "Input parameter is null");    	
    }

    @Test
	@DisplayName("传入null文件路径检查文件完整性应该抛出IllegalArgumentException异常")
	@Tags({
			@Tag("@id:61"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/26")
	})
    void testCheck_nameNullShouldThrowException() throws Exception {
        assertThrows(IllegalArgumentException.class,
                () -> FileIntegrityCheck.check((String)null, FileIntegrityCheck.CheckAlgorithm.SHA512, ""),
                "Input parameter is null");    	
    }	
}

