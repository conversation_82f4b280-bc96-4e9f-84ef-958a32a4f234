/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.strings;

/**
 * 【字符串处理】 字符串转义、反转义
 *
 * <AUTHOR>
 * @module 字符串处理
 * @date 2023/5/11 17:50
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class StringEscapeUtils {

    private StringEscapeUtils(){
        //do nothing
    }

    /**
     * 转义Java字符串
     *
     * <p>例如：</p>
     * <p>   input string: He didn't say, "Stop!" </p>
     * <p>   output string: He didn't say, \"Stop!\" </p>
     *
     * @param input 待转义字符串
     * @return java.lang.String  转义后的字符串
     * @throws
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/11
     */
    public static String escapeJava(String input){
        return org.apache.commons.text.StringEscapeUtils.escapeJava(input);
    }

    /**
     * 转义Json字符串
     *
     * <p>例如：</p>
     * <p>   input string: He didn't say, "Stop!" </p>
     * <p>   output string: He didn't say, \"Stop!\" </p>
     *
     * @param input 待转义字符串
     * @return java.lang.String  转义后的字符串
     * @throws
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/11
     */
    public static String escapeJson(String input){
        return org.apache.commons.text.StringEscapeUtils.escapeJson(input);
    }

    /**
     * 转义XML字符串
     * 转义后的字符串可以直接写入XML1.0文档
     *
     * <p>例如：</p>
     * <p>   input string: "bread" & "butter" </p>
     * <p>   output string: &quot;bread&quot; &amp; &quot;butter&quot; </p>
     *
     * @param input 待转义字符串
     * @return java.lang.String  转义后的字符串
     * @throws
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/11
     */
    public static String escapeXml10(String input){
        return org.apache.commons.text.StringEscapeUtils.escapeXml10(input);
    }

    /**
     * 转义XML字符串
     * 转义后的字符串可以直接写入XML1.1文档
     *
     * <p>例如：</p>
     * <p>   input string: "bread" & "butter" </p>
     * <p>   output string: &quot;bread&quot; &amp; &quot;butter&quot; </p>
     *
     * @param input 待转义字符串
     * @return java.lang.String  转义后的字符串
     * @throws
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/11
     */
    public static String escapeXml11(String input){
        return org.apache.commons.text.StringEscapeUtils.escapeXml11(input);
    }

    /**
     * 转义CSV字符串
     * 字符串包含逗号、换行或双引号时，返回结果是输入内容加上双引号
     *
     * <p>例如：</p>
     * <p>   input string: Venture "Extended Edition" </p>
     * <p>   output string: "Venture ""Extended Edition""" </p>
     *
     * @param input 待转义字符串
     * @return java.lang.String  转义后的字符串
     * @throws
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/11
     */
    public static String escapeCsv(String input){
        return org.apache.commons.text.StringEscapeUtils.escapeCsv(input);
    }

    /**
     * 转义Html3.0字符串
     *
     * @param input 待转义字符串
     * @return java.lang.String  转义后的字符串
     * @throws
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/11
     */
    public static String escapeHtml3(String input){
        return org.apache.commons.text.StringEscapeUtils.escapeHtml3(input);
    }

    /**
     * 转义Html4.0字符串
     *
     * @param input 待转义字符串
     * @return java.lang.String  转义后的字符串
     * @throws
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/11
     */
    public static String escapeHtml4(String input){
        return org.apache.commons.text.StringEscapeUtils.escapeHtml4(input);
    }

    /**
     * 转义XSI字符串
     *
     * <p>例如：</p>
     * <p>   input string: He didn't say, "Stop!" </p>
     * <p>   output string: He\ didn\'t\ say,\ \"Stop!\" </p>
     *
     * @param input 待转义字符串
     * @return java.lang.String  转义后的字符串
     * @throws
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/11
     */
    public static String escapeXSI(String input){
        return org.apache.commons.text.StringEscapeUtils.escapeXSI(input);
    }

    /**
     * 转义EcmaScript(js)字符串
     *
     * <p>例如：</p>
     * <p>   input string: He didn't say, "Stop!" </p>
     * <p>   output string: He didn\'t say, \"Stop!\" </p>
     *
     * @param input 待转义字符串
     * @return java.lang.String  转义后的字符串
     * @throws
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/11
     */
    public static String escapeEcmaScript(String input){
        return org.apache.commons.text.StringEscapeUtils.escapeEcmaScript(input);
    }

    /**
     * 反转义Java字符串
     *
     * <p>例如：</p>
     * <p>   input string: He didn't say, \"Stop!\" </p>
     * <p>   output string: He didn't say, "Stop!"</p>
     *
     * @param input 待反转义字符串
     * @return java.lang.String  反转义后的字符串
     * @throws
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/11
     */
    public static String unescapeJava(String input){
        return org.apache.commons.text.StringEscapeUtils.unescapeJava(input);
    }

    /**
     * 反转义Json字符串
     *
     * <p>例如：</p>
     * <p>   input string: He didn't say, \"Stop!\" </p>
     * <p>   output string: He didn't say, "Stop!" </p>
     *
     * @param input 待反转义字符串
     * @return java.lang.String  反转义后的字符串
     * @throws
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/11
     */
    public static String unescapeJson(String input){
        return org.apache.commons.text.StringEscapeUtils.unescapeJson(input);
    }

    /**
     * 反转义XML字符串
     *
     * <p>例如：</p>
     * <p>   input string: &quot;bread&quot; &amp; &quot;butter&quot; </p>
     * <p>   output string: "bread" & "butter" </p>
     *
     * @param input 待反转义字符串
     * @return java.lang.String  反转义后的字符串
     * @throws
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/11
     */
    public static String unescapeXml(String input){
        return org.apache.commons.text.StringEscapeUtils.unescapeXml(input);
    }

    /**
     * 反转义CSV字符串
     *
     * <p>例如：</p>
     * <p>   input string:  "Venture ""Extended Edition""" </p>
     * <p>   output string: Venture "Extended Edition" </p>
     *
     * @param input 待反转义字符串
     * @return java.lang.String  反转义后的字符串
     * @throws
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/11
     */
    public static String unescapeCsv(String input){
        return org.apache.commons.text.StringEscapeUtils.unescapeCsv(input);
    }

    /**
     * 反转义Html3.0字符串
     *
     * @param input 待反转义字符串
     * @return java.lang.String  反转义后的字符串
     * @throws
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/11
     */
    public static String unescapeHtml3(String input){
        return org.apache.commons.text.StringEscapeUtils.unescapeHtml3(input);
    }

    /**
     * 反转义Html4.0字符串
     *
     * @param input 待反转义字符串
     * @return java.lang.String  反转义后的字符串
     * @throws
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/11
     */
    public static String unescapeHtml4(String input){
        return org.apache.commons.text.StringEscapeUtils.unescapeHtml4(input);
    }

    /**
     * 反转义XSI字符串
     *
     * <p>例如：</p>
     * <p>   input string: He\ didn\'t\ say,\ \"Stop!\" </p>
     * <p>   output string: He didn't say, "Stop!" </p>
     *
     * @param input 待反转义字符串
     * @return java.lang.String  反转义后的字符串
     * @throws
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/11
     */
    public static String unescapeXSI(String input){
        return org.apache.commons.text.StringEscapeUtils.unescapeXSI(input);
    }

    /**
     * 反转义EcmaScript(js)字符串
     *
     * <p>例如：</p>
     * <p>   input string: He didn\'t say, \"Stop!\" </p>
     * <p>   output string: He didn't say, "Stop!" </p>
     *
     * @param input 待反转义字符串
     * @return java.lang.String  反转义后的字符串
     * @throws
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/11
     */
    public static String unescapeEcmaScript(String input){
        return org.apache.commons.text.StringEscapeUtils.unescapeEcmaScript(input);
    }
}
