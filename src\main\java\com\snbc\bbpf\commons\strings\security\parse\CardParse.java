/*
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.commons.strings.security.parse;


import org.apache.commons.lang3.StringUtils;

import static org.apache.commons.lang3.StringUtils.repeat;


/**
 * @ClassName:  CardParse
 * 身份证脱敏
 * @module:    字符脱敏
 * @Author:    yangweipeng
 * @date:      2023/05/06
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class CardParse implements IDesensitizedParse {
    /**
     * 右边显示字符数
     */
    private static final Integer RIGHT_LENGTH =4;
    /**
     * 【身份证号】显示最后四位，其他隐藏。共计18位或者15位，比如：*************1234
     *
     * @param srcStr
     * @return
     */
    @Override
    public String parseString(String srcStr) {
        if (StringUtils.isEmpty(srcStr)) {
            return srcStr;
        }
        int length = srcStr.length();
        if (length < RIGHT_LENGTH) {
            return srcStr;
        }
        return repeat('*', length-RIGHT_LENGTH)+srcStr.substring(length - RIGHT_LENGTH, length);
    }
}

