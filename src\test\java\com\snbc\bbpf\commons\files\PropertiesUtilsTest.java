/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.files;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.net.URISyntaxException;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;


/**
 * Properties工具类单元测试类
 *
 * <AUTHOR>
 * @module 文件处理模块
 * @date 2023-10-17 16:37
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class PropertiesUtilsTest {
    private String filePath;

    private static final String VALUE_IS_NOT_NUMBER_TYPE = "key not exists or value is not number type";

    @BeforeEach
    @DisplayName("准备资源")
    @Tags({
            @Tag("@id:75"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/11/01")
    })
    void setup() throws URISyntaxException {

        filePath = "./db.properties";
        FileUtils.write(filePath,"#driver config for mysql\n" +
                "driverClass=com.mysql.jdbc.Driver\n" +
                " = 123\n" +
                "123\n" +
                "url=*************************************************************************" +
                "user=root\n" +
                "password=root\n" +
                "formatSql=true\n" +
                "maxIdle=5\n" +
                "minIdle=abc\n" +
                "priceOk=10.01\n" +
                "priceNg=yyyy\n" +
                "doubleOk=1000000.9999999\n" +
                "doubleNg=xxxxx\n" +
                "arrayDemo=config1,config2,config3\n" +
                "arraySpaceDemo=config1,config2 config3\n" +
                "updateKey=3721\n" +
                "willUpdatedKey=4728");
    }


    @AfterEach
    @DisplayName("删除临时资源")
    @Tags({
            @Tag("@id:75"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/11/01")
    })
    void tearDown(){
        filePath = "./db.properties";
        FileUtils.delete(filePath);
    }



    @DisplayName("测试正常获取Int类型值")
    @Tags({
            @Tag("@id:75"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/10/20")
    })
    @Test
    void testGetIntValue() {
        Assertions.assertEquals(5, PropertiesUtils.getIntValue(filePath, "maxIdle"));
    }

    @DisplayName("测试异常获取Int类型值")
    @Tags({
            @Tag("@id:75"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/10/20")
    })
    @Test
    void testGetIntValue_with_exception() {
        assertThrows(IllegalArgumentException.class,
                () -> PropertiesUtils.getIntValue(filePath, "minIdle"),
                VALUE_IS_NOT_NUMBER_TYPE);
    }

    @DisplayName("测试正常获取Float类型值")
    @Tags({
            @Tag("@id:75"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/10/20")
    })
    @Test
    void testGetFloatValue() {
        Assertions.assertEquals(Float.valueOf("10.01"), PropertiesUtils.getFloatValue(filePath, "priceOk"));
    }

    @DisplayName("测试异常获取Float类型值")
    @Tags({
            @Tag("@id:75"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/10/20")
    })
    @Test
    void testGetFloatValue_with_exception() {
        assertThrows(IllegalArgumentException.class,
                () -> PropertiesUtils.getFloatValue(filePath, "priceNg"),
                VALUE_IS_NOT_NUMBER_TYPE);
    }

    @DisplayName("测试正常获取Double类型值")
    @Tags({
            @Tag("@id:75"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/10/20")
    })
    @Test
    void testGetDoubleValue() {
        Assertions.assertEquals(Double.valueOf("1000000.9999999"), PropertiesUtils.getDoubleValue(filePath, "doubleOk"));
    }

    @DisplayName("测试异常获取Double类型值")
    @Tags({
            @Tag("@id:75"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/10/20")
    })
    @Test
    void testGetDoubleValue_with_exception() {
        assertThrows(IllegalArgumentException.class,
                () -> PropertiesUtils.getDoubleValue(filePath, "doubleNg"),
                VALUE_IS_NOT_NUMBER_TYPE);
    }


    @DisplayName("测试正常获取boolean类型值")
    @Tags({
            @Tag("@id:75"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/10/17")
    })
    @Test
    void testGetBooleanValue() {
        final Boolean formatSqlValue = PropertiesUtils.getBooleanValue(filePath, "formatSql");
        Assertions.assertTrue(formatSqlValue);
    }

    @DisplayName("测试获取非boolean类型值")
    @Tags({
            @Tag("@id:75"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/10/17")
    })
    @Test
    void testGetBooleanValue_not_boolean() {
        Assertions.assertFalse(PropertiesUtils.getBooleanValue(filePath, "user"));
    }

    @DisplayName("测试正常获取String类型值")
    @Tags({
            @Tag("@id:75"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/10/17")
    })
    @Test
    void testGetStringValue() {
        final String userValue = PropertiesUtils.getStringValue(filePath, "password");
        Assertions.assertEquals("root", userValue);
    }

    @DisplayName("测试正常获取key值，file不存在")
    @Tags({
            @Tag("@id:75"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/10/17")
    })
    @Test
    void testGetStringValue_file_nofund() {
        assertThrows(IllegalArgumentException.class,
                () -> PropertiesUtils.getStringValue("xxxxx.properties", "user"),
                "is not file or file not exitst");
    }

    @DisplayName("测试正常获取array值")
    @Tags({
            @Tag("@id:75"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/10/17")
    })
    @Test
    void testGetArrayStringValue() {
        Assertions.assertArrayEquals(new String[]{"config1", "config2", "config3"},
                PropertiesUtils.getArrayStringValue(filePath, "arrayDemo"));
    }

    @DisplayName("测试正常获取array值,自定义分隔符号")
    @Tags({
            @Tag("@id:75"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/10/17")
    })
    @Test
    void testGetArrayStringValueWithDelimiter() {
        Assertions.assertArrayEquals(new String[]{"config1,config2", "config3"},
                PropertiesUtils.getArrayStringValue(filePath, "arraySpaceDemo", " "));
    }

    @DisplayName("测试正常获取key是否存在(存在、正则，不存在")
    @Tags({
            @Tag("@id:75"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/10/17")
    })
    @Test
    void isExistsKey() {
        Assertions.assertTrue(PropertiesUtils.isExistsKey(filePath, "user"));
        Assertions.assertTrue(PropertiesUtils.isExistsKey(filePath, "[a-z]{4}"));
        Assertions.assertFalse(PropertiesUtils.isExistsKey(filePath, "xxxx"));
    }

    @DisplayName("根据文件路径修改指定key的value，如果不存在则新增")
    @Tags({
            @Tag("@id:75"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/10/17")
    })
    @Test
    void testUpdateOrCreate() {
        final String userValue = PropertiesUtils.getStringValue(filePath, "user");
        Assertions.assertEquals("root", userValue);
        PropertiesUtils.updateOrCreate(filePath, "user", "admin");
        PropertiesUtils.updateOrCreate(filePath, "showSql", "true");
        final String newValue = PropertiesUtils.getStringValue(filePath, "user");
        Assertions.assertEquals("admin", newValue);
        final Boolean isShowSql = PropertiesUtils.getBooleanValue(filePath, "showSql");
        Assertions.assertTrue(isShowSql);
    }

    @DisplayName("根据文件路径修改指定key的value，如果不存在则新增,参数异常场景")
    @Tags({
            @Tag("@id:75"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/10/17")
    })
    @Test
    void testUpdateOrCreate_with_exception() {
        assertThrows(IllegalArgumentException.class,
                () -> PropertiesUtils.updateOrCreate(filePath, "", "admin"),
                "key or value is not allowed empty");
        assertThrows(IllegalArgumentException.class,
                () -> PropertiesUtils.updateOrCreate(filePath, "root", ""),
                "key or value is not allowed empty");
    }

    @DisplayName("修改key，oldkey不存在,新key已存在")
    @Tags({
            @Tag("@id:75"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/10/20")
    })
    @Test
    void testUpdateKey_with_exception() {
        assertThrows(IllegalArgumentException.class,
                () -> PropertiesUtils.updateKey(filePath, "notExistKey", "newKey"),
                "oldKey not exits");
        assertThrows(IllegalArgumentException.class,
                () -> PropertiesUtils.updateKey(filePath, "updateKey", "user"),
                "newKey exits");
        assertThrows(IllegalArgumentException.class,
                () -> PropertiesUtils.updateKey(filePath, null, "user"),
                "key is not allowed empty");

    }

    @DisplayName("修改key")
    @Tags({
            @Tag("@id:75"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/10/20")
    })
    @Test
    void testUpdateKey() {
        PropertiesUtils.updateKey(filePath, "willUpdatedKey", "DoneUpdatedKey");
        assertTrue(PropertiesUtils.isExistsKey(filePath,"DoneUpdatedKey"));
        assertFalse(PropertiesUtils.isExistsKey(filePath,"willUpdatedKey"));
    }

    @DisplayName("删除key")
    @Tags({
            @Tag("@id:75"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/10/27")
    })
    @Test
    void testDeleteKey() {
        String willDeleteKey = "willDeleteKey";
        PropertiesUtils.updateOrCreate(filePath, willDeleteKey, "willDelteValue");
        assertTrue(PropertiesUtils.isExistsKey(filePath, willDeleteKey));
        PropertiesUtils.deleteByKey(filePath, willDeleteKey);
        assertFalse(PropertiesUtils.isExistsKey(filePath, willDeleteKey));

    }
    @DisplayName("删除key，参数异常")
    @Tags({
            @Tag("@id:75"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/10/27")
    })
    @Test
    void testDeleteKey_with_exception() {
        assertThrows(IllegalArgumentException.class,
                () -> PropertiesUtils.deleteByKey(filePath, null),
                "key is not allowed empty");
    }

    @DisplayName("获取集合")
    @Tags({
            @Tag("@id:75"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/10/27")
    })
    @Test
    void testGetMap() {
        final Map<String, String> map = PropertiesUtils.getMap(filePath);
        assertTrue(map.containsKey("password"));
        assertTrue(map.containsKey("formatSql"));
    }

    @DisplayName("获取集合,带有key")
    @Tags({
            @Tag("@id:75"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/10/27")
    })
    @Test
    void testGetMap_withKey() {
        final Map<String, String> map = PropertiesUtils.getMap(filePath,"double");
        assertTrue(map.containsKey("doubleOk"));
        assertTrue(map.containsKey("doubleNg"));
    }



}