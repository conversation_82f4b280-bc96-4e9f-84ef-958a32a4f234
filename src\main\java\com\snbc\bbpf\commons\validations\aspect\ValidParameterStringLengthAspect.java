/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.validations.aspect;

import com.snbc.bbpf.commons.strings.StringEmptyCheck;
import com.snbc.bbpf.commons.validations.annotation.ValidStringLength;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;

/**
 * 字符串长度注解方法参数的切面类
 *
 * <AUTHOR>
 * @module
 * @date 2023/10/30 16:57
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
@Aspect
public class ValidParameterStringLengthAspect {
    //扫描所有配置@ValidStringLength注解的方法
    @Pointcut("execution(@com.snbc.bbpf.commons.validations.annotation.ValidStringLength *  *(..))")
    public void myPointcut() {
    }

    /*
     * 方法的环绕切面（对配置字符串长度注解注解的方法参数进行校验）
     *
     * @param joinPoint
     * @return Object
     * @since 1.3.0
     * <AUTHOR>
     * @date 2023/10/30
     */
    @Around("myPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {

        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Parameter[] parameters = method.getParameters();
        // 所有的参数
        Object[] args = joinPoint.getArgs();
        // 校验每个参数
        for (int i = 0; i < parameters.length; i++) {
            Parameter parameter = parameters[i];
            // 获取参数注解
            ValidStringLength anno = parameter.getAnnotation(ValidStringLength.class);
            // 存在@ValidStringLength，忽略
            if (null == anno) {
                continue;
            }

            int min = anno.min();
            if (args[i] == null && min > 0) {
                throw new IllegalArgumentException(StringEmptyCheck.isEmpty(anno.message()) ?
                        ("method[" + joinPoint.getSignature().getName() + "]parameter[" + parameter.getName() + "]can't be null") : anno.message());
            }

            int max = anno.max();
            if (((String) args[i]).length() < min || ((String) args[i]).length() > max) {
                throw new IllegalArgumentException(StringEmptyCheck.isEmpty(anno.message()) ?
                        ("method[" + joinPoint.getSignature().getName() + "]parameter[" + parameter.getName() + "]value length error") : anno.message());
            }
        }
        return joinPoint.proceed();
    }

}
