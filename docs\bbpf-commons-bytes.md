# 字节处理 (com.snbc.bbpf.commons.bytes)

## 类概览 

| 类名 | 功能描述 |
|------|----------|
| ByteOrderUtils | 提供字节数组根据大小端转换功能，支持与基本数据类型互转 |
| ByteArrayMergeUtils | 提供多个字节数组合并功能 |
| EndianHandler | 提供字节数组大小端处理功能 |
| BcdHandler | 提供BCD码编解码功能，支持数字与BCD码字节数组互转 |
| TlvEncoder | 提供TLV格式数据编码功能，支持多种数据类型的自动识别和序列化 (com.snbc.bbpf.commons.bytes.tlv) |
| TlvDataType | TLV数据类型枚举，定义Type字段值与Java类型的映射关系 (com.snbc.bbpf.commons.bytes.tlv) |
| TlvUtils | TLV工具类，提供长度计算、格式验证等辅助功能 (com.snbc.bbpf.commons.bytes.tlv) |
| ProtocolMessage | 协议消息数据结构，定义完整的协议消息格式 (com.snbc.bbpf.commons.bytes.protocol) |
| ProtocolMessageEncoder | 协议消息编码器，将消息对象编码为二进制数据 (com.snbc.bbpf.commons.bytes.protocol) |
| ProtocolMessageDecoder | 协议消息解码器，将二进制数据解码为消息对象 (com.snbc.bbpf.commons.bytes.protocol) |

## ByteOrderUtils - 字节序转换工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| toByteArray(char val, ByteOrder order) | 将char类型根据大小端转换为字节数组 | val: 待转换的char值<br>order: 字节序（大端/小端） | byte[]: 转换后的字节数组 |
| toByteArray(short val, ByteOrder order) | 将short类型根据大小端转换为字节数组 | val: 待转换的short值<br>order: 字节序（大端/小端） | byte[]: 转换后的字节数组 |
| toByteArray(int val, ByteOrder order) | 将int类型根据大小端转换为字节数组 | val: 待转换的int值<br>order: 字节序（大端/小端） | byte[]: 转换后的字节数组 |
| toByteArray(long val, ByteOrder order) | 将long类型根据大小端转换为字节数组 | val: 待转换的long值<br>order: 字节序（大端/小端） | byte[]: 转换后的字节数组 |
| toChar(byte[] bytes, ByteOrder order) | 将字节数组根据大小端转换为char类型 | bytes: 待转换的字节数组<br>order: 字节序（大端/小端） | char: 转换后的char值 |
| toShort(byte[] bytes, ByteOrder order) | 将字节数组根据大小端转换为short类型 | bytes: 待转换的字节数组<br>order: 字节序（大端/小端） | short: 转换后的short值 |
| toUnsignedShortInt(byte[] bytes, ByteOrder order) | 将字节数组根据大小端转换为int类型的ushort类型 | bytes: 待转换的字节数组<br>order: 字节序（大端/小端） | int: 转换后的无符号short值 |
| toInt(byte[] bytes, ByteOrder order) | 将字节数组根据大小端转换为int类型 | bytes: 待转换的字节数组<br>order: 字节序（大端/小端） | int: 转换后的int值 |
| toUnsignedIntLong(byte[] bytes, ByteOrder order) | 将字节数组根据大小端转换为long类型的uint类型 | bytes: 待转换的字节数组<br>order: 字节序（大端/小端） | long: 转换后的无符号int值 |
| toLong(byte[] bytes, ByteOrder order) | 将字节数组根据大小端转换为long类型 | bytes: 待转换的字节数组<br>order: 字节序（大端/小端） | long: 转换后的long值 |
| toUnsignedLongString(byte[] bytes, ByteOrder order) | 将字节数组根据大小端转换为String类型的ulong类型 | bytes: 待转换的字节数组<br>order: 字节序（大端/小端） | String: 转换后的无符号long值 |
| getByteSection(byte[] context, int start, int length) | 获取字节数组的部分内容 | context: 源字节数组<br>start: 起始位置<br>length: 长度 | byte[]: 截取的字节数组 |

### 使用注意事项

1. **字节数组长度匹配**: 在转换字节数组到基本类型时，需确保字节数组长度与目标类型字节数匹配，例如:
   - char: 2字节（Character.BYTES）
   - short: 2字节（Short.BYTES）
   - int: 4字节（Integer.BYTES）
   - long: 8字节（Long.BYTES）

2. **ByteOrder参数**: 所有方法都需要明确指定ByteOrder（大端或小端）:
   - ByteOrder.BIG_ENDIAN: 高位字节在前（网络字节序）
   - ByteOrder.LITTLE_ENDIAN: 低位字节在前（x86处理器常用）

3. **数组边界检查**: 使用`getByteSection`时，如果请求长度超过源数组可用长度，方法会自动调整长度，而不是抛出异常。

4. **无符号值处理**: 对于无符号类型转换（如`toUnsignedShortInt`、`toUnsignedIntLong`、`toUnsignedLongString`），返回值类型会比原始类型更大一级，以容纳可能的最大值。

### 使用示例

```java
import java.nio.ByteOrder;

// 将int转换为字节数组（小端模式）
byte[] littleEndianBytes = ByteOrderUtils.toByteArray(1, ByteOrder.LITTLE_ENDIAN);
// 输出: [1, 0, 0, 0]

// 将int转换为字节数组（大端模式）
byte[] bigEndianBytes = ByteOrderUtils.toByteArray(1, ByteOrder.BIG_ENDIAN);
// 输出: [0, 0, 0, 1]

// 将大端字节数组转换为int
int intValue = ByteOrderUtils.toInt(new byte[]{0x00, 0x00, 0x04, 0x18}, ByteOrder.BIG_ENDIAN);
// 输出: 1048

// 将小端字节数组转换为int
int intValueLittle = ByteOrderUtils.toInt(new byte[]{0x18, 0x04, 0x00, 0x00}, ByteOrder.LITTLE_ENDIAN);
// 输出: 1048

// 截取字节数组的一部分
byte[] section = ByteOrderUtils.getByteSection(new byte[]{0x01, 0x02, 0x03, 0x04, 0x05}, 1, 3);
// 输出: [0x02, 0x03, 0x04]

// 将char转换为字节数组（大端模式）
byte[] charBytes = ByteOrderUtils.toByteArray('A', ByteOrder.BIG_ENDIAN);
// 输出: [0, 65]

// 将字节数组转换为char（大端模式）
char charValue = ByteOrderUtils.toChar(new byte[]{0, 65}, ByteOrder.BIG_ENDIAN);
// 输出: 'A'
```

## ByteArrayMergeUtils - 字节数组合并工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| mergeBytes(byte[] data1, byte[] data2) | 合并两个字节数组到一个字节数组 | data1: 第一个字节数组<br>data2: 第二个字节数组 | byte[]: 合并后的字节数组 |
| mergeBytes(byte[]... values) | 合并多个字节数组到一个字节数组 | values: 需要合并的字节数组列表 | byte[]: 合并后的字节数组 |

### 使用注意事项

1. **异常处理**: 以下情况会抛出`IllegalArgumentException`异常:
   - 所有输入参数都为null: "All bytes are null"
   - 不提供任何参数: "values.length is 0"

2. **null参数处理**: 
   - 当合并多个数组且某些参数为null时，会自动忽略null参数
   - 当两个参数都为null时才会抛出异常

3. **内存效率**: 方法内部会创建新数组以存储合并结果，处理大量或大型字节数组时要注意内存使用。

### 使用示例

```java
// 合并两个字节数组
byte[] array1 = new byte[]{0x00, 0x01};
byte[] array2 = new byte[]{0x02, 0x03};
byte[] merged = ByteArrayMergeUtils.mergeBytes(array1, array2);
// 输出: [0x00, 0x01, 0x02, 0x03]

// 合并多个字节数组
byte[] array3 = new byte[]{0x04, 0x05};
byte[] mergedMultiple = ByteArrayMergeUtils.mergeBytes(array1, array2, array3);
// 输出: [0x00, 0x01, 0x02, 0x03, 0x04, 0x05]

// 当输入包含null时的处理
byte[] mergedWithNull = ByteArrayMergeUtils.mergeBytes(array1, null);
// 输出: [0x00, 0x01]

// 异常情况
try {
    // 所有输入都为null时抛出异常
    ByteArrayMergeUtils.mergeBytes(null, null);
} catch (IllegalArgumentException e) {
    System.out.println("异常: " + e.getMessage()); // 输出: 异常: All bytes are null
}

try {
    // 空参数时抛出异常
    ByteArrayMergeUtils.mergeBytes();
} catch (IllegalArgumentException e) {
    System.out.println("异常: " + e.getMessage()); // 输出: 异常: values.length is 0
}
```

## EndianHandler - 大小端转换工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| endian(byte[] bytes, int byteSize, int beginIndex, int endIndex) | 将字节数组根据字节数、开始/结束位置转换大或小端 | bytes: 待处理的字节数组<br>byteSize: 字节数<br>beginIndex: 开始位置<br>endIndex: 结束位置 | byte[]: 处理后的字节数组 |
| endian(byte[] bytes, int byteSize, int beginIndex) | 将字节数组根据字节数、开始位置转换大或小端 | bytes: 待处理的字节数组<br>byteSize: 字节数<br>beginIndex: 开始位置 | byte[]: 处理后的字节数组 |
| endian(byte[] bytes, int byteSize) | 将字节数组根据字节数转换大或小端 | bytes: 待处理的字节数组<br>byteSize: 字节数 | byte[]: 处理后的字节数组 |
| endian(byte[] bytes) | 将字节数组转换大或小端（默认4字节一组） | bytes: 待处理的字节数组 | byte[]: 处理后的字节数组 |

### 使用注意事项

1. **参数限制**: 多种情况会抛出`IllegalArgumentException`异常:
   - 字节数组长度小于2: "bytes array parameter invalid"
   - 字节大小小于2: "byteSize parameter invalid"
   - 索引小于0: "index parameter < 0 invalid"
   - 索引超出数组长度: "index parameter > length invalid"
   - 索引参数无效（开始>=结束或索引和超出范围）: "index parameter invalid"
   - 字节数组长度与字节大小不匹配: "bytes.length parameter invalid"

2. **字节大小设置**: 默认按4字节一组处理，可通过参数指定其他大小（如2字节），但必须大于等于2。

3. **处理规则**: 方法会按照指定字节大小分组，然后在每组内反转字节顺序，改变大小端。

4. **结果类型**: 方法返回一个新的字节数组，不会修改原始输入。

### 使用示例

```java
// 将字节数组按4字节一组进行大小端转换
byte[] data = new byte[]{0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07};
byte[] converted = EndianHandler.endian(data);
// 输出: [0x03, 0x02, 0x01, 0x00, 0x07, 0x06, 0x05, 0x04]

// 将字节数组按2字节一组进行大小端转换
byte[] convertedShort = EndianHandler.endian(data, 2);
// 输出: [0x01, 0x00, 0x03, 0x02, 0x05, 0x04, 0x07, 0x06]

// 将字节数组的部分内容按4字节一组进行大小端转换
byte[] partialConverted = EndianHandler.endian(data, 4, 0, 4);
// 输出: [0x03, 0x02, 0x01, 0x00]

// 异常情况
try {
    // 字节数组长度小于2时抛出异常
    EndianHandler.endian(new byte[]{0x01});
} catch (IllegalArgumentException e) {
    System.out.println("异常: " + e.getMessage()); // 输出: 异常: bytes array parameter invalid
}

try {
    // 字节数小于2时抛出异常
    EndianHandler.endian(data, 1);
} catch (IllegalArgumentException e) {
    System.out.println("异常: " + e.getMessage()); // 输出: 异常: byteSize parameter invalid
}
```

## BcdHandler - BCD编解码工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| encode(String value) | 将数字字符串使用BCD编码方式编码为字节数组 | value: 要编码的数字字符串 | byte[]: 编码后的BCD字节数组 |
| encode(BigInteger value) | 将BigInteger类型的值进行BCD编码 | value: 要编码的BigInteger值 | byte[]: 编码后的BCD字节数组 |
| encode(BigInteger value, int length) | 将BigInteger类型的值进行BCD编码，指定长度 | value: 要编码的BigInteger值<br>length: 编码后的字节数组长度 | byte[]: 编码后的BCD字节数组 |
| encode(long value, int length) | 将long类型的值进行BCD编码，指定长度 | value: 要编码的long值<br>length: 编码后的字节数组长度 | byte[]: 编码后的BCD字节数组 |
| encode(long value) | 将long类型的值进行BCD编码 | value: 要编码的long值 | byte[]: 编码后的BCD字节数组 |
| decode(byte[] bcd) | 将BCD编码的字节数组解码为BigInteger类型的值 | bcd: 要解码的BCD字节数组 | BigInteger: 解码后的值 |
| decodeAsString(byte[] bcd, boolean stripLeadingZero) | 将BCD码字节数组转换为十进制字符串 | bcd: 要解码的BCD字节数组<br>stripLeadingZero: 是否去除前导零 | String: 解码后的字符串 |
| singleAsciiToBcd(byte asc) | 将单个ASCII码转换为BCD码 | asc: ASCII码字节 | byte: 转换后的BCD码 |
| asciiToBcd(byte[] ascii) | 将ASCII码转换为BCD码的字节数组 | ascii: ASCII码字节数组 | byte[]: 转换后的BCD码字节数组 |
| bcdToAscii(byte[] bytes) | 将BCD码字节数组转换为ASCII码 | bytes: BCD码字节数组 | byte[]: 转换后的ASCII码字节数组 |

### 使用注意事项

1. **输入限制**: 以下情况会抛出`IllegalArgumentException`异常:
   - 非数字字符串编码: "Only numeric strings can be encoded"
   - 负数编码: "Only supports non negative numbers"
   - 值超过指定长度: "Value does not fit in byte array of length x"
   - BCD编码格式错误: "Illegal byte xxxx at n"

2. **前导零处理**: 解码BCD为字符串时，可选择是否保留前导零（stripLeadingZero参数）。

3. **大小端**: BCD处理默认使用大端表示（最高有效位在前）。

4. **编码特性**: 
   - 一个字节可以表示两个十进制数字（每4位一个数字）
   - 编码后的字节数组长度为原始十进制数值位数的一半（向上取整）

5. **性能考虑**: 对于非常大的数值处理时（大于63位），会自动转换为字符串处理以提高效率。

### 使用示例

```java
import java.math.BigInteger;

// 将数字字符串编码为BCD码
byte[] bcd1 = BcdHandler.encode("31");
// 输出: [0x31]

byte[] bcd2 = BcdHandler.encode("231");
// 输出: [0x02, 0x31]

// 将BigInteger编码为BCD码
byte[] bcd3 = BcdHandler.encode(new BigInteger("123456"));
// 输出: [0x12, 0x34, 0x56]

// 将long值编码为BCD码，指定长度
byte[] bcd4 = BcdHandler.encode(31L, 2);
// 输出: [0x00, 0x31]

// 将BCD码解码为BigInteger
BigInteger value = BcdHandler.decode(new byte[]{0x12, 0x34, 0x56});
// 输出: 123456

// 将BCD码解码为字符串
String str1 = BcdHandler.decodeAsString(new byte[]{0x01, 0x23}, true);
// 输出: "123"

String str2 = BcdHandler.decodeAsString(new byte[]{0x01, 0x23}, false);
// 输出: "0123"

// 将ASCII码转换为BCD码
byte[] ascii = new byte[]{'1', '2', '3', '4'};
byte[] bcdFromAscii = BcdHandler.asciiToBcd(ascii);
// 输出: [0x12, 0x34]

// 将BCD码转换为ASCII码
byte[] bcd = new byte[]{0x12, 0x34};
byte[] asciiFromBcd = BcdHandler.bcdToAscii(bcd);
// 输出: ['1', '2', '3', '4']

// 异常情况
try {
    // 非数字字符串编码时抛出异常
    BcdHandler.encode("12A");
} catch (IllegalArgumentException e) {
    System.out.println("异常: " + e.getMessage()); // 输出: 异常: Only numeric strings can be encoded
}

try {
    // 负数编码时抛出异常
    BcdHandler.encode(-123L);
} catch (IllegalArgumentException e) {
    System.out.println("异常: " + e.getMessage()); // 输出: 异常: Only supports non negative numbers
}
```

## TLV编码器 - Type-Length-Value格式数据编码

### 概述

TLV编码器提供了完整的Type-Length-Value格式数据编码功能，支持多种数据类型的自动识别和序列化。TLV是一种广泛使用的数据序列化格式，常用于网络协议和数据存储。

### 核心类

- **TlvEncoder**: 核心编码器类，提供数据编码功能
- **TlvDataType**: 数据类型枚举，定义Type值与Java类型的映射
- **TlvUtils**: 工具类，提供长度计算、格式验证等辅助功能

### 支持的数据类型

- 基本类型：byte、short、int、long、float、double、boolean
- 大数类型：BigInteger、BigDecimal
- 字符串类型：UTF-8和ASCII编码
- 复杂类型：Map、List/Array、byte[]
- 特殊类型：null值

### 快速开始

```java
import com.snbc.bbpf.commons.bytes.tlv.*;

// 创建编码器
TlvEncoder encoder = new TlvEncoder();

// 编码基本数据
byte[] result1 = encoder.encode(12345);
byte[] result2 = encoder.encode("Hello World");
byte[] result3 = encoder.encode(Arrays.asList(1, 2, 3));

// 编码Map
Map<String, Object> data = new HashMap<>();
data.put("id", 100);
data.put("name", "张三");
byte[] result4 = encoder.encode(data);
```

### 详细文档

完整的API文档和使用示例请参考：[TLV编码器详细文档](bbpf-commons-tlv.md)

## 协议消息编解码器 - 自定义协议格式处理

### 概述

协议消息编解码器提供了完整的自定义协议消息格式的编解码功能，支持固定消息头、扩展消息头和消息体的处理。该协议使用小端字节序和UTF-8字符串编码，专门用于处理特定的二进制通信协议。

### 核心类

- **ProtocolMessage**: 协议消息数据结构，包含所有消息字段
- **ProtocolMessageEncoder**: 消息编码器，将消息对象编码为二进制数据
- **ProtocolMessageDecoder**: 消息解码器，将二进制数据解码为消息对象

### 协议格式

```
[固定消息头(31字节)] + [扩展消息头(变长)] + [消息体(变长)] + [校验码(可选)]
```

- **固定消息头**: 包含消息标识、协议版本、消息类型、序号、时间戳等
- **扩展消息头**: 包含商户编号、产品编号、应用编号、保留信息等
- **消息体**: 业务数据，可以是任意二进制格式（包括TLV格式）

### 快速开始

```java
import com.snbc.bbpf.commons.bytes.protocol.*;

// 创建协议消息
ProtocolMessage message = new ProtocolMessage((short) 101, 1L, System.currentTimeMillis());
message.setTenantId("tenant_test");
message.setProductId("product_123");
message.setAppKey("app_key_abc");
message.setMessageBody("Hello World".getBytes(StandardCharsets.UTF_8));

// 编码消息
byte[] encoded = ProtocolMessageEncoder.encode(message);

// 解码消息
ProtocolMessage decoded = ProtocolMessageDecoder.decode(encoded);
```

### 实际应用验证

该编解码器已通过实际消息数据验证，能够正确处理包含中文内容的复杂协议消息，支持：

- 多种消息类型的编解码
- UTF-8中文字符串的正确处理
- 复杂TLV结构消息体的处理
- 小端字节序的正确转换

### 详细文档

完整的API文档和使用示例请参考：[协议消息编解码器详细文档](bbpf-commons-protocol.md)