package com.snbc.bbpf.commons.geo;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GeoCoordinateConverter单元测试类
 *
 * <AUTHOR>
 * @module 地理坐标模块
 * @date 2025-05-08
 * @copyright 2025 山东新北洋信息技术股份有限公司. All rights reserved
 */
class GeoCoordinateConverterTest {

    @Test
    @DisplayName("测试WGS84转GCJ02 - 中国境内坐标")
    @Tags({
            @Tag("@id:121"),
            @Tag("@author:xuping2"),
            @Tag("@date:2025/05/08")
    })
    void testWgs84ToGcj02InChina() {
        double[] result = GeoCoordinateConverter.wgs84ToGcj02(116.404, 39.915);
        assertNotNull(result);
        assertEquals(2, result.length);
        assertNotEquals(116.404, result[0], 0.0001);
        assertNotEquals(39.915, result[1], 0.0001);
    }

    @Test
    @DisplayName("测试WGS84转GCJ02 - 中国境外坐标")
    @Tags({
            @Tag("@id:121"),
            @Tag("@author:xuping2"),
            @Tag("@date:2025/05/08")
    })
    void testWgs84ToGcj02OutOfChina() {
        double[] result = GeoCoordinateConverter.wgs84ToGcj02(140.0, 60.0);
        assertNotNull(result);
        assertEquals(2, result.length);
        assertEquals(140.0, result[0], 0.01);
        assertEquals(60.0, result[1], 0.01);
    }

    @Test
    @DisplayName("测试GCJ02转WGS84 - 中国境内坐标")
    @Tags({
            @Tag("@id:121"),
            @Tag("@author:xuping2"),
            @Tag("@date:2025/05/08")
    })
    void testGcj02ToWgs84InChina() {
        double[] result = GeoCoordinateConverter.gcj02ToWgs84(116.404, 39.915);
        assertNotNull(result);
        assertEquals(2, result.length);
        assertNotEquals(116.404, result[0], 0.0001);
        assertNotEquals(39.915, result[1], 0.0001);
    }

    @Test
    @DisplayName("测试GCJ02转WGS84 - 中国境外坐标")
    @Tags({
            @Tag("@id:121"),
            @Tag("@author:xuping2"),
            @Tag("@date:2025/05/08")
    })
    void testGcj02ToWgs84OutOfChina() {
        double[] result = GeoCoordinateConverter.gcj02ToWgs84(140.0, 60.0);
        assertNotNull(result);
        assertEquals(2, result.length);
        assertEquals(139.99, result[0], 0.01);
        assertEquals(60.0, result[1], 0.01);
    }



    @Test
    @DisplayName("测试BD09转GCJ02 - 中国境内坐标")
    @Tags({
            @Tag("@id:120"),
            @Tag("@author:xuping2"),
            @Tag("@date:2025/05/08")
    })
    void testBd09ToGcj02InChina() {
        double[] result = GeoCoordinateConverter.bd09ToGcj02(116.404, 39.915);
        assertNotNull(result);
        assertEquals(2, result.length);
        assertNotEquals(116.404, result[0], 0.0001);
        assertNotEquals(39.915, result[1], 0.0001);
    }

    @Test
    @DisplayName("测试BD09转GCJ02 - 中国境外坐标")
    @Tags({
            @Tag("@id:120"),
            @Tag("@author:xuping2"),
            @Tag("@date:2025/05/08")
    })
    void testBd09ToGcj02OutOfChina() {
        double[] result = GeoCoordinateConverter.bd09ToGcj02(140.0, 60.0);
        assertNotNull(result);
        assertEquals(2, result.length);
        assertEquals(140.0, result[0], 0.01);
        assertEquals(60.0, result[1], 0.01);
    }

    @Test
    @DisplayName("测试GCJ02转BD09 - 中国境内坐标")
    @Tags({
            @Tag("@id:120"),
            @Tag("@author:xuping2"),
            @Tag("@date:2025/05/08")
    })
    void testGcj02ToBd09InChina() {
        double[] result = GeoCoordinateConverter.gcj02ToBd09(116.404, 39.915);
        assertNotNull(result);
        assertEquals(2, result.length);
        assertNotEquals(116.404, result[0], 0.0001);
        assertNotEquals(39.915, result[1], 0.0001);
    }

    @Test
    @DisplayName("测试GCJ02转BD09 - 中国境外坐标")
    @Tags({
            @Tag("@id:120"),
            @Tag("@author:xuping2"),
            @Tag("@date:2025/05/08")
    })
void testGcj02ToBd09OutOfChina() {
    // 调用GeoCoordinateConverter的gcj02ToBd09方法，将中国境外的GCJ02坐标(140.0, 60.0)转换为BD09坐标，结果存入result数组。
    double[] result = GeoCoordinateConverter.gcj02ToBd09(140.0, 60.0);
    // 断言result不为null，确保方法返回了结果。
    assertNotNull(result);
     // 断言result数组长度为2，确保返回的是经纬度对。
    assertEquals(2, result.length);
    // 断言转换后经度等于140.0，误差不超过0.0001，期望中国境外坐标不变。
    assertEquals(140.0, result[0], 0.01);
    // 断言转换后纬度等于60.0，误差不超过0.0001，期望中国境外坐标不变。
    assertEquals(60.0, result[1], 0.01);
}

    @Test
    @DisplayName("测试WGS84转BD09 - 中国境内坐标")
    @Tags({
            @Tag("@id:119"),
            @Tag("@author:xuping2"),
            @Tag("@date:2025/05/08")
    })
    void testWgs84ToBd09InChina() {
        double[] result = GeoCoordinateConverter.wgs84ToBd09(116.404, 39.915);
        assertNotNull(result);
        assertEquals(2, result.length);
        assertNotEquals(116.404, result[0], 0.0001);
        assertNotEquals(39.915, result[1], 0.0001);
    }

    @Test
    @DisplayName("测试WGS84转BD09 - 中国境外坐标")
    @Tags({
            @Tag("@id:119"),
            @Tag("@author:xuping2"),
            @Tag("@date:2025/05/08")
    })
    void testWgs84ToBd09OutOfChina() {
        double[] result = GeoCoordinateConverter.wgs84ToBd09(140.0, 60.0);
        assertNotNull(result);
        assertEquals(2, result.length);
        assertEquals(140.01, result[0], 0.01);
        assertEquals(60.01, result[1], 0.01);
    }

    @Test
    @DisplayName("测试BD09转WGS84 - 中国境内坐标")
    @Tags({
            @Tag("@id:119"),
            @Tag("@author:xuping2"),
            @Tag("@date:2025/05/08")
    })
    void testBd09ToWgs84InChina() {
        double[] result = GeoCoordinateConverter.bd09ToWgs84(116.404, 39.915);
        assertNotNull(result);
        assertEquals(2, result.length);
        assertNotEquals(116.404, result[0], 0.0001);
        assertNotEquals(39.915, result[1], 0.0001);
    }

    @Test
    @DisplayName("测试BD09转WGS84 - 中国境外坐标")
    @Tags({
            @Tag("@id:119"),
            @Tag("@author:xuping2"),
            @Tag("@date:2025/05/08")
    })
    void testBd09ToWgs84OutOfChina() {
        double[] result = GeoCoordinateConverter.bd09ToWgs84(140.0, 60.0);
        assertNotNull(result);
        assertEquals(2, result.length);
        assertEquals(139.984, result[0], 0.02);
        assertEquals(59.98, result[1], 0.02);
    }
}
