/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.randoms;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;

import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 随机字符串测试类
 *
 * <AUTHOR>
 * @module 字符串
 * @date 2023/4/28 3:43
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class RandomStringUtilsTest {
    @DisplayName("指定位数生成随机字符仅字母（带有大小写）")
    @Tags({
            @Tag("@id:18"),
            @Tag("@author:<PERSON><PERSON><PERSON><PERSON>"),
            @Tag("@date:2023/4/28")
    })
    @Test
    void testRandom_alphabetic() {
        final String upperLetters = RandomStringUtils.randomAlphabetic(3, true);
        assertEquals(3, upperLetters.length());
        assertEquals(true, upperLetters.matches("[A-Z]{3}"));

    }


    @DisplayName("指定位数生成随机字符仅字母数字（带有大小写）")
    @Tags({
            @Tag("@id:18"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/4/28")
    })
    @Test
    void testRandom_alpha_numeric() {
        final String alphaNumericLetters = RandomStringUtils.randomAlphanumeric(3, false);
        assertEquals(3, alphaNumericLetters.length());
        assertEquals(true, alphaNumericLetters.matches("[0-9a-z]{3}"));
    }

    @DisplayName("指定位数指定字符串生成随机字符")
    @Tags({
            @Tag("@id:18"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/4/28")
    })
    @Test
    void testRandom_fix_str() {
        final String randomFixStr = RandomStringUtils.randomFixChars("211Hello985", 3);
        assertEquals(3, randomFixStr.length());
        assertEquals(true, randomFixStr.matches("[12985Hello]{3}"));
    }

    @DisplayName("指定位数指定字符数组生成随机字符")
    @Tags({
            @Tag("@id:18"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/4/28")
    })
    @Test
    void testRandomFixChars_fix_chars() {
        char[] fixStr = {'2', '1', '1', 'H', 'e', 'l', 'l', 'o', '9', '8', '5'};
        final String randomFixStr = RandomStringUtils.randomFixChars(fixStr, 3);
        assertEquals(3, randomFixStr.length());
        assertEquals(true, randomFixStr.matches("[12985Hello]{3}"));
    }

    @DisplayName("指定正则表达式生成随机字符")
    @Tags({
            @Tag("@id:18"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/4/28")
    })
    @Test
    void testRandomByRegex_with_reg() {
        final String randomRegStr = RandomStringUtils.randomByRegex("[3-9]{3}[s-w]{2}");
        assertEquals(5, randomRegStr.length());
        assertEquals(true, randomRegStr.matches("[3-9s-w]{5}"));
    }

    @DisplayName("指定正则表达式生成随机字符_高级")
    @Tags({
            @Tag("@id:18"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/4/28")
    })
    @Test
    void testRandomByRegex_with_reg_advance() {
        final String randomRegStr = RandomStringUtils.randomByRegex("\\d{3,5}\\W\\w\\D\\S\\s[a-z]?[m-o]+");
        assertTrue(randomRegStr.length() >= 3);
        assertEquals(true, randomRegStr.matches("\\d{3,5}\\W\\w\\D\\S\\s[a-z]?[m-o]+"));
    }

    @DisplayName("指定位数指定正则表达式生成随机字符")
    @Tags({
            @Tag("@id:18"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/4/28")
    })
    @Test
    void testRandomByRegex_with_reg_with_num() {
        final String randomRegStr = RandomStringUtils.randomByRegex("[3-9]{3}[s-w]{2}", 3);
        assertEquals(3, randomRegStr.length());
        assertEquals(true, randomRegStr.matches("[3-9s-w]{3}"));
    }

    @DisplayName("简单性能测试1秒中1000次随机字符串生成")
    @Tags({
            @Tag("@id:18"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/15")
    })
    @Test
    @Timeout(value = 2, unit = TimeUnit.SECONDS)
    void testRandomByRegex_with_reg_with_num_pefermance() {
        for (int i = 0; i < 1000; i++) {
            RandomStringUtils.randomByRegex("[3-9]{3}[s-w]{2}", 3);
        }
    }
}