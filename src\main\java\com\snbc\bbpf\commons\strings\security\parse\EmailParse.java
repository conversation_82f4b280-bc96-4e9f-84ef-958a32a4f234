/*
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.commons.strings.security.parse;

import org.apache.commons.lang3.StringUtils;

/**
 * @ClassName:  EmailParse
 *   邮件脱敏
 * @module:    字符脱敏
 * @Author:    yangweipeng
 * @date:      2023/05/06
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class EmailParse implements IDesensitizedParse {

    /**
     * 左边字符数
     */
    private static final Integer LEFT_LENGTH =1;

    /**
     * 【电子邮箱 邮箱前缀仅显示第一个字母，前缀其他隐藏，用星号代替，@及后面的地址显示，比如：d**@126.com>
     *
     * @param email
     * @return
     */
    @Override
    public String parseString(String email) {
        if(StringUtils.isEmpty(email)){
            return email;
        }
        int index = StringUtils.indexOf(email, "@");
        return (index <= LEFT_LENGTH)?email:
                StringUtils.rightPad(StringUtils.left(email, LEFT_LENGTH), index, "*").
                        concat(StringUtils.mid(email, index, StringUtils.length(email)));
    }
}

