/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.strings;

import java.util.regex.Pattern;

/**
 * 判断字符串是否为整数或浮点数
 *
 * <AUTHOR>
 * @module 字符串处理
 * @date 2023/5/5 12:54
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class StringDigitCheck {

    /**
     * 整数的正则表达式
     */
    public static final Pattern patternNumber = Pattern.compile("^[-\\+]?[\\d]+$");
    /**
     * 浮点数的正则表达式
     */
    public static final Pattern patternFloat = Pattern.compile("^[+-]?[\\d]+([\\.][\\d]+)?([Ee][+-]?[\\d]+)?$");

    /**
     * 私有化构造函数
     */
    private StringDigitCheck() {
    }

    /*
     * 判断字符串是否为整数，支持带有正负号的数值和0的判断
     *
     * @param str
     * @return boolean
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/5
     */
    public static boolean isNumber(String str) {

        return !StringEmptyCheck.isEmpty(str) && patternNumber.matcher(str).matches();
    }

    /*
     * 判断字符串是否为浮点数，支持带有正负号的数值、科学计数法和0的判断
     *
     * @param str
     * @return boolean
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/5
     */
    public static boolean isFloat(String str) {

        return !StringEmptyCheck.isEmpty(str) && patternFloat.matcher(str).matches();
    }
}
