# 随机数生成 (com.snbc.bbpf.commons.randoms)

## 类概览 

| 类名 | 功能描述 |
|------|----------|
| RandomStringUtils | 提供随机字符串生成功能，包括字母、数字、字母数字混合等 |
| RandomNumberUtils | 提供随机数字生成功能，包括整数、小数、范围内随机数等 |
| IdUtils | 提供指定格式随机字符串、UUID和ULID生成功能 |
| RandomLetterPicker | 提供基于规则的随机字符生成功能，支持自定义字符范围 |
| SnowflakeUtils | 提供基于雪花算法的分布式ID生成功能 |

## RandomStringUtils - 随机字符串工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| randomAlphabetic(int length) | 生成指定长度的随机字母字符串 | length: 字符串长度 | String: 随机字母字符串 |
| randomAlphanumeric(int length) | 生成指定长度的随机字母数字字符串 | length: 字符串长度 | String: 随机字母数字字符串 |
| randomNumeric(int length) | 生成指定长度的随机数字字符串 | length: 字符串长度 | String: 随机数字字符串 |
| randomAscii(int length) | 生成指定长度的随机ASCII字符串 | length: 字符串长度 | String: 随机ASCII字符串 |
| random(int length, String chars) | 从指定字符集中生成随机字符串 | length: 字符串长度<br>chars: 字符集 | String: 随机字符串 |
| randomPassword(int length) | 生成指定长度的随机密码 | length: 密码长度 | String: 随机密码 |
| randomChinese(int length) | 生成指定长度的随机中文字符串 | length: 字符串长度 | String: 随机中文字符串 |

### 注意事项

1. 参数校验：所有方法的length参数必须大于0，否则会抛出IllegalArgumentException异常。
2. 性能考虑：当生成大量随机字符串时，可能会影响性能，特别是randomChinese方法。
3. 字符集限制：randomChinese方法生成的中文字符范围有限，不包含所有汉字。
4. 随机性：对于Linux/Unix/macOS系统会使用SecureRandom.getInstance("NativePRNGNonBlocking")非阻塞算法避免CPU熵不足导致的阻塞问题，而其他系统使用SecureRandom.getInstanceStrong()提供更高的随机性；之前版本统一使用标准Random。
5. 长度限制：为防止性能问题，最大推荐生成长度为128个字符。
6. 线程安全：所有方法都是线程安全的，可以在多线程环境中使用。

### 使用示例

```java
// 生成指定长度的随机字母字符串
String randomLetters = RandomStringUtils.randomAlphabetic(10);  // 例如："XqCvElMpRs"

// 生成指定长度的随机字母数字字符串
String randomAlphanumeric = RandomStringUtils.randomAlphanumeric(8);  // 例如："a7Bf9X2z"

// 生成指定长度的随机数字字符串
String randomNumbers = RandomStringUtils.randomNumeric(6);  // 例如："359218"

// 生成指定长度的随机ASCII字符串
String randomAscii = RandomStringUtils.randomAscii(10);  // 例如："&*%Jk3$#8q"

// 从指定字符集中生成随机字符串
String randomFromChars = RandomStringUtils.random(5, "ABCDEF0123456789");  // 例如："B3F5A"

// 生成指定长度的随机密码（包含大小写字母、数字和特殊字符）
String randomPassword = RandomStringUtils.randomPassword(12);  // 例如："kJ5*pQ9#zR2&"

// 生成指定长度的随机中文字符串
String randomChinese = RandomStringUtils.randomChinese(4);  // 例如："满载北京"
```

## RandomNumberUtils - 随机数字工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| randomInt() | 生成随机整数 | 无 | int: 随机整数 |
| randomInt(int max) | 生成[0, max)范围内的随机整数 | max: 最大值(不含) | int: 随机整数 |
| randomInt(int min, int max) | 生成[min, max)范围内的随机整数 | min: 最小值(含)<br>max: 最大值(不含) | int: 随机整数 |
| randomLong() | 生成随机长整数 | 无 | long: 随机长整数 |
| randomLong(long max) | 生成[0, max)范围内的随机长整数 | max: 最大值(不含) | long: 随机长整数 |
| randomLong(long min, long max) | 生成[min, max)范围内的随机长整数 | min: 最小值(含)<br>max: 最大值(不含) | long: 随机长整数 |
| randomDouble() | 生成随机双精度浮点数 | 无 | double: 随机双精度浮点数 |
| randomDouble(double max) | 生成[0, max)范围内的随机双精度浮点数 | max: 最大值(不含) | double: 随机双精度浮点数 |
| randomDouble(double min, double max) | 生成[min, max)范围内的随机双精度浮点数 | min: 最小值(含)<br>max: 最大值(不含) | double: 随机双精度浮点数 |
| randomBytes(int length) | 生成指定长度的随机字节数组 | length: 数组长度 | byte[]: 随机字节数组 |

### 注意事项

1. 参数校验：对于范围型方法，必须确保max > min，否则会抛出IllegalArgumentException异常。
2. 边界值：randomInt/randomLong/randomDouble 返回的值不包含max边界值，包含min边界值。
3. 特殊情况：当max等于Integer.MAX_VALUE时，randomInt(max)方法可能返回负数，这是因为内部使用了Random.nextInt()。
4. 标准值：randomDouble()默认返回0.0到1.0之间的随机值（不包含1.0）。
5. 性能考虑：randomBytes方法在生成大量随机字节时可能会消耗较多资源。
6. 内部依赖：该类内部使用了RandomStringUtils中的方法来实现randomNumeric，所以继承了其随机性质。

### 使用示例

```java
// 生成随机整数
int randomInt = RandomNumberUtils.randomInt();  // 例如：-2147483648 到 2147483647 之间

// 生成[0, max)范围内的随机整数
int randomIntWithMax = RandomNumberUtils.randomInt(100);  // 例如：0 到 99 之间

// 生成[min, max)范围内的随机整数
int randomIntWithRange = RandomNumberUtils.randomInt(10, 20);  // 例如：10 到 19 之间

// 生成随机长整数
long randomLong = RandomNumberUtils.randomLong();  // 例如：-9223372036854775808 到 9223372036854775807 之间

// 生成[0, max)范围内的随机长整数
long randomLongWithMax = RandomNumberUtils.randomLong(1000L);  // 例如：0 到 999 之间

// 生成[min, max)范围内的随机长整数
long randomLongWithRange = RandomNumberUtils.randomLong(100L, 200L);  // 例如：100 到 199 之间

// 生成随机双精度浮点数
double randomDouble = RandomNumberUtils.randomDouble();  // 例如：0.0 到 1.0 之间

// 生成[0, max)范围内的随机双精度浮点数
double randomDoubleWithMax = RandomNumberUtils.randomDouble(10.0);  // 例如：0.0 到 10.0 之间

// 生成[min, max)范围内的随机双精度浮点数
double randomDoubleWithRange = RandomNumberUtils.randomDouble(5.0, 15.0);  // 例如：5.0 到 15.0 之间

// 生成指定长度的随机字节数组
byte[] randomBytes = RandomNumberUtils.randomBytes(16);  // 生成16字节的随机字节数组
```

## IdUtils - 指定格式随机字符串工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| generateString(String prefix, int randomLength) | 生成带有前缀、年月日和随机数的字符串 | prefix: 前缀字符串<br>randomLength: 随机数长度 | String: 生成的字符串 |
| generateStringWithoutHyphen(String prefix) | 生成带有前缀和UUID的字符串(不含连字符) | prefix: 前缀字符串 | String: 生成的字符串 |
| generateStringWithHyphen(String prefix) | 生成带有前缀和UUID的字符串(含连字符) | prefix: 前缀字符串 | String: 生成的字符串 |
| generateUUID(boolean removeHyphens) | 生成UUID字符串 | removeHyphens: 是否去除连字符 | String: 生成的UUID字符串 |
| generateUUIDsWithHyphens(int count) | 生成指定数量的UUID字符串数组(含连字符) | count: 数量 | String[]: UUID字符串数组 |
| generateUUIDsWithoutHyphens(int count) | 生成指定数量的UUID字符串数组(不含连字符) | count: 数量 | String[]: UUID字符串数组 |
| generateULID() | 生成有序的ULID字符串 | 无 | String: ULID字符串 |

### 注意事项

1. 空值处理：所有接受prefix参数的方法，如果传入null或空字符串，会将prefix视为空字符串而不会抛出异常。
2. 日期格式：generateString方法使用当前日期（格式为yyyyMMdd），即每天生成的字符串前缀部分都相同。
3. UUID特性：UUID是随机生成的，不具有有序性，不适合作为数据库主键，但适合需要全局唯一标识的场景。
4. ULID特性：ULID是有序的，适合作为数据库主键，它结合了时间戳和随机性，保证了唯一性和排序特性。
5. 线程安全：generateULID方法使用了UlidCreator.getMonotonicUlid()实现，可以保证在同一毫秒内生成的ID也是递增的。
6. 依赖关系：ULID功能依赖于com.github.f4b6a3.ulid库，使用前需确保项目中包含此依赖。
7. 性能考虑：批量生成UUID/ULID时(generateUUIDsWithHyphens/generateUUIDsWithoutHyphens)，会有一定的性能消耗。

### 使用示例

```java
// 生成带有前缀、年月日和随机数的字符串
String str1 = IdUtils.generateString("ORDER-", 6);  // 例如："ORDER-20240806123456"

// 生成带有前缀和UUID的字符串(不含连字符)
String str2 = IdUtils.generateStringWithoutHyphen("USER-");  // 例如："USER-123e4567e89b12d3a456************"

// 生成带有前缀和UUID的字符串(含连字符)
String str3 = IdUtils.generateStringWithHyphen("PROD-");  // 例如："PROD-123e4567-e89b-12d3-a456-************"

// 生成UUID字符串
String uuid1 = IdUtils.generateUUID(false);  // 例如："123e4567-e89b-12d3-a456-************"
String uuid2 = IdUtils.generateUUID(true);   // 例如："123e4567e89b12d3a456************"

// 生成指定数量的UUID字符串数组
String[] uuids1 = IdUtils.generateUUIDsWithHyphens(3);
String[] uuids2 = IdUtils.generateUUIDsWithoutHyphens(3);

// 生成有序的ULID字符串
String ulid = IdUtils.generateULID();  // 例如："01ARZ3NDEKTSV4RRFFQ69G5FAV"
```

## RandomLetterPicker - 基于规则的随机字符生成工具

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| builder() | 创建Builder实例 | 无 | Builder: 构建器实例 |
| pickRandomLetter() | 从配置的字符集中随机挑选一个字符 | 无 | String: 随机字符 |
| constructByCharacterRange(List<String> bounds) | 通过字符范围构造RandomLetterPicker | bounds: 字符范围列表 | RandomLetterPicker: 实例 |

### Builder API

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| addAllByEnum(Class<E> enumClass) | 添加枚举类中的所有字符 | enumClass: 实现Letter接口的枚举类 | Builder: 构建器实例 |
| addAll(List<String> list) | 添加字符列表 | list: 字符列表 | Builder: 构建器实例 |
| add(String letter) | 添加单个字符 | letter: 要添加的字符 | Builder: 构建器实例 |
| remove(String remove) | 移除指定字符 | remove: 要移除的字符 | Builder: 构建器实例 |
| setRandom(Random random) | 设置随机数生成器 | random: 随机数生成器 | Builder: 构建器实例 |
| build() | 构建RandomLetterPicker实例 | 无 | RandomLetterPicker: 实例 |

### 注意事项

1. 内置枚举：包含了几个预定义的字符集合枚举：DigitLetter（数字）、LowerCaseLetter（小写字母）、UpperCaseLetter（大写字母）和SymbolLetter（特殊符号）。
2. 字符范围校验：使用constructByCharacterRange方法时，bounds参数必须是偶数长度的列表，每对元素表示一个字符范围的起止字符，且起始字符的编码必须小于结束字符的编码，否则会抛出IllegalArgumentException异常。
3. 线程安全：RandomLetterPicker实例在创建后是线程安全的，内部字符列表是不可变的。
4. 随机性：默认使用Java标准的Random类，可以通过Builder.setRandom()方法自定义随机数生成器，如使用SecureRandom提高随机性。
5. 空字符集：如果构建的RandomLetterPicker没有添加任何字符，调用pickRandomLetter()方法会抛出IndexOutOfBoundsException异常。
6. 性能考虑：频繁调用pickRandomLetter()方法时，应该重用RandomLetterPicker实例而不是每次都创建新实例。
7. 正则支持：RandomLetterPicker主要用于支持RandomStringUtils的randomByRegex方法，可以根据正则表达式生成随机字符。

### 使用示例

```java
// 使用Builder模式创建RandomLetterPicker
RandomLetterPicker digitPicker = RandomLetterPicker.builder()
    .addAllByEnum(DigitLetter.class)
    .build();

// 随机挑选一个数字字符
String randomDigit = digitPicker.pickRandomLetter();  // 例如："7"

// 创建包含大写字母和数字的RandomLetterPicker
RandomLetterPicker alphanumericPicker = RandomLetterPicker.builder()
    .addAllByEnum(UpperCaseLetter.class)
    .addAllByEnum(DigitLetter.class)
    .build();

// 随机挑选一个大写字母或数字
String randomChar = alphanumericPicker.pickRandomLetter();  // 例如："A"或"5"

// 通过字符范围构造RandomLetterPicker
List<String> bounds = Arrays.asList("A", "Z");
RandomLetterPicker upperCasePicker = RandomLetterPicker.constructByCharacterRange(bounds);

// 随机挑选一个大写字母
String randomUpperCase = upperCasePicker.pickRandomLetter();  // 例如："T"

// 自定义字符集
RandomLetterPicker customPicker = RandomLetterPicker.builder()
    .add("@")
    .add("#")
    .add("$")
    .add("%")
    .build();

// 随机挑选一个特殊字符
String randomSpecial = customPicker.pickRandomLetter();  // 例如："#"
```

## SnowflakeUtils - 雪花算法分布式ID生成工具

雪花算法是一种分布式ID生成算法，可以在集群环境中生成全局唯一、有序的ID，适用于跨机房场景。雪花ID一般为19位，比UUID更适合作为数据库索引。

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| SnowflakeUtils() | 构造函数，使用默认的工作ID和数据中心ID | 无 | SnowflakeUtils: 实例 |
| SnowflakeUtils(long workerId) | 构造函数，指定工作ID | workerId: 工作ID(0-31) | SnowflakeUtils: 实例 |
| SnowflakeUtils(long workerId, long dataCenterId) | 构造函数，指定工作ID和数据中心ID | workerId: 工作ID(0-31)<br>dataCenterId: 数据中心ID(0-31) | SnowflakeUtils: 实例 |
| genId() | 生成18位雪花ID，使用自动获取的工作ID和数据中心ID | 无 | Long: 雪花ID |
| genId(int count) | 生成指定位数的雪花ID，使用自动获取的工作ID和数据中心ID | count: ID位数(1-18) | Long: 雪花ID |
| genIdByCustomerWorkId(int count, int workId) | 生成指定位数和工作ID的雪花ID | count: ID位数(1-18)<br>workId: 工作ID(0-31) | Long: 雪花ID |
| genIdByCustomerDataId(int count, int dataId) | 生成指定位数和数据中心ID的雪花ID | count: ID位数(1-18)<br>dataId: 数据中心ID(0-31) | Long: 雪花ID |
| genId(int count, int dataId, int workId) | 生成指定位数、数据中心ID和工作ID的雪花ID | count: ID位数(1-18)<br>dataId: 数据中心ID(0-31)<br>workId: 工作ID(0-31) | Long: 雪花ID |
| getInstance() | 获取SnowflakeUtils的单例实例 | 无 | SnowflakeUtils: 单例实例 |
| snowflakeId() | 生成19位雪花ID | 无 | Long: 雪花ID |
| nextId() | 生成下一个19位雪花ID | 无 | long: 雪花ID |
| nextIdStr() | 生成下一个雪花ID的字符串表示 | 无 | String: 雪花ID字符串 |
| getWorkerId(long id) | 从雪花ID中提取工作ID | id: 雪花ID | long: 工作ID |
| getDataCenterId(long id) | 从雪花ID中提取数据中心ID | id: 雪花ID | long: 数据中心ID |
| getGenerateDateTime(long id) | 从雪花ID中提取生成时间 | id: 雪花ID | long: 时间戳 |

### 注意事项

1. ID位数限制：genId相关方法生成的ID位数范围必须在1-18之间，如果超出范围会返回18位ID。
2. 工作ID和数据中心ID范围：workerId和dataCenterId的有效范围是0-31，超出范围会导致异常。
3. 时钟回拨处理：雪花算法依赖系统时钟，当发生时钟回拨时，默认容忍2秒的回拨，超过会抛出异常，可以使用带timeOffset参数的构造函数来自定义容忍时间。
4. 自动ID分配：如果没有手动指定workerId和dataCenterId，系统会尝试根据主机名和IP地址自动生成，这在集群环境中可能不够唯一。
5. ID生成速度：每毫秒可以生成4096个不重复ID，超过这个速率会导致方法阻塞到下一毫秒。
6. 雪花ID结构：标准19位雪花ID由1位符号位+41位时间戳+5位数据中心ID+5位工作机器ID+12位序列号组成。
7. 长度截断：使用小于19位的ID时需要注意唯一性，因为会截断部分信息，特别是在高并发环境下。
8. 时间起点：默认的时间起点是2010年11月4日，可以通过构造函数修改，但修改后可能导致与之前生成的ID重复。
9. 系统时钟依赖：为提高性能，可以使用SystemClock类获取缓存的系统时间，减少系统调用。

### 使用示例

```java
// 快速创建雪花ID（18位）
Long id1 = SnowflakeUtils.genId();  // 例如：123456789012345678

// 创建指定位数的雪花ID
Long id2 = SnowflakeUtils.genId(10);  // 例如：1234567890

// 创建指定工作ID的雪花ID
Long id3 = SnowflakeUtils.genIdByCustomerWorkId(15, 5);  // 例如：123456789012345

// 创建指定数据中心ID的雪花ID
Long id4 = SnowflakeUtils.genIdByCustomerDataId(15, 3);  // 例如：123456789012345

// 创建指定工作ID和数据中心ID的雪花ID
Long id5 = SnowflakeUtils.genId(15, 3, 5);  // 例如：123456789012345

// 创建雪花ID（19位）
Long id6 = SnowflakeUtils.snowflakeId();  // 例如：1234567890123456789

// 实例化SnowflakeUtils并生成ID
SnowflakeUtils snowflake = new SnowflakeUtils(1, 2);  // 指定工作ID和数据中心ID
long id7 = snowflake.nextId();  // 例如：1234567890123456789
String id8 = snowflake.nextIdStr();  // 例如："1234567890123456789"

// 从ID中提取信息
long workerId = SnowflakeUtils.getWorkerId(id7);  // 例如：1
long dataCenterId = SnowflakeUtils.getDataCenterId(id7);  // 例如：2
long timestamp = snowflake.getGenerateDateTime(id7);  // 获取ID生成时间戳
``` 