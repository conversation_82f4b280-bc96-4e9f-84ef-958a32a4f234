package com.snbc.bbpf.commons.validations.validator;

import com.snbc.bbpf.commons.validations.ValidException;
import org.junit.jupiter.api.Test;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import static org.junit.jupiter.api.Assertions.*;
class IdentityCardValidatorTest {
    private final IdentityCardValidator validator = new IdentityCardValidator();
    @Test
    @DisplayName("输入非空")
    @Tags({
            @Tag("@id:36"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/6/28")
    })
    void testValidateWithValidIdNum() {
        //130130199303271212
        String validIdNum = "310103197802073244";
        String validMessage = "Valid ID number";
        assertDoesNotThrow(() -> validator.validate(validIdNum, validMessage));
    }
    @Test
    @DisplayName("输入为空")
    @Tags({
            @Tag("@id:36"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/6/28")
    })
    void testValidateWithNullIdNum() {
        String nullIdNum = null;
        String validMessage = "Valid ID number";
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                validator.validate(nullIdNum, validMessage));
        assertEquals("ID card number is empty or in wrong format", exception.getMessage());
    }
    @Test
    @DisplayName("身份证号码格式错误")
    @Tags({
            @Tag("@id:3"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/6/28")
    })
    void testValidateWithInvalidIdNumFormat() {
        String invalidIdNum = "1234567890123451";
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                validator.validate(invalidIdNum, null));
        assertEquals("ID card number format error", exception.getMessage());
    }
}
