/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.strings;

import org.apache.commons.lang3.StringUtils;

/**
 * 【字符串处理】 判断字符串是否以指定子字符串开头或结尾
 *
 * <AUTHOR>
 * @module 字符串处理
 * @date 2025/6/27 10:42
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class StringStartEndCheck {

    private StringStartEndCheck() {
        throw new IllegalStateException("StringStartEndCheck class");
    }
    /**
     * 判断字符串是否以指定子字符串开头
     *
     * @param value       待验证文本
     * @param prefix      子字符串
     * @return {@code true}: 以子字符串开头<br>{@code false}: 不以子字符串开头
     * @since 1.6.0
     * <AUTHOR>
     * @date 2025/6/27
     */
    public static boolean startsWith(final String value, final String prefix) {
        return StringUtils.isNotEmpty(value) && StringUtils.isNotEmpty(prefix) && value.startsWith(prefix);
    }

    /**
     * 判断字符串是否以指定子字符串结尾
     *
     * @param value       待验证文本
     * @param suffix      子字符串
     * @return {@code true}: 以子字符串结尾<br>{@code false}: 不以子字符串结尾
     * @since 1.6.0
     * <AUTHOR>
     * @date 2025/6/27
     */
    public static boolean endsWith(final String value, final String suffix) {
        return StringUtils.isNotEmpty(value) && StringUtils.isNotEmpty(suffix) && value.endsWith(suffix);
    }
}