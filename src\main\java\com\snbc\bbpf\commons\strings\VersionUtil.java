/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.strings;

/**
 * 1.比较表示程序不同版本的两个字符串。
 * 2.比较versionLeft和versionRight，如果versionLeft大于versionRight则返回true。
 * 3.比较versionLeft和versionRight，如果versionLeft大于等于versionRight则返回true。
 * 4.检查versionLeft是否小于versionRight,小于返回true
 * 5.比较两个版本字符串，如果versionLeft小于或等于versionRight则返回true
 * 6.检查两个版本字符串是否相等，等于返回true。
 * 需要支持对带数字版本号 1.0.0 和带字符的版本号 V1.0.0 或V1.0.alpha 等版本的大小比较。
 * 支持相同字母开头的版本号比较 （不单单是V字母开头的）
 * <p>
 *
 * <AUTHOR>
 * @module 版本号比较工具类
 * @date 2023/4/29 9:58
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class VersionUtil {
    private String versionLeft;
    private String versionRight;

    /**
     * 创建VersionUtil实例并设置左侧版本号
     * <p>
     *     示例：build("V1.1.4") 返回一个VersionUtil实例，versionLeft为"V1.1.4"
     * </p>
     * @param versionLeft 左侧版本字符串
     * @return VersionUtil实例
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/29
     */
    public static VersionUtil build(String versionLeft) {
        VersionUtil versionUtil = new VersionUtil();
        versionUtil.setVersionLeft(versionLeft);
        return versionUtil;
    }

    /**
     * 比较表示程序不同版本的两个字符串
     * <p>
     *     示例：compare("V1.1.4", "V1.1.3") = 1
     * </p>
     * @param version1 第一个版本字符串
     * @param version2 第二个版本字符串
     * @return 1（version1更大）、0（相等）、-1（version1更小）
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/29
     */
    public static int compare(String version1, String version2) {
        return version1.compareTo(version2);
    }

    /**
     * 比较versionLeft和versionRight，如果versionLeft大于versionRight则返回true
     * <p>
     *     示例：build("V1.1.4").greaterThan("V1.1.3") = true
     * </p>
     * @param versionRight 待比较的右侧版本字符串
     * @return 左侧版本大于右侧时返回true
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/29
     */
    public boolean greaterThan(String versionRight) {
        this.versionRight = versionRight;
        return compare(versionLeft, versionRight) > 0;
    }

    /**
     * 比较versionLeft和versionRight，如果versionLeft大于等于versionRight则返回true
     * <p>
     *     示例：build("V1.1.2").greaterThanOrEqualTo("V1.1.2") = true
     * </p>
     * @param versionRight 待比较的右侧版本字符串
     * @return 左侧版本大于等于右侧时返回true
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/29
     */
    public boolean greaterThanOrEqualTo(String versionRight) {
        this.versionRight = versionRight;
        return compare(versionLeft, versionRight) >= 0;
    }

    /**
     * 检查versionLeft是否小于versionRight，小于返回true
     * <p>
     *     示例：build("V1.1.0").lessThan("V1.1.1") = true
     * </p>
     * @param versionRight 待比较的右侧版本字符串
     * @return 左侧版本小于右侧时返回true
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/29
     */
    public boolean lessThan(String versionRight) {
        this.versionRight = versionRight;
        return compare(versionLeft, versionRight) < 0;
    }

    /**
     * 比较两个版本字符串，如果versionLeft小于或等于versionRight则返回true
     * <p>
     *     示例：build("V1.1.2").lessThanOrEqualTo("V1.1.2") = true
     * </p>
     * @param versionRight 待比较的右侧版本字符串
     * @return 左侧版本小于等于右侧时返回true
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/29
     */
    public boolean lessThanOrEqualTo(String versionRight) {
        this.versionRight = versionRight;
        return compare(versionLeft, versionRight) <= 0;
    }

    /**
     * 检查两个版本字符串是否相等，等于返回true
     * <p>
     *     示例：build("V1.1.2").equalTo("V1.1.2") = true
     * </p>
     * @param versionRight 待比较的右侧版本字符串
     * @return 两个版本相等时返回true
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/29
     */
    public boolean equalTo(String versionRight) {
        this.versionRight = versionRight;
        return compare(versionLeft, versionRight) == 0;
    }


    /**
     * 设置左侧版本字符串
     * <p>
     *     示例：setVersionLeft("V1.2.0") 将versionLeft更新为"V1.2.0"
     * </p>
     * @param versionLeft 新的左侧版本字符串
     * @return void
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/29
     */
    public void setVersionLeft(String versionLeft) {
        this.versionLeft = versionLeft;
    }


}
