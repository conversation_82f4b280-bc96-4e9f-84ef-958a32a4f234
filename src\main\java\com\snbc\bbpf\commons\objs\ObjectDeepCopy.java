/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.objs;

import com.google.gson.Gson;
import jdk.nashorn.internal.ir.debug.ObjectSizeCalculator;



/**
 * 【对象处理】深拷贝对象
 *
 * <p>将对象进行深拷贝</p>
 * <p>功能列表</p>
 * <p>1.将对象进行深拷贝，可设置限制拷贝对象大小 </p>
 *
 * <p>引入Gson第三方JAR包，该包在本项目中使用了深度拷贝功能(对拷贝对象进行序列化-》再反序列化成新的对象)，
 * 对拷贝对象没有额外要求，但使用当前功能进行深度拷贝可能性能慢于clone方法，若对性能有严格要求的话推荐自己实现clone方法。
 * Gson第三方JAR包与其他实现方法进行对比：
 * 一、重载clone() 
 * 优点：
 *	 1. 不需要引入第三方包 
 *	 2. 系统开销小  
 * 缺点：
 *	 1. 可用性较差，每次新增成员变量可能需要修改clone()方法 
 *	 2. 拷贝类（包括其成员变量）需要实现Cloneable接口
 * 
 * 二、Apache Commons Lang	  
 * 优点：
 *	 1. 可用性强，新增成员变量不需要修改拷贝方法 
 * 缺点：
 *	 1. 需要引入Apache Commons Lang第三方JAR包 
 *	 2. 拷贝类（包括其成员变量）需要实现Serializable接口 
 * 
 * 三、Gson 
 * 优点：
 *	 1. 可用性强，新增成员变量不需要修改拷贝方法 
 *	 2. 对拷贝类没有要求，不需要实现额外接口和方法   
 * 缺点：
 *	 1. 需要引入Gson第三方JAR包 
 * 
 * 四、Jackson  
 * 优点：
 *	 1. 可用性强，新增成员变量不需要修改拷贝方法 
 * 缺点：
 *	 1. 需要引入Jackson第三方JAR包 
 *	 2. 拷贝类（包括其成员变量）需要实现默认的无参构造函数 
 * 
 * 五、Kryo
 * 优点：
 *	 1. 可用性强，新增成员变量不需要修改拷贝方法 
 * 缺点：
 *	 1、对偏byte类型的对象，拷贝速度非常快；但对偏String类型的对象，拷贝速度比较慢
 *	 2、自定义类型要求有无参构造函数，对内部类要求定义为静态内部类且要有无参构造函数
 *
 * 由于考虑深度拷贝功能尽量对拷贝对象不做过多要求，所以选择Kryo，Gson，Jackson进一步验证性能：
 * 对偏byte类型的对象性能，相同字节数组1000次拷贝：Kryo(900ms) 优于 Gson(26506ms)
 * 对偏String类型的对象性能，相同字符串1000次拷贝：Gson(210ms) 优于 Kryo(727ms)
 * 对偏String类型的对象性能，相同字符串100000次拷贝：Gson(1251ms) 优于 Jackson(3534ms)
 * 但Kryo对拷贝对象有一定要求，综合考虑最后选择Gson用于实现深度拷贝功能.
 * </p>
 *
 * <AUTHOR>
 * @module
 * @date 2023/6/8 20:30
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class ObjectDeepCopy {

    private static final int ONE_MILLION_BITS = 1024 * 1024;
    private static final int DEFAULT_OBJSIZE = 8;
    public static final String PARAMETER_EMPTY = "Object is null!";
    public static final String PARAMETER_SIZETOOBIG = "Object size is too big!";

    private ObjectDeepCopy() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 将对象进行深拷贝
     *
     * <p>例如：输入对象String obj="123"，返回对象objOut="123", obj与objOut是两个不同的对象</p>  
     *
     * @param obj 待拷贝的对象
     * @param lLimitObjSize 限制对象大小(单位:M)，为0时不限制对象大小
     * @return 拷贝后的新对象
     * @throws IllegalArgumentException 如果输入对象为null、对象大小超过限制大小
     * @since 1.1.0
     * <AUTHOR>
     * @date 2023/6/8
     */
    public static Object deepCopy(Object obj, long lLimitObjSize) {
		if (obj == null) {
			throw new IllegalArgumentException(PARAMETER_EMPTY);			
		}
		if (lLimitObjSize != 0) {
	        long lObjSize = ObjectSizeCalculator.getObjectSize(obj);
			if (lLimitObjSize * ONE_MILLION_BITS < lObjSize) {
				throw new IllegalArgumentException(PARAMETER_SIZETOOBIG); 						
			}
		}
	
		Gson gson = new Gson();
		return gson.fromJson(gson.toJson(obj), obj.getClass());
    }

    /**
     * 将对象进行深拷贝
     *
     * <p>例如：输入对象String obj="123"，返回对象objOut="123", obj与objOut是两个不同的对象</p>  
     *
     * @param obj 待拷贝的对象
     * @return 拷贝后的新对象
     * @throws IllegalArgumentException 如果输入对象为null、对象大小超过限制大小
     * @since 1.1.0
     * <AUTHOR>
     * @date 2023/6/8
     */
    public static Object deepCopy(Object obj) {
		if (obj == null) {
			throw new IllegalArgumentException(PARAMETER_EMPTY);			
		}
		
		return deepCopy(obj, DEFAULT_OBJSIZE);
    }
}


