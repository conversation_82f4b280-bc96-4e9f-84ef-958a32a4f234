/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.randoms;

import com.snbc.bbpf.commons.strings.VersionUtil;
import com.snbc.bbpf.commons.system.OSType;
import com.snbc.bbpf.commons.system.SystemInfoUtil;
import java.security.SecureRandom;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.regex.Pattern;

/**
 * 【字符串处理】随机字符串处理
 * 生成随机字符串类，包含如下功能：
 * 生成指定位数的字符串（26分字母（包括大小写）和数字）。
 * 生成包含26个字母（包括大小写），指定位数的字符串。
 * 根据正则表达式，指定位数生成匹配正则表达式的字符串。
 * 根据正则表达式生成字符串。
 * 根据指定字符集合，指定位数生成字符串。
 * 以上场景下还可以指定返回的大小写格式。
 *
 * <AUTHOR>
 * @module 字符串处理
 * @date 2023/4/27 5:34 下午
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class RandomStringUtils {

    private static final int NUM_OF_UPPER_LIMIT = 20;
    private static final int MIN_RANGE_1 = 56320;
    private static final int MAX_RANGE_1 = 57343;

    private static final int MIN_RANGE_2 = 55296;
    private static final int MAX_RANGE_2 = 56191;
    private static final int START_RANGE_2 = 56320;
    private static final int MIN_RANGE_3 = 56192;
    private static final int MAX_RANGE_3 = 56319;

    private static final int MAX_RANDOM_LENGTH = 128;

    private static final int RADIX = 10;

    private static final Pattern EMPTY_REGEX = Pattern.compile("");
    private static final String PARAM_IS_NULL = "param is null";
    //仅在linux或unix下支持的非堵塞算法，用于解决随机是由于CPU熵不够导致的卡死问题
    private static final String ALGORITHM_NATIVEPRNGNONBLOCKING = "NativePRNGNonBlocking";
    /**
     * 自定义的Random类
     */
    private static Random random;

    static {
        try {
            final String javaVersion = System.getProperty("java.version");
            if (VersionUtil.compare(javaVersion, "1.8.201") > 0) {
                if(SystemInfoUtil.getSystemType().equals(OSType.LINUX)||
                        SystemInfoUtil.getSystemType().equals(OSType.UNIX)||
                        SystemInfoUtil.getSystemType().equals(OSType.MACOS)){
                    random = SecureRandom.getInstance(ALGORITHM_NATIVEPRNGNONBLOCKING);
                }else{
                    random = SecureRandom.getInstanceStrong();
                }
            } else {
                random = new Random();
            }
        } catch (Exception e) {
            throw new RuntimeException("Error generating random.", e);
        }
    }

    private RandomStringUtils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * # 生成包含26个字母（包括大小写），指定位数的字符串
     *
     * @param count   指定位数
     * @param isUpper 是否大写
     * @return String 指定位数的随机字符串
     * @throws
     * <AUTHOR>
     * @date 2023/4/27
     * @since 1.0.0
     */
    public static String randomAlphabetic(final int count, final boolean isUpper) {
        String randomStr = random(count, true, false);
        return isUpper ? randomStr.toUpperCase(Locale.ENGLISH) : randomStr.toLowerCase(Locale.ENGLISH);
    }

    /**
     * # 生成指定位数的字符串（26分字母（包括大小写）和数字）。
     *
     * @param count   指定位数
     * @param isUpper 是否大写
     * @return String 指定位数的随机字符串
     * @throws
     * <AUTHOR>
     * @date 2023/4/27
     * @since 1.0.0
     */
    public static String randomAlphanumeric(final int count, final boolean isUpper) {
        String randomStr = random(count, true, true);
        return isUpper ? randomStr.toUpperCase(Locale.ENGLISH) : randomStr.toLowerCase(Locale.ENGLISH);
    }


    /**
     * # 根据指定字符集合，指定位数生成字符串
     *
     * @param chars 指定字符集合
     * @param count 指定字符长度
     * @return String 返回随机字符串
     * @throws
     * <AUTHOR>
     * @date 2023/4/28
     * @since 1.0.0
     */
    public static String randomFixChars(char[] chars, final int count) {
        Objects.requireNonNull(chars, PARAM_IS_NULL);
        if (chars.length == 0) {
            throw new IllegalArgumentException(PARAM_IS_NULL);
        }
        if (count < 1) {
            throw new IllegalArgumentException("param lenght not allow zero or minus ");
        }
        return random(count, 0, chars.length, false, false, chars, random);
    }

    /**
     * # 根据指定字符串，指定位数生成字符串
     *
     * @param str   指定字符串
     * @param count 指定字符长度
     * @return String 返回随机字符串
     * @throws
     * <AUTHOR>
     * @date 2023/4/28
     * @since 1.0.0
     */
    public static String randomFixChars(String str, final int count) {
        Objects.requireNonNull(str, PARAM_IS_NULL);
        if (str.length() == 0) {
            throw new IllegalArgumentException(PARAM_IS_NULL);
        }
        return randomFixChars(str.toCharArray(), count);
    }


    /**
     * # 随机生成指定位数（可指定 是否是字符或数字）
     *
     * @param count   指定的位数
     * @param letters 是否是字符
     * @param numbers 是否是数字
     * @return String 随机字符串
     * @throws
     * <AUTHOR>
     * @date 2023/4/28
     * @since 1.0.0
     */
    public static String random(final int count, final boolean letters, final boolean numbers) {
        return random(count, 0, 0, letters, numbers, null, random);
    }


    /**
     * # 计算随机字符的的通用方法
     *
     * @param count   指定随机字符串的长度
     * @param start   开始位置（指定字符串）
     * @param end     结束位置（指定字符串）
     * @param letters 是否支持字符
     * @param numbers 是否支持数字
     * @param chars   指定chars数组
     * @param random  一个随机类
     * @return 随机字符串
     * @throws ArrayIndexOutOfBoundsException
     * @throws IllegalArgumentException
     * <AUTHOR>
     * @date 2023/4/28
     */
    private static String random(int count, int start, int end, final boolean letters, final boolean numbers,
                                 final char[] chars, final Random random) {
        if (count == 0) {
            return "";
        } else if (count < 0) {
            throw new IllegalArgumentException("Requested random string length " + count + " is less than 0.");
        } else {
            //DO nothing
        }
        if (chars != null && chars.length == 0) {
            throw new IllegalArgumentException("The chars array must not be empty");
        }

        if (start == 0 && end == 0) {
            if (chars != null) {
                end = chars.length;
            } else {
                if (isNotLetterAndNumber(letters, numbers)) {
                    end = Integer.MAX_VALUE;
                } else {
                    end = 'z' + 1;
                    start = ' ';
                }
            }
        } else {
            if (end <= start) {
                throw new IllegalArgumentException(
                        "Parameter end (" + end + ") must be greater than start (" + start + ")");
            }
        }

        final char[] buffer = new char[count];
        final int gap = end - start;

        while (count-- != 0) {
            char ch;
            if (chars == null) {
                ch = (char) (random.nextInt(gap) + start);
            } else {
                ch = chars[random.nextInt(gap) + start];
            }
            if (isLetter(letters, ch)
                    || isNumber(numbers, ch)
                    || isNotLetterAndNumber(letters, numbers)) {
                if (ch >= MIN_RANGE_1 && ch <= MAX_RANGE_1) {
                    if (count == 0) {
                        count++;
                    } else {
                        // low surrogate, insert high surrogate after putting it in
                        buffer[count] = ch;
                        count--;
                        buffer[count] = (char) (MIN_RANGE_2 + random.nextInt(MAX_RANDOM_LENGTH));
                    }
                } else if (ch >= MIN_RANGE_2 && ch <= MAX_RANGE_2) {
                    if (count == 0) {
                        count++;
                    } else {
                        // high surrogate, insert low surrogate before putting it in
                        buffer[count] = (char) (START_RANGE_2 + random.nextInt(MAX_RANDOM_LENGTH));
                        count--;
                        buffer[count] = ch;
                    }
                } else if (ch >= MIN_RANGE_3 && ch <= MAX_RANGE_3) {
                    // private high surrogate, no effing clue, so skip it
                    count++;
                } else {
                    buffer[count] = ch;
                }
            } else {
                count++;
            }
        }
        return new String(buffer);
    }

    /**
     * 判断不是字母也不是数字
     *
     * @param letters 是否支持字母
     * @param numbers 是否支持数字
     * @return boolean
     * @throws
     * <AUTHOR>
     * @date 2023/4/28
     * @since 1.0.0
     */
    private static boolean isNotLetterAndNumber(boolean letters, boolean numbers) {
        return !letters && !numbers;
    }

    /**
     * 是否支持数字，且生成的字符是否是数字
     *
     * @param numbers 是否支持数据
     * @param ch      生成的值
     * @return boolean
     * @throws
     * <AUTHOR>
     * @date 2023/4/28
     * @since 1.0.0
     */
    private static boolean isNumber(boolean numbers, char ch) {
        return numbers && Character.isDigit(ch);
    }

    /**
     * 是否支持字母，且生成的字符是否是字母
     *
     * @param letters 是否支持字母
     * @param ch      生成字符
     * @return boolean
     * @throws
     * <AUTHOR>
     * @date 2023/7/31
     * @since 1.0.0
     */
    private static boolean isLetter(boolean letters, char ch) {
        return letters && Character.isLetter(ch);
    }


    /**
     * # 根据正则表达式生成随机字符串（最多20个）
     *
     * @param regex 正则表达式
     * @return 随机字符串
     * @throws
     * <AUTHOR>
     * @date 2023/4/28
     * @since 1.0.0
     */
    public static String randomByRegex(final String regex) {
        final Map<String, RandomLetterPicker> userDefinedPickers = new HashMap<>();

        final RandomLetterPickers pickers = new RandomLetterPickers(random);
        String expanded = new RegexNormalizer(NUM_OF_UPPER_LIMIT, random).normalizeQuantifiers(regex);

        final String[] regexCharacters = expanded.split(EMPTY_REGEX.toString());
        final int length = regexCharacters.length;

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            String character = regexCharacters[i];
            RandomLetterPicker picker = null;
            String candidateCharacter = null;
            switch (character) {
                case "\\":
                    try {
                        character = regexCharacters[++i];
                    } catch (ArrayIndexOutOfBoundsException e) {
                        throw new RuntimeException("Detected invalid escape character", e);
                    }
                    GetPicker getPicker = new GetPicker(pickers, character, picker, candidateCharacter).invoke();
                    picker = getPicker.getPicker();
                    candidateCharacter = getPicker.getCandidateCharacter();
                    break;
                case "[":
                    try {
                        RandomLetterPickers.ScannedUserDefinedPicker scannedUserDefinedPicker =
                                RandomLetterPickers.scan(regexCharacters, i);
                        i = scannedUserDefinedPicker.getCursor();

                        picker = getRandomLetterPicker(userDefinedPickers, scannedUserDefinedPicker,
                                scannedUserDefinedPicker.getKey());
                    } catch (ArrayIndexOutOfBoundsException e) {
                        throw new RuntimeException("Occurs parsing error", e);
                    }
                    break;
                case ".":
                    picker = pickers.getAny();
                    break;
                default:
                    candidateCharacter = character;
            }

            int repetitionNum = 1;
            if (i + 1 < length) {
                String nextCharacter = regexCharacters[i + 1];
                if ("{".equals(nextCharacter)) {
                    int j = i + 1;
                    StringBuilder sbForQuantifier = new StringBuilder();
                    try {
                        while (!"}".equals(nextCharacter = regexCharacters[++j])) {
                            sbForQuantifier.append(nextCharacter);
                        }

                        repetitionNum = Integer.parseInt(sbForQuantifier.toString(), RADIX);
                        i = j;
                    } catch (ArrayIndexOutOfBoundsException | NumberFormatException e) {
                        // do nothing
                        throw new RuntimeException("array index out.", e);
                    }
                }
            }

            if (picker != null) {
                for (int j = 0; j < repetitionNum; j++) {
                    sb.append(picker.pickRandomLetter());
                }
            } else if (candidateCharacter != null) {
                for (int j = 0; j < repetitionNum; j++) {
                    sb.append(candidateCharacter);
                }
            } else {
                throw new RuntimeException("Occurs parsing error");
            }
        }

        return sb.toString();
    }

    /**
     * 获得随机的字符picker
     *
     * @param userDefinedPickers       用户定义的picker
     * @param scannedUserDefinedPicker 扫描用户定义picker
     * @param key                      key值
     * @return @see com.snbc.bbpf.commons.randoms.RandomLetterPicker
     * @throws
     * <AUTHOR>
     * @date 2023/4/28
     * @since 1.0.0
     */
    private static RandomLetterPicker getRandomLetterPicker(Map<String, RandomLetterPicker> userDefinedPickers,
                                                            RandomLetterPickers.ScannedUserDefinedPicker scannedUserDefinedPicker,
                                                            String key) {
        RandomLetterPicker picker;

        RandomLetterPicker userDefinedPicker = RandomLetterPicker
                .constructByCharacterRange(scannedUserDefinedPicker.getBounds());

        userDefinedPickers.computeIfAbsent(key, k -> userDefinedPicker);


        picker = userDefinedPickers.get(key);
        return picker;
    }

    /**
     * # 根据正则表达式生成指定位数字符串
     *
     * @param regex 正则表达式
     * @return num 指定位数的字符串
     * @throws
     * <AUTHOR>
     * @date 2023/4/28
     * @since 1.0.0
     */
    public static String randomByRegex(final String regex, final int num) {
        return randomFixChars(randomByRegex(regex), num);
    }

    private static class GetPicker {
        private RandomLetterPickers pickers;
        private String character;
        private RandomLetterPicker picker;
        private String candidateCharacter;

        public GetPicker(RandomLetterPickers pickers, String character,
                         RandomLetterPicker picker, String candidateCharacter) {
            this.pickers = pickers;
            this.character = character;
            this.picker = picker;
            this.candidateCharacter = candidateCharacter;
        }

        public RandomLetterPicker getPicker() {
            return picker;
        }

        public String getCandidateCharacter() {
            return candidateCharacter;
        }

        public GetPicker invoke() {
            switch (character) {
                case "w":
                    picker = pickers.getWord();
                    break;
                case "d":
                    picker = pickers.getDigit();
                    break;
                case "W":
                    picker = pickers.getNotWord();
                    break;
                case "D":
                    picker = pickers.getNotDigit();
                    break;
                case "s":
                    picker = pickers.getSpace();
                    break;
                case "S":
                    picker = pickers.getAny();
                    break;
                default:
                    candidateCharacter = character;
            }
            return this;
        }
    }

}
