/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.validations.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 验证注解
 *
 * <AUTHOR>
 * @module 字符串校验模块
 * @date 2023/7/16
 * @since 1.1.0
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD,ElementType.METHOD,ElementType.PARAMETER})
public @interface ValidString {
    // 验证类型
    ValidStringType validStringType() default ValidStringType.REGEX;

    // 错误信息
    String message() default "";

    String regex() default "";
}
