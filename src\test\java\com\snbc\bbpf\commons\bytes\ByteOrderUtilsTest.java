package com.snbc.bbpf.commons.bytes;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.nio.ByteOrder;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * byte数组根据大小端转换单元测试类
 *
 * <AUTHOR>
 * @module 字节处理模块
 * @date 2024/8/15 20:30
 * @copyright 2024 山东新北洋信息技术股份有限公司. All rights reserved
 */
class ByteOrderUtilsTest {


    @Test
    @DisplayName("将char类型根据大小端转换为字节数组,转换大端")
    @Tags({
            @Tag("@id:91"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/8/15")
    })
    void toByteArray_charToBig() {
        assertArrayEquals(new byte[] {0x00, 0x41 },
                ByteOrderUtils.toByteArray('A', ByteOrder.BIG_ENDIAN));
    }

    @Test
    @DisplayName("将char类型根据大小端转换为字节数组，转换小端")
    @Tags({
            @Tag("@id:91"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/8/15")
    })
    void toByteArray_charToLittle() {
        assertArrayEquals(new byte[] {0x41, 0x00 },
                ByteOrderUtils.toByteArray('A', ByteOrder.LITTLE_ENDIAN));
    }


    @Test
    @DisplayName("将int类型根据大小端转换为字节数组,转换大端")
    @Tags({
            @Tag("@id:91"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/8/15")
    })
    void toByteArray_intToBig() {
        assertArrayEquals(new byte[] {0x00, 0x00, 0x04, 0x18 },
                ByteOrderUtils.toByteArray(1048, ByteOrder.BIG_ENDIAN));
    }

    @Test
    @DisplayName("将int类型根据大小端转换为字节数组，转换小端")
    @Tags({
            @Tag("@id:91"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/8/15")
    })
    void toByteArray_intToLittle() {
        assertArrayEquals(new byte[] {0x18, 0x04, 0x00, 0x00 },
                ByteOrderUtils.toByteArray(1048, ByteOrder.LITTLE_ENDIAN));
    }


    @Test
    @DisplayName("将字节数组根据大小端转换为int数据，转换小端")
    @Tags({
            @Tag("@id:91"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/8/15")
    })
    void toInt_byteToInt() {
        assertEquals(-1048,
                ByteOrderUtils.toInt(new byte[] {(byte)0xe8, (byte)0xfb, (byte)0xff, (byte)0xff }, ByteOrder.LITTLE_ENDIAN));
    }

    @Test
    @DisplayName("将字节数组根据大小端转换为uint数据，转换小端,并且将转换后的数字转为字节在验证")
    @Tags({
            @Tag("@id:91"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/8/15")
    })
    void toUInt_byteToUint() {
        assertEquals(4294966248L,
                ByteOrderUtils.toUnsignedIntLong(new byte[] {(byte)0xe8, (byte)0xfb, (byte)0xff, (byte)0xff }, ByteOrder.LITTLE_ENDIAN));

        assertArrayEquals(new byte[] {(byte)0xe8, (byte)0xfb, (byte)0xff, (byte)0xff, 0x00, 0x00, 0x00, 0x00 },
                ByteOrderUtils.toByteArray(4294966248L, ByteOrder.LITTLE_ENDIAN));
    }


    @Test
    @DisplayName("将字节数组根据大小端转换为uint数据，转换小端,转换字节数组中的一部分")
    @Tags({
            @Tag("@id:91"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/8/15")
    })
    void toUInt_byteToUint_Section() {
        byte[] testArr = {0,0,0,(byte)0xe8, (byte)0xfb, (byte)0xff, (byte)0xff,0,0,3,4};

        assertEquals(4294966248L,
                ByteOrderUtils.toUnsignedIntLong(ByteOrderUtils.getByteSection(testArr,3,4), ByteOrder.LITTLE_ENDIAN));

    }

    @Test
    @DisplayName("将字节数组根据大小端转换为uint数据，转换小端,转换字节数组中的一部分,length-start超过数组长度")
    @Tags({
            @Tag("@id:91"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/8/15")
    })
    void toUInt_byteToUint_Section_lengthError() {
        byte[] testArr = {0,0,0,(byte)0xe8, (byte)0xfb, (byte)0xff, (byte)0xff,0,0,3,4};

        assertEquals(4294966248L,
                ByteOrderUtils.toUnsignedIntLong(ByteOrderUtils.getByteSection(testArr,3,8), ByteOrder.LITTLE_ENDIAN));
        assertEquals(67305472L,
                ByteOrderUtils.toUnsignedIntLong(ByteOrderUtils.getByteSection(testArr,7,8), ByteOrder.LITTLE_ENDIAN));
    }

}
