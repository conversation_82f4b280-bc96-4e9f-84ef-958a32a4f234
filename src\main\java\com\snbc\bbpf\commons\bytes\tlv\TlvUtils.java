/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.bytes.tlv;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

/**
 * TLV格式数据处理工具类
 * <p>提供TLV格式数据的辅助处理功能</p>
 * <p>包括数据长度计算、类型识别、格式验证等工具方法</p>
 * 
 * <AUTHOR>
 * @module 字节处理
 * @date 2025/07/25 11:00
 * @copyright 2024 山东新北洋信息技术股份有限公司. All rights reserved
 * @since 1.7.0
 */
public class TlvUtils {
    
    /**
     * 私有构造函数，防止实例化
     */
    private TlvUtils() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }
    
    /**
     * 计算数据编码后的字节长度
     * @param data 要计算长度的数据
     * @return 编码后的字节长度
     * @throws IllegalArgumentException 如果数据类型不支持
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public static int calculateValueLength(Object data) {
        if (data == null) {
            return 0;
        }
        
        TlvDataType dataType = TlvDataType.fromJavaType(data.getClass());
        if (dataType == null) {
            throw new IllegalArgumentException("Unsupported data type: " + data.getClass().getName());
        }
        
        return calculateValueLength(data, dataType);
    }
    
    /**
     * 计算指定类型数据编码后的字节长度
     * @param data 要计算长度的数据
     * @param dataType TLV数据类型
     * @return 编码后的字节长度
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public static int calculateValueLength(Object data, TlvDataType dataType) {
        if (data == null) {
            return 0;
        }
        
        switch (dataType) {
            case BYTE:
                return Byte.BYTES;
            case SHORT:
                return Short.BYTES;
            case INTEGER:
                return Integer.BYTES;
            case LONG:
                return Long.BYTES;
            case BIG_INTEGER:
                return ((java.math.BigInteger) data).toByteArray().length;
            case FLOAT:
                return Float.BYTES;
            case DOUBLE:
                return Double.BYTES;
            case BIG_DECIMAL:
                return ((java.math.BigDecimal) data).toString().getBytes(StandardCharsets.UTF_8).length;
            case BOOLEAN:
                return 1;
            case STRING_UTF8:
                return ((String) data).getBytes(StandardCharsets.UTF_8).length;
            case STRING_ASCII:
                return ((String) data).getBytes(StandardCharsets.US_ASCII).length;
            case BYTE_ARRAY:
                return ((byte[]) data).length;
            case MAP:
                return calculateMapLength((java.util.Map<?, ?>) data);
            case ARRAY:
                return calculateArrayLength((Iterable<?>) data);
            default:
                throw new IllegalArgumentException("Unsupported data type: " + dataType);
        }
    }
    
    /**
     * 计算Map编码后的长度
     * @param map Map对象
     * @return 编码后的字节长度
     */
    private static int calculateMapLength(java.util.Map<?, ?> map) {
        int length = Integer.BYTES; // 元素数量字段
        
        for (java.util.Map.Entry<?, ?> entry : map.entrySet()) {
            // Key的TLV长度
            length += calculateTlvLength(entry.getKey());
            // Value的TLV长度
            length += calculateTlvLength(entry.getValue());
        }
        
        return length;
    }
    
    /**
     * 计算数组编码后的长度
     * @param iterable Iterable对象
     * @return 编码后的字节长度
     */
    private static int calculateArrayLength(Iterable<?> iterable) {
        int length = Integer.BYTES; // 元素数量字段
        
        for (Object element : iterable) {
            length += calculateTlvLength(element);
        }
        
        return length;
    }
    
    /**
     * 计算单个对象的完整TLV长度（包括Type和Length字段）
     * @param data 数据对象
     * @return 完整TLV长度
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public static int calculateTlvLength(Object data) {
        return calculateTlvLength(data, 1, 4); // 默认Type 1字节，Length 4字节
    }
    
    /**
     * 计算单个对象的完整TLV长度（包括Type和Length字段）
     * @param data 数据对象
     * @param typeLength Type字段长度
     * @param lengthFieldSize Length字段长度
     * @return 完整TLV长度
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public static int calculateTlvLength(Object data, int typeLength, int lengthFieldSize) {
        int valueLength = calculateValueLength(data);
        return typeLength + lengthFieldSize + valueLength;
    }
    
    /**
     * 验证TLV数据格式是否正确
     * @param tlvData TLV格式的字节数组
     * @param typeLength Type字段长度
     * @param lengthFieldSize Length字段长度
     * @param byteOrder 字节序
     * @return 如果格式正确返回true，否则返回false
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public static boolean validateTlvFormat(byte[] tlvData, int typeLength, int lengthFieldSize, ByteOrder byteOrder) {
        if (tlvData == null || tlvData.length < typeLength + lengthFieldSize) {
            return false;
        }
        
        try {
            ByteBuffer buffer = ByteBuffer.wrap(tlvData);
            buffer.order(byteOrder);
            
            // 跳过Type字段
            buffer.position(typeLength);
            
            // 读取Length字段
            long length;
            switch (lengthFieldSize) {
                case 1:
                    length = buffer.get() & 0xFF;
                    break;
                case 2:
                    length = buffer.getShort() & 0xFFFF;
                    break;
                case 4:
                    length = buffer.getInt() & 0xFFFFFFFFL;
                    break;
                case 8:
                    length = buffer.getLong();
                    break;
                default:
                    return false;
            }
            
            // 验证长度是否匹配
            int expectedTotalLength = typeLength + lengthFieldSize + (int) length;
            return tlvData.length == expectedTotalLength && length >= 0;
            
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 从TLV数据中提取Type值
     * @param tlvData TLV格式的字节数组
     * @param typeLength Type字段长度
     * @param byteOrder 字节序
     * @return Type值
     * @throws IllegalArgumentException 如果数据格式不正确
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public static int extractType(byte[] tlvData, int typeLength, ByteOrder byteOrder) {
        if (tlvData == null || tlvData.length < typeLength) {
            throw new IllegalArgumentException("Invalid TLV data: insufficient length for type field");
        }
        
        ByteBuffer buffer = ByteBuffer.wrap(tlvData, 0, typeLength);
        buffer.order(byteOrder);
        
        switch (typeLength) {
            case 1:
                return buffer.get() & 0xFF;
            case 2:
                return buffer.getShort() & 0xFFFF;
            case 4:
                return buffer.getInt();
            default:
                throw new IllegalArgumentException("Unsupported type length: " + typeLength);
        }
    }
    
    /**
     * 从TLV数据中提取Length值
     * @param tlvData TLV格式的字节数组
     * @param typeLength Type字段长度
     * @param lengthFieldSize Length字段长度
     * @param byteOrder 字节序
     * @return Length值
     * @throws IllegalArgumentException 如果数据格式不正确
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public static long extractLength(byte[] tlvData, int typeLength, int lengthFieldSize, ByteOrder byteOrder) {
        if (tlvData == null || tlvData.length < typeLength + lengthFieldSize) {
            throw new IllegalArgumentException("Invalid TLV data: insufficient length for length field");
        }
        
        ByteBuffer buffer = ByteBuffer.wrap(tlvData, typeLength, lengthFieldSize);
        buffer.order(byteOrder);
        
        switch (lengthFieldSize) {
            case 1:
                return buffer.get() & 0xFF;
            case 2:
                return buffer.getShort() & 0xFFFF;
            case 4:
                return buffer.getInt() & 0xFFFFFFFFL;
            case 8:
                return buffer.getLong();
            default:
                throw new IllegalArgumentException("Unsupported length field size: " + lengthFieldSize);
        }
    }
    
    /**
     * 从TLV数据中提取Value部分
     * @param tlvData TLV格式的字节数组
     * @param typeLength Type字段长度
     * @param lengthFieldSize Length字段长度
     * @return Value部分的字节数组
     * @throws IllegalArgumentException 如果数据格式不正确
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public static byte[] extractValue(byte[] tlvData, int typeLength, int lengthFieldSize) {
        if (tlvData == null || tlvData.length < typeLength + lengthFieldSize) {
            throw new IllegalArgumentException("Invalid TLV data: insufficient length");
        }
        
        int valueOffset = typeLength + lengthFieldSize;
        int valueLength = tlvData.length - valueOffset;
        
        if (valueLength < 0) {
            throw new IllegalArgumentException("Invalid TLV data: negative value length");
        }
        
        byte[] value = new byte[valueLength];
        System.arraycopy(tlvData, valueOffset, value, 0, valueLength);
        return value;
    }
    
    /**
     * 创建一个简单的TLV编码器实例
     * @return TLV编码器实例
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public static TlvEncoder createDefaultEncoder() {
        return new TlvEncoder();
    }
    
    /**
     * 创建一个自定义配置的TLV编码器实例
     * @param byteOrder 字节序
     * @param charset 字符编码
     * @param typeLength Type字段长度
     * @param lengthFieldSize Length字段长度
     * @return TLV编码器实例
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public static TlvEncoder createEncoder(ByteOrder byteOrder, Charset charset, int typeLength, int lengthFieldSize) {
        return new TlvEncoder(byteOrder, charset, typeLength, lengthFieldSize);
    }
}
