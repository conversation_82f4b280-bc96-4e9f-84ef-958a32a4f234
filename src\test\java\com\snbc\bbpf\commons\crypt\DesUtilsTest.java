/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.crypt;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.nio.charset.StandardCharsets;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;


/**
 * DesUtils测试类
 *
 * <AUTHOR>
 * @module
 * @date 2023/11/26 21:35
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class DesUtilsTest {
    //待加密原文（长度不为8的倍数）
    String content;
    //待加密原文（长度为8的倍数）
    String contentEight;
    //密钥(密钥长度必须是 8 的倍数，最少 24 位)
    private String key;
    //密钥向量(长度必须等于8)
    private byte[] keyIv;

    @BeforeEach
    void setup() {
        content = "12345678啊哈哈<!@#$%^&*()_+=-<>?:asdZXC";
        contentEight = "12345678";
        key = "123456781234567812345678";
        keyIv = new byte[]{0, 0, 0, 0, 0, 0, 0, 0};
    }

    @Test
    @DisplayName("cbc模式密钥为空抛出异常")
    @Tags({
            @Tag("@id:83"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/11/27")
    })
    void testEncryptCbc_EmptyKey() {
        RuntimeException thrown = assertThrows(RuntimeException.class,
                () -> DesUtils.encryptCbc(new byte[0], keyIv, content.getBytes(StandardCharsets.UTF_8), Padding.PKCS5PADDING));
        assertEquals("cipher do final failure", thrown.getMessage());
    }

    @Test
    @DisplayName("cbc模式密钥长度错误抛出异常")
    @Tags({
            @Tag("@id:83"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/11/27")
    })
    void testEncryptCbc_WrongKey() {
        RuntimeException thrown = assertThrows(RuntimeException.class,
                () -> DesUtils.encryptCbc("123".getBytes(StandardCharsets.UTF_8), keyIv, content.getBytes(StandardCharsets.UTF_8), Padding.PKCS5PADDING));
        assertEquals("cipher do final failure", thrown.getMessage());
    }

    @Test
    @DisplayName("cbc模式向量为空抛出异常")
    @Tags({
            @Tag("@id:83"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/11/27")
    })
    void testEncryptCbc_EmptyKeyIv() {
        RuntimeException thrown = assertThrows(RuntimeException.class,
                () -> DesUtils.encryptCbc(key.getBytes(StandardCharsets.UTF_8), new byte[0], content.getBytes(StandardCharsets.UTF_8), Padding.PKCS5PADDING));
        assertEquals("cipher do final failure", thrown.getMessage());
    }

    @Test
    @DisplayName("cbc模式向量长度错误抛出异常")
    @Tags({
            @Tag("@id:83"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/11/27")
    })
    void testEncryptCbc_WrongKeyIv() {
        RuntimeException thrown = assertThrows(RuntimeException.class,
                () -> DesUtils.encryptCbc(key.getBytes(StandardCharsets.UTF_8), new byte[]{0, 0, 0}, content.getBytes(StandardCharsets.UTF_8), Padding.PKCS5PADDING));
        assertEquals("cipher do final failure", thrown.getMessage());
    }

    @Test
    @DisplayName("cbc模式文本为null抛出异常")
    @Tags({
            @Tag("@id:83"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/11/27")
    })
    void testEncryptCbc_NullData() {

        RuntimeException thrown = assertThrows(RuntimeException.class,
                () -> DesUtils.encryptCbc(key.getBytes(StandardCharsets.UTF_8), keyIv, null, Padding.PKCS5PADDING));
        assertEquals("cipher do final failure", thrown.getMessage());
    }

    @Test
    @DisplayName("cbc模式文本为empty抛出异常")
    @Tags({
            @Tag("@id:83"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/11/27")
    })
    void testEncryptCbc_EmptyData() {

        RuntimeException thrown = assertThrows(RuntimeException.class,
                () -> DesUtils.encryptCbc(key.getBytes(StandardCharsets.UTF_8), keyIv, new byte[0], Padding.PKCS5PADDING));
        assertEquals("cipher do final failure", thrown.getMessage());
    }

    @Test
    @DisplayName("cbc模式Padding为空抛出异常")
    @Tags({
            @Tag("@id:83"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/11/27")
    })
    void testEncryptCbc_NullPadding() {
        RuntimeException thrown = assertThrows(RuntimeException.class,
                () -> DesUtils.encryptCbc(key.getBytes(StandardCharsets.UTF_8), keyIv, content.getBytes(StandardCharsets.UTF_8), null));
        assertEquals("cipher do final failure", thrown.getMessage());
    }

    @Test
    @DisplayName("cbc模式PKCS5PADDING加密成功")
    @Tags({
            @Tag("@id:83"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/11/27")
    })
    void testEncryptCbc_PKCS5() {

        Assertions.assertDoesNotThrow(() -> DesUtils.encryptCbc(key.getBytes(StandardCharsets.UTF_8), keyIv, content.getBytes(StandardCharsets.UTF_8), Padding.PKCS5PADDING));
    }

    @Test
    @DisplayName("cbc模式PKCS7PADDING加密成功")
    @Tags({
            @Tag("@id:83"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/11/27")
    })
    void testEncryptCbc_PKCS7() {

        Assertions.assertDoesNotThrow(() -> DesUtils.encryptCbc(key.getBytes(StandardCharsets.UTF_8), keyIv, content.getBytes(StandardCharsets.UTF_8), Padding.PKCS7PADDING));
    }

    @Test
    @DisplayName("cbc模式PKCS5PADDING解密成功")
    @Tags({
            @Tag("@id:83"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/11/27")
    })
    void testDecryptCbc_PKCS5() {

        byte[] encryptResult = DesUtils.encryptCbc(key.getBytes(StandardCharsets.UTF_8), keyIv, content.getBytes(StandardCharsets.UTF_8), Padding.PKCS5PADDING);
        byte[] decryptResult = DesUtils.decryptCbc(key.getBytes(StandardCharsets.UTF_8), keyIv, encryptResult, Padding.PKCS5PADDING);
        Assertions.assertEquals(content, new String(decryptResult, StandardCharsets.UTF_8));
    }

    @Test
    @DisplayName("cbc模式PKCS7PADDING解密成功")
    @Tags({
            @Tag("@id:83"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/11/27")
    })
    void testDecryptCbc_PKCS7() {

        byte[] encryptResult = DesUtils.encryptCbc(key.getBytes(StandardCharsets.UTF_8), keyIv, content.getBytes(StandardCharsets.UTF_8), Padding.PKCS7PADDING);
        byte[] decryptResult = DesUtils.decryptCbc(key.getBytes(StandardCharsets.UTF_8), keyIv, encryptResult, Padding.PKCS7PADDING);
        Assertions.assertEquals(content, new String(decryptResult, StandardCharsets.UTF_8));
    }

    @Test
    @DisplayName("cbc模式NOPADDING,加密文本长度不为8的倍数，抛出异常")
    @Tags({
            @Tag("@id:83"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/11/27")
    })
    void testEncryptCbc_NOPADDING_WrongData() {

        RuntimeException thrown = assertThrows(RuntimeException.class,
                () -> DesUtils.encryptCbc(key.getBytes(StandardCharsets.UTF_8), keyIv, content.getBytes(StandardCharsets.UTF_8), Padding.NOPADDING));
        assertEquals("cipher do final failure", thrown.getMessage());
    }


    @Test
    @DisplayName("cbc模式NOPADDING加密成功")
    @Tags({
            @Tag("@id:83"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/11/27")
    })
    void testEncryptCbc_NOPADDING() {

        Assertions.assertDoesNotThrow(() -> DesUtils.encryptCbc(key.getBytes(StandardCharsets.UTF_8), keyIv, contentEight.getBytes(StandardCharsets.UTF_8), Padding.NOPADDING));
    }

    @Test
    @DisplayName("cbc模式NOPADDING解密成功")
    @Tags({
            @Tag("@id:83"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/11/27")
    })
    void testDecryptCbc_NOPADDING() {

        byte[] encryptResult = DesUtils.encryptCbc(key.getBytes(StandardCharsets.UTF_8), keyIv, contentEight.getBytes(StandardCharsets.UTF_8), Padding.NOPADDING);
        byte[] decryptResult = DesUtils.decryptCbc(key.getBytes(StandardCharsets.UTF_8), keyIv, encryptResult, Padding.NOPADDING);
        Assertions.assertEquals(contentEight, new String(decryptResult, StandardCharsets.UTF_8));
    }


    @Test
    @DisplayName("ecb模式密钥为空抛出异常")
    @Tags({
            @Tag("@id:83"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/11/27")
    })
    void testEncryptEcb_EmptyKey() {
        RuntimeException thrown = assertThrows(RuntimeException.class,
                () -> DesUtils.encryptEcb(new byte[0], content.getBytes(StandardCharsets.UTF_8), Padding.PKCS5PADDING));
        assertEquals("cipher do final failure", thrown.getMessage());
    }

    @Test
    @DisplayName("ecb模式密钥长度错误抛出异常")
    @Tags({
            @Tag("@id:83"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/11/27")
    })
    void testEncryptEcb_WrongKey() {
        RuntimeException thrown = assertThrows(RuntimeException.class,
                () -> DesUtils.encryptEcb("123".getBytes(StandardCharsets.UTF_8), content.getBytes(StandardCharsets.UTF_8), Padding.PKCS5PADDING));
        assertEquals("cipher do final failure", thrown.getMessage());
    }

    @Test
    @DisplayName("ecb模式文本为empty抛出异常")
    @Tags({
            @Tag("@id:83"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/11/27")
    })
    void testEncryptEcb_EmptyData() {

        RuntimeException thrown = assertThrows(RuntimeException.class,
                () -> DesUtils.encryptEcb(key.getBytes(StandardCharsets.UTF_8), new byte[0], Padding.PKCS5PADDING));
        assertEquals("cipher do final failure", thrown.getMessage());
    }

    @Test
    @DisplayName("ecb模式文本为null抛出异常")
    @Tags({
            @Tag("@id:83"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/11/27")
    })
    void testEncryptEcb_NullData() {

        RuntimeException thrown = assertThrows(RuntimeException.class,
                () -> DesUtils.encryptEcb(key.getBytes(StandardCharsets.UTF_8), null, Padding.PKCS5PADDING));
        assertEquals("cipher do final failure", thrown.getMessage());
    }

    @Test
    @DisplayName("ecb模式Padding为空抛出异常")
    @Tags({
            @Tag("@id:83"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/11/27")
    })
    void testEncryptEcb_NullPadding() {
        RuntimeException thrown = assertThrows(RuntimeException.class,
                () -> DesUtils.encryptEcb(key.getBytes(StandardCharsets.UTF_8), content.getBytes(StandardCharsets.UTF_8), null));
        assertEquals("cipher do final failure", thrown.getMessage());
    }

    @Test
    @DisplayName("ecb模式PKCS5PADDING加密成功")
    @Tags({
            @Tag("@id:83"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/11/27")
    })
    void testEncryptEcb_PKCS5() {

        Assertions.assertDoesNotThrow(() -> DesUtils.encryptEcb(key.getBytes(StandardCharsets.UTF_8), content.getBytes(StandardCharsets.UTF_8), Padding.PKCS5PADDING));
    }

    @Test
    @DisplayName("ecb模式PKCS5PADDING解密成功")
    @Tags({
            @Tag("@id:83"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/11/27")
    })
    void testDecryptEcb_PKCS5() {

        byte[] encryptResult = DesUtils.encryptEcb(key.getBytes(StandardCharsets.UTF_8), content.getBytes(StandardCharsets.UTF_8), Padding.PKCS5PADDING);
        byte[] decryptResult = DesUtils.decryptEcb(key.getBytes(StandardCharsets.UTF_8), encryptResult, Padding.PKCS5PADDING);
        Assertions.assertEquals(content, new String(decryptResult, StandardCharsets.UTF_8));
    }

    @Test
    @DisplayName("ecb模式NOPADDING,加密文本长度不为8的倍数，抛出异常")
    @Tags({
            @Tag("@id:83"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/11/27")
    })
    void testEncryptEcb_NOPADDING_WrongData() {

        RuntimeException thrown = assertThrows(RuntimeException.class,
                () -> DesUtils.encryptEcb(key.getBytes(StandardCharsets.UTF_8), content.getBytes(StandardCharsets.UTF_8), Padding.NOPADDING));
        assertEquals("cipher do final failure", thrown.getMessage());
    }


    @Test
    @DisplayName("ecb模式NOPADDING加密成功")
    @Tags({
            @Tag("@id:83"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/11/27")
    })
    void testEncryptEcb_NOPADDING() {

        Assertions.assertDoesNotThrow(() -> DesUtils.encryptEcb(key.getBytes(StandardCharsets.UTF_8), contentEight.getBytes(StandardCharsets.UTF_8), Padding.NOPADDING));
    }

    @Test
    @DisplayName("ecb模式NOPADDING解密成功")
    @Tags({
            @Tag("@id:83"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/11/27")
    })
    void testDecryptEcb_NOPADDING() {

        byte[] encryptResult = DesUtils.encryptEcb(key.getBytes(StandardCharsets.UTF_8), contentEight.getBytes(StandardCharsets.UTF_8), Padding.NOPADDING);
        byte[] decryptResult = DesUtils.decryptEcb(key.getBytes(StandardCharsets.UTF_8), encryptResult, Padding.NOPADDING);
        Assertions.assertEquals(contentEight, new String(decryptResult, StandardCharsets.UTF_8));
    }

}
