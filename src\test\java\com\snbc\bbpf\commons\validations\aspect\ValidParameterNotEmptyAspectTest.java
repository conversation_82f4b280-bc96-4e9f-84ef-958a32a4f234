/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.validations.aspect;

import com.snbc.bbpf.commons.objs.ObjectEmptyCheck;
import com.snbc.bbpf.commons.validations.annotation.ValidNotEmpty;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 非空验证注解方法参数的测试bean
 *
 * <AUTHOR>
 * @module
 * @date 2023/9/21 13:59
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class ValidParameterNotEmptyAspectTest {

    @Test
    @DisplayName("未添加注解，不进行参数非空校验")
    @Tags({
            @Tag("@id:45"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/21")
    })
    void testNoAnnotation_noAnnotation() {
        assertTrue(noAnnotation(""));
    }

    @Test
    @DisplayName("方法未添加注解，不进行参数非空校验")
    @Tags({
            @Tag("@id:45"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/21")
    })
    void testNoMethodAnno_noMethodAnno() {
        assertTrue(noMethodAnno(""));
    }

    @Test
    @DisplayName("方法参数未添加注解，不进行参数非空校验")
    @Tags({
            @Tag("@id:45"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/21")
    })
    void testNoParameterAnno_noParameterAnno() {
        assertTrue(noParameterAnno("", "2"));
    }

    @Test
    @DisplayName("方法参数添加注解，参数为空")
    @Tags({
            @Tag("@id:45"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/21")
    })
    void testAnnoString_annoStringEmpty() {
        IllegalArgumentException thrown1 = assertThrows(IllegalArgumentException.class,
                () -> annoString(""));
        assertEquals("method[annoString]parameter[arg0]can't be empty", thrown1.getMessage());
    }

    @Test
    @DisplayName("方法参数添加注解，参数不为空")
    @Tags({
            @Tag("@id:45"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/21")
    })
    void testAnnoString_annoString() {
        assertFalse(annoString("1"));
    }

    @Test
    @DisplayName("多个方法参数添加注解，某个参数为空")
    @Tags({
            @Tag("@id:45"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/21")
    })
    void testAnnoObj_annoObj() {
        Map<String, String> map = new HashMap<>();
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> annoObj("1", 1, map));
        assertEquals("成绩不能为空", thrown.getMessage());
    }

    @Test
    @DisplayName("方法参数添加注解，参数为空")
    @Tags({
            @Tag("@id:45"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/21")
    })
    void testAnnoBean_annoBean() {
        IllegalArgumentException thrown1 = assertThrows(IllegalArgumentException.class,
                () -> annoBean(null));
        assertEquals("method[annoBean]parameter[arg0]can't be empty", thrown1.getMessage());
    }

    public Boolean noAnnotation(String str) {
        return ObjectEmptyCheck.isEmpty(str);
    }

    public Boolean noMethodAnno(@ValidNotEmpty String str) {
        return ObjectEmptyCheck.isEmpty(str);
    }

    @ValidNotEmpty
    public Boolean noParameterAnno(String str1, String str2) {
        return ObjectEmptyCheck.isEmpty(str1) || ObjectEmptyCheck.isEmpty(str2);
    }

    @ValidNotEmpty
    public Boolean annoString(@ValidNotEmpty String str) {
        return ObjectEmptyCheck.isEmpty(str);
    }

    @ValidNotEmpty
    public Boolean annoObj(@ValidNotEmpty(message = "客户姓名不能为空") String name, @ValidNotEmpty(message = "年龄不能为空") Integer age, @ValidNotEmpty(message = "成绩不能为空") Map<String, String> grade) {
        return ObjectEmptyCheck.isEmpty(name) || ObjectEmptyCheck.isEmpty(age) || ObjectEmptyCheck.isEmpty(grade);
    }

    @ValidNotEmpty
    public Boolean annoBean(@ValidNotEmpty Family family) {
        return ObjectEmptyCheck.isEmpty(family);
    }

    class Family {
        @ValidNotEmpty
        private String family;

        public void setFamily(String family) {
            this.family = family;
        }
    }
}
