# BBPf Commons API 使用指南

## 目录

1. [简介](#简介)
2. [字符串处理](bbpf-commons-strings.md)
3. [日期时间处理](bbpf-commons-dates.md)
4. [集合处理](bbpf-commons-collects.md)
5. [文件处理](bbpf-commons-files.md)
6. [加密解密](bbpf-commons-crypt.md)
7. [随机数生成](bbpf-commons-randoms.md)
8. [对象处理](bbpf-commons-objs.md)
9. [反射处理](bbpf-commons-reflects.md)
10.[字节处理](bbpf-commons-bytes.md)
11. [JSON处理](bbpf-commons-jsons.md)
12. [XML处理](bbpf-commons-xmls.md)
13. [表达式处理](bbpf-commons-expressions.md)
14. [精确计算](bbpf-commons-calculations.md)
15. [校验处理](bbpf-commons-validations.md)
16. [FTP处理](bbpf-commons-ftps.md)
17. [系统工具](bbpf-commons-system.md)
18. [地理](bbpf-commons-geo.md)
 

## 简介 

BBPf Commons是新北洋基础业务平台提供的通用工具类库，涵盖了日常Java开发中的常见场景。本文档提供了完整的API参考和使用示例。

Maven 依赖:
```xml
<dependency>
    <groupId>com.snbc.bbpf</groupId>
    <artifactId>bbpf-commons</artifactId>
    <version>1.6.0</version>
</dependency>
```

BBPf Commons API提供了丰富的功能和工具类，涵盖了日常Java开发中的常见场景。本文档提供了完整的API参考和使用示例，希望能够帮助您更好地理解和使用BBPf Commons API。