package com.snbc.bbpf.commons.collects;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * 类描述:    .
 *
 * <AUTHOR>
 * 创建时间:  [2023/11/7 09:45]
 */
class CollectsUtilsTest {


    @DisplayName("测试对象并集")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping"),
            @Tag("@date:2023/11/7")
    })
    @Test
    void testIntersection() {
        List<Integer> list1 = new ArrayList<>(Arrays.asList(1, 2, 3, 4, 5));
        List<Integer> list2 = new ArrayList<>(Arrays.asList(3, 4, 5, 6, 7));
        List<Integer> list3 = new ArrayList<>(Arrays.asList(5, 6, 7, 8, 9));
        List<Integer> intersection = CollectsUtils.intersection(list1, list2, list3);
        assertEquals(1, intersection.size());
        assertEquals(5, (int) intersection.get(0));
    }

    @DisplayName("测试对象并集null")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping"),
            @Tag("@date:2023/11/7")
    })
    @Test
    void testIntersection_null() {
        List<Integer> list1 = new ArrayList<>(Arrays.asList(1, 2, 3, 4, 5));
        List<Integer> list2 = new ArrayList<>(Arrays.asList(3, 4, 5, 6, 7));
        List<Integer> list3 = new ArrayList<>(Arrays.asList(5, 6, 7, 8, 9));
        List<Integer> intersection = CollectsUtils.intersection(list1, null, null, list2, list3);
        assertEquals(1, intersection.size());
        assertEquals(5, (int) intersection.get(0));
    }


    @DisplayName("测试对象并集null")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping"),
            @Tag("@date:2023/11/7")
    })
    @Test
    void testIntersection_null2() {
        List<Integer> intersection = CollectsUtils.intersection();
        assertEquals(0, intersection.size());
    }

    @DisplayName("测试对象并集")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping"),
            @Tag("@date:2023/11/7")
    })
    @Test
    void testUnion_null() {
        List<Integer> intersection = CollectsUtils.union();
        assertEquals(0, intersection.size());
    }

    @DisplayName("测试对象并集")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping"),
            @Tag("@date:2023/11/7")
    })
    @Test
    void testUnion_null2() {
        List<Integer> intersection = CollectsUtils.union(null);
        assertEquals(0, intersection.size());
    }

    @DisplayName("测试对象并集")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping"),
            @Tag("@date:2023/11/7")
    })
    @Test
    void testUnion() {
        List<Integer> list1 = new ArrayList<>(Arrays.asList(1, 2, 3, 4, 5));
        List<Integer> list2 = new ArrayList<>(Arrays.asList(3, 4, 5, 6, 7));
        List<Integer> list3 = new ArrayList<>(Arrays.asList(5, 6, 7, 8, 9));

        List<Integer> union = CollectsUtils.union(list1, list2, list3);
        assertEquals(9, union.size());

    }

    @DisplayName("测试对象删除")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping"),
            @Tag("@date:2023/11/7")
    })
    @Test
    void testRemoveElements_null() {

        List<Integer> union = CollectsUtils.removeElements(null, null);
        assertEquals(Collections.emptyList(), union);
    }

    @DisplayName("测试对象删除")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping"),
            @Tag("@date:2023/11/7")
    })
    @Test
    void testRemoveElements_null2() {
        List<Integer> union = CollectsUtils.removeElements(new ArrayList<>(), null);
        assertEquals(new ArrayList<>(), union);
    }

    @DisplayName("测试对象删除")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping"),
            @Tag("@date:2023/11/7")
    })
    @Test
    void testRemoveElements() {
        List<Integer> listA = new ArrayList<>(Arrays.asList(1, 2, 3, 4, 5));
        List<Integer> listB = new ArrayList<>(Arrays.asList(3, 4, 5));

        List<Integer> union = CollectsUtils.removeElements(listA, listB);
        assertEquals(2, union.size());
    }

    @DisplayName("测试去除重复元素")
    @Tags({
            @Tag("@id:107"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2024/05/27")
    })
    @Test
    void testDistinct() {
        List<Integer> source = new ArrayList<>(Arrays.asList(1, 2, 2, 4, 4, 5));
        List<Integer> expect = new ArrayList<>(Arrays.asList(1, 2, 4, 5));

        List<Integer> result = CollectsUtils.distinct(source);
        Assertions.assertIterableEquals(expect, result);
    }

    @DisplayName("测试去除重复元素-列表为null")
    @Tags({
            @Tag("@id:107"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2024/05/27")
    })
    @Test
    void testDistinctWithNull() {
        List<Integer> source = null;
        List<Integer> result = CollectsUtils.distinct(source);
        Assertions.assertTrue(result.isEmpty());
    }

    @DisplayName("测试去除重复元素-列表中有null")
    @Tags({
            @Tag("@id:107"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2024/05/27")
    })
    @Test
    void testDistinctWithNulls() {
        List<Integer> source = new ArrayList<>(Arrays.asList(1, 2, null, 2, 4, 4, null, 5));
        List<Integer> expect = new ArrayList<>(Arrays.asList(1, 2, null, 4, 5));
        List<Integer> result = CollectsUtils.distinct(source);
        Assertions.assertIterableEquals(expect, result);
    }
}

class Person {
    private String name;
    private int age;

    public Person(String name, int age) {
        this.name = name;
        this.age = age;
    }

    // 省略构造方法和其他方法

    // Getter和Setter方法

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }
}
