/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.xmls;

import com.snbc.bbpf.commons.files.FileUtils;
import com.snbc.bbpf.commons.strings.StringEmptyCheck;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.xml.sax.ErrorHandler;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;
import org.xml.sax.SAXParseException;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.File;
import java.io.StringReader;
import java.nio.charset.StandardCharsets;


/**
 * 基于DTD验证XML 处理类
 * <p>功能列表</p>
 * <p>1.基于DTD验证XML是否符合要求</p>
 * <p>2.DTD可以是文件也可以是DTD文件的字符串</p>
 * <p>3.对于无DTD的xml进行格式验证。要符合xml的基本格式</p>
 *
 * <p></p>
 * <AUTHOR>
 * @module XML模块
 * @date 2023/10/14 20:30
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class XmlValidateUtils {

	private static final String PARAMETER_EMPTY = "Input parameter is empty";
	private static final String PARAMETER_INVALID = "XML is invalid";
    private XmlValidateUtils() {
        throw new IllegalStateException("Utility class");
    }


    /**
     * 对xml文件、传入DTD文件内容后并验证是否符合格式要求
     * @param xmlFilePath 待验证的xml文件
     * @param dtdFileContent DTD文件内容
     * @return 验证结果
	 * @throws Exception 如果参数异常或验证异常
     * @since 1.3.0
     * <AUTHOR>
     * @date 2023/10/14
     */
	public static boolean validateDTD(String xmlFilePath, String dtdFileContent) {
		// 检查参数
		if (StringEmptyCheck.isEmpty(xmlFilePath) || StringEmptyCheck.isEmpty(dtdFileContent)) {
			throw new IllegalArgumentException(PARAMETER_EMPTY);
		}

        try {
			SAXReader saxReader = new SAXReader();
			saxReader.setValidation(false);
            Document document = saxReader.read(new File(xmlFilePath));
            Element rootElement = document.getRootElement();
            String root = rootElement.getName();

			String newDocType = String.format("<!DOCTYPE %s [%s]>", root, dtdFileContent);

			String xmlContent = FileUtils.readString(xmlFilePath, StandardCharsets.UTF_8);
			if (xmlContent.contains("<!DOCTYPE")) {
				int begin = xmlContent.indexOf("<!DOCTYPE");
				int end = 0;
				if (xmlContent.contains("<!ELEMENT")) {
					end = xmlContent.indexOf(">", xmlContent.indexOf("]", begin + 1) + 1);
				} else {
					end = xmlContent.indexOf(">", begin + 1);
				}
				if (end <= begin) {
					throw new IllegalArgumentException(PARAMETER_INVALID);
				}
				String strRep = xmlContent.substring(begin, end + 1);
				xmlContent = xmlContent.replace(strRep, newDocType);

			} else {
				int begin = xmlContent.indexOf("<?xml ");
				int end = xmlContent.indexOf(">", begin + 1);

				if (end <= begin) {
					throw new IllegalArgumentException(PARAMETER_INVALID);
				}
				String strRep = xmlContent.substring(begin, end + 1);
				xmlContent = xmlContent.replace(strRep, strRep + "\r\n" + newDocType);
			}

            DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
            dbf.setValidating(true);
			dbf.setExpandEntityReferences(false);
			dbf.setFeature("http://xml.org/sax/features/external-general-entities", false);
			dbf.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
			dbf.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd", false);
            DocumentBuilder db = dbf.newDocumentBuilder();
			db.setEntityResolver((publicId, systemId) -> new InputSource(new StringReader(dtdFileContent)));
			db.setErrorHandler(
					new ErrorHandler() {
						@Override
						public void error(SAXParseException exception) throws SAXException {
             throw exception;
						}
						@Override
						public void fatalError(SAXParseException exception) throws SAXException {
             throw exception;
						}
						@Override
						public void warning(SAXParseException exception) throws SAXException {
             throw exception;
						}
					});
			db.parse(new InputSource(new StringReader(xmlContent)));
        } catch (Exception e) {
			// 在此返回false表示验证失败
			return false;
        }

		return true;
	}

	/**
     * 对xml文件、传入DTD文件后并验证是否符合格式要求
     * @param xmlFilePath 待验证的xml文件
     * @param dtdFilePath DTD文件
     * @return 验证结果
	 * @throws Exception 如果参数异常或验证异常
	 * @since 1.3.0
	 * <AUTHOR>
	 * @date 2023/10/14
	 */
	public static boolean validate(String xmlFilePath, String dtdFilePath) {
		// 检查参数
		if (StringEmptyCheck.isEmpty(xmlFilePath) || StringEmptyCheck.isEmpty(dtdFilePath)) {
			throw new IllegalArgumentException(PARAMETER_EMPTY);
		}

		String dtdFileContent = FileUtils.readString(dtdFilePath,StandardCharsets.UTF_8);
		return validateDTD(xmlFilePath, dtdFileContent);
	}

	/**
     * 对xml文件验证是否符合格式要求
     * @param xmlFilePath 待验证的xml文件
     * @return 验证结果
	 * @throws Exception 如果参数异常或验证异常
	 * @since 1.3.0
	 * <AUTHOR>
	 * @date 2023/10/14
	 */
	public static boolean validate(String xmlFilePath) {
		// 检查参数
		if (StringEmptyCheck.isEmpty(xmlFilePath)) {
			throw new IllegalArgumentException(PARAMETER_EMPTY);
		}

		try {
			SAXReader saxReader = new SAXReader();
			saxReader.setValidation(false);
			saxReader.read(new File(xmlFilePath));

		} catch (Exception e) {
			// 在此返回false表示验证失败
			return false;
		}

		return true;
	}


}
