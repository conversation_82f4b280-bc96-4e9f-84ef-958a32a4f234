/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.validations.validator;

import com.snbc.bbpf.commons.strings.StringEmptyCheck;
import com.snbc.bbpf.commons.validations.ValidException;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

/**
 * URL验证器实现了StringValidator接口，用于验证URL地址的格式是否正确。
 *
 * <AUTHOR>
 * @date 2023/7/16
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class UrlValidator implements IStringValidator {
    public static final Pattern URL_PATTERN = Pattern.compile("^(http|https)://([\\w-]+\\.)+[\\w-]+(/[\\w-./?%&=]*)?$");

    /**
     * 验证URL地址格式
     *
     * @param url     URL地址字符串
     * @param message 错误消息
     * @throws ValidException 如果URL地址为空或格式错误
     * <AUTHOR>
     * @date 2023/7/16
     * @since 1.1.0
     */
    @Override
    public void validate(String url, String message) throws IllegalArgumentException {
        if (StringUtils.isEmpty(url)) {
            throw new IllegalArgumentException("URL address is empty or null");
        }
        // 验证URL地址格式
        if (!URL_PATTERN.matcher(url).matches()) {
            throw new IllegalArgumentException(!StringEmptyCheck.isEmpty(message) ? message : "Incorrect URL address format");
        }
    }
}