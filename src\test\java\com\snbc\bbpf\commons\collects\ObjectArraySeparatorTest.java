package com.snbc.bbpf.commons.collects;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertIterableEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * 将对象集合和带有指定分隔符的字符串进行互相转换 单元测试类
 *
 * <AUTHOR>
 * @module 集合处理
 * @date 2023/4/25 19:01
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class ObjectArraySeparatorTest {

    @Test
    @DisplayName("将a和b字符数组使用逗号分隔拼接成字符串")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_joinArrayHasTwoString() {
        assertEquals("a,b", ObjectArraySeparator.join(new String[]{"a","b"}, ","));
    }
    @Test
    @DisplayName("将a字符数组使用逗号分隔拼接成字符串")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_joinArrayHasOneString() {
        assertEquals("a", ObjectArraySeparator.join(new String[]{"a"}, ","));
    }
    @Test
    @DisplayName("将Empty字符串数组使用逗号分隔拼接成字符串返回空串")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_joinArrayHasEmptyString() {
        assertEquals("", ObjectArraySeparator.join(new String[]{}, ","));
    }

    @Test
    @DisplayName("将NULL字符串数组使用逗号分隔拼接成字符串返回异常")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_joinArrayHasNullString() {
        assertThrows(IllegalArgumentException.class,
                () -> ObjectArraySeparator.join((String[]) null, ","),
                ObjectArraySeparator.CANNOT_BE_NULL);
    }
    @Test
    @DisplayName("将包含NULL字符串数组使用逗号分隔拼接成字符串返回异常")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_joinChildHaveNullString() {
        String[] strs = new String[]{"a",null};
        assertThrows(IllegalArgumentException.class,
                () -> ObjectArraySeparator.join(strs, ","),
                ObjectArraySeparator.CANNOT_BE_NULL);
    }
    @Test
    @DisplayName("将字符串数组使用NULL分隔拼接成字符串返回异常")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_joinNullSpearator() {
        assertThrows(IllegalArgumentException.class,
                () -> ObjectArraySeparator.join(new String[]{"a","b"}, null),
                ObjectArraySeparator.CANNOT_BE_NULL);
    }
    @Test
    @DisplayName("将字符串数组使用空串分隔拼接成字符串可正常返回")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_joinEmptyStringSpearator() {
        assertEquals("ab", ObjectArraySeparator.join(new String[]{"a","b"}, ""));
    }
    @Test
    @DisplayName("将带逗号的字符串分成字符串数组")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testSplitToString_shouldSplitArray() {
        assertArrayEquals(new String[]{"a","b"},
                ObjectArraySeparator.splitToString("a,b", ","));
    }

    @Test
    @DisplayName("将只带逗号的字符串分成Empty字符串数组")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testSplitToString_shouldSplitHaveEmptyArray() {
        assertArrayEquals(new String[]{},
                ObjectArraySeparator.splitToString(",", ","));
    }
    @Test
    @DisplayName("将NULL字符串时会参数异常")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testSplitToString_paramsIsNullStringShouldException() {
        assertThrows(IllegalArgumentException.class,
                () -> ObjectArraySeparator.splitToString(null, ","),
                ObjectArraySeparator.CANNOT_BE_NULL);
    }
    @Test
    @DisplayName("将NULL分隔符时会参数异常")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testSplitToString_paramsIsNullSpearatorShouldException() {
        assertThrows(IllegalArgumentException.class,
                () -> ObjectArraySeparator.splitToString("a,b", null),
                ObjectArraySeparator.CANNOT_BE_NULL);
    }
    @Test
    @DisplayName("将带分隔符为字符串最后一个字符时时会参数异常")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testSplitToString_paramsLastIsSpearatorShouldException() {
        assertArrayEquals(new String[]{"a"},
                ObjectArraySeparator.splitToString("a,", ","));
    }
    @Test
    @DisplayName("将list中2个字符串用逗号分隔")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin__joinListHasTwoString() {
        assertEquals("a,b",
                ObjectArraySeparator.join(Arrays.asList("a","b"), ","));
    }
    @Test
    @DisplayName("将只有一个元素list可以正常转换为字符串")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_joinOneSizeListHasOneString() {
        assertEquals("a",
                ObjectArraySeparator.join(Arrays.asList("a"), ","));
    }
    @Test
    @DisplayName("将list中1个Empty字符串用逗号分隔")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_joinListHasEmptyString() {
        assertEquals("a,",
                ObjectArraySeparator.join(Arrays.asList("a",""), ","));
    }

    @Test
    @DisplayName("将list是NULL抛出异常")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_joinListHasNullString() {
        assertThrows(IllegalArgumentException.class,
                () -> ObjectArraySeparator.join((List<String>) null, ","),
                ObjectArraySeparator.CANNOT_BE_NULL);
    }
    @Test
    @DisplayName("将list中1个NULL字符串用逗号分隔会出异常")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_testJoin_joinChildHaveNullString() {
        assertThrows(IllegalArgumentException.class,
                () -> ObjectArraySeparator.join(Arrays.asList("a",null), ","),
                ObjectArraySeparator.CANNOT_BE_NULL);
    }

    @Test
    @DisplayName("将list用null分隔符进行分隔会出异常")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_testJoin_joinSparatorIsNullString() {
        assertThrows(IllegalArgumentException.class,
                () -> ObjectArraySeparator.join(Arrays.asList("a","b"), null),
                ObjectArraySeparator.CANNOT_BE_NULL);
    }

    @Test
    @DisplayName("将list中字符串用Empty分隔")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_joinSparatorIsEmptyString() {
        assertEquals("ab",
                ObjectArraySeparator.join(Arrays.asList("a","b"), ""));
    }
    @Test
    @DisplayName("将带逗号的字符串分隔成list")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testSplitToList_shouldSparatorList() {
        assertIterableEquals(Arrays.asList("a","b"),
                ObjectArraySeparator.splitToList("a,b", ","));
    }

    @Test
    @DisplayName("将不带逗号的字符串分隔成list")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testSplitToList_shouldNotSparatorList() {
        assertIterableEquals(Arrays.asList("ab"),
                ObjectArraySeparator.splitToList("ab", ","));
    }

    @Test
    @DisplayName("将带逗号的字符串有一个是Empty字符串能正常被分隔成list")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testSplitToList_haveOneStringIsEmptySparatorList() {
        assertIterableEquals(Arrays.asList("a","b"),
                ObjectArraySeparator.splitToList("a,b,", ","));
    }
    @Test
    @DisplayName("将NULL字符串分隔成list时抛出异常")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testSplitToList_NullStringShouldExpection() {
        assertThrows(IllegalArgumentException.class,
                () -> ObjectArraySeparator.splitToList(null, ","),
                ObjectArraySeparator.CANNOT_BE_NULL);
    }
    @Test
    @DisplayName("将字符串使用null分隔符分隔成list时抛出异常")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testSplitToList_NullSeparatorShouldExpection() {
        assertThrows(IllegalArgumentException.class,
                () -> ObjectArraySeparator.splitToList("a,b", null),
                ObjectArraySeparator.CANNOT_BE_NULL);
    }
    @Test
    @DisplayName("将Empty字符串分隔成list时按照字符变成字符串数组")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testSplitToList_EmptySeparatorShouldSplitOneCharList() {
        assertIterableEquals(Arrays.asList("a",",","b"),
                ObjectArraySeparator.splitToList("a,b", ""));
    }
    @Test
    @DisplayName("将map转换为带逗号分隔符的字符串,期中kv分隔符使用等号")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_shouldMapToHasSparatorStringByKVSparatorIsEqual() {
        assertEquals("a=1,b=2",
                ObjectArraySeparator.join(new HashMap<String, String>() {{
                    put("a", "1");
                    put("b", "2");
        }}, ","));
    }
    @Test
    @DisplayName("将只有一个元素的map转换为带逗号分隔符的字符串,期中kv分隔符使用等号")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_shouldOneSizeMapByKVSparatorIsEqual() {
        assertEquals("a=1",
                ObjectArraySeparator.join(new HashMap<String, String>() {{
                    put("a", "1");
                }}, ","));
    }

    @Test
    @DisplayName("将包括Empty的Value的map转换为带逗号分隔符的字符串,期中kv分隔符使用等号")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_hasEmptyValueMapToHasSparatorStringByKVSparatorIsEqual() {
        assertEquals("a=1,b=",
                ObjectArraySeparator.join(new HashMap<String, String>() {{
                    put("a", "1");
                    put("b", "");
                }}, ","));
    }

    @Test
    @DisplayName("将包括Empty的Key的map转换为带逗号分隔符的字符串,期中kv分隔符使用等号，为Empty的key排在最前面")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_hasEmptyKeyMapToHasSparatorStringByKVSparatorIsEqual() {
        assertEquals("=2,a=1",
                ObjectArraySeparator.join(new HashMap<String, String>() {{
                    put("a", "1");
                    put("", "2");
                }}, ","));
    }

    @Test
    @DisplayName("将包括null的Key的map转换为带逗号分隔符的字符串抛出异常")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_hasNullKeyMapShouldException() {
        assertThrows(IllegalArgumentException.class,
                () -> ObjectArraySeparator.join(new HashMap<String, String>() {{
                                                    put("a", "1");
                                                    put(null, "2");
                                                }}, ","),
                ObjectArraySeparator.CANNOT_BE_NULL);
    }

    @Test
    @DisplayName("将包括null的value的map转换为带逗号分隔符的字符串时抛出异常")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_hasNullValueMapShouldException() {
        assertThrows(IllegalArgumentException.class,
                () -> ObjectArraySeparator.join(new HashMap<String, String>() {{
                    put("a", "1");
                    put("b", null);
                }}, ","),
                ObjectArraySeparator.CANNOT_BE_NULL);
    }
    @Test
    @DisplayName("将Null的map转换为带逗号分隔符的字符串时抛出异常")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_hasNullMapShouldException() {
        assertThrows(IllegalArgumentException.class,
                () -> ObjectArraySeparator.join((Map<String, String>) null, ","),
                ObjectArraySeparator.CANNOT_BE_NULL);
    }
    @Test
    @DisplayName("将map转换为带NULL分隔符的字符串,期中kv分隔符使用等号")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_hasNullSparatorShouldException() {
        assertThrows(IllegalArgumentException.class,
                () -> ObjectArraySeparator.join(new HashMap<String, String>() {{
                    put("a", "1");
                    put("b", "2");
                }}, null),
                ObjectArraySeparator.CANNOT_BE_NULL);
    }
    @Test
    @DisplayName("将map转换为带Empty分隔符的字符串,期中kv分隔符使用等号")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_hasEmptySparatorShouldJoinString() {
        assertEquals("a=1b=2",
                ObjectArraySeparator.join(new HashMap<String, String>() {{
                    put("a", "1");
                    put("b", "2");
                }}, ""));
    }
    @Test
    @DisplayName("将map转换为带Empty分隔符的字符串,期中kv分隔符使用等号")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_hasSparatorShouldJoinString() {
        assertEquals("a=1|b=2",
                ObjectArraySeparator.join(new HashMap<String, String>() {{
                    put("a", "1");
                    put("b", "2");
                }}, "|"));
    }
    @Test
    @DisplayName("带字符串的Map能正常转换为带KV的分隔符字符串")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_haveKvSpearatorShouldJoinString() {
        assertEquals("a:1,b:2", ObjectArraySeparator.join(new HashMap<String, String>() {{
            put("a", "1");
            put("b", "2");
        }}, ",", ":"));
    }
    @Test
    @DisplayName("带字符串的Map能正常转换为带KV的分隔符字符串")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_haveKvSpearatorAndNullMapShouldException() {
        assertThrows(IllegalArgumentException.class,
                () -> ObjectArraySeparator.join((Map<String,String>)null, ",", ":"),
                ObjectArraySeparator.CANNOT_BE_NULL);
    }

    @Test
    @DisplayName("带字符串的Map转换为带Empty的KV的分隔符字符串抛出异常")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_haveNullKvSpearatorShouldException() {
        assertThrows(IllegalArgumentException.class,
                () -> ObjectArraySeparator.join(new HashMap<String, String>() {{
                            put("a", "1");
                            put("b", "2");
                        }}, ",", null),
                ObjectArraySeparator.CANNOT_BE_NULL);
    }
    @Test
    @DisplayName("带字符串的Map能正常转换为带KV的分隔符字符串")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_haveNullItemSpearatorShouldException() {
        assertThrows(IllegalArgumentException.class,
                () -> ObjectArraySeparator.join(new HashMap<String, String>() {{
                    put("a", "1");
                    put("b", "2");
                }}, null, ":"),
                ObjectArraySeparator.CANNOT_BE_NULL);
    }
    @Test
    @DisplayName("带字符串的Map能正常转换为带KV的分隔符字符串")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_haveEmptyItemSpearatorShouldJoinString() {
        assertEquals("a:1b:2", ObjectArraySeparator.join(new HashMap<String, String>() {{
            put("a", "1");
            put("b", "2");
        }}, "", ":"));
    }
    @Test
    @DisplayName("带字符串的Map能正常转换为带KV的分隔符字符串")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_haveEmptyKVSpearatorShouldJoinString() {
        assertEquals("a1,b2", ObjectArraySeparator.join(new HashMap<String, String>() {{
            put("a", "1");
            put("b", "2");
        }}, ",", ""));
    }
    @Test
    @DisplayName("带Empty的Key的字符串的Map能正常转换为带KV的分隔符字符串")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_haveEmptyItemKeyShouldJoinString() {
        assertEquals(":1,b:2", ObjectArraySeparator.join(new HashMap<String, String>() {{
            put("", "1");
            put("b", "2");
        }}, ",", ":"));
    }
    @Test
    @DisplayName("带Empty字符串的Value的Map能正常转换为带KV的分隔符字符串")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_haveEmptyItemValueShouldJoinString() {
        assertEquals("a:,b:2", ObjectArraySeparator.join(new HashMap<String, String>() {{
            put("a", "");
            put("b", "2");
        }}, ",", ":"));
    }
    @Test
    @DisplayName("将只有一个元素的Map能正常转换为带KV的分隔符字符串")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_notItemSeparatorValueShouldJoinString() {
        assertEquals("a:1", ObjectArraySeparator.join(new HashMap<String, String>() {{
            put("a", "1");
        }}, ",", ":"));
    }
    @Test
    @DisplayName("带Null的Key的Map能正常转换为带KV的分隔符字符串")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_haveNullItemKeyShouldException() {
        assertThrows(IllegalArgumentException.class,
                () -> ObjectArraySeparator.join(new HashMap<String, String>() {{
                    put(null, "1");
                    put("b", "2");
                }}, ",", ":"),
                ObjectArraySeparator.CANNOT_BE_NULL);
    }
    @Test
    @DisplayName("带null的value的字符串的Map能正常转换为带KV的分隔符字符串")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_haveNullItemValueShouldException() {
        assertThrows(IllegalArgumentException.class,
                () -> ObjectArraySeparator.join(new HashMap<String, String>() {{
                    put("a", null);
                    put("b", "2");
                }}, ",", ":"),
                ObjectArraySeparator.CANNOT_BE_NULL);
    }
    @Test
    @DisplayName("带分隔符的字符串可以被正常转换为map")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testSplitToMap_shouldSplitToMap() {
        assertEquals(new HashMap<String, String>() {{
            put("a", "1");
            put("b", "2");
        }},ObjectArraySeparator.splitToMap("a:1,b:2", ",",":"));
    }
    @Test
    @DisplayName("带分隔符的字符串可以默认kv分隔符为等号被正常转换为map")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testSplitToMap_shouldSplitToMapKVSeparatorIsEqual() {
        assertEquals(new HashMap<String, String>() {{
            put("a", "1");
            put("b", "2");
        }},ObjectArraySeparator.splitToMap("a=1,b=2", ","));
    }
    @Test
    @DisplayName("将不带分隔符的字符串可以被正常转换为map")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testSplitToMap_notItemSparatorShouldSplitToMap() {
        assertEquals(new HashMap<String, String>() {{
            put("a", "1");
        }},ObjectArraySeparator.splitToMap("a:1", ",",":"));
    }
    @Test
    @DisplayName("将不带分隔符的字符串默认kv分隔符为等号可以被正常转换为map")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testSplitToMap_notItemSparatorShouldSplitToMapKVSeparatorIsEqual() {
        assertEquals(new HashMap<String, String>() {{
            put("a", "1");
        }},ObjectArraySeparator.splitToMap("a=1", ","));
    }
    @Test
    @DisplayName("NULL字符串转换为带KV的分隔符Map时出现异常")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_haveKvSpearatorAndNullMapSplitShouldException() {
        assertThrows(IllegalArgumentException.class,
                () -> ObjectArraySeparator.splitToMap((String)null, ",", ":"),
                ObjectArraySeparator.CANNOT_BE_NULL);
    }

    @Test
    @DisplayName("NULL字符串转换为Map时默认kv分隔符为等号出现异常")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_haveKvSpearatorAndNullMapSplitKVSeparatorIsEqualShouldException() {
        assertThrows(IllegalArgumentException.class,
                () -> ObjectArraySeparator.splitToMap((String)null, ","),
                ObjectArraySeparator.CANNOT_BE_NULL);
    }

    @Test
    @DisplayName("带字符串转换为带Empty的KV的分隔符Map时抛出异常")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_haveNullKvSpearatorSplitShouldException() {
        assertThrows(IllegalArgumentException.class,
                () -> ObjectArraySeparator.splitToMap("a:1,b:2", ",", null),
                ObjectArraySeparator.CANNOT_BE_NULL);
    }

    @Test
    @DisplayName("带字符串转换为带KV的分隔符Map其中itemKey为Empty时抛出异常")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_haveKVSparatorAndNullItemSpearatorShouldException() {
        assertThrows(IllegalArgumentException.class,
                () -> ObjectArraySeparator.splitToMap("a:1,b:2", null, ":"),
                ObjectArraySeparator.CANNOT_BE_NULL);
    }
    @Test
    @DisplayName("带字符串转换为带默认kv分隔符为等号的Map其中itemKey为Empty时抛出异常")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_haveKVSparatorAndNullItemSpearatorKVSeparatorIsEqualShouldException() {
        assertThrows(IllegalArgumentException.class,
                () -> ObjectArraySeparator.splitToMap("a:1,b:2", null ),
                ObjectArraySeparator.CANNOT_BE_NULL);
    }
    @Test
    @DisplayName("将带Empty的Key字符串转换为带KV的分隔符Map")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_haveEmptyItemKeyShouldSplit() {
        assertEquals(new HashMap<String, String>() {{
            put("", "1");
            put("b", "2");
        }},ObjectArraySeparator.splitToMap(":1,b:2", ",",":"));
    }
    @Test
    @DisplayName("将带Empty的Key字符串转换为带默认kv分隔符为等号的Map")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_haveEmptyItemKeyKVSeparatorIsEqualShouldSplit() {
        assertEquals(new HashMap<String, String>() {{
            put("", "1");
            put("b", "2");
        }},ObjectArraySeparator.splitToMap("=1,b=2", ","));
    }
    @Test
    @DisplayName("将不带item分隔符的字符串转换为带KV的分隔符Map")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_notItemKeyShouldSplit() {
        assertEquals(new HashMap<String, String>() {{
            put("a", "1");
        }},ObjectArraySeparator.splitToMap("a:1", ",",":"));
    }
    @Test
    @DisplayName("将不带item分隔符的字符串转换为默认kv分隔符为等号的Map")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_notItemKeyKVSeparatorIsEqualShouldSplit() {
        assertEquals(new HashMap<String, String>() {{
            put("a", "1");
        }},ObjectArraySeparator.splitToMap("a=1", ","));
    }
    @Test
    @DisplayName("将没有分隔符的字符串转换为带KV的分隔符Map时抛出数据越界异常")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_notSparatorStringShouldException() {
        assertThrows(ArrayIndexOutOfBoundsException.class,
                () -> ObjectArraySeparator.splitToMap("aaa", ",", ":"),
                ObjectArraySeparator.CANNOT_BE_NULL);
    }
    @Test
    @DisplayName("将没有分隔符的字符串转换为带默认kv分隔符为等号Map时抛出数据越界异常")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_notSparatorStringKVSeparatorIsEqualShouldException() {
        assertThrows(ArrayIndexOutOfBoundsException.class,
                () -> ObjectArraySeparator.splitToMap("aaa", ","),
                ObjectArraySeparator.CANNOT_BE_NULL);
    }
    @Test
    @DisplayName("带Empty的value字符串转换为带KV的分隔符Map时抛出数组越界异常")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_haveEmptyItemValueShouldException() {
        assertThrows(ArrayIndexOutOfBoundsException.class,
                () -> ObjectArraySeparator.splitToMap("a:,b:2", ",", ":"),
                ObjectArraySeparator.CANNOT_BE_NULL);
    }
    @Test
    @DisplayName("带Empty的value字符串转换为带默认kv分隔符为等号Map时抛出数组越界异常")
    @Tags({
            @Tag("@id:8"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/25")
    })
    void testJoin_haveEmptyItemValueKVSeparatorIsEqualShouldException() {
        assertThrows(ArrayIndexOutOfBoundsException.class,
                () -> ObjectArraySeparator.splitToMap("a:,b:2", ","),
                ObjectArraySeparator.CANNOT_BE_NULL);
    }

}
