/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.commons.crypt;


import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Base64;
import java.util.Date;
import java.util.Map;

/**
 * JwtTokenUtil类用于生成和解析JWT令牌。
 *
 * 示例：
 * <pre>
 *     JwtTokenUtil jwtTokenUtil = new JwtTokenUtil();
 *     Map<String, Object> claims = new HashMap<>();
 *     claims.put("username", "testUser");
 *     String privateKeyString = "yourPrivateKeyString";//rsa私钥
 *     long expiration = 60; // 令牌过期时间为60分钟
 *     String token = jwtTokenUtil.generateToken(claims, privateKeyString, expiration);
 *
 *     String publicKeyString = "yourPublicKeyString";//rsa公钥
 *     Claims tokenClaims = jwtTokenUtil.getClaimsFromToken(token, publicKeyString);
 * </pre>
 *
 * <AUTHOR>
 * @module 加解密模块
 * @date 2023/12/05
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class JwtTokenUtil {

    /**
     * 生成令牌
     * 过期时间的最小单位为分钟
     *
     * @param claims 具体参数
     * @param privateKeyString 私钥字符串
     * @param expiration 过期时间（分钟）
     * @throws IllegalArgumentException 如果参数非法
     * @return 返回生成的令牌ng
     * @date 2023/12/05 15:00
     * @since 1.4.0
    4     */
    public String generateToken(Map<String, Object> claims,String privateKeyString,long expiration) throws IllegalArgumentException {
        // 参数校验
        if (claims == null || claims.isEmpty()) {
            throw new IllegalArgumentException("claims cannot be null or empty");
        }
        if (privateKeyString == null || privateKeyString.isEmpty()) {
            throw new IllegalArgumentException("privateKeyString cannot be null or empty");
        }
        if (expiration <= 0) {
            throw new IllegalArgumentException("expiration must be greater than 0");
        }

        PrivateKey privateKey = getPrivateKey(privateKeyString);
        return Base64.getEncoder().encodeToString(Jwts.builder()
                .claims(claims)
                .expiration(Date.from(LocalDateTime.now().plusMinutes(expiration)
                        .atZone(ZoneId.systemDefault()).toInstant()))
                .signWith(privateKey)
                .compact().getBytes());
    }

    /**
     * 从令牌中获取声明
     *
     * @param token 令牌字符串
     * @param publicKeyString 公钥字符串
     * @return 返回从令牌中获取的声明
     * @throws UnsupportedEncodingException 如果字符编码不支持
     * @auther yangweipeng
     * @date 2023/12/05 15:00
     * @since 1.4.0
     */
    public Claims getClaimsFromToken(String token,String publicKeyString) throws IllegalArgumentException,UnsupportedEncodingException {
        // 参数校验
        if (token == null || token.isEmpty()) {
            throw new IllegalArgumentException("token cannot be null or empty");
        }
        if (publicKeyString == null || publicKeyString.isEmpty()) {
            throw new IllegalArgumentException("publicKeyString cannot be null or empty");
        }

        PublicKey publicKey = getPublicKey(publicKeyString);
        return Jwts.parser().verifyWith(publicKey).build()
                .parseSignedClaims(new String(Base64.getDecoder().decode(token), StandardCharsets.UTF_8)).getPayload();
    }

    /**
     * 获取私钥
     *
     * @param privateKey 私钥字符串
     * @return 返回私钥
     * @throws IllegalArgumentException 如果私钥非法
     * <AUTHOR>
     * @date 2023/12/05 15:00
     * @since 1.4.0
     */
    private static PrivateKey getPrivateKey(String privateKey) {
        PrivateKey privateKeyTmp = null;
        PKCS8EncodedKeySpec priPKCS8;
        try {
            // 将私钥字符串转换为PKCS8EncodedKeySpec对象
            priPKCS8 = new PKCS8EncodedKeySpec(Base64.getDecoder().decode(privateKey));
            // 创建一个RSA密钥工厂
            KeyFactory keyf = KeyFactory.getInstance("RSA");
            // 生成私钥
            privateKeyTmp = keyf.generatePrivate(priPKCS8);
        } catch (NoSuchAlgorithmException | InvalidKeySpecException e) {
            // 如果私钥非法，抛出异常
            throw new IllegalArgumentException("Invalid private key", e);
        }
        // 返回私钥
        return privateKeyTmp;
    }

    /**
     * 获取公钥
     *
     * @param pubKey 公钥字符串
     * @return 返回公钥
     * @throws IllegalArgumentException 如果公钥非法
     * <AUTHOR>
     * @date 2023/12/05 15:00
     * @since 1.4.0
     */
    private static PublicKey getPublicKey(String pubKey) {
        PublicKey publicKeyTmp = null;
        try {
            X509EncodedKeySpec bobPubKeySpec = new X509EncodedKeySpec(Base64.getDecoder().decode(pubKey));
            KeyFactory keyf = KeyFactory.getInstance("RSA");
            publicKeyTmp = keyf.generatePublic(bobPubKeySpec);
        } catch (NoSuchAlgorithmException | InvalidKeySpecException e) {
            throw new IllegalArgumentException("Invalid public key", e);
        }
        return publicKeyTmp;
    }

}

