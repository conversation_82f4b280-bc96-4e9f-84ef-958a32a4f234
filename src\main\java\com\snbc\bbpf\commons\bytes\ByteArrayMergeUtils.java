/*
 * 版权所有 2009-2024山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.bytes;

/**
 * byte数组合并处理类
 * <p>将2个或多个byte[] 按顺序合并成一个byte[]</p>
 *
 * <AUTHOR>
 * @module 字节处理
 * @date 2024/6/7 19:18
 * @copyright 2024 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class ByteArrayMergeUtils {

	private ByteArrayMergeUtils() {
		throw new IllegalStateException("Utility class");
	}


	/**
	* 合并两个字节数组到一个字节数组
	*
	* <p>例如：输入{0x00,0x01},{0x02,0x03} </p>  
	* <p>返回{0x00,0x01,0x02,0x03} </p>
	*
	* @param data1 字节数组1
	* @param data2 字节数组2
	* @return byte[] 合并后的字节数字
	* @since 1.0.0
	* <AUTHOR>
	* @date 2024/6/7
	*/
	public static byte[] mergeBytes(byte[] data1, byte[] data2) {
		if (data1 == null && data2 == null) {		
			throw new IllegalArgumentException("All bytes are null");		
		}

		if (data1 == null) {
			return data2;
		}

		if (data2 == null) {
			return data1;
		}
	
		byte[] result = new byte[data1.length + data2.length];
		System.arraycopy(data1, 0, result, 0, data1.length);
		System.arraycopy(data2, 0, result, data1.length, data2.length);
		return result;
	}


	/**
	* 合并多个字节数组到一个字节数组
	*
	* @param values 动态字节数字参数
	* @return byte[] 合并后的字节数字
	* @since 1.0.0
	* <AUTHOR>
	* @date 2024/6/7
	*/
	public static byte[] mergeBytes(byte[]... values) {
		if (values.length == 0) {
			throw new IllegalArgumentException("values.length is 0");					
		}

		boolean bAllHaveNull = true;
		for (byte[] value : values) {
			if (value != null) {
				bAllHaveNull = 	false;
				break;
			}
		}
		
		if (bAllHaveNull) {		
			throw new IllegalArgumentException("All bytes are null");		
		}

		//只有一个字节数组则直接返回
		if (values.length == 1) {
			return values[0];
		}

		//计算长度
		int lengthByte = 0;
		for (byte[] value : values) {
			if (value == null) {
				continue;
			}
			lengthByte += value.length;
		}

		//合并多个字节数组到一个字节数组
		byte[] allBytes = new byte[lengthByte];
		int countLength = 0;
		for (byte[] value : values) {
			if (value == null) {
				continue;
			}
			System.arraycopy(value, 0, allBytes, countLength, value.length);
			countLength += value.length;
		}
		return allBytes;
	}

}

