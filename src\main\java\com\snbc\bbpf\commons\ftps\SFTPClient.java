/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.ftps;

import com.jcraft.jsch.Channel;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;
import com.snbc.bbpf.commons.files.FileUtils;
import com.snbc.bbpf.commons.ftps.config.ConnectConfig;
import com.snbc.bbpf.commons.objs.ObjectEmptyCheck;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 【数据传输】 sftp 客户端
 *  v1.2.0 sftp 客户端 暂未验证，不可用
 *
 * <AUTHOR>
 * @module 数据传输
 * @date 2023/9/26 14:22
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class SFTPClient extends BaseFtpClient implements ITransferClient {
    //SFTP 客户端
    private ChannelSftp channelSftp;

    /**
     * 连接SFTP服务端
     * @param
     * @return boolean 是否连接成功
     * @throws IllegalArgumentException 无效连接参数
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/8/23
     */
    @Override
    public synchronized void connect() {
        if (!isConnectParamValid(this.connectConfig)){
            throw new IllegalArgumentException("connectParam == null || connectParam 参数校验错误！");
        }
        if (isConnected()){
            return ;
        }
        JSch jSch = new JSch();
        try {
            if(this.connectConfig.getPrivateKeyFilePath()!=null){
                jSch.addIdentity(this.connectConfig.getPrivateKeyFilePath());
            }
            Session session = jSch.getSession(
                    this.connectConfig.getUserName(),
                    this.connectConfig.getHost(),
                    this.connectConfig.getPort());
            session.setPassword(this.connectConfig.getPassword());
            // 配置连接属性
            Properties config = new Properties();
            config.put("StrictHostKeyChecking","no");
            config.put("PreferredAuthentications",this.connectConfig.getAuthType().getType());
            config.put("X11Forwarding","no");

            session.setConfig(config);
            session.setTimeout((int) this.connectConfig.getConnectTimeout());
            // 进行sftp连接
            session.connect();
            // 获取通信通道
            Channel channel = session.openChannel("sftp");
            channel.connect((int)this.connectConfig.getConnectTimeout());
            channelSftp = (ChannelSftp) channel;
            this.isConnect.getAndSet(true);
        } catch (Exception e){
            throw new RuntimeException("connect exception!",e);
        }

    }

    /**
     * 断开连接
     * @param
     * @return void
     * @throws RuntimeException 断开连接是运行时异常
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/8/23
     */
    @Override
    public synchronized void disconnect(){
        isConnect.set(false);
        if (this.channelSftp == null){
            return;
        }
        Session session =null;
        try {
            session = this.channelSftp.getSession();
            if (this.channelSftp.isConnected()){
                this.channelSftp.disconnect();
            }
        } catch (JSchException e){
            throw new RuntimeException(e);
        } finally {
            if (session!=null){
                session.disconnect();
            }
            session = null;
            this.channelSftp = null;
        }
    }

    /**
     * 将本地文件上传到FTP下的指定目录
     * 默认上传成功不删除本地文件，如果远端文件已存在默认覆盖。
     * @param localFilePath  本地文件路径
     * @param remoteDirPath  远端文件路径
     * @return boolean 是否上传成功
     * @throws
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/8/23
     */
    @Override
    public boolean upload(String localFilePath, String remoteDirPath) {
        return this.upload(
                localFilePath,
                remoteDirPath,
                false,
                true);
    }

    /**
     * 将本地文件上传到FTP下的指定目录
     * @param localFilePath 本地文件路径
     * @param remoteDirPath 远端文件路径
     * @param deleteLocalFileIfUploadSuccess 上传成功是否删除本地文件
     * @param overwriteIfFileExist 如果远端文件已存在是否覆盖
     * @return boolean 上传是否成功
     * @throws
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/8/23
     */
    @Override
    public synchronized boolean upload(String localFilePath,
                                       String remoteDirPath,
                                       boolean deleteLocalFileIfUploadSuccess,
                                       boolean overwriteIfFileExist) {
        checkConnectStatus();

        File localFile = new File(localFilePath);
        if (!localFile.exists()||!localFile.isFile()){
            throw new IllegalArgumentException("local localFile is not exist or is not a localFile!");
        }
        String fileName = localFile.getName();

        String remoteDir = getDirPathWithoutLastSeparator(remoteDirPath);
        String remoteFilePath = remoteDir+File.separator+fileName;
        if (!overwriteIfFileExist && existFileInRemote(remoteFilePath)){
            throw new IllegalArgumentException("remote file is exist!");
        }

        try{
            this.channelSftp.put(localFilePath,remoteDir);
            if (deleteLocalFileIfUploadSuccess){
                Files.delete(localFile.toPath());
            }
            return true;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 判断远程目录或文件是否存在
     * @return 是否存在
     * <AUTHOR>
     * @date 2023/11/05
     * @since 1.4.0
     */
    public boolean existFileInRemote(String remoteFilePath) {
        boolean exists = false;
        try {
            this.channelSftp.stat(remoteFilePath);
            exists = true;
        } catch (SftpException e) {
            // 文件不存在
            exists = false;
        }
        return exists;
    }

    /**
     * 从远端下载文件到指定目录
     *
     * @param remoteFilePath  远端文件路径
     * @param localDirPath  本地文件目录路径
     * @return boolean 是否下载成功
     * @throws
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/8/23
     */
    @Override
    public boolean download(String remoteFilePath, String localDirPath) {
        return this.download(remoteFilePath,
                localDirPath,
                false,
                true);
    }

    /**
     * 从远端下载文件到指定目录
     * @param remoteFilePath  远端文件路径
     * @param localDirPath  本地文件目录路径
     * @param deleteRemoteFileIfDownloadSuccess  下载成功是否删除远端文件
     * @param overwriteIfFileExist 本地文件已存在是否覆盖
     * @return boolean 是否下载成功
     * @throws
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/8/23
     */
    @Override
    public synchronized boolean download(String remoteFilePath,
                                         String localDirPath,
                                         boolean deleteRemoteFileIfDownloadSuccess,
                                         boolean overwriteIfFileExist) {
        checkConnectStatus();
        if (!existFileInRemote(remoteFilePath)){
            throw new IllegalArgumentException("remote file not exist!");
        }

        String localFileName = getFileNameByFilePath(remoteFilePath);
        String localDir = getDirPathWithoutLastSeparator(localDirPath);
        File localFile = new File(localDir+File.separator+localFileName);
        if (!overwriteIfFileExist && localFile.exists()){
            throw new IllegalArgumentException("local file is exist!");
        }

        File localDirFile = localFile.getParentFile();
        if (!localDirFile.exists()||!localDirFile.isDirectory()){
            localDirFile.mkdirs();
        }
        try(OutputStream outputStream = new FileOutputStream(localFile)){
            this.channelSftp.get(remoteFilePath,outputStream);
            if (deleteRemoteFileIfDownloadSuccess){
                deleteRemoteFile(remoteFilePath);
            }
            return true;
        } catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    /**
     * 删除远程文件
     *
     * @param remoteFilePath 远程文件路径
     * @return 结果 由于没有权限或文件不存在等原因会导致返回false
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/9/26
     */
    @Override
    public synchronized boolean deleteRemoteFile(String remoteFilePath) {
        checkConnectStatus();
        try {
            this.channelSftp.rm(remoteFilePath);
            return true;
        } catch (SftpException e) {
            return false;
        }

    }

    /**
     * 批量删除文件名符合条件的远端文件
     *
     * @param remoteDirPath 远端文件目录
     * @param predicate     文件名过滤器
     * @return java.util.Map<java.lang.String, java.lang.Boolean>  符合条件的远程文件是否删除成功列表
     * @throws
     * <AUTHOR>
     * @date 2024/8/23
     * @since 1.5.0
     */
    @Override
    public Map<String, Boolean> batchDeleteRemoteFiles(String remoteDirPath, Predicate<String> predicate) {
        Map<String, Boolean> map = new HashMap<>();
        List<String> list = listFiles(remoteDirPath)
                .stream()
                .filter(predicate)
                .collect(Collectors.toList());
        for (String remoteFilePath : list) {
            String path = remoteDirPath+ LINUX_SEPARATOR +remoteFilePath;
            Boolean isSuccess = Boolean.FALSE;
            try {
                isSuccess = deleteRemoteFile(path);
            }catch(Exception e) {
                isSuccess = Boolean.FALSE;
            }
            map.put(path,isSuccess);
        }
        return map;
    }

    /**
     * 删除远程目录
     * 包含目录和文件
     *
     * @param remoteDir 远程文件路径
     * @return 结果
     * @throws RuntimeException 异常信息
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/9/26
     */
    @Override
    public synchronized boolean deleteRemoteDir(String remoteDir) {
        checkConnectStatus();
        try {
            this.channelSftp.rmdir(remoteDir);
        } catch (SftpException e) {
            throw new RuntimeException(e);
        }
        return true;
    }

    /**
     * 检查SFTP连接状态
     * @param
     * @return void
     * @throws IllegalStateException 抛出无效连接的异常。
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/8/23
     */
    private void checkConnectStatus() {
        if (this.channelSftp == null || !isConnected()){
            throw new IllegalStateException("invoke connect first!");
        }
    }
    /**
     * 检查连接参数是否有效
     * @param connectConfig 连接参数
     * @return boolean 是否有效
     * @throws
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/8/23
     */
    private static boolean isConnectParamValid(ConnectConfig connectConfig) {
        return connectConfig !=null && connectConfig.isValid();
    }

    /**
     * 列出远程默认目录的所有文件
     * @return 文件数组
     * <AUTHOR>
     * @date 2023/11/05
     * @since 1.4.0
     */
    @Override
    public List<String> listFiles(){
        try {
            List<String> readFiles = this.listFiles(this.channelSftp.pwd());
            if(readFiles==null){
                return Collections.emptyList();
            }
            return readFiles;
        } catch (SftpException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 列出指定远程目录的所有文件，目录不存在或目录下没有文件返回空数组
     * @param remoteDirPath 远程目录
     * @return 文件数组
     * <AUTHOR>
     * @date 2023/11/05
     * @since 1.4.0
     */
    @Override
    public List<String> listFiles(String remoteDirPath){
        return listFiles(remoteDirPath,null);
    }

    /**
     * 列出指定远程目录的符合条件的文件，目录不存在或目录下没有文件返回空数组
     *
     * @param remoteDirPath     远程目录
     * @param fileNamePredicate 文件名过滤器
     * @return java.util.List<java.lang.String> 返回文件名列表
     * @throws
     * <AUTHOR>
     * @date 2024/8/23
     * @since 1.5.0
     */
    @Override
    public List<String> listFiles(String remoteDirPath, Predicate<String> fileNamePredicate) {
        List<String> fileNames = new ArrayList<>();
        ChannelSftp.LsEntrySelector selector = lsEntry -> {
            if (fileNamePredicate == null || fileNamePredicate.test(lsEntry.getFilename())) {
                fileNames.add(lsEntry.getFilename());
            }
            return ChannelSftp.LsEntrySelector.CONTINUE;
        };
        try {
            channelSftp.ls(remoteDirPath,selector);
        } catch (SftpException e) {
            throw new RuntimeException(e);
        }
        return fileNames;
    }

    /**
     * 批量上传文件操作
     * 默认不删除本地文件，会覆盖远端重名文件
     *
     * @param localPath     本地文件路径
     * @param predicate     文件过滤器
     * @param remoteDirPath 远端目录
     * @return java.util.Map<java.lang.String, java.lang.Boolean> 返回文件上传成功列表
     * @throws
     * <AUTHOR>
     * @date 2024/8/23
     * @since 1.5.0
     */
    @Override
    public Map<String, Boolean> batchUpload(String localPath, Predicate<Path> predicate, String remoteDirPath) {
        return batchUpload(localPath, predicate, remoteDirPath, false, true);
    }

    /**
     * 批量上传文件操作
     *
     * @param localPath                      本地文件路径
     * @param predicate                      文件过滤器
     * @param remoteDirPath                  远端目录
     * @param deleteLocalFileIfUploadSuccess 上传成功后是否删除本地文件
     * @param overwriteIfFileExist           是否覆盖已有文件
     * @return java.util.Map<java.lang.String, java.lang.Boolean> 返回文件上传成功列表
     * @throws
     * <AUTHOR>
     * @date 2024/8/23
     * @since 1.5.0
     */
    @Override
    public Map<String, Boolean> batchUpload(String localPath,
                                            Predicate<Path> predicate,
                                            String remoteDirPath,
                                            boolean deleteLocalFileIfUploadSuccess,
                                            boolean overwriteIfFileExist) {
        List<Path> list = FileUtils.filter(localPath, predicate);
        Map<String, Boolean> result = new HashMap<>();
        for (Path path : list) {
            Boolean isSuccess = upload(path.toString(), remoteDirPath, deleteLocalFileIfUploadSuccess, overwriteIfFileExist);
            result.put(path.toAbsolutePath().toString(), isSuccess);
        }
        return result;
    }

    /**
     * 批量下载文件名符合条件的文件
     * 默认不删除远端文件，会覆盖本地重名文件
     *
     * @param remoteDirPath 远端文件路径
     * @param predicate      文件过滤器
     * @param localDirPath   本地文件路径
     * @return java.util.Map<java.lang.String, java.lang.Boolean> 返回文件下载成功列表
     * @throws
     * <AUTHOR>
     * @date 2024/8/23
     * @since 1.5.0
     */
    @Override
    public Map<String, Boolean> batchDownload(String remoteDirPath, Predicate<String> predicate, String localDirPath) {
        return batchDownload(remoteDirPath, predicate, localDirPath, false, true);
    }

    /**
     * 批量下载文件名符合条件的文件
     *
     * @param remoteDirPath                    远端文件路径
     * @param predicate                         文件过滤器
     * @param localDirPath                      本地文件路径
     * @param deleteRemoteFileIfDownloadSuccess 下载成功后是否删除远端文件
     * @param overwriteIfFileExist              是否覆盖已有文件
     * @return java.util.Map<java.lang.String, java.lang.Boolean> 返回文件下载成功列表
     * @throws
     * <AUTHOR>
     * @date 2024/8/23
     * @since 1.5.0
     */
    @Override
    public Map<String, Boolean> batchDownload(String remoteDirPath,
                                              Predicate<String> predicate,
                                              String localDirPath,
                                              boolean deleteRemoteFileIfDownloadSuccess,
                                              boolean overwriteIfFileExist) {
        List<String> list = listFiles(remoteDirPath, predicate);
        Map<String, Boolean> result = new HashMap<>();
        if (!ObjectEmptyCheck.isEmpty(list)) {
            for (String fileName : list) {
                String filePath = remoteDirPath+ LINUX_SEPARATOR +fileName;
                Boolean isSuccess = download(filePath, localDirPath, deleteRemoteFileIfDownloadSuccess, overwriteIfFileExist);
                result.put(filePath, isSuccess);
            }
        }
        return result;
    }

    /**
     * 在远端创建目录
     *
     * @param remoteDirPath 远端目录
     * @param dirName       需要创建的目录名称
     * @return boolean 是否创建成功
     * @throws
     * <AUTHOR>
     * @date 2024/8/23
     * @since 1.5.0
     */
    @Override
    public boolean createRemoteDir(String remoteDirPath, String dirName) {
        try {
            channelSftp.mkdir(remoteDirPath+ LINUX_SEPARATOR +dirName);
            return true;
        } catch (SftpException e) {
            return false;
        }
    }


}
