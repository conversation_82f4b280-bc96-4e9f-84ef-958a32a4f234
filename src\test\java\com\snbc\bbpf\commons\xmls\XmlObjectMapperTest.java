/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.xmls;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonRootName;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlCData;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.snbc.bbpf.commons.dates.LocalDateTimeUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * XmlTool测试类
 *
 * <AUTHOR>
 * @module XML模块
 * @date 2023-09-05 15:22
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class XmlObjectMapperTest {
    @DisplayName("测试序列化对象为xml")
    @Tags({
            @Tag("@id:50"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/9/05")
    })
    @Test
    void testMarshall() {
        final LocalDateTime now = LocalDateTime.of(2023, 9, 5, 15, 30, 45, 999);
        VemInfo vemInfo = new VemInfo(12,
                "新北洋1号售货机",
                LocalDateTimeUtils.toDate(now)
                ,
                now, "此售货机位于北京知春路");
        String xmlStr = XmlObjectMapper.marshall(vemInfo);
        assertEquals("<SNBCVem id=\"12\"><mark><![CDATA[此售货机位于北京知春路]]></mark><vemName>新北洋1号售货机</vemName><createTime>2023-09-05 15:30:45</createTime><updateTime>2023-09-05 15:30:45</updateTime></SNBCVem>",
                xmlStr);
    }

    @DisplayName("测试序列化对象为null的用例")
    @Tags({
            @Tag("@id:50"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/9/15")
    })
    @Test
    void testMarshall_with_null() {
        NullPointerException thrown = Assertions.assertThrows(NullPointerException.class, () -> {
            XmlObjectMapper.marshall(null);
        });
        Assertions.assertEquals("object is not allowed null", thrown.getMessage());

    }

    @DisplayName("测试xml转化为对象")
    @Tags({
            @Tag("@id:50"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/9/05")
    })
    @Test
    void testUnmarshall() {
        final LocalDateTime now = LocalDateTime.of(2023, 9, 5, 15, 30, 45, 0);
        VemInfo vemInfo = new VemInfo(12,
                "新北洋1号售货机",
                LocalDateTimeUtils.toDate(now)
                ,
                now, "此售货机位于北京知春路");
        final VemInfo targetVemInfo = XmlObjectMapper.unmarshall(XmlObjectMapper.marshall(vemInfo), VemInfo.class);
        assertEquals(vemInfo.getId(), targetVemInfo.getId());
        assertEquals(vemInfo.getMark(), targetVemInfo.getMark());
        assertEquals(vemInfo.getName(), targetVemInfo.getName());
        assertEquals(vemInfo.getCreateTime(), targetVemInfo.getCreateTime());
        assertEquals(vemInfo.getUpdateTime(), targetVemInfo.getUpdateTime());
    }

    @DisplayName("测试xml转化为对象异常")
    @Tags({
            @Tag("@id:50"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/9/14")
    })
    @Test
    void testUnmarshall_with_exception() {
        String str = "<SNBCVem id=\"12\"><mark><![CDATA[此售货机位于北京知春路]]></mark><vemName>新北洋1号售货机</vemName><containerName>新北洋1号主柜</containerName><createTime>2023-09-05 15:30:45</createTime><updateTime>2023-09-05 15:30:45</updateTime></SNBCVem>";
        RuntimeException thrown = Assertions.assertThrows(RuntimeException.class,
                () -> XmlObjectMapper.unmarshall(str, VemInfo.class));
        Assertions.assertNotNull(thrown);

    }


}

/**
 * XmlTool测试用Bean
 *
 * <AUTHOR>
 * @module 序列化/反序列化
 * @date 2023-09-05 15:22
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
@JsonRootName(value = "SNBCVem")
class VemInfo {

    @JacksonXmlProperty(isAttribute = true)
    private Integer id;
    /**
     * 售货机名称
     */
    @JacksonXmlProperty(localName = "vemName")
    private String name;
    /**
     * 售货机创建时间
     * 如果项目使用 spring.jackson.time-zone=Asia/Shanghai
     * spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @JacksonXmlProperty(localName = "createTime")
    private Date createTime;
    /**
     * 售货机更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @JacksonXmlProperty(localName = "updateTime")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @JacksonXmlCData
    private String mark;

    public VemInfo() {
    }


    public VemInfo(Integer id, String name, Date createTime, LocalDateTime updateTime, String mark) {
        this.id = id;
        this.name = name;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.mark = mark;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getMark() {
        return mark;
    }

    public void setMark(String mark) {
        this.mark = mark;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Override
    public String toString() {
        return "VemInfo{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", mark='" + mark + '\'' +
                '}';
    }
}
