# TLV编码器 (com.snbc.bbpf.commons.bytes.tlv)

## 概述

TLV（Type-Length-Value）编码器提供了一套完整的TLV格式数据编码功能，支持多种数据类型的自动识别和序列化。TLV是一种常用的数据序列化格式，广泛应用于网络协议、数据存储和通信系统中。

## 类概览

| 类名 | 功能描述 |
|------|----------|
| TlvDataType | TLV数据类型枚举，定义Type字段值与Java类型的映射关系 |
| TlvEncoder | TLV编码器核心类，提供数据编码功能 |
| TlvUtils | TLV工具类，提供辅助功能如长度计算、格式验证等 |

## TlvDataType - 数据类型枚举

### 支持的数据类型

#### 基本数据类型
- **BYTE** (0x01): 8位有符号整数
- **SHORT** (0x02): 16位有符号整数  
- **INTEGER** (0x03): 32位有符号整数
- **LONG** (0x04): 64位有符号整数
- **BIG_INTEGER** (0x05): 大整数
- **FLOAT** (0x10): 32位浮点数
- **DOUBLE** (0x11): 64位浮点数
- **BIG_DECIMAL** (0x12): 高精度小数
- **BOOLEAN** (0x20): 布尔值

#### 字符串类型
- **STRING_UTF8** (0x30): UTF-8编码字符串（默认）
- **STRING_ASCII** (0x31): ASCII编码字符串

#### 复杂类型
- **MAP** (0x40): 对象类型（Map接口实现）
- **ARRAY** (0x41): 数组类型（Iterable接口实现）
- **BYTE_ARRAY** (0x50): 字节数组

#### 特殊类型
- **NULL** (0x00): 空值

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| fromJavaType(Class<?> javaType) | 根据Java类型获取对应的TLV数据类型 | javaType: Java类型 | TlvDataType: 对应的TLV数据类型 |
| fromTypeValue(int typeValue) | 根据Type值获取对应的TLV数据类型 | typeValue: Type字段值 | TlvDataType: 对应的TLV数据类型 |
| registerCustomType(Class<?> javaType, TlvDataType tlvDataType) | 注册自定义类型映射 | javaType: Java类型<br>tlvDataType: TLV数据类型 | void |
| unregisterCustomType(Class<?> javaType) | 移除自定义类型映射 | javaType: Java类型 | void |
| isSupported(Class<?> javaType) | 检查是否支持指定的Java类型 | javaType: Java类型 | boolean: 是否支持 |

### 使用示例

```java
// 获取Java类型对应的TLV数据类型
TlvDataType intType = TlvDataType.fromJavaType(Integer.class);
// 输出: INTEGER

// 根据Type值获取数据类型
TlvDataType stringType = TlvDataType.fromTypeValue(0x30);
// 输出: STRING_UTF8

// 注册自定义类型映射
TlvDataType.registerCustomType(StringBuilder.class, TlvDataType.STRING_UTF8);

// 检查类型支持
boolean supported = TlvDataType.isSupported(String.class);
// 输出: true
```

## TlvEncoder - TLV编码器

### 构造函数

| 构造函数 | 说明 |
|----------|------|
| TlvEncoder() | 使用默认配置创建编码器（大端字节序，UTF-8编码，Type字段1字节，Length字段4字节） |
| TlvEncoder(ByteOrder byteOrder, Charset defaultCharset, int typeLength, int lengthFieldSize) | 使用自定义配置创建编码器 |

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| encode(Object data) | 编码数据为TLV格式，自动识别数据类型 | data: 要编码的数据 | byte[]: TLV格式字节数组 |
| encode(Object data, TlvDataType dataType) | 编码数据为TLV格式，指定数据类型 | data: 要编码的数据<br>dataType: TLV数据类型 | byte[]: TLV格式字节数组 |

### 编码格式说明

TLV格式结构：`[Type字段][Length字段][Value字段]`

- **Type字段**: 标识数据类型，长度可配置（1-4字节）
- **Length字段**: 标识Value部分的字节长度，长度可配置（1-8字节）
- **Value字段**: 实际数据内容

#### 复杂类型编码格式

**Map编码格式**:
```
[Type: MAP][Length][元素数量(4字节)][Key1的TLV][Value1的TLV][Key2的TLV][Value2的TLV]...
```

**Array编码格式**:
```
[Type: ARRAY][Length][元素数量(4字节)][Element1的TLV][Element2的TLV]...
```

### 使用示例

```java
import com.snbc.bbpf.commons.bytes.tlv.*;

// 创建默认编码器
TlvEncoder encoder = new TlvEncoder();

// 编码基本数据类型
byte[] intResult = encoder.encode(12345);
// 结果: [0x03, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x30, 0x39]

// 编码字符串
byte[] stringResult = encoder.encode("Hello");
// 结果: [0x30, 0x00, 0x00, 0x00, 0x05, 0x48, 0x65, 0x6C, 0x6C, 0x6F]

// 编码字节数组
byte[] byteArrayResult = encoder.encode(new byte[]{0x01, 0x02, 0x03});
// 结果: [0x50, 0x00, 0x00, 0x00, 0x03, 0x01, 0x02, 0x03]

// 编码Map
Map<String, Integer> map = new HashMap<>();
map.put("key1", 100);
map.put("key2", 200);
byte[] mapResult = encoder.encode(map);

// 编码List
List<Integer> list = Arrays.asList(1, 2, 3, 4, 5);
byte[] listResult = encoder.encode(list);

// 指定数据类型编码
byte[] asciiResult = encoder.encode("Hello", TlvDataType.STRING_ASCII);

// 创建自定义配置编码器
TlvEncoder customEncoder = new TlvEncoder(
    ByteOrder.LITTLE_ENDIAN,    // 小端字节序
    StandardCharsets.US_ASCII,  // ASCII编码
    2,                          // Type字段2字节
    2                           // Length字段2字节
);
```

## TlvUtils - 工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| calculateValueLength(Object data) | 计算数据编码后的Value部分字节长度 | data: 要计算的数据 | int: 字节长度 |
| calculateTlvLength(Object data) | 计算完整TLV长度（包括Type和Length字段） | data: 要计算的数据 | int: 总字节长度 |
| validateTlvFormat(byte[] tlvData, int typeLength, int lengthFieldSize, ByteOrder byteOrder) | 验证TLV数据格式是否正确 | tlvData: TLV数据<br>typeLength: Type字段长度<br>lengthFieldSize: Length字段长度<br>byteOrder: 字节序 | boolean: 是否有效 |
| extractType(byte[] tlvData, int typeLength, ByteOrder byteOrder) | 从TLV数据中提取Type值 | tlvData: TLV数据<br>typeLength: Type字段长度<br>byteOrder: 字节序 | int: Type值 |
| extractLength(byte[] tlvData, int typeLength, int lengthFieldSize, ByteOrder byteOrder) | 从TLV数据中提取Length值 | tlvData: TLV数据<br>typeLength: Type字段长度<br>lengthFieldSize: Length字段长度<br>byteOrder: 字节序 | long: Length值 |
| extractValue(byte[] tlvData, int typeLength, int lengthFieldSize) | 从TLV数据中提取Value部分 | tlvData: TLV数据<br>typeLength: Type字段长度<br>lengthFieldSize: Length字段长度 | byte[]: Value数据 |
| createDefaultEncoder() | 创建默认配置的编码器 | 无 | TlvEncoder: 编码器实例 |

### 使用示例

```java
// 计算数据长度
int valueLength = TlvUtils.calculateValueLength("Hello");
// 输出: 5

int tlvLength = TlvUtils.calculateTlvLength("Hello");
// 输出: 10 (1字节Type + 4字节Length + 5字节Value)

// 验证TLV格式
TlvEncoder encoder = new TlvEncoder();
byte[] tlvData = encoder.encode(123);
boolean isValid = TlvUtils.validateTlvFormat(tlvData, 1, 4, ByteOrder.BIG_ENDIAN);
// 输出: true

// 提取TLV各部分
int type = TlvUtils.extractType(tlvData, 1, ByteOrder.BIG_ENDIAN);
// 输出: 3 (INTEGER类型)

long length = TlvUtils.extractLength(tlvData, 1, 4, ByteOrder.BIG_ENDIAN);
// 输出: 4

byte[] value = TlvUtils.extractValue(tlvData, 1, 4);
// 输出: [0x00, 0x00, 0x00, 0x7B] (123的大端表示)

// 创建编码器
TlvEncoder defaultEncoder = TlvUtils.createDefaultEncoder();
```

## 使用注意事项

### 1. 字节序配置
- 默认使用大端字节序（网络字节序）
- 可通过构造函数配置为小端字节序
- 确保编码和解码使用相同的字节序

### 2. 字符编码
- String类型默认使用UTF-8编码
- 可通过指定TlvDataType.STRING_ASCII使用ASCII编码
- 可通过构造函数配置默认字符编码

### 3. 类型映射扩展
- 支持注册自定义类型映射
- 自定义映射优先于默认映射
- 使用完毕后建议清理自定义映射

### 4. 复杂类型处理
- Map和Array类型会递归编码其元素
- 确保所有元素类型都被支持
- 注意循环引用问题

### 5. 长度限制
- Type字段长度：1-4字节
- Length字段长度：1-8字节
- 根据数据大小选择合适的Length字段长度

### 6. 性能考虑
- 大型数据结构编码时注意内存使用
- 复杂嵌套结构可能影响性能
- 建议对频繁使用的编码器进行复用

## 完整示例

```java
import com.snbc.bbpf.commons.bytes.*;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;
import java.util.*;

public class TlvExample {
    public static void main(String[] args) {
        // 创建编码器
        TlvEncoder encoder = new TlvEncoder();
        
        // 编码各种数据类型
        Map<String, Object> data = new HashMap<>();
        data.put("id", 12345);
        data.put("name", "张三");
        data.put("age", 25);
        data.put("scores", Arrays.asList(85, 92, 78));
        data.put("metadata", new byte[]{0x01, 0x02, 0x03});
        
        // 编码整个Map
        byte[] encoded = encoder.encode(data);
        
        // 验证编码结果
        boolean isValid = TlvUtils.validateTlvFormat(
            encoded, 1, 4, ByteOrder.BIG_ENDIAN);
        System.out.println("编码结果有效: " + isValid);
        
        // 提取信息
        int type = TlvUtils.extractType(encoded, 1, ByteOrder.BIG_ENDIAN);
        System.out.println("数据类型: " + TlvDataType.fromTypeValue(type));
        
        long length = TlvUtils.extractLength(encoded, 1, 4, ByteOrder.BIG_ENDIAN);
        System.out.println("数据长度: " + length + " 字节");
    }
}
```
