/*
 * 版权所有 2009-2024山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.geo;

/**
 * WGS84和GCJ02坐标系转换工具类
 * <p>
 * 使用示例：<br>
 * 1. WGS84转GCJ02：<br>
 * GeoCoordinateConverter.wgs84ToGcj02(116.404, 39.915);<br>
 * 2. GCJ02转WGS84：<br>
 * GeoCoordinateConverter.gcj02ToWgs84(116.404, 39.915);<br>
 * </p>
 *
 * <AUTHOR>
 * @module 地理坐标模块
 * @date 2025-05-08
 * @copyright 2024 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class GeoCoordinateConverter {

    private static final double EE = 0.00669342162296594323;
    private static final double A = 6378245.0;
    private static final double OFFSET_LNG = 105.0;
    private static final double OFFSET_LAT = 35.0;
    private static final double ONE_HUNDRED_EIGHTY_POINT_ZERO = 180.0;
    private static final double ONE_POINT_ZERO = 1.0;
    private static final double TWO_POINT_ZERO = 2.0;
    private static final double CHINA_LNG_LEFT_BOUND = 72.004;
    private static final double CHINA_LNG_RIGHT_BOUND = 137.8347;
    private static final double CHINA_LAT_BOTTOM_BOUND = 0.8293;
    private static final double CHINA_LAT_TOP_BOUND = 55.8271;

    /**
     * WGS84转GCJ02
     * 
     * @param lng 经度
     * @param lat 纬度
     * @return 包含GCJ02经纬度的数组
     * @since 1.6.0
     * <AUTHOR>
     * @date 2025-05-08
     */
    public static double[] wgs84ToGcj02(double lng, double lat) {
        double dLat = transformLat(lng - OFFSET_LNG, lat - OFFSET_LAT);
        double dLng = transformLng(lng - OFFSET_LNG, lat - OFFSET_LAT);
        double radLat = lat / ONE_HUNDRED_EIGHTY_POINT_ZERO * Math.PI;
        double magic = Math.sin(radLat);
        magic = ONE_POINT_ZERO - EE * magic * magic;
        double sqrtMagic = Math.sqrt(magic);
        dLat = (dLat * ONE_HUNDRED_EIGHTY_POINT_ZERO) / ((A * (ONE_POINT_ZERO - EE)) / (magic * sqrtMagic) * Math.PI);
        dLng = (dLng * ONE_HUNDRED_EIGHTY_POINT_ZERO) / (A / sqrtMagic * Math.cos(radLat) * Math.PI);
        return new double[] { lng + dLng, lat + dLat };
    }

    /**
     * GCJ02转WGS84
     * 
     * @param lng 经度
     * @param lat 纬度
     * @return 包含WGS84经纬度的数组
     * @since 1.6.0
     * <AUTHOR>
     * @date 2025-05-08
     */
    public static double[] gcj02ToWgs84(double lng, double lat) {
        double dLat = transformLat(lng - OFFSET_LNG, lat - OFFSET_LAT);
        double dLng = transformLng(lng - OFFSET_LNG, lat - OFFSET_LAT);
        double radLat = lat / ONE_HUNDRED_EIGHTY_POINT_ZERO * Math.PI;
        double magic = Math.sin(radLat);
        magic = ONE_POINT_ZERO - EE * magic * magic;
        double sqrtMagic = Math.sqrt(magic);
        dLat = (dLat * ONE_HUNDRED_EIGHTY_POINT_ZERO) / ((A * (ONE_POINT_ZERO - EE)) / (magic * sqrtMagic) * Math.PI);
        dLng = (dLng * ONE_HUNDRED_EIGHTY_POINT_ZERO) / (A / sqrtMagic * Math.cos(radLat) * Math.PI);
        return new double[] { lng * TWO_POINT_ZERO - (lng + dLng), lat * TWO_POINT_ZERO - (lat + dLat) };
    }

     

    /**
     * 纬度转换公式(GCJ02加密算法)
     * 
     * @param x 经度偏移量(原始经度-105.0)
     * @param y 纬度偏移量(原始纬度-35.0)
     * @return 纬度偏移量转换结果
     * @note 此方法实现GCJ02加密算法中的纬度转换部分
     * @since 1.6.0
     * <AUTHOR>
     * @date 2025-05-08
     */
    private static final double NEG_ONE_HUNDRED_POINT_ZERO = -100.0;
    private static final double THREE_POINT_ZERO = 3.0;
    private static final double ZERO_POINT_TWO = 0.2;
    private static final double ZERO_POINT_ONE = 0.1;
    private static final double TWENTY_POINT_ZERO = 20.0;
    private static final double SIX_POINT_ZERO = 6.0;
    private static final double FORTY_POINT_ZERO = 40.0;
    private static final double ONE_HUNDRED_SIXTY_POINT_ZERO = 160.0;
    private static final double TWELVE_POINT_ZERO = 12.0;
    private static final double THREE_HUNDRED_TWENTY_POINT_ZERO = 320.0;
    private static final double THIRTY_POINT_ZERO = 30.0;

    /**
     * 纬度转换公式(GCJ02加密算法)
     * 
     * @param x 经度偏移量(原始经度-105.0)
     * @param y 经度偏移量(原始经度-105.0)
     * @return 纬度转换结果
     * @since 1.6.0
     * <AUTHOR>
     * @date 2025-05-08
     */
    private static double transformLat(double x, double y) {
        double ret = NEG_ONE_HUNDRED_POINT_ZERO + TWO_POINT_ZERO * x + THREE_POINT_ZERO * y + ZERO_POINT_TWO * y * y
                + ZERO_POINT_ONE * x * y + ZERO_POINT_TWO * Math.sqrt(Math.abs(x));
        ret += (TWENTY_POINT_ZERO * Math.sin(SIX_POINT_ZERO * x * Math.PI)
                + TWENTY_POINT_ZERO * Math.sin(TWO_POINT_ZERO * x * Math.PI)) * TWO_POINT_ZERO / THREE_POINT_ZERO;
        ret += (TWENTY_POINT_ZERO * Math.sin(y * Math.PI) + FORTY_POINT_ZERO * Math.sin(y / THREE_POINT_ZERO * Math.PI))
                * TWO_POINT_ZERO / THREE_POINT_ZERO;
        ret += (ONE_HUNDRED_SIXTY_POINT_ZERO * Math.sin(y / TWELVE_POINT_ZERO * Math.PI)
                + THREE_HUNDRED_TWENTY_POINT_ZERO * Math.sin(y * Math.PI / THIRTY_POINT_ZERO)) * TWO_POINT_ZERO
                / THREE_POINT_ZERO;
        return ret;
    }

    /**
     * 经度转换公式(GCJ02加密算法)
     * 
     * @param x 经度偏移量(原始经度-105.0)
     * @param y 纬度偏移量(原始纬度-35.0)
     * @return 经度偏移量转换结果
     * @note 此方法实现GCJ02加密算法中的经度转换部分
     */
    private static final double THREE_HUNDRED_POINT_ZERO = 300.0;
    private static final double ONE_HUNDRED_FIFTY_POINT_ZERO = 150.0;

    private static double transformLng(double x, double y) {
        double ret = THREE_HUNDRED_POINT_ZERO + x + TWO_POINT_ZERO * y + ZERO_POINT_ONE * x * x + ZERO_POINT_ONE * x * y
                + ZERO_POINT_ONE * Math.sqrt(Math.abs(x));
        ret += (TWENTY_POINT_ZERO * Math.sin(SIX_POINT_ZERO * x * Math.PI)
                + TWENTY_POINT_ZERO * Math.sin(TWO_POINT_ZERO * x * Math.PI)) * TWO_POINT_ZERO / THREE_POINT_ZERO;
        ret += (TWENTY_POINT_ZERO * Math.sin(x * Math.PI) + FORTY_POINT_ZERO * Math.sin(x / THREE_POINT_ZERO * Math.PI))
                * TWO_POINT_ZERO / THREE_POINT_ZERO;
        ret += (ONE_HUNDRED_FIFTY_POINT_ZERO * Math.sin(x / TWELVE_POINT_ZERO * Math.PI)
                + THREE_HUNDRED_POINT_ZERO * Math.sin(x / THIRTY_POINT_ZERO * Math.PI)) * TWO_POINT_ZERO
                / THREE_POINT_ZERO;
        return ret;
    }

    /**
     * BD09转GCJ02
     * 
     * @param lng 经度(BD09坐标系)
     * @param lat 纬度(BD09坐标系)
     * @return 包含GCJ02经纬度的数组
     * @since 1.6.0
     * <AUTHOR>
     * @date 2025-05-08
     */
    private static final double BD_OFFSET_X_VALUE = 0.0065;
    private static final double BD_OFFSET_Y_VALUE = 0.006;
    private static final double BD_MAGIC_SIN_FACTOR_VALUE = 0.00002;
    private static final double BD_MAGIC_COS_FACTOR_VALUE = 0.000003;

    public static double[] bd09ToGcj02(double lng, double lat) {
        double x = lng - BD_OFFSET_X_VALUE;
        double y = lat - BD_OFFSET_Y_VALUE;
        double z = Math.sqrt(x * x + y * y) - BD_MAGIC_SIN_FACTOR_VALUE * Math.sin(y * Math.PI);
        double theta = Math.atan2(y, x) - BD_MAGIC_COS_FACTOR_VALUE * Math.cos(x * Math.PI);
        double gcjLng = z * Math.cos(theta);
        double gcjLat = z * Math.sin(theta);
        return new double[] { gcjLng, gcjLat };
    }

    /**
     * GCJ02转BD09
     * 
     * @param lng 经度(GCJ02坐标系)
     * @param lat 纬度(GCJ02坐标系)
     * @return 包含BD09经纬度的数组
     * @since 1.6.0
     * <AUTHOR>
     * @date 2025-05-08
     */
    public static double[] gcj02ToBd09(double lng, double lat) {
        double z = Math.sqrt(lng * lng + lat * lat) + BD_MAGIC_SIN_FACTOR_VALUE * Math.sin(lat * Math.PI);
        double theta = Math.atan2(lat, lng) + BD_MAGIC_COS_FACTOR_VALUE * Math.cos(lng * Math.PI);
        return new double[] { z * Math.cos(theta) + BD_OFFSET_X_VALUE, z * Math.sin(theta) + BD_OFFSET_Y_VALUE };
    }

    /**
     * WGS84转BD09
     * 
     * @param lng 经度(WGS84坐标系)
     * @param lat 纬度(WGS84坐标系)
     * @return 包含BD09经纬度的数组
     * @since 1.6.0
     * <AUTHOR>
     * @date 2025-05-08
     */
    public static double[] wgs84ToBd09(double lng, double lat) {
        double[] gcj02 = wgs84ToGcj02(lng, lat);
        return gcj02ToBd09(gcj02[0], gcj02[1]);
    }

    /**
     * BD09转WGS84
     * 
     * @param lng 经度(BD09坐标系)
     * @param lat 纬度(BD09坐标系)
     * @return 包含WGS84经纬度的数组
     * @since 1.6.0
     * <AUTHOR>
     * @date 2025-05-08
     */
    public static double[] bd09ToWgs84(double lng, double lat) {
        double[] gcj02 = bd09ToGcj02(lng, lat);
        return gcj02ToWgs84(gcj02[0], gcj02[1]);
    }
}