/*
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.commons.system;

import java.util.List;

/**
 * 网络接口信息类，用于封装网络接口的名称、MAC地址和IP地址列表。
 * <AUTHOR>
 * @date 2024/06/11
 * @since 1.5.0
 */
public class NetworkInterfaceInfo {
    /**
     * 网络接口名称
     */
    private final String name;
    /**
     * 网络接口的MAC地址
     */
    private final String macAddress;
    /**
     *  网络接口的IP地址列表
     */
    private final List<String> ipAddresses;

    /**
     * 构造函数，初始化网络接口信息。
     *
     * @param name 网络接口名称
     * @param macAddress 网络接口的MAC地址
     * @param ipAddresses 网络接口的IP地址列表
     * <AUTHOR>
     * @date 2024/06/11
     * @since 1.5.0
     */
    public NetworkInterfaceInfo(String name, String macAddress, List<String> ipAddresses) {
        this.name = name;
        this.macAddress = macAddress;
        this.ipAddresses = ipAddresses;
    }

    /**
     * 获取网络接口名称。
     *
     * @return 网络接口名称
     */
    public String getName() {
        return name;
    }

    /**
     * 获取网络接口的MAC地址。
     *
     * @return 网络接口的MAC地址
     */
    public String getMacAddress() {
        return macAddress;
    }

    /**
     * 获取网络接口的IP地址列表。
     *
     * @return 网络接口的IP地址列表
     */
    public List<String> getIpAddresses() {
        return ipAddresses;
    }
}
