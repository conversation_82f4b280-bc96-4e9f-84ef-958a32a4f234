/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.bytes;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeAll;

import static java.time.Duration.ofMillis;
import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTimeoutPreemptively;


/**
 * byte数组大小端单元测试类
 *
 * <AUTHOR>
 * @module 字节处理
 * @date 2023/5/18 19:27
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class EndianHandlerTest {

	private static final int iTestByteSize = 4;
	private static final byte[] arTestBytes = new byte[]{0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07};
	private static byte[] arBigBytes;
	@BeforeAll
	@DisplayName("生成1M {0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07} 大的byte数组，以便性能测试")
	@Tags({
			@Tag("@id:3"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/5/18")
	})
	public static void beforeAll_generateArBigBytes () {
		int iCount = 1024 * 1024;
		byte[] arTemp = {0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x10,0x11};
		arBigBytes = new byte[iCount * arTemp.length];
		
		for (int i = 0; i < iCount; i++) {
			System.arraycopy(arTemp, 0, arBigBytes, i * arTemp.length, arTemp.length);
		}
	}
	
    @Test
	@DisplayName("大的byte数组性能测试(与CPU性能有关)")
	@Tags({
			@Tag("@id:3"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/5/18")
	})
	void testEndian_sizeBeginEnd_bigBytesPerformance () {
		assertTimeoutPreemptively(ofMillis(1000), ()->EndianHandler.endian(arBigBytes, iTestByteSize, 0, arBigBytes.length));
	}

    @Test
    @DisplayName("根据4字节数、开始/结束位置转换byte数组大小端")
    @Tags({
            @Tag("@id:3"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/5/18")
    })
    void testEndian_sizeBeginEnd_4Size() {
        assertArrayEquals(new byte[]{0x03,0x02,0x01,0x00,0x07,0x06,0x05,0x04}, 
			EndianHandler.endian(arTestBytes, iTestByteSize, 0, arTestBytes.length));
    }

    @Test
    @DisplayName("根据3字节数、开始/结束位置转换byte数组大小端")
    @Tags({
            @Tag("@id:3"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/5/18")
    })
    void testEndian_sizeBeginEnd_3Size() {
        assertArrayEquals(new byte[]{0x02,0x01,0x00,0x05,0x04,0x03}, 
			EndianHandler.endian(new byte[]{0x00,0x01,0x02,0x03,0x04,0x05}, 3, 0, 6));
    }

    @Test
    @DisplayName("输入null数组应该抛出提示参数是无效的IllegalArgumentException异常")
    @Tags({
            @Tag("@id:3"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/5/18")
    })
    void testEndian_sizeBeginEnd_shouldThrowExceptionForNll() {
		assertThrows(IllegalArgumentException.class,
				() -> EndianHandler.endian(null, iTestByteSize, 0, arTestBytes.length),
				"bytes array parameter invalid");
    }


    @Test
    @DisplayName("输入Empty数组应该抛出提示参数是无效的IllegalArgumentException异常")
    @Tags({
            @Tag("@id:3"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/5/18")
    })
    void testEndian_sizeBeginEnd_shouldThrowExceptionForEmpty() {
		assertThrows(IllegalArgumentException.class,
				() -> EndianHandler.endian(new byte[0], iTestByteSize, 0, arTestBytes.length),
				"bytes array parameter invalid");
    }

    @Test
    @DisplayName("输入字节数<2应该抛出提示字节数是无效的IllegalArgumentException异常")
    @Tags({
            @Tag("@id:3"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/5/18")
    })
    void testEndian_sizeBeginEnd_shouldThrowExceptionForSize() {
		assertThrows(IllegalArgumentException.class,
				() -> EndianHandler.endian(arTestBytes, 1, 0, arTestBytes.length),
				"byteSize parameter invalid");
    }

    @Test
    @DisplayName("输入开始位置<0应该抛出提示开始位置是无效的IllegalArgumentException异常")
    @Tags({
            @Tag("@id:3"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/5/18")
    })
    void testEndian_sizeBeginEnd_shouldThrowExceptionForBegin() {
		assertThrows(IllegalArgumentException.class,
				() -> EndianHandler.endian(arTestBytes, iTestByteSize, -1, arTestBytes.length),
				"index parameter invalid");
    }

    @Test
    @DisplayName("输入结束位置>最大长度应该抛出提示结束位置是无效的IllegalArgumentException异常")
    @Tags({
            @Tag("@id:3"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/5/18")
    })
    void testEndian_sizeBeginEnd_shouldThrowExceptionForEnd() {
		assertThrows(IllegalArgumentException.class,
				() -> EndianHandler.endian(arTestBytes, iTestByteSize, 0, arTestBytes.length + 3),
				"index parameter invalid");
    }

    @Test
    @DisplayName("输入开始位置与结束位置长度数组不被字节数整除最大长度应该抛出提示长度是无效的IllegalArgumentException异常")
    @Tags({
            @Tag("@id:3"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/5/18")
    })
    void testEndian_sizeBeginEnd_shouldThrowExceptionForBeginEnd() {
		assertThrows(IllegalArgumentException.class,
				() -> EndianHandler.endian(arTestBytes, iTestByteSize, 0, arTestBytes.length - 1),
				"bytes.length parameter invalid");
    }


    @Test
    @DisplayName("根据字节数、开始位置转换byte数组大小端")
    @Tags({
            @Tag("@id:3"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/5/18")
    })
    void testEndian_sizeBegin() {
        assertArrayEquals(new byte[]{0x03,0x02,0x01,0x00,0x07,0x06,0x05,0x04}, 
			EndianHandler.endian(arTestBytes, iTestByteSize, 0));
    }

    @Test
    @DisplayName("根据字节数位置转换byte数组大小端")
    @Tags({
            @Tag("@id:3"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/5/18")
    })
    void testEndian_size() {
        assertArrayEquals(new byte[]{0x03,0x02,0x01,0x00,0x07,0x06,0x05,0x04}, 
			EndianHandler.endian(arTestBytes, iTestByteSize));
    }

    @Test
    @DisplayName("转换byte数组大小端")
    @Tags({
            @Tag("@id:3"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/5/18")
    })
    void testEndian() {
        assertArrayEquals(new byte[]{0x03,0x02,0x01,0x00,0x07,0x06,0x05,0x04}, 
			EndianHandler.endian(arTestBytes));
    }
}


