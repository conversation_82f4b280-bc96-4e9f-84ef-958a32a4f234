/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.files;


import com.snbc.bbpf.commons.collects.ObjectArrayCheck;
import com.snbc.bbpf.commons.strings.StringDigitCheck;
import com.snbc.bbpf.commons.strings.StringEmptyCheck;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.util.Enumeration;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.function.Function;

/**
 * 【文件处理模块】properties文件的查找操作
 * 1. 根据文件路径和key查找value值
 * 2. 根据文件路径匹配key（正则）是否存在
 * 3. 根据文件路径匹配key（正则）返回符合的key和value集合
 * 4. 根据文件路径返回key和value集合
 * 5. 根据文件路径修改key
 * 6. 根据文件路径修改指定key的value，如果不存在则新增。
 * 7. 根据文件路径删除key（可正则）和value
 *
 * <AUTHOR>
 * @module 文件处理模块
 * @date 2023-10-17 15:07
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class PropertiesUtils {

    private static final String VALUE_IS_NOT_NUMBER_TYPE = "key not exists or value is not number type";
    public static final int MAX_KEY_LENGTH = 2;

    /**
     * 根据文件路径和key查找Int value值
     *
     * @param filePath 文件路径
     * @param key      需要查找的key
     * @return IntValue
     * <AUTHOR>
     * @date 2023/10/17
     * @since 1.3.0
     */
    public static Integer getIntValue(String filePath, String key) {
        final String stringValue = getStringValue(filePath, key);
        if (!StringDigitCheck.isNumber(stringValue)) {
            throw new IllegalArgumentException(VALUE_IS_NOT_NUMBER_TYPE);
        }
        return Integer.parseInt(stringValue);

    }

    /**
     * 根据文件路径和key查找Float value值
     *
     * @param filePath 文件路径
     * @param key      需要查找的key
     * @return floatValue
     * <AUTHOR>
     * @date 2023/10/17
     * @since 1.3.0
     */
    public static Float getFloatValue(String filePath, String key) {
        final String stringValue = getStringValue(filePath, key);
        if (!StringDigitCheck.isFloat(stringValue)) {
            throw new IllegalArgumentException(VALUE_IS_NOT_NUMBER_TYPE);
        }
        return Float.parseFloat(stringValue);
    }

    /**
     * 根据文件路径和key查找Double value值
     *
     * @param filePath 文件路径
     * @param key      需要查找的key
     * @return doubleValue
     * <AUTHOR>
     * @date 2023/10/17
     * @since 1.3.0
     */
    public static Double getDoubleValue(String filePath, String key) {
        final String stringValue = getStringValue(filePath, key);
        if (!StringDigitCheck.isFloat(stringValue)) {
            throw new IllegalArgumentException(VALUE_IS_NOT_NUMBER_TYPE);
        }
        return Double.parseDouble(stringValue);
    }

    /**
     * 根据文件路径和key查找Boolean value值
     *
     * @param filePath 文件路径
     * @param key      需要查找的key
     * @return booleanValue
     * <AUTHOR>
     * @date 2023/10/17
     * @since 1.3.0
     */
    public static Boolean getBooleanValue(String filePath, String key) {
        return Boolean.parseBoolean(getStringValue(filePath, key));
    }

    /**
     * 根据文件路径和key查找value值
     *
     * @param filePath 文件路径
     * @param key      需要查找的key
     * @return String Value
     * <AUTHOR>
     * @date 2023/10/17
     * @since 1.3.0
     */
    public static String getStringValue(String filePath, String key) {
        return commonHandle(filePath, properties -> properties.getProperty(key));
    }

    /**
     * 根据文件路径和key查找value值(默认以逗号分割）
     *
     * @param filePath 文件路径
     * @param key      需要查找的key
     * @return String Value
     * <AUTHOR>
     * @date 2023/10/17
     * @since 1.3.0
     */
    public static String[] getArrayStringValue(String filePath, String key) {
        return StringUtils.split(getStringValue(filePath, key), ",");

    }

    /**
     * 根据文件路径和key查找value值
     *
     * @param filePath  文件路径
     * @param key       需要查找的key
     * @param delimiter 分隔符
     * @return String Value
     * <AUTHOR>
     * @date 2023/10/17
     * @since 1.3.0
     */
    public static String[] getArrayStringValue(String filePath, String key, String delimiter) {
        return StringUtils.split(getStringValue(filePath, key), delimiter);
    }

    /**
     * 根据文件路径匹配key（正则）返回符合的key和value集合
     *
     * @param filePath 文件路径
     * @param key      匹配的key
     * @return String Value
     * <AUTHOR>
     * @date 2023/10/17
     * @since 1.3.0
     */
    public static Map<String, String> getMap(String filePath, String key) {
        return commonHandle(filePath, properties -> {
            final Enumeration<Object> keys = properties.keys();
            Map<String, String> result = new LinkedHashMap<>();
            while (keys.hasMoreElements()) {
                String propertyKey = (String) keys.nextElement();
                if (StringEmptyCheck.isBlank(key) || propertyKey.contains(key) || propertyKey.matches(key)) {
                    result.put(propertyKey, properties.getProperty(propertyKey));
                }
            }
            return result;
        });
    }

    /**
     * 根据文件路径返回key和value集合
     *
     * @param filePath 文件路径
     * @return String Value
     * <AUTHOR>
     * @date 2023/10/17
     * @since 1.3.0
     */
    public static Map<String, String> getMap(String filePath) {
        return getMap(filePath, null);
    }

    /**
     * 根据文件路径匹配key（正则）是否存在
     *
     * @param filePath 文件路径
     * @param key      需要查找的key
     * @return String Value
     * <AUTHOR>
     * @date 2023/10/17
     * @since 1.3.0
     */
    public static boolean isExistsKey(String filePath, String key) {
        return commonHandle(filePath, properties -> {
            final Enumeration<Object> keys = properties.keys();
            while (keys.hasMoreElements()) {
                String propertyKey = (String) keys.nextElement();
                if (properties.contains(key) || propertyKey.matches(key)) {
                    return Boolean.TRUE;
                }
            }
            return Boolean.FALSE;
        });
    }

    /**
     * 判断文件是否存在和是否是文件
     *
     * @param filePath 文件路径
     * @return File
     * <AUTHOR>
     * @date 2023/10/27
     * @since 1.3.0
     */
    private static File validateFile(String filePath) {
        final File file = new File(filePath);
        if (!file.isFile() || !file.exists()) {
            throw new IllegalArgumentException("is not file or file not exitst");
        }
        return file;
    }

    /**
     * 根据文件路径删除key（可正则）和value
     *
     * @param filePath 文件路径
     * @param key      需要查找的key
     * @return String Value
     * <AUTHOR>
     * @date 2023/10/17
     * @since 1.3.0
     */
    public static void deleteByKey(String filePath, String key) {
        if (StringEmptyCheck.isBlank(key)) {
            throw new IllegalArgumentException("key is not allowed empty");
        }
        File file = validateFile(filePath);

        final List<String> lines = FileUtils.readLines(file);
        final Iterator<String> iterator = lines.iterator();
        while (iterator.hasNext()) {
            String line = iterator.next();
            final String[] keyValues = StringUtils.split(line, "=");
            if (!isValideLine(line, keyValues)) {
                continue;
            }
            String trimkey = StringUtils.trim(keyValues[0]);
            if (key.equals(trimkey) || trimkey.matches(key)) {
                iterator.remove();
            }
        }

        FileUtils.writeLines(file, lines);

    }

    /**
     * 验证properties行是否有效
     *
     * @param line      行
     * @param keyValues 分割的key value
     * @return 是否有效行
     * <AUTHOR>
     * @date 2023/10/17
     * @since 1.3.0
     */
    private static boolean isValideLine(String line, String[] keyValues) {
        if (StringUtils.startsWith(StringUtils.trim(line), "#")) {
            return false;
        } else {
            if (ObjectArrayCheck.isEmpty(keyValues) || keyValues.length < MAX_KEY_LENGTH) {
                return false;
            }
            String trimkey = StringUtils.trim(keyValues[0]);
            if (StringEmptyCheck.isBlank(trimkey)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 根据文件路径修改key
     *
     * @param filePath 文件路径
     * @param oldkey   旧key
     * @param newKey   新key
     * @return
     * <AUTHOR>
     * @date 2023/10/17
     * @since 1.3.0
     */
    public static boolean updateKey(String filePath, String oldkey, String newKey) {
        if (StringUtils.isAnyBlank(oldkey, newKey)) {
            throw new IllegalArgumentException("key is not allowed empty");
        }
        if (isExistsKey(filePath, newKey)) {
            throw new IllegalArgumentException("newKey exits");
        }
        File file = validateFile(filePath);
        final List<String> lines = FileUtils.readLines(file);
        final Iterator<String> iterator = lines.iterator();
        boolean isExists = false;
        int index = 0;
        String value = "";
        while (iterator.hasNext()) {
            String line = iterator.next();
            final String[] keyValues = StringUtils.split(line, "=");
            if (!isValideLine(line, keyValues)) {
                index++;
                continue;
            }
            String trimkey = StringUtils.trim(keyValues[0]);
            if (oldkey.equals(trimkey)) {
                value = keyValues[1];
                isExists = true;
                break;
            }
            index++;
        }
        if (isExists) {
            lines.set(index, newKey + " = " + value);
            FileUtils.writeLines(file, lines);
        } else {
            throw new IllegalArgumentException("oldKey not exits");
        }

        return true;

    }

    /**
     * 根据文件路径修改指定key的value，如果不存在则新增。
     *
     * @param filePath 文件路径
     * @param key      需要查找的key
     * @param newValue 新值
     * @return
     * <AUTHOR>
     * @date 2023/10/17
     * @since 1.3.0
     */
    public static void updateOrCreate(String filePath, String key, String newValue) {
        if (StringUtils.isAnyBlank(key, newValue)) {
            throw new IllegalArgumentException("key or value is not allowed empty");
        }

        final File file = validateFile(filePath);
        final List<String> lines = FileUtils.readLines(file);
        final Iterator<String> iterator = lines.iterator();
        boolean isExists = false;
        int index = 0;
        while (iterator.hasNext()) {
            String line = iterator.next();
            final String[] keyValues = StringUtils.split(line, "=");
            if (!isValideLine(line, keyValues)) {
                index++;
                continue;
            }
            String trimkey = StringUtils.trim(keyValues[0]);
            if (key.equals(trimkey)) {
                isExists = true;
                break;

            }
            index++;
        }
        if (isExists) {
            lines.set(index, key + " = " + newValue);
        } else {
            lines.add(key + " = " + newValue);
        }
        FileUtils.writeLines(file, lines);


    }


    /**
     * 公共根据文件获取properties
     *
     * @param filePath 文件路径
     * @param function function
     * @return T
     * <AUTHOR>
     * @date 2023/10/17
     * @since 1.3.0
     */
    private static <T> T commonHandle(String filePath, Function<Properties, T> function) {
        File file = validateFile(filePath);
        try (BufferedInputStream bufferedInputStream = new BufferedInputStream(new FileInputStream(file))) {
            Properties properties = new Properties();
            properties.load(bufferedInputStream);
            return function.apply(properties);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
