/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.strings;

/**
 * 字符串修剪处理类
 * <p>对过长的字符串根据指定长度进行修剪，若字符串长度不超过指定长度，保留全部</p>
 * <p>功能列表</p>
 * <p>1.字符串长度超过指定长度，保留左部分，右侧补修饰符（右修剪） </p>
 * <p>2.字符串长度超过指定长度，保留右部分，左侧补修饰符（左修剪） </p>
 * <p>3.字符串长度超过指定长度，保留中间部分，两侧补修饰符（两侧修剪） </p>
 *
 * <AUTHOR>
 * @module 字符串处理
 * @date 2023/4/23 19:18
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class ShrinkHandler {

    private static final String DEFAULT_PADDING = "...";

    private ShrinkHandler() {
        throw new IllegalStateException("Utility class");
    }
    /**
     * 检查参数
     *
     * @param str          待处理的字符串
     * @param length       保留长度，要求>0
     * @param leftOffset   左偏移量
     * @param leftPadding  左缩略符
     * @param rightOffset  右偏移量
     * @param rightPadding 右缩略符
     * @throws IllegalArgumentException 如果输入字符串为空、保留长度<=0、偏移无效，填充字符为null
     * <AUTHOR>
     * @date 2023/4/23
     * @since 1.0.0
     */
    private static void checkParameter(String str, int length,
                                       int leftOffset, String leftPadding,
                                       int rightOffset, String rightPadding) {
        if (StringEmptyCheck.isEmpty(str)) {
            throw new IllegalArgumentException("Input parameter is empty");
        }

        if (length < 1) {
            throw new IllegalArgumentException("Length parameter invalid");
        }

        if (null == leftPadding || null == rightPadding) {
            throw new IllegalArgumentException("Padding is null");
        }

        if (leftOffset < 0 || rightOffset < 0) {
            throw new IllegalArgumentException("Input parameter < 0 invalid");
        }

        if (leftOffset >= str.length() || rightOffset >= str.length()) {
            throw new IllegalArgumentException("Input parameter > length invalid");
        }
        if ((leftOffset + rightOffset) >= str.length()) {
            throw new IllegalArgumentException("Input parameter invalid");
        }
    }


    /**
     * 字符串按指定保留长度进行右侧缩略，右侧补修饰符（右修剪），若字符串长度不超过指定长度，保留全部
     *
     * <p>例如：输入"12345678900987654321", 保留长度4 =>   返回"1234..."</p>
     *
     * @param str    待处理的字符串
     * @param length 保留长度，要求>0
     * @return 处理后的字符
     * @throws IllegalArgumentException 如果输入字符串为空、保留长度<1
     * <AUTHOR>
     * @date 2023/4/23
     * @since 1.0.0
     */
    public static String shrinkRight(String str, int length) {
        checkParameter(str, length, 0, "", 0, "");
        // 如果比指定长度短，直接返回
        if (str.length() <= (length + DEFAULT_PADDING.length())) {
            return str;
        }
        //非循环使用“+”拼接，性能不比StringBuilder差
        return str.substring(0, length) + DEFAULT_PADDING;
    }


    /**
     * 字符串按指定保留长度进行左侧缩略，左侧补修饰符（左修剪），若字符串长度不超过指定长度，保留全部
     *
     * <p>例如：输入"12345678900987654321", 保留长度4 =>   返回"...4321"</p>
     *
     * @param str    待处理的字符串
     * @param length 保留长度，要求>0
     * @return 处理后的字符
     * @throws IllegalArgumentException 如果输入字符串为空、保留长度<1
     * <AUTHOR>
     * @date 2023/4/23
     * @since 1.0.0
     */
    public static String shrinkLeft(String str, int length) {
        checkParameter(str, length, 0, "", 0, "");
        // 如果比指定长度短，直接返回
        if (str.length() <= (length + DEFAULT_PADDING.length())) {
            return str;
        }
        return DEFAULT_PADDING + str.substring(str.length() - length);
    }

    /**
     * 字符串按左偏移、指定保留长度进行两侧缩略，两侧补修饰符（两侧修剪），若字符串长度不超过指定长度，保留全部
     *
     * <p>例如：输入"12345678900987654321", 左偏移量4，保留长度4 =>   返回"...5678..."</p>
     *
     * @param str        待处理的字符串
     * @param length     保留长度，要求>0
     * @param leftOffset 左偏移量，<4则保留左侧
     * @return 处理后的字符
     * @throws IllegalArgumentException 如果输入字符串为空、保留长度<=0、偏移无效
     * <AUTHOR>
     * @date 2023/4/23
     * @since 1.0.0
     */
    public static String shrinkLR(String str, int length, int leftOffset) {
        checkParameter(str, length, leftOffset, "", 0, "");
        if ((length + leftOffset) > str.length()) {
            return shrinkLR(str, leftOffset, DEFAULT_PADDING, 0, "");
        } else {
            return shrinkLR(str, leftOffset, DEFAULT_PADDING,
                    str.length() - length - leftOffset, DEFAULT_PADDING);
        }
    }

    /**
     * 字符串按左右偏移进行两侧缩略，两侧补修饰符（两侧修剪）
     *
     * <p>例如：输入"12345678900987654321", 字符串左偏移量4,左缩略符"++",</p>
     * <p>右偏移量5,右缩略符"===" =>   返回"++56789009876==="</p>
     *
     * @param str          待处理的字符串
     * @param leftOffset   左偏移量
     * @param leftPadding  左缩略符
     * @param rightOffset  右偏移量
     * @param rightPadding 右缩略符
     * @return 处理后的字符
     * @throws IllegalArgumentException 如果输入字符串为空、保留长度<=0、偏移无效
     * <AUTHOR>
     * @date 2023/4/23
     * @since 1.0.0
     */
    public static String shrinkLR(String str,
                                  int leftOffset, String leftPadding,
                                  int rightOffset, String rightPadding) {
        checkParameter(str, 1, leftOffset, leftPadding, rightOffset, rightPadding);
        String strTemp = str;
        if (leftOffset > leftPadding.length()) {
            strTemp = leftPadding + str.substring(leftOffset);
        }

        if (rightOffset > rightPadding.length()) {
            strTemp = strTemp.substring(0, strTemp.length() - rightOffset) + rightPadding;
        }

        return strTemp;
    }

}
