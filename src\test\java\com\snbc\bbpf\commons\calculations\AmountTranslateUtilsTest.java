/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.calculations;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

/**
 * 大小写金额互相转换单元测试类
 *
 * <AUTHOR>
 * @module 字节处理
 * @date 2023/7/20 19:40
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class AmountTranslateUtilsTest {

    @Test
	@DisplayName("int型小写金额转换大写金额")
	@Tags({
			@Tag("@id:39"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/7/20")
	})
    void testToUpper() {
        assertEquals("贰仟伍佰陆拾叁", AmountTranslateUtils.toUpper(2563));
    }
	
    @Test
	@DisplayName("最大int型小写金额转换大写金额")
	@Tags({
			@Tag("@id:39"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/7/20")
	})
    void testToUpper_intMax() {
        assertEquals("贰拾壹亿肆仟柒佰肆拾捌万叁仟陆佰肆拾柒", 
			AmountTranslateUtils.toUpper(Integer.MAX_VALUE));
    }

    @Test
	@DisplayName("int型小写金额转换大写金额")
	@Tags({
			@Tag("@id:39"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/7/20")
	})
    void testToUpper_0() {
        assertEquals("零", AmountTranslateUtils.toUpper(0));
    }

    @Test
	@DisplayName("小于0的int型小写金额转换大写金额")
	@Tags({
			@Tag("@id:39"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/7/20")
	})
    void testToUpper_intLessThan0ShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() ->AmountTranslateUtils.toUpper(-10),
				"Less than 0!");
    }

    @Test
	@DisplayName("小于0的long型小写金额转换大写金额")
	@Tags({
			@Tag("@id:39"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/7/20")
	})
    void testToUpper_longLessThan0ShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() ->AmountTranslateUtils.toUpper(-20L),
				"Less than 0!");
    }

    @Test
	@DisplayName("最大long型小写金额转换大写金额")
	@Tags({
			@Tag("@id:39"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/7/20")
	})
    void testToUpper_longMax() {
        assertEquals("玖佰贰拾贰京叁仟叁佰柒拾贰兆零叁佰陆拾捌亿伍仟肆佰柒拾柒万伍仟捌佰零柒", 
			AmountTranslateUtils.toUpper(Long.MAX_VALUE));
    }

    @Test
	@DisplayName("long型小写金额转换大写金额")
	@Tags({
			@Tag("@id:39"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/7/20")
	})
    void testToUpper_9000300450L() {
        assertEquals("玖拾亿零叁拾万零肆佰伍拾", AmountTranslateUtils.toUpper(9000300450L));
    }

	@Test
	@DisplayName("long型小写金额转换大写金额")
	@Tags({
			@Tag("@id:39"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/7/20")
	})
	void testToUpper_63702034801L() {String sss = AmountTranslateUtils.toUpper(63702034801L);
		assertEquals("陆佰叁拾柒亿零贰佰零叁万肆仟捌佰零壹", AmountTranslateUtils.toUpper(63702034801L));
	}

    @Test
	@DisplayName("大写金额转换long型小写金额")
	@Tags({
			@Tag("@id:39"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/7/20")
	})
    void testToLong() {
        assertEquals(63702034801L, AmountTranslateUtils.toLong("陆佰叁拾柒亿零贰佰叁万肆仟捌佰零壹"));
    }

	@Test
	@DisplayName("大写金额转换long型小写金额")
	@Tags({
			@Tag("@id:39"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/7/20")
	})
	void testToLong_60002004800L() {
		assertEquals(60002004800L, AmountTranslateUtils.toLong("陆佰亿零贰佰万肆仟捌佰"));
	}

	@Test
	@DisplayName("大写金额转换int型小写金额")
	@Tags({
			@Tag("@id:39"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/7/20")
	})
    void testToInt() {
        assertEquals(309034901, AmountTranslateUtils.toInt("叁亿玖佰叁万肆仟玖佰零壹"));
    }
	
    @Test
	@DisplayName("大写金额转换最大int型小写金额")
	@Tags({
			@Tag("@id:39"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/7/20")
	})
    void testToInt_intMax() {
        assertEquals(Integer.MAX_VALUE, 
			AmountTranslateUtils.toInt("贰拾壹亿肆仟柒佰肆拾捌万叁仟陆佰肆拾柒"));
    }

    @Test
	@DisplayName("无效的大写金额转换int型小写金额抛出无效字符串")
	@Tags({
			@Tag("@id:39"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/7/20")
	})
    void testToInt_invalidShouldThrowExceptionForInvalid() {
		assertThrows(IllegalArgumentException.class,
				() ->AmountTranslateUtils.toInt("肆亿壹仟零叁亿零柒"),
				"String is invalid!");
    }

    @Test
	@DisplayName("太大的大写金额转换int型小写金额抛出溢出异常")
	@Tags({
			@Tag("@id:39"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/7/20")
	})
    void testToInt_tooBigShouldThrowExceptionForOverflow() {
		assertThrows(IllegalArgumentException.class,
				() ->AmountTranslateUtils.toInt("肆兆壹仟零叁亿零柒"),
				"String is Overflow!");
    }

    @Test
	@DisplayName("null的大写金额转换int型小写金额抛出无效字符串")
	@Tags({
			@Tag("@id:39"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/7/20")
	})
    void testToInt_nullShouldThrowExceptionForInvalid() {
		assertThrows(IllegalArgumentException.class,
				() ->AmountTranslateUtils.toInt(null),
				"String is empty!");
    }

    @Test
	@DisplayName("empty的大写金额转换int型小写金额抛出无效字符串")
	@Tags({
			@Tag("@id:39"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/7/20")
	})
    void testToInt_emptyShouldThrowExceptionForInvalid() {
		assertThrows(IllegalArgumentException.class,
				() ->AmountTranslateUtils.toInt(""),
				"String is empty!");
    }

    @Test
	@DisplayName("blank的大写金额转换int型小写金额抛出无效字符串")
	@Tags({
			@Tag("@id:39"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/7/20")
	})
    void testToInt_blankShouldThrowExceptionForInvalid() {
		assertThrows(IllegalArgumentException.class,
				() ->AmountTranslateUtils.toInt("  "),
				"String is empty!");
    }

    @Test
	@DisplayName("太大的大写金额转换long型小写金额抛出溢出异常")
	@Tags({
			@Tag("@id:39"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/7/20")
	})
    void testToLong_tooBigShouldThrowExceptionForOverflow() {
		assertThrows(IllegalArgumentException.class,
				() ->AmountTranslateUtils.toLong("玖仟玖佰贰拾贰京叁仟叁佰柒拾贰兆零叁佰陆拾捌亿"),
				"String is Overflow!");
    }

	
    @Test
	@DisplayName("大写金额转换最大long型小写金额")
	@Tags({
			@Tag("@id:39"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/7/20")
	})
    void testToLong_longMax() {
        assertEquals(Long.MAX_VALUE, 
			AmountTranslateUtils.toLong("玖佰贰拾贰京叁仟叁佰柒拾贰兆零叁佰陆拾捌亿伍仟肆佰柒拾柒万伍仟捌佰零柒"));
    }

}

