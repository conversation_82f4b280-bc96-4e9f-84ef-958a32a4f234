/*
 * 版权所有 2009-2024山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.files;

/**
 * 文件权限枚举类
 *
 * <AUTHOR>
 * @module 文件处理
 * @date 2024/8/9 11:32
 * @copyright 2024 山东新北洋信息技术股份有限公司. All rights reserved
 */
public enum FilePermissionType {
    READ("read"),
    WRITE("write"),
    EXECUTE("execute");

    private final String permission;

    /**
     * 构造方法
     *
     * @param permission 权限名称
     * @since 1.0.0
     * <AUTHOR>
     * @date 2024/8/9
     */
    FilePermissionType(String permission) {
        this.permission = permission;
    }

    /**
     * 获取枚举的权限名称
     *
     * @return java.lang.String
     * @since 1.0.0
     * <AUTHOR>
     * @date 2024/8/9
     */
    public String getPermission() {
        return permission;
    }

}
