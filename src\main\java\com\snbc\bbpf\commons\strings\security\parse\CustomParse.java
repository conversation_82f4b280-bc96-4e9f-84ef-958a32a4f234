/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.strings.security.parse;

/**
 * @ClassName: CustomParse
 * 自定义脱敏解析器
 * 支持指定保留前几位和后几位，其余用指定符号替换
 * @module: 字符脱敏
 * @Author: yangweipeng
 * @date: 2025/07/28
 * @copyright 2025 山东新北洋信息技术股份有限公司. All rights reserved
 * @since 1.7.0
 */
public class CustomParse implements IDesensitizedParse {

    private final int prefixKeep;
    private final int suffixKeep;
    private final String maskSymbol;

    /**
     * 构造函数
     * @param prefixKeep 保留前几位
     * @param suffixKeep 保留后几位
     * @param maskSymbol 脱敏符号
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/28
     */
    public CustomParse(int prefixKeep, int suffixKeep, String maskSymbol) {
        this.prefixKeep = Math.max(0, prefixKeep);
        this.suffixKeep = Math.max(0, suffixKeep);
        this.maskSymbol = maskSymbol != null && !maskSymbol.isEmpty() ? maskSymbol : "*";
    }

    /**
     * 默认构造函数（保留前3位后4位，用*替换）
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/28
     */
    public CustomParse() {
        this(3, 4, "*");
    }

    /**
     * 自定义脱敏解析
     * 保留指定的前几位和后几位，中间部分用指定符号替换
     * 
     * 处理规则：
     * 1. 如果字符串长度 <= (prefixKeep + suffixKeep)，则不进行脱敏
     * 2. 如果prefixKeep或suffixKeep为0，则相应部分不保留
     * 3. 中间部分的长度 = 原字符串长度 - prefixKeep - suffixKeep
     * 4. 中间部分全部用maskSymbol替换
     * 
     * @param srcStr 原始字符串
     * @return 脱敏后的字符串
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/28
     */
    @Override
    public String parseString(String srcStr) {
        if (srcStr == null || srcStr.isEmpty()) {
            return srcStr;
        }

        int length = srcStr.length();
        
        // 如果字符串长度不足以进行脱敏，直接返回原字符串
        if (length <= (prefixKeep + suffixKeep)) {
            return srcStr;
        }

        StringBuilder result = new StringBuilder();
        
        // 添加前缀保留部分
        if (prefixKeep > 0) {
            result.append(srcStr, 0, prefixKeep);
        }
        
        // 添加中间脱敏部分
        int maskLength = length - prefixKeep - suffixKeep;
        for (int i = 0; i < maskLength; i++) {
            result.append(maskSymbol);
        }
        
        // 添加后缀保留部分
        if (suffixKeep > 0) {
            result.append(srcStr.substring(length - suffixKeep));
        }
        
        return result.toString();
    }

    /**
     * 获取保留前几位
     * @return 保留前几位
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/28
     */
    public int getPrefixKeep() {
        return prefixKeep;
    }

    /**
     * 获取保留后几位
     * @return 保留后几位
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/28
     */
    public int getSuffixKeep() {
        return suffixKeep;
    }

    /**
     * 获取脱敏符号
     * @return 脱敏符号
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/28
     */
    public String getMaskSymbol() {
        return maskSymbol;
    }

    @Override
    public String toString() {
        return "CustomParse{" +
                "prefixKeep=" + prefixKeep +
                ", suffixKeep=" + suffixKeep +
                ", maskSymbol='" + maskSymbol + '\'' +
                '}';
    }
}
