/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.crypt;

import com.snbc.bbpf.commons.objs.ObjectEmptyCheck;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESedeKeySpec;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.Security;

/**
 * cipher加解密公共封装类
 *
 * <AUTHOR>
 * @module
 * @date 2023/11/26 22:04
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class CipherUtils {

    static {
        //默认的安全程序只支持少数算法，设置BC安全提供程序扩展支持SM4等算法和PKCS7PADDING等填充方式
        Security.addProvider(new BouncyCastleProvider());
    }

    private CipherUtils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 创建密钥对象
     *
     * @param key                不同算法密钥长度要求不同
     * @param symmetricAlgorithm Algorithm枚举类
     * @return Key
     * <AUTHOR>
     * @date 2023/11/27
     * @since 1.4.0
     */
    private static SecretKey getSecretKey(byte[] key, SymmetricAlgorithm symmetricAlgorithm) throws Exception {
        SecretKey secretKey;
        if (symmetricAlgorithm == SymmetricAlgorithm.DES_3DES) {
            SecretKeyFactory factory = SecretKeyFactory.getInstance(symmetricAlgorithm.getValue());
            DESedeKeySpec spec = new DESedeKeySpec(key);
            secretKey = factory.generateSecret(spec);
        } else {
            secretKey=new SecretKeySpec(key, symmetricAlgorithm.getValue());
        }
        return secretKey;
    }

    /**
     * 生成Cipher，获取指定算法
     *
     * @param symmetricAlgorithm Algorithm枚举类
     * @param workingMode        WorkingMode枚举类
     * @param padding            Padding枚举类
     * @param providerName       安全提供者名称 如BouncyCastleProvider.PROVIDER_NAME
     * @return Cipher
     * @throws NoSuchPaddingException   没有该填充方式
     * @throws NoSuchAlgorithmException 没有该算法
     * <AUTHOR>
     * @date 2023/11/27
     * @since 1.4.0
     */
    private static Cipher getCipher(SymmetricAlgorithm symmetricAlgorithm, WorkingMode workingMode, Padding padding, String providerName)
            throws NoSuchPaddingException, NoSuchAlgorithmException, NoSuchProviderException {

        return Cipher.getInstance(symmetricAlgorithm.getValue() + "/" + workingMode.getValue() + "/" + padding.getValue(),providerName);
    }

    /**
     * 参数校验
     *
     * @param symmetricAlgorithm Algorithm枚举
     * @param workingMode        WorkingMode枚举
     * @param padding            Padding枚举
     * @param data               加解密文本
     * @return void
     * @throws
     * <AUTHOR>
     * @date 2023/11/27
     * @since 1.4.0
     */
    private static void checkParameters(SymmetricAlgorithm symmetricAlgorithm, WorkingMode workingMode, Padding padding, byte[] data) {
        if (ObjectEmptyCheck.isEmpty(symmetricAlgorithm)) {
            throw new IllegalArgumentException("algorithm is null");
        }
        if (ObjectEmptyCheck.isEmpty(workingMode)) {
            throw new IllegalArgumentException("workingMode is null");
        }
        if (ObjectEmptyCheck.isEmpty(padding)) {
            throw new IllegalArgumentException("padding is null");
        }
        if (null == data || data.length == 0) {
            throw new IllegalArgumentException("data is null");
        }
    }

    /**
     * 对称加解密操作（有初始化向量）
     *
     * @param opMode      加解密类型 加密为Cipher.ENCRYPT_MODE，解密为Cipher.DECRYPT_MODE
     * @param symmetricAlgorithm         Algorithm枚举
     * @param workingMode WorkingMode枚举
     * @param padding     Padding枚举
     * @param key         加解密密钥
     * @param keyIv       初始向量
     * @param data        加解密数据
     * @return byte
     * @throws
     * <AUTHOR>
     * @date 2023/11/27
     * @since 1.4.0
     */
    public static byte[] doFinal(int opMode, SymmetricAlgorithm symmetricAlgorithm, WorkingMode workingMode, Padding padding,
                                 byte[] key, byte[] keyIv, byte[] data) {

        try {

            //参数校验
            checkParameters(symmetricAlgorithm, workingMode, padding, data);
            //获取SecretKey对象
            SecretKey secretKey = getSecretKey(key, symmetricAlgorithm);
            //获取指定转换的密码对象Cipher（参数：算法/工作模式/填充模式/安全程序名称）
            //指定加密提供者为BouncyCastleProvider
            Cipher cipher = getCipher(symmetricAlgorithm, workingMode, padding, BouncyCastleProvider.PROVIDER_NAME);
            //创建向量参数规范也就是初始化向量
            IvParameterSpec ips = new IvParameterSpec(keyIv);
            //用密钥和一组算法参数规范初始化此Cipher对象
            cipher.init(opMode, secretKey,ips);
            //执行加解密操作
            return cipher.doFinal(data);
        } catch (Exception ex) {
            throw new RuntimeException("cipher do final failure", ex);
        }
    }

    /**
     * 对称加解密操作（无初始化向量）
     *
     * @param opMode             加解密类型 加密为Cipher.ENCRYPT_MODE，解密为Cipher.DECRYPT_MODE
     * @param symmetricAlgorithm Algorithm枚举
     * @param workingMode        WorkingMode枚举
     * @param padding            Padding枚举
     * @param key                加解密密钥
     * @param data               加解密数据
     * @return byte
     * @throws
     * <AUTHOR>
     * @date 2023/11/27
     * @since 1.4.0
     */
    public static byte[] doFinal(int opMode, SymmetricAlgorithm symmetricAlgorithm, WorkingMode workingMode, Padding padding, byte[] key, byte[] data) {
        try {

            checkParameters(symmetricAlgorithm, workingMode, padding, data);
            //获取SecretKey对象
            Key secretKey = getSecretKey(key, symmetricAlgorithm);
            //获取指定转换的密码对象Cipher（参数：算法/工作模式/填充模式/安全程序名称）
            //指定加密提供者为BouncyCastleProvider
            Cipher cipher = getCipher(symmetricAlgorithm, workingMode, padding, BouncyCastleProvider.PROVIDER_NAME);
            //创建向量参数规范也就是初始化向量
            //用密钥和一组算法参数规范初始化此Cipher对象（加密模式）
            cipher.init(opMode, secretKey);
            //执行加解密操作
            return cipher.doFinal(data);
        } catch (Exception ex) {
            throw new RuntimeException("cipher do final failure", ex);
        }
    }
}
