/*
 * 版权所有 2009-2024 山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.files;

import com.snbc.bbpf.commons.strings.StringEmptyCheck;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;


/**
 * 对文件进行gzip处理类
 * <p>Gzip文件解压和压缩工具类</p>
 * <p>1.将指定文件解压到指定目录，默认目录不存在进行创建，如果文件已经存在，默认覆盖保存。</p>
 * <p>2.将指定文件压缩成指定文件，如果传入是目录，需要抛出异常，不支持目录。</p>
 *
 * 使用范例：
 *  解压指定文件：GzipUtils.decompress("D:\demo\idea_settings.txt.gz", "d:\zipdemo")
 *  压缩指定文件：GzipUtils.compress("D:\demo\idea_settings.txt","D:\zipdemo\new.txt.gz")
 *
 * <AUTHOR>
 * @date 2024/6/3 19:36
 * @since 1.5.0
 */
public final class GzipUtils {

    public static final int MAX_COMPRESS_LEVEL = 9;

    private GzipUtils(){
        throw new IllegalStateException("Utility class");
    }

    private static final int BUFF_SIZE = 1024;

    private static final String SUPPORT_FILE_SUFFIX_NAME = ".gz";

    /**
     * <p>将指定文件解压到指定目录，默认目录不存在进行创建，如果文件已经存在，默认覆盖保存</p>
     *
     * @param gzipFilePath 待解压文件地址
     * @param destDir 解压目标文件夹
     * <AUTHOR>
     * @date 2024/06/03 21:00
     * @since 1.5.0
     */
    public static void decompress(String gzipFilePath, String destDir) {
        File gzipFile = validateArgusForDecompress(gzipFilePath, destDir);
        //创建解压根目录  与压缩文件同名
        String gzipFileName = gzipFile.getName();
        gzipFileName = gzipFileName.substring(0, gzipFileName.indexOf(SUPPORT_FILE_SUFFIX_NAME));
        destDir = destDir + File.separator + gzipFileName;
        File distDirFile = new File(destDir);

		try (GZIPInputStream gzipIn = new GZIPInputStream(new FileInputStream(gzipFile));
		FileOutputStream fos = new FileOutputStream(distDirFile)) {
			byte[] buffer = new byte[BUFF_SIZE];
			int length = 0;
			while ((length = gzipIn.read(buffer)) >= 0) {
				fos.write(buffer, 0, length);
			}
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
		
    }

    /**
     * <p>校验解压方法参数</p>
     *
     * @param gzipFilePath 待解压文件地址
     * @param destDir 解压目标文件夹
     * @return java.io.File 待解压文件对象
     * <AUTHOR>
     * @date 2024/06/03 21:00
     * @since 1.5.0
     */
    private static File validateArgusForDecompress(String gzipFilePath, String destDir) {
        if (StringEmptyCheck.isBlank(gzipFilePath)) {
            throw new IllegalArgumentException("The Source Gzip File Path is Blank");
        }
        if (StringEmptyCheck.isBlank(destDir)) {
            throw new IllegalArgumentException("The Destination Directory is Blank");
        }
        if (!FileTypeChecker.isFileType(gzipFilePath,FileType.GZ)) {
            throw new IllegalArgumentException("The Source File Suffix Name is Error ,Only Support .gz Type File");
        }
        File destDirFile = new File(destDir);
        if (!destDirFile.exists() && !destDirFile.mkdir()) {
            throw new IllegalArgumentException("The Destination Directory is Not mkdir");
        }
        if(!destDirFile.isDirectory()){
            throw new IllegalArgumentException("The Source Path is Not Directory");
        }

        File gzipFile = new File(gzipFilePath);
        if (!gzipFile.exists()) {
            throw new IllegalArgumentException("The Source Gzip File Not Exists");
        }
        return gzipFile;
    }


    /**
     * <p>将指定文件压缩成指定文件，默认压缩比最高级别，可以指定。如果传入是目录，需要抛出异常，不支持目录</p>
     *
     * @param sourceFilePath 待压缩文件路径 例如： D:\zipdemo\\unzip.txt
     * @param gzipFilePath 压缩目标文件路径（含文件名） 例子： D:\\zipdemo\\new.txt.gz
     * <AUTHOR>
     * @date 2024/06/03 21:00
     * @since 1.5.0
     */
    public static void compress(String sourceFilePath, String gzipFilePath){
        File sourceFile = validateArgusForCompress(sourceFilePath, gzipFilePath);
		try (FileInputStream fis = new FileInputStream(sourceFile);
			GZIPOutputStream gzipOut = new GZIPOutputStream(new FileOutputStream(gzipFilePath))) {
			byte[] buffer = new byte[BUFF_SIZE];
			int length;
			while ((length = fis.read(buffer)) >= 0) {
				gzipOut.write(buffer, 0, length);
			}
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
    }

    /**
     * <p>校验压缩文件方法入参</p>
     *
     * @param sourceFilePath 待压缩文件路径 例如： D:\zipdemo\\unzip.txt
     * @param gzipFilePath 压缩目标文件路径（含文件名） 例子： D:\\zipdemo\\new.txt.gz
     * @return java.io.File 带压缩文件File对象
     * <AUTHOR>
     * @date 2024/06/03 21:00
     * @since 1.5.0
     */
    private static File validateArgusForCompress(String sourceFilePath, String gzipFilePath) {
        if (StringEmptyCheck.isBlank(sourceFilePath)) {
            throw new IllegalArgumentException("The Source File is Blank");
        }
        if (StringEmptyCheck.isBlank(gzipFilePath)) {
            throw new IllegalArgumentException("The Destination File Path is Blank");
        }
        if (!gzipFilePath.endsWith(SUPPORT_FILE_SUFFIX_NAME)) {
            throw new IllegalArgumentException("The Destination File Suffix Name is Error ,Only Support .gz Type File");
        }
        File sourceFile = new File(sourceFilePath);
        if(!sourceFile.exists()){
            throw new IllegalArgumentException("The Source Path is Not Exists");
        }
        if(!sourceFile.isFile()){
            throw new IllegalArgumentException("The Source Path is Not File");
        }
        File gzipFile = new File(gzipFilePath);
        File parentFile = gzipFile.getParentFile();
        if (!parentFile.exists()) {
            throw new IllegalArgumentException("The Destination File Path is illegal");
        }
        return sourceFile;
    }



}
