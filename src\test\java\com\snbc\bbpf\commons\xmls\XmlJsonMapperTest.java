/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.xmls;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;


import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * JsonXml测试类
 *
 * <AUTHOR>
 * @module xml模块
 * @date 2023-09-07 16:13
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */

class XmlJsonMapperTest {


    @DisplayName("测试XML字符串为空")
    @Tags({
            @Tag("@id:54"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testUnmarshall_is_empty() {
        RuntimeException thrown = Assertions.assertThrows(RuntimeException.class,
                () -> XmlJsonMapper.marshall("", VemInfo.class));
        Assertions.assertNotNull(thrown);
    }
    @DisplayName("测试XML字符串为null")
    @Tags({
            @Tag("@id:54"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testUnmarshall_is_null() {
        RuntimeException thrown = Assertions.assertThrows(RuntimeException.class,
                () -> XmlJsonMapper.marshall(null, VemInfo.class));
        Assertions.assertNotNull(thrown);
    }
    @DisplayName("测试XML字符串为错误格式")
    @Tags({
            @Tag("@id:54"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testUnmarshall_is_error() {
        final String xmlStr = "<SNBCVem id=\"12\"><ma<![CDATA[此售货机位于北京知春路]]></mark><vemName>新北洋1号售货机</vemName><createTime>2023-09-05 15:30:45</createTime><updateTime>2023-09-05 15:30:45</updateTime></SNBCVem>";
        RuntimeException thrown = Assertions.assertThrows(RuntimeException.class,
                () -> XmlJsonMapper.marshall(xmlStr, VemInfo.class));
        Assertions.assertNotNull(thrown);
    }
    @DisplayName("测试xml字符串反序列化为json字符串")
    @Tags({
            @Tag("@id:54"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testUnmarshall() {
        String jsonStr = "{\"id\":12,\"name\":\"新北洋1号售货机\",\"createTime\":\"2023-09-05 15:30:45\",\"updateTime\":\"2023-09-05 15:30:45\",\"mark\":\"此售货机位于北京知春路\"}";
        String xmlStr = "<SNBCVem id=\"12\"><mark><![CDATA[此售货机位于北京知春路]]></mark><vemName>新北洋1号售货机</vemName><createTime>2023-09-05 15:30:45</createTime><updateTime>2023-09-05 15:30:45</updateTime></SNBCVem>";
        assertEquals(jsonStr, XmlJsonMapper.marshall(xmlStr, VemInfo.class));

    }


}

