# 表达式处理(com.snbc.bbpf.commons.expressions)

## 类概览 

| 类名 | 功能描述 |
|------|----------|
| StringExpressionUtils | 提供解析带有表达式的字符串,并进行运算处理的功能 |

## StringExpressionUtils - 表达式工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| calculateBigDecimal(String expression, Map<String, Object> args) | 对字符串表达式、传入参数后并计算结果 | expression: 字符串表达式<br>args: key为变量名，value为变量值 | BigDecimal: 计算结果 |
| calculate(String expression, Map<String, Object> args) | 对字符串表达式、传入参数后并计算结果 | expression: 字符串表达式<br>args: key为变量名，value为变量值 | double: 计算结果 |
| calculate(String expression) | 对算术字符串表达式计算结果 | expression: 字符串表达式 | double: 计算结果 |
| calculate(String expression, Map<String, Object> args, int newScale, RoundingMode roundingMode) | 对字符串表达式、传入参数后并计算，返回保留精度结果 | expression: 字符串表达式<br>args: key为变量名，value为变量值<br>newScale: 精度<br>roundingMode: 精确计算模式 | double: 计算结果 |
| calculate(String expression, int newScale, RoundingMode roundingMode) | 对算术字符串表达式计算，返回保留精度结果 | expression: 字符串表达式<br>newScale: 精度<br>roundingMode: 精确计算模式 | double: 计算结果 |

### 注意事项

1. **表达式引擎**：内部使用Aviator作为表达式引擎，直接将表达式编译成Java字节码执行，具有轻量级和高性能的特点。

2. **参数校验**：
   - 表达式不能为null或空字符串，否则将抛出`IllegalArgumentException`异常
   - 当需要使用变量时，args参数不能为空Map，否则将抛出异常
   - 精度参数newScale不能为负数，否则将抛出`IllegalArgumentException`异常
   - 传入的RoundingMode不能为null或不适用的舍入模式

3. **类型转换**：
   - 方法内部会尝试将参数值转换为BigDecimal类型进行计算
   - 支持的参数类型包括：BigDecimal、String、BigInteger及其他Number类型
   - 不支持的参数类型将抛出`IllegalArgumentException`异常

4. **异常处理**：
   - 所有计算错误和不合法的表达式都会抛出RuntimeException
   - 使用时应当进行适当的异常捕获和处理

5. **变量命名**：
   - 在表达式中使用的变量名必须与args中的key完全匹配
   - 如果表达式中使用了args中不存在的变量名，将抛出异常

6. **精度处理**：
   - 不带精度参数的方法会保留原始计算精度
   - 带精度参数的方法会按照指定的精度和舍入模式处理结果

### 使用示例

```java
// 创建参数Map
Map<String, Object> args = new HashMap<>();
args.put("x", 2);
args.put("y", 3);

// 计算表达式（带参数）
BigDecimal result1 = StringExpressionUtils.calculateBigDecimal("x+y+x*y", args);
System.out.println("Result (BigDecimal): " + result1);  // 输出: Result (BigDecimal): 11

// 计算表达式（带参数，返回double）
double result2 = StringExpressionUtils.calculate("x+y+x*y", args);
System.out.println("Result (double): " + result2);  // 输出: Result (double): 11.0

// 计算简单表达式（无参数）
double result3 = StringExpressionUtils.calculate("2+3+2*3");
System.out.println("Simple expression result: " + result3);  // 输出: Simple expression result: 11.0

// 使用精度控制（带参数）
Map<String, Object> divArgs = new HashMap<>();
divArgs.put("x", 1.0);
divArgs.put("y", 3);
double result4 = StringExpressionUtils.calculate("x/y", divArgs, 2, RoundingMode.UP);
System.out.println("Division with precision: " + result4);  // 输出: Division with precision: 0.34

// 使用精度控制（无参数）
double result5 = StringExpressionUtils.calculate("1.0/3", 2, RoundingMode.UP);
System.out.println("Division with precision (no args): " + result5);  // 输出: Division with precision (no args): 0.34

// 异常情况示例
try {
    // 表达式为空时抛出异常
    StringExpressionUtils.calculate("", args);
} catch (IllegalArgumentException e) {
    System.out.println("Exception: " + e.getMessage());  // 输出: Exception: Input parameter is empty
}

try {
    // 表达式无效时抛出异常
    StringExpressionUtils.calculate("x)2", args);
} catch (RuntimeException e) {
    System.out.println("Exception: Invalid expression");
}

try {
    // 负数精度时抛出异常
    StringExpressionUtils.calculate("x/y", args, -6, RoundingMode.UP);
} catch (IllegalArgumentException e) {
    System.out.println("Exception: " + e.getMessage());  // 输出: Exception: Index parameter invalid
}
```

### 高级用法示例

```java
// 使用更复杂的表达式
Map<String, Object> complexArgs = new HashMap<>();
complexArgs.put("a", 10);
complexArgs.put("b", 5);
complexArgs.put("c", 2);

// 计算复杂表达式
double complexResult = StringExpressionUtils.calculate("a * (b + c) / (b - c)", complexArgs);
System.out.println("Complex expression: " + complexResult);  // 输出: Complex expression: 23.333333333333332

// 为复杂表达式添加精度控制
double preciseResult = StringExpressionUtils.calculate("a * (b + c) / (b - c)", complexArgs, 2, RoundingMode.HALF_UP);
System.out.println("Complex expression with precision: " + preciseResult);  // 输出: Complex expression with precision: 23.33

// 使用不同类型的数值（BigDecimal, Integer等）
Map<String, Object> mixedArgs = new HashMap<>();
mixedArgs.put("bd", new BigDecimal("123.456"));
mixedArgs.put("i", 100);
mixedArgs.put("d", 2.5);

double mixedResult = StringExpressionUtils.calculate("bd + i * d", mixedArgs);
System.out.println("Mixed types: " + mixedResult);  // 输出类似: Mixed types: 373.456
``` 