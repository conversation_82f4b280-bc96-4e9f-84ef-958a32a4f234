/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.strings.security.processor;

import com.google.auto.service.AutoService;
import com.snbc.bbpf.commons.strings.security.annotation.Desensitized;

import javax.annotation.processing.AbstractProcessor;
import javax.annotation.processing.Messager;
import javax.annotation.processing.ProcessingEnvironment;
import javax.annotation.processing.Processor;
import javax.annotation.processing.RoundEnvironment;
import javax.lang.model.SourceVersion;
import javax.lang.model.element.Element;
import javax.lang.model.element.ElementKind;
import javax.lang.model.element.TypeElement;
import javax.lang.model.type.TypeMirror;
import javax.tools.Diagnostic;
import java.util.LinkedHashSet;
import java.util.Set;

/**
 * 字符串脱敏注解处理器
 * 编译时检查注解使用是否正确
 *
 * <AUTHOR>
 * @module 注解
 * @date 2023/9/19
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
@AutoService(Processor.class)
public class ValidDesensitizedProcessor extends AbstractProcessor {
    private Messager messager;
    @Override
    public synchronized void init(ProcessingEnvironment processingEnv) {
        super.init(processingEnv);
        messager = processingEnv.getMessager();
    }

    /**
     * 处理编译时注解检查逻辑
     * @param annotations 当前处理的注解类型集合
     * @param roundEnv 编译环境上下文
     * @return 是否处理成功（始终返回true）
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/9/21
     */
    @Override
    public boolean process(Set<? extends TypeElement> annotations, RoundEnvironment roundEnv) {
        Set<? extends Element> elementsAnnotatedWith = roundEnv.getElementsAnnotatedWith(Desensitized.class);
        for (Element element : elementsAnnotatedWith){
            if (element.getKind()!= ElementKind.FIELD){
                messager.printMessage(
                        Diagnostic.Kind.ERROR,
                        String.format("Only FIELD can be annotated with @%s", Desensitized.class.getSimpleName()),
                        element);

            } else {
                TypeMirror stringType = processingEnv.getElementUtils()
                    .getTypeElement(String.class.getCanonicalName()).asType();
                if (!processingEnv.getTypeUtils().isSameType(element.asType(), stringType)) {
                    messager.printMessage(
                        Diagnostic.Kind.ERROR,
                        String.format("Only String type can be annotated with @%s", Desensitized.class.getSimpleName()),
                        element);
                }
            }
        }
        return true;
    }

    @Override
    public Set<String> getSupportedAnnotationTypes() {
        Set<String> annotataions = new LinkedHashSet<>();
        annotataions.add(Desensitized.class.getCanonicalName());
        return annotataions;
    }

    @Override
    public SourceVersion getSupportedSourceVersion() {
        return SourceVersion.latestSupported();
    }
}
