/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.bytes;

import java.math.BigInteger;
import java.util.regex.Pattern;

/**
 * BCD码处理类
 * <p>BCD码 用4位二进制数来表示1位十进制数中的0~9这10个数码，
 * 是一种二进制的数字编码形式，用二进制编码的十进制代码</p>
 * <p>功能列表</p>
 * <p>1.将带数字（十进制）的字符串转换为BCD编码字节数组（大端） </p>
 * <p>2.将数字转换为BCD字节数组（大端） </p>
 * <p>3.将数字转换为特定长度的BCD码的字节数组(大端) </p>
 * <p>4.将数字(long)转换为特定长度的BCD码的字节数组(大端) </p>
 * <p>5.将数字(long)转换为BCD码的字节数组(大端) </p>
 * <p>6.将BCD码的字节数组(大端)转换为数字(BigInteger) </p>
 * <p>7.将BCD码的字节数组(大端)转换为十进制字符串 </p>
 * <p>8.将单个ascii码转换为bcd码 </p>
 * <p>9.将ascii码转换为bcd码的字节数组(大端) </p>
 *
 * <AUTHOR>
 * @module 字节处理
 * @date 2023/4/20 15:06
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class BcdHandler {

    private BcdHandler() {
        throw new IllegalStateException("Utility class");
    }
    //BCD码正则
    private static final Pattern BCD_PATTERN = Pattern.compile("\\d+");
    //BCD码长度系数
    private static final int BCD_LENGTH_RATIO = 2;
    //BCD码位移操作长度
    private static final int BCD_DISPLACEMENT_LENGTH = 4;
    //判断BIG INT的长度
    private static final int BIG_INT_CHECK_LENGTH = 63;
    //BIG INT字节长度
    private static final int BIG_INT_BIT_LENGTH = 8;
    //十进制系数
    private static final int DECIMAL_RATIO = 10;
    //ACSII中0操作符
    private static final int ASCII_ZERO = 48;

    /**
     * 将数字字符串使用二进制编码十进制（BCD）编码方式编码为字节数组。
     * 如果输入字符串不是数字，则抛出IllegalArgumentException。
     * 例如：
     * <p>
     *     BcdHandler.encode("31")  => new byte[] { 0x31 }
     *     BcdHandler.encode("231") => new byte[] { 0x02, 0x31 }
     *     BcdHandler.encode("0") => new byte[] { 0x00 }
     *     BcdHandler.encode("031") => new byte[] { 0x00, 0x31 }
     *
     * </p>
     * @param value 要编码的数字字符串
     * @return 编码后的BCD值的字节数组
     * @throws IllegalArgumentException 如果输入字符串不是数字
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/21       
     */
    public static byte[] encode(String value){
        if (!BCD_PATTERN.matcher(value).matches()) {
            throw new IllegalArgumentException("Only numeric strings can be encoded");
        }

        final byte[] bcd = new byte[(value.length() + 1) / BCD_LENGTH_RATIO];
        int i;
        int j;
        if (value.length() % BCD_LENGTH_RATIO == 1) {
            bcd[0] = (byte) (value.codePointAt(0) & 0xf);
            i = 1;
            j = 1;
        } else {
            i = 0;
            j = 0;
        }
        for ( ; i < bcd.length; i++, j+= BCD_LENGTH_RATIO) {
            bcd[i] = (byte) ( ((value.codePointAt(j) & 0xf) << BCD_DISPLACEMENT_LENGTH) |
                    (value.codePointAt(j + 1) & 0xf) );
        }
        return bcd;
    }

    /**
     * 将BigInteger类型的值进行编码，返回编码后的字节数组。
     * 如果输入的值不符合要求，将会抛出IllegalArgumentException。
     * 如果输入的值的位数超过了BIG_INT_CHECK_LENGTH，将会将其转换为字符串进行编码。
     * 例如：
     * <p>
     *     BcdHandler.encode(31)  => new byte[] { 0x31 }
     *     BcdHandler.encode(231) => new byte[] { 0x02, 0x31 }
     *     BcdHandler.encode("0") => new byte[] { 0x00 }
     *
     * </p>
     * @param value 要编码的BigInteger类型的值
     * @return 编码后的字节数组
     * @throws IllegalArgumentException 如果输入的值不符合要求
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/21
     */
    public static byte[] encode(BigInteger value) {
        checkParams(value);
        if (value.bitLength() > BIG_INT_CHECK_LENGTH) {
            return encode(value.toString());
        } else {
            return encode(value.longValue());
        }
    }
    /**
     * 检查BigInteger参数是否非负数
     * @param value 要检查的BigInteger值
     * @return void
     * @throws IllegalArgumentException 如果输入值为负数则抛出此异常
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/23
     */
    private static void checkParams(BigInteger value) {
        if (value.signum() == -1) {
            throw new IllegalArgumentException("Only supports non negative numbers");
        }
    }
    /**
     * 检查long参数是否非负数
     * @param value 要检查的long值
     * @return void
     * @throws IllegalArgumentException 如果输入值为负数则抛出此异常
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/23
     */
    private static void checkParams(long value) {
        if (value < 0) {
            throw new IllegalArgumentException("Only supports non negative numbers");
        }
    }
    /**
     * 将BigInteger类型的值进行编码，返回编码后的字节数组。
     * 如果输入的值不符合要求，将会抛出IllegalArgumentException。
     * 如果输入的值的位数超过了指定的长度，则会抛出IllegalArgumentException。
     * 如果输入的值的位数超过了BIG_INT_CHECK_LENGTH，则会将其转换为字符串进行编码。
     * 例如：
     * <p>
     *     BcdHandler.encode(31, 2)  => new byte[] { 0x00, 0x31 }
     *     BcdHandler.encode(231, 4) => new byte[] { 0x00, 0x00, 0x02, 0x31 }
     *     BcdHandler.encode(0,2) => new byte[] { 0x00, 0x00 }
     *     BcdHandler.encode(100, 1)  throw IllegalArgumentException
     * </p>
     * @param value 要编码的BigInteger类型的值
     * @param length 编码后的字节数组长度
     * @return 编码后的字节数组
     * @throws IllegalArgumentException 如果输入的值不符合要求，或者超过了指定的长度
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/21
     */
    public static byte[] encode(BigInteger value, int length) {
        checkParams(value);
        if (value.bitLength() > length * BIG_INT_BIT_LENGTH) {
            throw new IllegalArgumentException(String.format("Value does not fit in byte array of length %s", length));
        }
        if (value.bitLength() > BIG_INT_CHECK_LENGTH) {
            //sonar不修改，这里必须这样需要按照具体长度输出
            return encode(String.format("%0" + (length * BCD_LENGTH_RATIO) + "d", value));
        } else {
            return encode(value.longValue(), length);
        }
    }

    /**
     * 将long类型的值进行编码，返回编码后的字节数组。
     * 如果输入的值不符合要求，将会抛出IllegalArgumentException。
     * 如果输入的值的位数超过了指定的长度，则会抛出IllegalArgumentException。
     * 例如：
     * <p>
     *     BcdHandler.encode(31, 2)  => new byte[] { 0x00, 0x31 }
     *     BcdHandler.encode(231, 4) => new byte[] { 0x00, 0x00, 0x02, 0x31 }
     *     BcdHandler.encode(0,2) => new byte[] { 0x00, 0x00 }
     *     BcdHandler.encode(100, 1)  throw IllegalArgumentException
     * </p>
     * @param value 要编码的long类型的值
     * @param length 编码后的字节数组长度
     * @return 编码后的字节数组
     * @throws IllegalArgumentException 如果输入的值不符合要求，或者超过了指定的长度
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/21
     */
    public static byte[] encode(long value, int length) {
        checkParams(value);
        if (value == 0) {
            return new byte[length];
        }
        final byte[] bcd = new byte[length];

        for (int i = bcd.length - 1; i >= 0; i--) {
            int b = (int) (value % DECIMAL_RATIO);
            value /= DECIMAL_RATIO;
            b |= (value % DECIMAL_RATIO) << BCD_DISPLACEMENT_LENGTH;
            value /= DECIMAL_RATIO;
            bcd[i] = (byte) b;
        }
        if (value != 0) {
            throw new IllegalArgumentException(String.format("Value does not fit in byte array of length %s" , length));
        }

        return bcd;
    }



    /**
     * 将long类型的值进行编码，返回编码后的字节数组。
     * 如果输入的值不符合要求，将会抛出IllegalArgumentException。
     * 例如：
     * <p>
     *     BcdHandler.encode(31)  => new byte[] { 0x31 }
     *     BcdHandler.encode(231) => new byte[] { 0x02, 0x31 }
     *     BcdHandler.encode("0") => new byte[] { 0x00 }
     *
     * </p>
     * @param value 要编码的long类型的值
     * @return 编码后的字节数组
     * @throws IllegalArgumentException 如果输入的值不符合要求
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/21
     */
    public static byte[] encode(long value) {
        checkParams(value);
        if (value == 0) {
            return new byte[1];
        }
        final int length = (int) Math.log10(value) + 1;
        return encode(value, (length + 1) / BCD_LENGTH_RATIO);
    }

    /**
     * 将BCD编码的字节数组解码为BigInteger类型的值。
     * 如果输入的字节数组不符合BCD编码规则，将会抛出IllegalArgumentException。
     * 例如：
     * <p>
     *     BcdHandler.decode(new byte[] { 0x31})  => 31
     *     BcdHandler.decode(new byte[] { 0x02, 0x31}) => 231
     *     BcdHandler.decode(new byte[] { 0x0 }) => 0
     *
     * </p>
     * @param bcd 要解码的BCD编码的字节数组
     * @return 解码后的BigInteger类型的值
     * @throws IllegalArgumentException 如果输入的字节数组不符合BCD编码规则
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/21
     */
    public static BigInteger decode(byte[] bcd) {
        BigInteger value = BigInteger.ZERO;
        for (int i =  0; i < bcd.length; i++) {
            final int high = (bcd[i] & 0xff) >> BCD_DISPLACEMENT_LENGTH;
            final int low = bcd[i] & 0xf;

            if (high > DECIMAL_RATIO || low > DECIMAL_RATIO) {
                throw new IllegalArgumentException(String.format("Illegal byte %x%x at %d", high, low, i));
            }
            value = value.multiply((BigInteger.TEN)).add(BigInteger.valueOf(high));
            value = value.multiply((BigInteger.TEN)).add(BigInteger.valueOf(low));
        }

        return value;
    }

    /**
     * 将BCD编码的字节数组解码为字符串。
     * 如果输入的字节数组不符合BCD编码规则，将会抛出IllegalArgumentException。
     * 例如：
     * <p>
     *     BcdHandler.decodeAsString(new byte[] { 0x31 }, true)  => “31”
     *     BcdHandler.decodeAsString(new byte[] { 0x02, 0x31 }, true) => “231”
     *     BcdHandler.decodeAsString(new byte[] { 0x02, 0x31 }, false) => ”0231“
     *     BcdHandler.decodeAsString(new byte[] { 0x00 }, true) => "0“
     *     BcdHandler.decodeAsString(new byte[] { 0x00 }, false) => "00"
     * </p>
     * @param bcd 要解码的BCD编码的字节数组
     * @param stripLeadingZero 是否去掉解码后字符串前面的0
     * @return 解码后的字符串
     * @throws IllegalArgumentException 如果输入的字节数组不符合BCD编码规则
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/21
     */
    public static String decodeAsString(byte[] bcd, boolean stripLeadingZero) {
        final StringBuilder buf = new StringBuilder(bcd.length * BCD_LENGTH_RATIO);
        for (int i = 0; i < bcd.length; i++) {
            final int high = ( bcd[i] & 0xff) >> BCD_DISPLACEMENT_LENGTH;
            final int low =  bcd[i] & 0xf;

            if (high > DECIMAL_RATIO || low > DECIMAL_RATIO) {
                throw new IllegalArgumentException(String.format("Illegal byte %x%x at %d", high, low, i));
            }
            buf.append((char) (0x30 | high));
            buf.append((char) (0x30 | low));
        }
        return stripLeadingZero && buf.charAt(0) == '0' ? buf.substring(1) : buf.toString();
    }

    /**
     * 将单个ascii码转换为bcd码
     * 例如:
     * <p>
     *     BcdHandler.singleAsciiToBcd((byte)96)) => (byte)48
     * </p>
     * @param asc 单个ascii码值
     * @return  byte bcd码
     * @since 1.0.0
     * @date 2023/4/22
     * <AUTHOR>
     */
    public static byte singleAsciiToBcd(byte asc) {
        byte bcd;

        if ((asc >= '0') && (asc <= '9')) {
            bcd = (byte) (asc - '0');
        } else if ((asc >= 'A') && (asc <= 'F')) {
            bcd = (byte) (asc - 'A' + DECIMAL_RATIO);
        } else if ((asc >= 'a') && (asc <= 'f')) {
            bcd = (byte) (asc - 'a' + DECIMAL_RATIO);
        } else {
            bcd = (byte) (asc - ASCII_ZERO);
        }
        return bcd;
    }

    /**
     * 将ascii码字节数组转换为bcd码字节数组
     * 例如：
     * <p>
     *     BcdHandler.asciiToBcd(new byte[] {48,48,49,49}) => new byte[]{0,17}
     * </p>
     * @param ascii ascii字节数组
     * @return  byte[] bcd码字节数组
     * @since 1.0.0
     * @date 2023/4/22
     * <AUTHOR>
     */
    public static byte[] asciiToBcd(byte[] ascii) {
        int length = ascii.length;
        byte[] bcd = new byte[length / BCD_LENGTH_RATIO];
        int j = 0;
        for (int i = 0; i < (length + 1) / BCD_LENGTH_RATIO; i++) {
            bcd[i] = singleAsciiToBcd(ascii[j++]);
            bcd[i] = (byte) ((j >= length ? 0x00 :
                    (singleAsciiToBcd(ascii[j++]) & 0xff)) + (bcd[i] << BCD_DISPLACEMENT_LENGTH));
        }
        return bcd;
    }
    /**
     * 将bcd码字节数组转换为ascii码
     * 例如：
     * <p>
     *     BcdHandler.bcdToAscii(new byte[] {0,17}) => new byte[]{0,0,1,1}
     *     BcdHandler.bcdToAscii(new byte[]{0x01, 0x02, 0x03, 0x04, 0x05, 0x06})  => new byte[]{0x12, 0x34, 0x56}
     * </p>
     * @param bytes 待转换的bcd码字节数组
     * @return 转换后的ascii码字节数组
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/22
     */
    public static byte[] bcdToAscii(byte[] bytes) {
        byte[] temp = new byte[bytes.length * BCD_LENGTH_RATIO];
        // Convert each BCD byte to two ASCII bytes
        for (int i = 0; i < bytes.length; i++) {
            temp[i * BCD_LENGTH_RATIO] = (byte) ((bytes[i] >> BCD_DISPLACEMENT_LENGTH) & 0x0f);
            temp[i * BCD_LENGTH_RATIO + 1] = (byte) (bytes[i] & 0x0f);
        }
        return temp;
    }
}
