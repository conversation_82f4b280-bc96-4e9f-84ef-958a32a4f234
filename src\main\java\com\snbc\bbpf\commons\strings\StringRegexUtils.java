/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.strings;

import java.util.regex.Pattern;

/**
 * 【字符串处理】 对常用的字符串格式进行校验，去除字符串中指定字符或字符串
 * <p> 1.验证是否IP格式。</p>
 * <p> 2.验证是否手机号码（仅国内）。</p>
 * <p> 3.验证是否为URL。</p>
 * <p> 4.验证是否身份证号，支持15位和18位。</p>
 * <p> 5.验证是否电子邮箱地址格式。</p>
 * <p> 6.对字符串是否都是汉字或包含汉字进行校验，包括生僻字。</p>
 * <p> 7.验证是否银行卡号（包括不支持校验位和使用Luhm校验算法的校验位）</p>

 *
 * <AUTHOR>
 * @module 字符串处理
 * @date 2023/4/26 9:58
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class StringRegexUtils {

    /**
     * 正则：手机号（精确）
     * <p>移动：134(0-8)、135、136、137、138、139、147、150、151、152、157、158、159、178、182、183、184、187、188</p>
     * <p>联通：130、131、132、145、155、156、175、176、185、186</p>
     * <p>电信：133、153、173、177、180、181、189</p>
     * <p>全球星：1349</p>
     * <p>虚拟运营商：170</p>
     */
    public static final String REGEX_MOBILE_EXACT  = "^((13[0-9])|(14[5,7])|(15[0-3,5-9])|(17[0,3,5-8])|(18[0-9])|(147))\\d{8}$";
    /**
     * 正则：电话号码
     */
    public static final String REGEX_TEL           = "^0\\d{2,3}[- ]?\\d{7,8}";
    /**
     * 正则：身份证号码15位
     */
    public static final String REGEX_ID_CARD15     = "^[1-9]\\d{7}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}$";
    /**
     * 正则：身份证号码18位
     */
    public static final String REGEX_ID_CARD18     = "^[1-9]\\d{5}[1-9]\\d{3}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}([0-9Xx])$";
    /**
     * 正则：邮箱
     */
    public static final String REGEX_EMAIL         = "^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$";
    /**
     * 正则：URL
     */
    public static final String REGEX_URL           = "[a-zA-z]+://[^\\s]*";
    /**
     * 正则：汉字
     */
    public static final String REGEX_ZH            = "^[\\u4e00-\\u9fa5]+$";
    /**
     * 正则：用户名，取值范围为a-z,A-Z,0-9,"_",汉字，不能以"_"结尾,用户名必须是6-20位
     */
    public static final String REGEX_USERNAME      = "^[\\w\\u4e00-\\u9fa5]{6,20}(?<!_)$";
     /**
     * 正则：IP地址
     */
    public static final String REGEX_IP            = "((2[0-4]\\d|25[0-5]|[01]?\\d\\d?)\\.){3}(2[0-4]\\d|25[0-5]|[01]?\\d\\d?)";
    /**
     * 常量d
     */
    public static final String CONSTR_D            = "\\d+";
    /**
     * 常量10
     */
    public static final int NUMBER_TEN            = 10;
    /**
     * 常量2
     */
    public static final int NUMBER_TWO            = 2;
    /**
     * 常量2
     */
    public static final int NUMBER_ZERO            = 0;
    /**
     * 常量2
     */
    public static final int NUMBER_ONE           = 1;
    /**
     * 验证身份证号码15位
     *
     * @param input 待验证文本
     * @return {@code true}: 匹配<br>{@code false}: 不匹配
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/26
     */
    public static boolean isIDCard15(CharSequence input) {
        return isMatch(REGEX_ID_CARD15, input);
    }

    /**
     * 验证身份证号码18位
     *
     * @param input 待验证文本
     * @return {@code true}: 匹配<br>{@code false}: 不匹配
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/26
     */
    public static boolean isIDCard18(CharSequence input) {
        return isMatch(REGEX_ID_CARD18, input);
    }

    /**
     * 验证邮箱
     *
     * @param input 待验证文本
     * @return {@code true}: 匹配<br>{@code false}: 不匹
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/26
     */
    public static boolean isEmail(CharSequence input) {
        return isMatch(REGEX_EMAIL, input);
    }

    /**
     * 验证汉字
     *
     * @param input 待验证文本
     * @return {@code true}: 匹配<br>{@code false}: 不匹配
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/26
     */
    public static boolean isZh(CharSequence input) {
        return isMatch(REGEX_ZH, input);
    }
    /**
     * 验证IP地址
     *
     * @param input 待验证文本
     * @return {@code true}: 匹配<br>{@code false}: 不匹配
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/26
     */
    public static boolean isIP(CharSequence input) {
        return isMatch(REGEX_IP, input);
    }


    /**
     * 验证手机号（精确）
     *
     * @param input 待验证文本
     * @return {@code true}: 匹配<br>{@code false}: 不匹配
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/26
     */
    public static boolean isMobileExact(CharSequence input) {
        return isMatch(REGEX_MOBILE_EXACT, input);
    }

    /**
     * 验证电话号码
     *
     * @param input 待验证文本
     * @return {@code true}: 匹配<br>{@code false}: 不匹配
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/26
     */
    public static boolean isTel(CharSequence input) {
        return isMatch(REGEX_TEL, input);
    }



    /**
     * 验证URL
     *
     * @param input 待验证文本
     * @return {@code true}: 匹配<br>{@code false}: 不匹配
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/26
     */
    public static boolean isURL(CharSequence input) {
        return isMatch(REGEX_URL, input);
    }


    /**
     * 验证用户名
     * <p>取值范围为a-z,A-Z,0-9,"_",汉字，不能以"_"结尾,用户名必须是6-20位</p>
     *
     * @param input 待验证文本
     * @return {@code true}: 匹配<br>{@code false}: 不匹配
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/26
     */
    public static boolean isUsername(CharSequence input) {
        return isMatch(REGEX_USERNAME, input);
    }


    /**
     * 判断是否匹配正则
     *
     * @param regex 正则表达式
     * @param input 要匹配的字符串
     * @return {@code true}: 匹配<br>{@code false}: 不匹配
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/26
     */
    public static boolean isMatch(String regex, CharSequence input) {
        return input != null && input.length() > NUMBER_ZERO && Pattern.matches(regex, input);
    }


    /**
     * 判断是否是银行卡号
     *
     * @param cardId 银行卡号
     * @return {@code true}: 匹配<br>{@code false}: 不匹配
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/26
     */
    public static boolean isBankCard(String cardId) {
        char bit = getBankCardCheckCode(cardId.substring(0, cardId.length() - NUMBER_ONE));
        if (bit == 'N') {
            return false;
        }
        return cardId.charAt(cardId.length() - NUMBER_ONE) == bit;
    }

    /**
     * 从不含校验位的银行卡卡号采用 Luhm 校验算法获得校验位
     *
     * @param nonCheckCodeCardId 银行卡号
     * @return 字符
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/26
     */
    public static char getBankCardCheckCode(String nonCheckCodeCardId) {
        if(!isMatch(CONSTR_D,nonCheckCodeCardId)){
            return 'N';
        }
        char[] chs = nonCheckCodeCardId.trim().toCharArray();
        int luhmSum = NUMBER_ZERO;
        for (int i = chs.length - NUMBER_ONE, j = NUMBER_ZERO; i >= NUMBER_ZERO; i--, j++) {
            int k = chs[i] - '0';
            if (j % NUMBER_TWO == NUMBER_ZERO) {
                k *= NUMBER_TWO;
                k = k / NUMBER_TEN + k % NUMBER_TEN;
            }
            luhmSum += k;
        }
        return (luhmSum % NUMBER_TEN == NUMBER_ZERO) ? '0' : (char) ((NUMBER_TEN - luhmSum % NUMBER_TEN) + '0');
    }
}
