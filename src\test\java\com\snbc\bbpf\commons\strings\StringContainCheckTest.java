/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.strings;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullSource;
import org.junit.jupiter.params.provider.ValueSource;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.assertFalse;

class StringContainCheckTest {

    @ParameterizedTest
    @DisplayName("字符串包含字母")
    @Tags({
            @Tag("@id:15"),
            @Tag("@author:liulimin"),
            @Tag("@date:2023/5/4")
    })
    @ValueSource(strings = {"a1234$","12a34$","z1234$","1234$Z","12中文4$Z"})
    void testContainsLetter_isContain(String input) {
        assertTrue(StringContainCheck.containsLetter(input));
    }


    @ParameterizedTest
    @DisplayName("字符串不包含字母")
    @Tags({
            @Tag("@id:15"),
            @Tag("@author:liulimin"),
            @Tag("@date:2023/5/4")
    })
    @ValueSource(strings = {""," ","12 $","12$","12中文4$"})
    void testContainsLetter_notContain(String input) {
        assertFalse(StringContainCheck.containsLetter(input));
    }

    @ParameterizedTest
    @DisplayName("字符串不包含字母-入参为null")
    @Tags({
            @Tag("@id:15"),
            @Tag("@author:liulimin"),
            @Tag("@date:2023/5/4")
    })
    @NullSource
    void testContainsLetter_notContain_inputNull(String input) {
        assertFalse(StringContainCheck.containsLetter(input));
    }

    @ParameterizedTest
    @DisplayName("字符串包含数字")
    @Tags({
            @Tag("@id:15"),
            @Tag("@author:liulimin"),
            @Tag("@date:2023/5/4")
    })
    @ValueSource(strings = {"a1$","9a$","a$0"})
    void testContainsNumber_isContain(String input) {
        assertTrue(StringContainCheck.containsNumber(input));
    }


    @ParameterizedTest
    @DisplayName("字符串不包含数字")
    @Tags({
            @Tag("@id:15"),
            @Tag("@author:liulimin"),
            @Tag("@date:2023/5/4")
    })
    @ValueSource(strings = {""," ","a $","$","z中文$"})
    void testContainsNumber_notContain(String input) {
        assertFalse(StringContainCheck.containsNumber(input));
    }

    @ParameterizedTest
    @DisplayName("字符串不包含数字-入参为null")
    @Tags({
            @Tag("@id:15"),
            @Tag("@author:liulimin"),
            @Tag("@date:2023/5/4")
    })
    @NullSource
    void testContainsNumber_notContain_inputNull(String input) {
        assertFalse(StringContainCheck.containsNumber(input));
    }


    @ParameterizedTest
    @DisplayName("字符串包含空格")
    @Tags({
            @Tag("@id:15"),
            @Tag("@author:liulimin"),
            @Tag("@date:2023/5/4")
    })
    @ValueSource(strings = {" ","a1 $"," 9a$","a$0 "})
    void testContainsBlank_isContain(String input) {
        assertTrue(StringContainCheck.containsBlank(input));
    }


    @ParameterizedTest
    @DisplayName("字符串不包含空格")
    @Tags({
            @Tag("@id:15"),
            @Tag("@author:liulimin"),
            @Tag("@date:2023/5/4")
    })
    @ValueSource(strings = {"","a$","1$","z中文$"})
    void testContainsBlank_notContain(String input) {
        assertFalse(StringContainCheck.containsBlank(input));
    }

    @ParameterizedTest
    @DisplayName("字符串不包含空格-入参为null")
    @Tags({
            @Tag("@id:15"),
            @Tag("@author:liulimin"),
            @Tag("@date:2023/5/4")
    })
    @NullSource
    void testContainsBlank_notContain_inputNull(String input) {
        assertFalse(StringContainCheck.containsBlank(input));
    }


    @ParameterizedTest
    @DisplayName("字符串包含浮点数")
    @Tags({
            @Tag("@id:15"),
            @Tag("@author:liulimin"),
            @Tag("@date:2023/5/4")
    })
    @ValueSource(strings = {"1.3","1e3","a1.3$","9.4a$","a$+0.2"})
    void testContainsFloat_isContain(String input) {
        assertTrue(StringContainCheck.containsFloat(input));
    }


    @ParameterizedTest
    @DisplayName("字符串不包含浮点数")
    @Tags({
            @Tag("@id:15"),
            @Tag("@author:liulimin"),
            @Tag("@date:2023/5/4")
    })
    @ValueSource(strings = {""," ","a $","$","z中文$","a1.b","a1b","1.b","a.b"})
    void testContainsFloat_notContain(String input) {
        assertFalse(StringContainCheck.containsFloat(input));
    }

    @ParameterizedTest
    @DisplayName("字符串不包含浮点数-入参为null")
    @Tags({
            @Tag("@id:15"),
            @Tag("@author:liulimin"),
            @Tag("@date:2023/5/4")
    })
    @NullSource
    void testContainsFloat_notContain_inputNull(String input) {
        assertFalse(StringContainCheck.containsFloat(input));
    }
}