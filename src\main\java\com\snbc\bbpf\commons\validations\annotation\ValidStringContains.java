package com.snbc.bbpf.commons.validations.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 字符串包含注解
 *
 * <AUTHOR>
 * @module 注解
 * @date 2023/8/30
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 * @since 1.2.0
 */
@Target(value = {ElementType.FIELD,ElementType.METHOD,ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ValidStringContains {
    // 字符串
    String value() default "";

    // 错误消息
    String message() default "";
}
