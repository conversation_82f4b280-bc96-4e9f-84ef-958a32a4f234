/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.dates;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.time.temporal.Temporal;
import java.time.temporal.TemporalAdjusters;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @module Date日期处理
 * 对Date、LocalDate、LocalDateTime、ZonedDateTime进行时间计算。
 * <p>计算当前时间一定时间（天，小时，分钟，秒，毫秒）之前或之后的时间</p>
 * <p>计算两个时间之间间隔时差（天，小时，分钟，秒，毫秒）<p>
 * <p>计算当前时间一定天数之前或之后的00点00分00秒或23点59分59秒的时间</p>
 * <p>计算当前时间所在当前年/季/月/周第一天的起始时间和最后一天的起始时间，可区分00点00分00秒或23点59分59秒</p>
 * <p>计算当前时间的（天，小时，分钟，秒，毫秒）数</p>
 * <p>根据（天，小时，分钟，秒，毫秒）数转换成时间</p>
 * <p>判断某个时间是否在start时间和end时间之间</p>
 * <p>将LocalDate转换为Date,LocalDateTime，ZonedDateTime</p>
 * <p>返回两个日期间的 每一分钟天/月/年的时间对象的列表</p>
 * <p>返回指定时间或当前时间前或后天/月/年的时间对象的列表</p>
 * @date 2023/4/23 9:58
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class LocalDateUtils extends BaseDateDiffUtil {

    public static final String LOCAL_DATE_IS_NULL = "localDate is null";

    /**
     * #LocalDate 加任意年月日
     *
     * @param localDate 制定日期
     * @param years     年
     * @param months    月
     * @param days      日
     * @return LocalDate
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static LocalDate plus(LocalDate localDate, int years, int months, int days) {
        final Temporal temporal = commonPlus(localDate, years, months, days, 0, 0, 0, 0);
        return (LocalDate) temporal;
    }

    /**
     * 计算时间差
     *
     * @param start    开始时间
     * @param end      结束时间
     * @param timeUnit 时间单位
     * @return
     * @throws <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static long diff(LocalDate start, LocalDate end, TimeUnit timeUnit) {
        return commondDiff(java.sql.Date.valueOf(start),
                java.sql.Date.valueOf(end),
                timeUnit);
    }

    /**
     * # 增加天数
     *
     * @param localDate 日期
     * @param days      天数
     * @return localDate
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static LocalDate plusDays(LocalDate localDate, int days) {
        return plus(localDate, 0, 0, days);
    }

    /**
     * # 增加年
     *
     * @param localDate 日期
     * @param years     年
     * @return localDate
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static LocalDate plusYears(LocalDate localDate, int years) {
        return plus(localDate, years, 0, 0);
    }

    /**
     * # 增加月
     *
     * @param localDate 日期
     * @param month     年
     * @return localDate
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static LocalDate plusMonths(LocalDate localDate, int months) {
        return plus(localDate, 0, months, 0);
    }

    /**
     * # 获取制定时间当年的第一日，默认00点00分00秒
     *
     * @param localDate 日期
     * @return LocalDate
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static LocalDate firstDayOfYear(LocalDate localDate) {
        return localDate.with(TemporalAdjusters.firstDayOfYear());
    }

    /**
     * # 获取制定时间当年的最后一日，默认00点00分00秒
     *
     * @param localDate 日期
     * @return LocalDate
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static LocalDate lastDayOfYear(LocalDate localDate) {
        return localDate.with(TemporalAdjusters.lastDayOfYear());
    }

    /**
     * # 获取制定时间当月的第一日，默认00点00分00秒
     *
     * @param localDate 日期
     * @return LocalDate
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static LocalDate firstDayOfMonth(LocalDate localDate) {
        return localDate.with(TemporalAdjusters.firstDayOfMonth());
    }

    /**
     * # 获取制定时间当月的最后一日，默认00点00分00秒
     *
     * @param localDate 日期
     * @return LocalDate
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static LocalDate lastDayOfMonth(LocalDate localDate) {
        return localDate.with(TemporalAdjusters.lastDayOfMonth());
    }

    /**
     * # 获取制定时间的年
     *
     * @param localDate 日期
     * @return 年数
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static int year(LocalDate localDate) {
        return yearTemporal(localDate);
    }

    /**
     * # 获取制定时间的月数
     *
     * @param localDate 日期
     * @return 月数
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static int month(LocalDate localDate) {
        return monthTemporal(localDate);
    }

    /**
     * # 获取制定时间的天数
     *
     * @param localDate 日期
     * @return 天数
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static int day(LocalDate localDate) {
        return dayTemporal(localDate);
    }


    /**
     * # 根据年月日构造LocalDate
     *
     * @param years  年
     * @param months 月
     * @param days   日
     * @return LocalDate
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static LocalDate ofLocalDate(int years, int months, int days) {
        return LocalDate.of(years, months, days);
    }

    /**
     * 判断now是否在start和end之间
     * @param now 时间
     * @param start 开始时间
     * @param end 结束时间
     * @return 是否在start和end之间
     * <AUTHOR>
     * @since 1.1.0
     * @date 2023/4/21
     */
    public static boolean isEffectiveDate(LocalDate now, LocalDate start, LocalDate end){
        if(now==null || start==null || end==null){
            throw new IllegalArgumentException("argument is null");
        }

        if(start.isAfter(end)){
            throw new IllegalArgumentException("end_time is less than start_time");
        }
        if(now.isBefore(start)){
            return false;
        }
        return now.isBefore(end);
    }

    /**
     * 将LocalDate转换为Date
     * @param localDate {@link java.time.LocalDate}
     * @return {@link java.util.Date}
     * <AUTHOR>
     * @since 1.1.0
     * @date 2023/4/21
     */
    public static Date toDate(LocalDate localDate){
        return toDate(localDate, ZoneId.systemDefault());
    }

    /**
     * 将LocalDate转换为Date
     * @param localDate {@link java.time.LocalDate}
     * @param zoneId {@link java.time.ZoneId}
     * @return {@link java.util.Date}
     * <AUTHOR>
     * @since 1.1.0
     * @date 2023/4/21
     */
    public static Date toDate(LocalDate localDate, ZoneId zoneId){
        if(localDate==null){
            throw new IllegalArgumentException(LOCAL_DATE_IS_NULL);
        }
        return Date.from(localDate.atStartOfDay(Optional.ofNullable(zoneId).orElse(ZoneId.systemDefault())).toInstant());
    }

    /**
     * 将LocalDate转换为LocalDateTime
     * @param localDate {@link java.time.LocalDate}
     * @return {@link java.time.LocalDateTime}
     * <AUTHOR>
     * @since 1.1.0
     * @date 2023/4/21
     */
    public static LocalDateTime toLoacalDateTime(LocalDate localDate){
        if(localDate==null){
            throw new IllegalArgumentException(LOCAL_DATE_IS_NULL);
        }
        return LocalDateTime.of(localDate, LocalTime.of(0,0,0));
    }

    /**
     * 将LocalDate转换为ZonedDateTime
     * @param localDate {@link java.time.LocalDate}
     * @return {@link java.time.ZonedDateTime}
     * <AUTHOR>
     * @since 1.1.0
     * @date 2023/4/21
     */
    public static ZonedDateTime toZonedDateTime(LocalDate localDate){
        return toZonedDateTime(localDate, ZoneId.systemDefault());
    }

    /**
     * 将LocalDate转换为ZonedDateTime
     * @param localDate {@link java.time.LocalDate}
     * @return {@link java.time.ZonedDateTime}
     * <AUTHOR>
     * @since 1.1.0
     * @date 2023/4/21
     */
    public static ZonedDateTime toZonedDateTime(LocalDate localDate, ZoneId zoneId){
        if(localDate == null){
            throw new IllegalArgumentException(LOCAL_DATE_IS_NULL);
        }
        return ZonedDateTime.of(localDate,LocalTime.of(0,0,0),
                Optional.ofNullable(zoneId).orElse(ZoneId.systemDefault()));
    }

    /**
     * 获取两个时间之间相隔年的列表
     * 包含开始时间start和结束时间end
     * 当start或者end为空时,或者start大于end时，抛出异常
     * @param start {@link java.time.LocalDate} 时间1
     * @param end {@link java.time.LocalDate}  时间2
     * @param val 间隔
     * @return {@link java.time.LocalDate}
     * <AUTHOR>
     * @since 1.5.0
     * @date 2024/5/28
     */
    public static List<LocalDate> getBetweenDateByYear(LocalDate start, LocalDate end, int val){
        return getBetweenDate(start, end, ChronoUnit.YEARS, val);
    }

    /**
     * 获取两个时间之间相隔季度的列表
     * 包含开始时间start和结束时间end
     * 当start或者end为空时,或者start大于end时，抛出异常
     * @param start {@link java.time.LocalDate} 时间1
     * @param end {@link java.time.LocalDate}  时间2
     * @param val 间隔
     * @return {@link java.time.LocalDate}
     * <AUTHOR>
     * @since 1.5.0
     * @date 2024/5/28
     */
    public static List<LocalDate> getBetweenDateByQuarter(LocalDate start, LocalDate end, int val){
        return getBetweenDate(start, end, ChronoUnit.MONTHS, val*QUARTER_MONTH);
    }

    /**
     * 获取两个时间之间相隔月份的列表
     * 包含开始时间start和结束时间end
     * 当start或者end为空时,或者start大于end时，抛出异常
     * @param start {@link java.time.LocalDate} 时间1
     * @param end {@link java.time.LocalDate}  时间2
     * @param val 间隔
     * @return {@link java.time.LocalDate}
     * <AUTHOR>
     * @since 1.5.0
     * @date 2024/5/28
     */
    public static List<LocalDate> getBetweenDateByMonths(LocalDate start, LocalDate end, int val){
        return getBetweenDate(start, end, ChronoUnit.MONTHS, val);
    }

    /**
     * 获取两个时间之间相隔天数的列表
     * 包含开始时间start和结束时间end
     * 当start或者end为空时,或者start大于end时，抛出异常
     * @param start {@link java.time.LocalDate} 时间1
     * @param end {@link java.time.LocalDate}  时间2
     * @param val 间隔
     * @return {@link java.time.LocalDate}
     * <AUTHOR>
     * @since 1.5.0
     * @date 2024/5/28
     */
    public static List<LocalDate> getBetweenDateByDays(LocalDate start, LocalDate end, int val){
        return getBetweenDate(start, end, ChronoUnit.DAYS, val);
    }

    /**
     * 获取两个时间之间相隔小时的列表
     * 包含开始时间start和结束时间end
     * 当start或者end为空时,或者start大于end时，抛出异常
     * @param start {@link java.time.LocalDate} 时间1
     * @param end {@link java.time.LocalDate}  时间2
     * @param val 间隔
     * @return {@link java.time.LocalDate}
     * <AUTHOR>
     * @since 1.5.0
     * @date 2024/5/28
     */
    public static List<LocalDate> getBetweenDateByHours(LocalDate start, LocalDate end, int val){
        return getBetweenDate(start, end, ChronoUnit.HOURS, val);
    }

    /**
     * 获取两个时间之间相隔分钟数的列表
     * 包含开始时间start和结束时间end
     * 当start或者end为空时,或者start大于end时，抛出异常
     * @param start {@link java.time.LocalDate} 时间1
     * @param end {@link java.time.LocalDate}  时间2
     * @param val 间隔
     * @return {@link java.time.LocalDate}
     * <AUTHOR>
     * @since 1.5.0
     * @date 2024/5/28
     */
    public static List<LocalDate> getBetweenDateByMinutes(LocalDate start, LocalDate end, int val){
        return getBetweenDate(start, end, ChronoUnit.MINUTES, val);
    }

    /**
     * 获取两个时间之间的日期
     * 包含开始时间start和结束时间end
     * 当start或者end为空时,或者start大于end时，抛出异常
     * @param start {@link java.time.LocalDate} 时间1
     * @param end {@link java.time.LocalDate}  时间2
     * @param field {@link java.time.temporal.ChronoUnit} 时间类型，支持年，月，日，时，分
     * @param val 间隔值 ，当负数时，表示从end开始，往前推
     * @return {@link java.time.LocalDate}
     * <AUTHOR>
     * @since 1.5.0
     * @date 2024/5/28
     */
    public static List<LocalDate> getBetweenDate(LocalDate start, LocalDate end, ChronoUnit field, int val){
        return getBetweenDate(start, end, field, val, true, true);
    }

    /**
     * 获取两个时间之间的日期
     * 当start或者end为空时,或者start大于end时，抛出异常
     * @param start {@link java.time.LocalDate} 时间1
     * @param end {@link java.time.LocalDate}  时间2
     * @param field {@link java.time.temporal.ChronoUnit} 时间类型，支持年，月，日，时，分
     * @param val 间隔值 ，当负数时，表示从end开始，往前推
     * @param isIncludeStart 是否包含开始时间
     * @param isIncludeEnd 是否包含结束时间
     * @return {@link java.time.LocalDate}
     * <AUTHOR>
     * @since 1.5.0
     * @date 2024/5/28
     */
    public static List<LocalDate> getBetweenDate(LocalDate start, LocalDate end, ChronoUnit field, int val, boolean isIncludeStart, boolean isIncludeEnd){
        if(start==null || end == null){
            throw new IllegalArgumentException("argument is null");
        }
        if(field != ChronoUnit.YEARS && field != ChronoUnit.MONTHS && field != ChronoUnit.DAYS){
            throw new IllegalArgumentException("field is not support");
        }
        if(val==0){
            throw new IllegalArgumentException("val cannot be 0");
        }
        if(start.isAfter(end)){
            throw new IllegalArgumentException("start must be before end");
        }
        // 获取start和end之间的时间

        List<LocalDate> dates = new LinkedList<>();
        if (isIncludeStart){
            dates.add(start);
        }
        int addVal = Math.abs(val);

        LocalDate current = start;
        while (current.isBefore(end)) {
            // 增加val个单位
            current = current.plus(addVal, field);
            // 添加当前时间到列表
            dates.add(current);
        }
        if (!isIncludeEnd){
            dates.remove(dates.size()-1);
        }
        if (val < 0) {
            Collections.reverse(dates);
        }
        return dates;
    }

    /**
     * 返回指定时间后N天/月/年的时间对象的列表
     * @param start 开始时间
     * @param field 单位
     * @param val 值
     * @return list
     * <AUTHOR>
     * @since 1.5.0
     * @date 2024/6/20
     */
    public List<LocalDate> getAfterDates(LocalDate start, ChronoUnit field, int val){
        if(val < 0){
            throw new IllegalArgumentException("val must be positive");
        }
        return getNextDates(start,field, val, false);
    }

    /**
     * 返回指定时间前N天/月/年的时间对象的列表
     * @param start 开始时间
     * @param field 单位
     * @param val 值
     * @return list
     * <AUTHOR>
     * @since 1.5.0
     * @date 2024/6/20
     */
    public List<LocalDate> getBeforeDates(LocalDate start, ChronoUnit field, int val){
        if(val < 0){
            throw new IllegalArgumentException("val must be positive");
        }
        return getNextDates(start,field, -val, false);
    }

    /**
     * 返回指定时间前或后N天/月/年的时间对象的列表
     * @param start 开始时间
     * @param field 单位
     * @param val 值
     * @param isIncludeStart 是否包含开始时间
     * @return list
     * <AUTHOR>
     * @since 1.5.0
     * @date 2024/6/20
     */
    public static List<LocalDate> getNextDates(LocalDate start, ChronoUnit field, int val, boolean isIncludeStart){
        if(start==null){
            throw new IllegalArgumentException("start is null");
        }
        if(field != ChronoUnit.YEARS && field != ChronoUnit.MONTHS && field != ChronoUnit.DAYS){
            throw new IllegalArgumentException("field is not support");
        }
        if(val == 0){
            return Collections.singletonList(start);
        }
        List<LocalDate> dates = new LinkedList<>();
        if(isIncludeStart){
            dates.add(start);
        }

        LocalDate current = start;
        int addVal = val/Math.abs(val);
        while(val!=0){
            // 增加val个单位
            current = current.plus(addVal, field);
            // 添加当前时间到列表
            dates.add(current);
            val += -addVal;
        }
        return dates;
    }

}
