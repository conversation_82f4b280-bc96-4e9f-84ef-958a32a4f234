/*
 * 版权所有 2009-2024山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.system;

import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.net.ServerSocket;
import java.util.concurrent.TimeUnit;


/**
 * 【系统资源与诊断】连通性测试
 *
 * <AUTHOR>
 * @module 系统资源与诊断
 * @date 2024/5/27 13:17
 * @copyright 2024 山东新北洋信息技术股份有限公司. All rights reserved
 */
class ConnectCheckUtilsTest {


    @DisplayName("测试主机是否可达-入参为空的情况")
    @Tags({
            @Tag("@id:105"),
            @Tag("@author:xuy<PERSON><PERSON>"),
            @Tag("@date:2024/5/27")
    })
    @Test
    void testHostIsAliveParameterNull() {

        IllegalArgumentException thrown = Assertions.assertThrows(IllegalArgumentException.class,
                () -> ConnectCheckUtils.hostIsAlive(null, 5, TimeUnit.SECONDS));
        Assertions.assertEquals("addr,time,timeUnit must not be null", thrown.getMessage());
    }


    @DisplayName("测试主机是否可达-不可达")
    @Tags({
            @Tag("@id:105"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2024/5/27")
    })
    @Test
    void testHostIsAliveNotReachable() {

        boolean canReach = ConnectCheckUtils.hostIsAlive("asdf", 5, TimeUnit.SECONDS);
        Assertions.assertFalse(canReach);

        canReach = ConnectCheckUtils.hostIsAlive("*************", 5, TimeUnit.SECONDS);
        Assertions.assertFalse(canReach);

    }

    @DisplayName("测试主机是否可达-hacker")
    @Tags({
            @Tag("@id:105"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2024/5/27")
    })
    @Test
    void testHostIsAliveWithHacker() {

        boolean canReach = ConnectCheckUtils.hostIsAlive("127.0.0.1 || echo 'hello' ", 5, TimeUnit.SECONDS);
        Assertions.assertFalse(canReach);


    }

    @DisplayName("测试主机是否可达")
    @Tags({
            @Tag("@id:105"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2024/5/27")
    })
    @Test
    void testHostIsAlive() {
        boolean canReach = ConnectCheckUtils.hostIsAlive("127.0.0.1", 2, TimeUnit.SECONDS);
        Assertions.assertTrue(canReach);

    }

    @DisplayName("测试主机是否可达-ipv6")
    @Tags({
            @Tag("@id:105"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2024/5/27")
    })
    @Test
    void testHostIsAliveIpv6() {
        boolean canReach = ConnectCheckUtils.hostIsAliveIpv6("::1", 2, TimeUnit.SECONDS);
        Assertions.assertTrue(canReach);

    }


    @DisplayName("测试主机端口是否可达-入参为空的情况")
    @Tags({
            @Tag("@id:105"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2024/5/27")
    })
    @Test
    void testHostPortIsAliveParameterNull() {

        IllegalArgumentException thrown = Assertions.assertThrows(IllegalArgumentException.class,
                () -> ConnectCheckUtils.hostPortIsAlive(null, null, 5, TimeUnit.SECONDS));
        Assertions.assertEquals("addr，port,time，timeunit must not be null", thrown.getMessage());
    }


    @DisplayName("测试主机端口是否可达-不可达")
    @Tags({
            @Tag("@id:105"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2024/5/27")
    })
    @Test
    void testHostPortIsAliveNotReachableNoHost() {
        boolean canReach = ConnectCheckUtils.hostPortIsAlive("asdf", 22, 5, TimeUnit.SECONDS);
        Assertions.assertFalse(canReach);
    }


    @DisplayName("测试主机端口是否可达-可达")
    @Tags({
            @Tag("@id:105"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2024/5/27")
    })
    @Test
    void testHostPortIsAlive() {
        int randomPort = RandomUtils.nextInt(40000, 50000);
        try (ServerSocket socket = new ServerSocket(randomPort)) {
            boolean canReach = ConnectCheckUtils.hostPortIsAlive("127.0.0.1", randomPort, 2, TimeUnit.SECONDS);
            Assertions.assertTrue(canReach);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @DisplayName("测试主机端口是否可达-可达")
    @Tags({
            @Tag("@id:105"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2024/5/27")
    })
    @Test
    void testHostPortIsAliveIpv6() {
        int randomPort = RandomUtils.nextInt(40000, 50000);
        try (ServerSocket socket = new ServerSocket(randomPort)) {
            boolean canReach = ConnectCheckUtils.hostPortIsAlive("::1", randomPort, 2, TimeUnit.SECONDS);
            Assertions.assertTrue(canReach);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}