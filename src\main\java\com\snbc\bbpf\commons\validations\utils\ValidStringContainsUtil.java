/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.validations.utils;


import com.snbc.bbpf.commons.objs.ObjectEmptyCheck;
import com.snbc.bbpf.commons.strings.StringEmptyCheck;
import com.snbc.bbpf.commons.validations.annotation.ValidStringContains;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Locale;

import static com.snbc.bbpf.commons.validations.utils.ValidNotEmptyUtil.GETMETHOD_PRE_LENGTH;

/**
 * StringContains注解验证工具类
 *
 * <AUTHOR>
 * @module 注解
 * @date 2023/8/30
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class ValidStringContainsUtil {

    public static final String VALUE_CONTAINS_ERROR = " value contains error";

    private ValidStringContainsUtil() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 用于校验 JavaBean 中使用StringContains注解的字段
     * 字段必须包含getXXX方法
     *
     * @param object 待验证的JavaBean对象
     * @return void
     * @throws IllegalArgumentException 对象为空或验证失败时抛出异常
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/8/30
     */
    public static void stringFieldsValidate(Object object) throws IllegalArgumentException {
        if (ObjectEmptyCheck.isEmpty(object)) {
            throw new IllegalArgumentException("object can't be null");
        }

        Field[] fields = object.getClass().getDeclaredFields();
        for (Field field : fields) {
            //获取对应注解
            ValidStringContains annotation = field.getAnnotation(ValidStringContains.class);
            if (null == annotation) {
                continue;
            }
            String value = annotation.value();
            String message = annotation.message();
            //根据get方法取出属性值
            validContains(object, field, value, message);
        }
    }

    /**
     * 获取字段并进行校验长度
     *
     * @param object  对象
     * @param field   字段
     * @param value   包含值
     * @param message 异常消息
     * @return void
     * @throws IllegalArgumentException 字段值为空或不包含指定值时抛出
     * <AUTHOR>
     * @date 2023/8/30
     * @since 1.2.0
     */
    private static void validContains(Object object, Field field, String value, String message) {
        String fieldValue = getStringFieldValue(field, object);
        if (fieldValue == null) {
            throw new IllegalArgumentException(!StringEmptyCheck.isEmpty(message) ? message : (field.getName() + VALUE_CONTAINS_ERROR));
        } else {
            if (!fieldValue.contains(value)) {
                throw new IllegalArgumentException(!StringEmptyCheck.isEmpty(message) ? message : (field.getName() + VALUE_CONTAINS_ERROR));
            }
        }
    }

    /*
     * 反射获取get方法值
     *
     * @param field
     * @param instance
     * @throws ValidateException 反射异常
     * @return Object
     * @since 1.1.0
     * <AUTHOR>
     * @date 2023/7/27
     */
    private static String getStringFieldValue(Field field, Object instance) throws IllegalArgumentException {
        for (Method method : instance.getClass().getDeclaredMethods()) {
            String methodName = method.getName();
            String fieldName = field.getName();
            //防止自定义非属性 getXXX方法影响
            if (!methodName.startsWith("get")) {
                continue;
            }
            if ((methodName.length() == (fieldName.length() + GETMETHOD_PRE_LENGTH))
                    && methodName.toLowerCase(Locale.ENGLISH).endsWith(fieldName.toLowerCase(Locale.ENGLISH))) {
                try {
                    return (String) method.invoke(instance);
                } catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException e) {
                    throw new IllegalArgumentException(e.getMessage(), e);
                }
            }
        }
        throw new IllegalArgumentException("get method not found");
    }

}
