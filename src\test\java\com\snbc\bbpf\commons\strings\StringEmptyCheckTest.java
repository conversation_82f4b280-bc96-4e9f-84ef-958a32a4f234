/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.strings;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 字符串空或空白判断测试类
 *
 * <AUTHOR>
 * @module 字符串非空判断
 * @date 2023/4/25 19:09
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class StringEmptyCheckTest {

    @Test
    @DisplayName("空字符串返回true")
    @Tags({
            @Tag("@id:11"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/4/25")
    })
    void testIsEmpty_empty() {
        assertTrue(StringEmptyCheck.isEmpty(""));
    }

    @Test
    @DisplayName("null返回true")
    @Tags({
            @Tag("@id:11"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/4/25")
    })
    void testIsEmpty_emptyForNull() {
        assertTrue(StringEmptyCheck.isEmpty(null));
    }

    @Test
    @DisplayName("null字符串返回false")
    @Tags({
            @Tag("@id:11"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/4/25")
    })
    void testIsEmpty_notEmptyForStringNull() {
        assertFalse(StringEmptyCheck.isEmpty("null"));
    }

    @Test
    @DisplayName("非空字符串返回false")
    @Tags({
            @Tag("@id:11"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/4/25")
    })
    void testIsEmpty_notEmptyForString() {
        assertFalse(StringEmptyCheck.isEmpty("a"));
    }

    @Test
    @DisplayName("空格返回false")
    @Tags({
            @Tag("@id:11"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/4/25")
    })
    void testIsEmpty_notEmptyForBlank() {
        assertFalse(StringEmptyCheck.isEmpty(" "));
    }

    @Test
    @DisplayName("空字符串返回true")
    @Tags({
            @Tag("@id:11"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/4/25")
    })
    void testIsBlank_empty() {
        assertTrue(StringEmptyCheck.isBlank(""));
    }

    @Test
    @DisplayName("null返回true")
    @Tags({
            @Tag("@id:11"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/4/25")
    })
    void testIsBlank_emptyForNull() {
        assertTrue(StringEmptyCheck.isBlank(null));
    }

    @Test
    @DisplayName("null字符串返回false")
    @Tags({
            @Tag("@id:11"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/4/25")
    })
    void testIsBlank_notEmptyForStringNull() {
        assertFalse(StringEmptyCheck.isBlank("null"));
    }

    @Test
    @DisplayName("非空字符串返回false")
    @Tags({
            @Tag("@id:11"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/4/25")
    })
    void testIsBlank_notEmptyForString() {
        assertFalse(StringEmptyCheck.isBlank("a"));
    }

    @Test
    @DisplayName("空格返回true")
    @Tags({
            @Tag("@id:11"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/4/25")
    })
    void testIsBlank_emptyFromBlank() {
        assertTrue(StringEmptyCheck.isBlank(" "));
    }

    @Test
    @DisplayName("多个空格返回true")
    @Tags({
            @Tag("@id:11"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/4/25")
    })
    void testIsBlank_emptyForManyBlank() {
        assertTrue(StringEmptyCheck.isBlank("   "));
    }

    @Test
    @DisplayName("包含空格的字符串返回false")
    @Tags({
            @Tag("@id:11"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/4/25")
    })
    void testIsBlank_notEmptyForABCandBlank() {
        assertFalse(StringEmptyCheck.isBlank("  ABC "));
    }

    @Test
    @DisplayName("包含null与空格的字符串返回false")
    @Tags({
            @Tag("@id:11"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/4/25")
    })
    void testIsBlank_notEmptyForNullandBlank() {
        assertFalse(StringEmptyCheck.isBlank("null "));
    }

    @Test
    @DisplayName("多个非空字符串，返回false")
    @Tags({
            @Tag("@id:90"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/12/12")
    })
    void testIsAnyEmpty_notEmpty() {
        assertFalse(StringEmptyCheck.isAnyEmpty("a","b","c"));
    }
    @Test
    @DisplayName("多个非空字符串，有一个空白字符串，返回true")
    @Tags({
            @Tag("@id:90"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/12/12")
    })
    void testIsAnyEmpty_oneIsEmpty() {
        assertTrue(StringEmptyCheck.isAnyEmpty("","b","c"));
    }
    @Test
    @DisplayName("多个非空字符串，有一个空格字符串，返回false")
    @Tags({
            @Tag("@id:90"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/12/12")
    })
    void testIsAnyEmpty_oneIsBlank() {
        assertFalse(StringEmptyCheck.isAnyEmpty(" ","b","c"));
    }
    @Test
    @DisplayName("多个非空字符串，有一个空字符串，返回true")
    @Tags({
            @Tag("@id:90"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/12/12")
    })
    void testIsAnyEmpty_oneIsNull() {
        assertTrue(StringEmptyCheck.isAnyEmpty(null,"b","c"));
    }

    @Test
    @DisplayName("多个非空字符串判断是否空白或空，返回false")
    @Tags({
            @Tag("@id:90"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/12/12")
    })
    void testIsAnyBlank_noBlank() {
        assertFalse(StringEmptyCheck.isAnyBlank("a","b","c"));
    }

    @Test
    @DisplayName("多个非空字符串,有一个null字符串，返回true")
    @Tags({
            @Tag("@id:90"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/12/12")
    })
    void testIsAnyBlank_oneIsNull() {
        assertTrue(StringEmptyCheck.isAnyBlank(null,"b","c"));
    }
    @Test
    @DisplayName("多个非空字符串,有一个空白字符串，返回true")
    @Tags({
            @Tag("@id:90"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/12/12")
    })
    void testIsAnyBlank_oneIsEmpty() {
        assertTrue(StringEmptyCheck.isAnyBlank("","b","c"));
    }
    @Test
    @DisplayName("多个非空字符串,有一个空白字符串，返回true")
    @Tags({
            @Tag("@id:90"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/12/12")
    })
    void testIsAnyBlank_oneIsBlank() {
        assertTrue(StringEmptyCheck.isAnyBlank(" ","b","c"));
    }


    @Test
    @DisplayName("空字符串返回true")
    @Tags({
            @Tag("@id:94"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/5/31")
    })
    void testIsEmptyOrNull_empty() {
        assertTrue(StringEmptyCheck.isEmptyOrNull(""));
    }

    @Test
    @DisplayName("null返回true")
    @Tags({
            @Tag("@id:94"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/5/31")
    })
    void testIsEmptyOrNull_emptyForNull() {
        assertTrue(StringEmptyCheck.isEmptyOrNull(null));
    }

    @Test
    @DisplayName("非空字符串返回false")
    @Tags({
            @Tag("@id:94"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/5/31")
    })
    void testIsEmptyOrNull_notEmptyForString() {
        assertFalse(StringEmptyCheck.isEmptyOrNull("a"));
    }

    @Test
    @DisplayName("空格返回false")
    @Tags({
            @Tag("@id:94"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/5/31")
    })
    void testIsEmptyOrNull_notEmptyForBlank() {
        assertFalse(StringEmptyCheck.isEmptyOrNull(" "));
    }
    @Test
    @DisplayName("null字符串返回true")
    @Tags({
            @Tag("@id:94"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/5/31")
    })
    void testIsEmptyOrNull_notNullStr() {
        assertTrue(StringEmptyCheck.isEmptyOrNull("null"));
    }
    @Test
    @DisplayName("NULL字符串返回true")
    @Tags({
            @Tag("@id:94"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/5/31")
    })
    void testIsEmptyOrNull_notNULLStr() {
        assertTrue(StringEmptyCheck.isEmptyOrNull("NULL"));
    }
    @Test
    @DisplayName("NULL+空格字符串返回true")
    @Tags({
            @Tag("@id:94"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/5/31")
    })
    void testIsEmptyOrNull_notNULLBlank() {
        assertTrue(StringEmptyCheck.isEmptyOrNull(" NULL "));
    }


    @Test
    @DisplayName("空字符串返回true")
    @Tags({
            @Tag("@id:94"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/5/31")
    })
    void testIsBlankOrNull_empty() {
        assertTrue(StringEmptyCheck.isBlankOrNull(""));
    }

    @Test
    @DisplayName("null返回true")
    @Tags({
            @Tag("@id:94"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/5/31")
    })
    void testIsBlankOrNull_emptyForNull() {
        assertTrue(StringEmptyCheck.isBlankOrNull(null));
    }

    @Test
    @DisplayName("非空字符串返回true")
    @Tags({
            @Tag("@id:94"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/5/31")
    })
    void testIsBlankOrNull_notEmptyForString() {
        assertTrue(StringEmptyCheck.isBlankOrNull("  "));
    }

    @Test
    @DisplayName("空格返回true")
    @Tags({
            @Tag("@id:94"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/5/31")
    })
    void testIsBlankOrNull_notEmptyForBlank() {
        assertTrue(StringEmptyCheck.isBlankOrNull(" "));
    }
    @Test
    @DisplayName("null字符串返回true")
    @Tags({
            @Tag("@id:94"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/5/31")
    })
    void testIsBlankOrNull_notNullStr() {
        assertTrue(StringEmptyCheck.isBlankOrNull("null"));
    }
    @Test
    @DisplayName("NULL字符串返回true")
    @Tags({
            @Tag("@id:94"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/5/31")
    })
    void testIsBlankOrNull_notNULLStr() {
        assertTrue(StringEmptyCheck.isBlankOrNull("NULL"));
    }
    @Test
    @DisplayName("NULL+空格字符串返回true")
    @Tags({
            @Tag("@id:94"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/5/31")
    })
    void testIsBlankOrNull_notNULLBlank() {
        assertTrue(StringEmptyCheck.isBlankOrNull(" NULL "));
    }


    @Test
    @DisplayName("多个非空字符串，返回false")
    @Tags({
            @Tag("@id:94"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/5/31")
    })
    void testIsAnyEmptyOrNull_notEmpty() {
        assertFalse(StringEmptyCheck.isAnyEmptyOrNull("a","b","c"));
    }
    @Test
    @DisplayName("多个非空字符串，有一个空白字符串，返回true")
    @Tags({
            @Tag("@id:94"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/5/31")
    })
    void testIsAnyEmptyOrNull_oneIsEmpty() {
        assertTrue(StringEmptyCheck.isAnyEmptyOrNull("","b","c"));
    }
    @Test
    @DisplayName("多个非空字符串，有一个空格字符串，返回false")
    @Tags({
            @Tag("@id:94"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/5/31")
    })
    void testIsAnyEmptyOrNull_oneIsBlank() {
        assertFalse(StringEmptyCheck.isAnyEmptyOrNull(" ","b","c"));
    }
    @Test
    @DisplayName("多个非空字符串，有一个空字符串，返回true")
    @Tags({
            @Tag("@id:94"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/5/31")
    })
    void testIsAnyEmptyOrNull_oneIsNull() {
        assertTrue(StringEmptyCheck.isAnyEmptyOrNull(null,"b","c"));
    }

    @Test
    @DisplayName("多个字符串，有一个null字符串，返回true")
    @Tags({
            @Tag("@id:94"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/5/31")
    })
    void testIsAnyEmptyOrNull_oneIsNullStr() {
        assertTrue(StringEmptyCheck.isAnyEmptyOrNull("b","null","c"));
    }

    @Test
    @DisplayName("多个字符串，有两个Null字符串，返回true")
    @Tags({
            @Tag("@id:94"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/5/31")
    })
    void testIsAnyEmptyOrNull_twoIsNullStr() {
        assertTrue(StringEmptyCheck.isAnyEmptyOrNull("b","Null","Null"));
    }
    @Test
    @DisplayName("多个字符串，有一个Null+空格字符串，返回true")
    @Tags({
            @Tag("@id:94"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/5/31")
    })
    void testIsAnyEmptyOrNull_oneIsNullBlankStr() {
        assertTrue(StringEmptyCheck.isAnyEmptyOrNull("b","Null  ","c"));
    }

    @Test
    @DisplayName("多个非空字符串判断是否空白或空，返回false")
    @Tags({
            @Tag("@id:94"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/5/31")
    })
    void testIsAnyBlankOrNull_noBlank() {
        assertFalse(StringEmptyCheck.isAnyBlankOrNull("a","b","c"));
    }

    @Test
    @DisplayName("多个非空字符串,有一个null字符串，返回true")
    @Tags({
            @Tag("@id:94"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/5/31")
    })
    void testIsAnyBlankOrNull_oneIsNull() {
        assertTrue(StringEmptyCheck.isAnyBlankOrNull(null,"b","c"));
    }
    @Test
    @DisplayName("多个非空字符串,有一个空白字符串，返回true")
    @Tags({
            @Tag("@id:94"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/5/31")
    })
    void testIsAnyBlankOrNull_oneIsEmpty() {
        assertTrue(StringEmptyCheck.isAnyBlankOrNull("","b","c"));
    }
    @Test
    @DisplayName("多个非空字符串,有一个空白字符串，返回true")
    @Tags({
            @Tag("@id:94"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/5/31")
    })
    void testIsAnyBlankOrNull_oneIsBlank() {
        assertTrue(StringEmptyCheck.isAnyBlankOrNull(" ","b","c"));
    }

    @Test
    @DisplayName("多个字符串，有一个null字符串，返回true")
    @Tags({
            @Tag("@id:94"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/5/31")
    })
    void testIsAnyBlankOrNull_oneIsNullStr() {
        assertTrue(StringEmptyCheck.isAnyBlankOrNull("b","null","c"));
    }

    @Test
    @DisplayName("多个字符串，有两个Null字符串，返回true")
    @Tags({
            @Tag("@id:94"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/5/31")
    })
    void testIsAnyBlankOrNull_twoIsNullStr() {
        assertTrue(StringEmptyCheck.isAnyBlankOrNull("b","Null","Null"));
    }
    @Test
    @DisplayName("多个字符串，有一个Null+空格字符串，返回true")
    @Tags({
            @Tag("@id:94"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/5/31")
    })
    void testIsAnyBlankOrNull_oneIsNullBlankStr() {
        assertTrue(StringEmptyCheck.isAnyBlankOrNull("b"," Null","c"));
    }
}
    