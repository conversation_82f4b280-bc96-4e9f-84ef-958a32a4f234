/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.validations.utils;


import com.snbc.bbpf.commons.validations.ValidException;
import com.snbc.bbpf.commons.validations.annotation.ValidString;
import com.snbc.bbpf.commons.validations.annotation.ValidStringType;
import com.snbc.bbpf.commons.validations.validator.IStringValidator;
import com.snbc.bbpf.commons.validations.validator.RegexValidator;
import com.snbc.bbpf.commons.validations.validator.StringValidationStrategyFactory;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 字符串校验工具类
 *
 * <AUTHOR>
 * @module 字符串校验模块
 * @date 2023/7/16
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class ValidStringUtil {

    private ValidStringUtil() {
        throw new IllegalStateException("Utility class");
    }
    /**
     * 该方法用于验证字符串是否符合指定的验证类型。根据验证类型选择对应的验证器进行验证。
     *
     * @example
     *
     * <p>// 创建一个字符串验证器</p>
     * <p>IStringValidator validator = StringValidationStrategyFactory.getValidator(ValidateType.BANK_CARD);</p>
     *
     * <p>// 验证银行卡号是否符合要求</p>
     * <p>try {</p>
     *  <p>   validator.validate("***********", "银行卡号不符合要求");</p>
     * <p>} catch (ValidateException e) {</p>
     *  <p>   System.out.println(e.getMessage());</p>
     * <p>}</p>
     * 参数：
     * @param str：待验证的字符串
     * @param message：验证失败时的错误信息
     * @param regex：正则表达式（当验证类型为REGEX时必填）
     * @param validStringType：验证类型，可选值为ValidateType枚举中的值
     * @return 无返回值
     * @throws IllegalArgumentException ：验证失败时抛出的异常，包含错误信息
     * <AUTHOR>
     * @date 2023/7/16
     * @since 1.1.0
     */
    public static void validateString(String str, String message, String regex, ValidStringType validStringType)
            throws IllegalArgumentException {
        IStringValidator validator = StringValidationStrategyFactory.getValidator(validStringType);
        if (validator != null) {
            validator.validate(str, message);
        } else {
            if (validStringType == ValidStringType.REGEX) {
                if ("".equals(regex)) {
                    throw new IllegalArgumentException("No regular expression defined");
                }
                IStringValidator stringValidator = new RegexValidator(regex);
                stringValidator.validate(str, message);
            }
        }
    }


    /**
     * 该方法用于验证对象是否符合指定的验证规则。根据注解中的验证类型选择对应的验证器进行验证。
     *
     * @example
     *
     *<p>    public class User {</p>
     *<p>         @Validate(validateType = validStringType.REGEX, regex = "[a-zA-Z0-9]+", message = "字段不合法")</p>
     *<p>         private String field;</p>
     *<p>         @Validate(validateType = validStringType.CHINESE_CHARACTER)</p>
     *<p>         private String cnName;</p>
     *<p>         @Validate(validateType = validStringType.EMAIL)</p>
     *<p>         private String email;</p>
     *<p>         @Validate(validateType = validStringType.PHONE_NUMBER)</p>
     *<p>         private String phone;</p>
     *<p>         @Validate(validateType = validStringType.IDENTITY_CARD)</p>
     *<p>         private String idCard;</p>
     *<p>         }</p>
     *<p>         User obj = new User();</p>
     *<p>         obj.setField("abc123");</p>
     *<p>         obj.setEmail("<EMAIL>");</p>
     *<p>         obj.setCnName("欧阳");</p>
     *<p>         obj.setPhone("13693388173");</p>
     *<p>         obj.setIdCard("11010119900101001X");</p>
     *<p>         ValidateUtil.validateObject(obj);</p>
     * </p>
     * 参数：
     * @param obj：待验证的对象
     * @throws IllegalAccessException：当无法访问对象的字段时抛出的异常
     * @throws ValidException ：验证失败时抛出的异常，包含错误信息
     * @return 无返回值
     * @throws ValidException ：验证失败时抛出的异常，包含错误信息
     * <AUTHOR>
     * @date 2023/7/16
     * @since 1.1.0
     */
    public static void validateObject(Object obj) throws IllegalArgumentException,IllegalAccessException {
        if (obj == null) {
            throw new IllegalArgumentException("Parameter is empty");
        }
        Field[] fields = getAllFields(obj);
        for (Field field : fields) {
            ValidString validate = field.getAnnotation(ValidString.class);
            if(null==validate){
                return;
            }
            field.setAccessible(true);
            String fieldValue = (String) field.get(obj);
            if (validate.validStringType() == ValidStringType.REGEX) {
                if ("".equals(validate.regex())) {
                    throw new IllegalArgumentException("No regular expression defined");
                }
                IStringValidator stringValidator = new RegexValidator(validate.regex());
                stringValidator.validate(fieldValue, validate.message());
            } else {
                IStringValidator stringValidator = StringValidationStrategyFactory
                        .getValidator(validate.validStringType());
                stringValidator.validate(fieldValue, validate.message());
            }
        }
    }

    private static Field[] getAllFields(Object object) {
        // 获取对象的Class
        Class<?> clazz = object.getClass();

        // 创建一个列表来存储所有字段
        List<Field> fieldsList = new ArrayList<>();

        // 循环获取当前类及其父类的所有字段
        while (clazz != null) {
            // 获取当前类的所有字段
            Field[] fields = clazz.getDeclaredFields();

            // 将字段添加到列表中
            fieldsList.addAll(Arrays.asList(fields));

            // 获取父类的Class
            clazz = clazz.getSuperclass();
        }

        // 将列表转换为数组并返回
        return fieldsList.toArray(new Field[0]);
    }
}
