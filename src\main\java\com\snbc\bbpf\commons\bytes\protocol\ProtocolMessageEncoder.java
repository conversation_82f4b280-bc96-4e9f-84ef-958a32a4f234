/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.bytes.protocol;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;

/**
 * 协议消息编码器
 * <p>将ProtocolMessage对象编码为符合协议规范的二进制数据</p>
 * <p>使用小端字节序和UTF-8字符串编码</p>
 * 
 * <AUTHOR>
 * @module 字节处理
 * @date 2025/07/25 15:30
 * @copyright 2024 山东新北洋信息技术股份有限公司. All rights reserved
 * @since 1.7.0
 */
public class ProtocolMessageEncoder {
    
    /** 固定消息头长度 */
    public static final int FIXED_HEADER_LENGTH = 31;
    
    /**
     * 编码协议消息为二进制数据
     * @param message 要编码的协议消息
     * @return 编码后的二进制数据
     * @throws IllegalArgumentException 如果消息为null或数据无效
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public static byte[] encode(ProtocolMessage message) {
        if (message == null) {
            throw new IllegalArgumentException("Protocol message cannot be null");
        }
        
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            // 编码固定消息头
            byte[] fixedHeader = encodeFixedHeader(message);
            baos.write(fixedHeader);
            
            // 编码扩展消息头
            byte[] extendedHeader = encodeExtendedHeader(message);
            baos.write(extendedHeader);
            
            // 编码消息体
            byte[] messageBody = message.getMessageBody();
            if (messageBody.length > 0) {
                baos.write(messageBody);
            }
            
            // 编码校验码（如果存在）
            byte[] checksum = message.getChecksum();
            if (checksum.length > 0) {
                baos.write(checksum);
            }
            
            return baos.toByteArray();
            
        } catch (IOException e) {
            throw new RuntimeException("Failed to encode protocol message", e);
        }
    }
    
    /**
     * 编码固定消息头
     * @param message 协议消息
     * @return 固定消息头的二进制数据
     */
    private static byte[] encodeFixedHeader(ProtocolMessage message) {
        ByteBuffer buffer = ByteBuffer.allocate(FIXED_HEADER_LENGTH);
        buffer.order(ByteOrder.LITTLE_ENDIAN); // 使用小端字节序
        
        // 消息标识 (4字节)
        buffer.put(message.getMessageId());
        
        // 协议版本 (1字节)
        buffer.put(message.getProtocolVersion());
        
        // 消息类型 (2字节)
        buffer.putShort(message.getMessageType());
        
        // 消息序号 (8字节)
        buffer.putLong(message.getSequenceNumber());
        
        // 时间戳 (8字节)
        buffer.putLong(message.getTimestamp());
        
        // 扩展消息头数据长度 (4字节)
        buffer.putInt(message.calculateExtendedHeaderLength());
        
        // 消息体数据长度 (4字节)
        buffer.putInt(message.getBodyLength());
        
        return buffer.array();
    }
    
    /**
     * 编码扩展消息头
     * @param message 协议消息
     * @return 扩展消息头的二进制数据
     */
    private static byte[] encodeExtendedHeader(ProtocolMessage message) {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            
            // 编码商户编号
            encodeStringField(baos, message.getTenantId());
            
            // 编码产品编号
            encodeStringField(baos, message.getProductId());
            
            // 编码应用编号
            encodeStringField(baos, message.getAppKey());
            
            // 编码保留信息
            encodeByteArrayField(baos, message.getReservedInfo());
            
            return baos.toByteArray();
            
        } catch (IOException e) {
            throw new RuntimeException("Failed to encode extended header", e);
        }
    }
    
    /**
     * 编码字符串字段（长度+内容格式）
     * @param baos 输出流
     * @param value 字符串值
     * @throws IOException IO异常
     */
    private static void encodeStringField(ByteArrayOutputStream baos, String value) throws IOException {
        byte[] stringBytes = value.getBytes(StandardCharsets.UTF_8);
        
        // 写入长度（4字节，小端）
        ByteBuffer lengthBuffer = ByteBuffer.allocate(4);
        lengthBuffer.order(ByteOrder.LITTLE_ENDIAN);
        lengthBuffer.putInt(stringBytes.length);
        baos.write(lengthBuffer.array());
        
        // 写入内容
        if (stringBytes.length > 0) {
            baos.write(stringBytes);
        }
    }
    
    /**
     * 编码字节数组字段（长度+内容格式）
     * @param baos 输出流
     * @param value 字节数组值
     * @throws IOException IO异常
     */
    private static void encodeByteArrayField(ByteArrayOutputStream baos, byte[] value) throws IOException {
        // 写入长度（4字节，小端）
        ByteBuffer lengthBuffer = ByteBuffer.allocate(4);
        lengthBuffer.order(ByteOrder.LITTLE_ENDIAN);
        lengthBuffer.putInt(value.length);
        baos.write(lengthBuffer.array());
        
        // 写入内容
        if (value.length > 0) {
            baos.write(value);
        }
    }
    
    /**
     * 编码固定消息头（静态方法，便于单独使用）
     * @param messageType 消息类型
     * @param sequenceNumber 消息序号
     * @param timestamp 时间戳
     * @param extendedHeaderLength 扩展消息头长度
     * @param bodyLength 消息体长度
     * @return 固定消息头的二进制数据
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public static byte[] encodeFixedHeader(short messageType, long sequenceNumber, long timestamp,
                                         int extendedHeaderLength, int bodyLength) {
        ByteBuffer buffer = ByteBuffer.allocate(FIXED_HEADER_LENGTH);
        buffer.order(ByteOrder.LITTLE_ENDIAN);
        
        // 消息标识
        buffer.put(new byte[]{'S', 'T', 'U', 'M'});
        
        // 协议版本
        buffer.put((byte) 1);
        
        // 消息类型
        buffer.putShort(messageType);
        
        // 消息序号
        buffer.putLong(sequenceNumber);
        
        // 时间戳
        buffer.putLong(timestamp);
        
        // 扩展消息头数据长度
        buffer.putInt(extendedHeaderLength);
        
        // 消息体数据长度
        buffer.putInt(bodyLength);
        
        return buffer.array();
    }
    
    /**
     * 计算字符串字段编码后的长度
     * @param value 字符串值
     * @return 编码后的字节长度（包括4字节长度字段）
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public static int calculateStringFieldLength(String value) {
        if (value == null || value.isEmpty()) {
            return 4; // 只有长度字段
        }
        return 4 + value.getBytes(StandardCharsets.UTF_8).length;
    }
    
    /**
     * 计算字节数组字段编码后的长度
     * @param value 字节数组值
     * @return 编码后的字节长度（包括4字节长度字段）
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public static int calculateByteArrayFieldLength(byte[] value) {
        if (value == null || value.length == 0) {
            return 4; // 只有长度字段
        }
        return 4 + value.length;
    }
}
