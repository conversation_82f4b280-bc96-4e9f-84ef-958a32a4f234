/*
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.commons.strings.security.parse;

import org.apache.commons.lang3.StringUtils;

/**
 * @ClassName:  FixedPhoneParse
 *   固定电话脱敏
 * @module:    字符脱敏
 * @Author:    yangweipeng
 * @date:      2023/05/06
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class FixedPhoneParse implements IDesensitizedParse {
    /**
     * 右边显示字符数
     */
    private static final Integer RIGHT_LENGTH =4;
    /**
     *  固定电话 后四位，其他隐藏，比如1234
     *
     * @param srcStr
     * @return
     */
    @Override
    public String parseString(String srcStr) {
        if(StringUtils.isEmpty(srcStr)){
            return srcStr;
        }
        return StringUtils.leftPad(StringUtils.right(srcStr, RIGHT_LENGTH),
                StringUtils.length(srcStr), "*");
    }
}

