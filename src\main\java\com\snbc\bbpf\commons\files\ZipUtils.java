/*
 * 版权所有 2009-2023 山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.files;

import org.apache.commons.lang3.StringUtils;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

/**
 * Zip文件解压和压缩工具类
 *  1.将指定文件解压到指定目录，默认目录不存在进行创建，如果文件已经存在，默认覆盖保存。
 *  2.将指定目录或文件压缩成指定文件，默认压缩比最高级别，可以指定
 *
 * 使用范例：
 *  解压指定文件：ZipUtils.decompress("D:\zipdemo\idea_settings.zip", "d:\zipdemo")
 *  压缩指定文件：ZipUtils.compress("D:\zipdemo\idea_settings","D:\zipdemo\new.zip")
 *
 * <AUTHOR>
 * @date 2023/12/2 10:36
 * @since 1.4.0
 */
public final class ZipUtils {

    public static final int MAX_COMPRESS_LEVEL = 9;

    private ZipUtils(){
        throw new IllegalStateException("Utility class");
    }

    private static final int BUFF_SIZE = 1024;

    private static final String SUPPORT_FILE_SUFFIX_NAME = ".zip";

    /**
     * <p>解压文件到指定目录</p>
     *
     * @param zipFilePath 待解压文件地址
     * @param destDir 解压目标文件夹
     * <AUTHOR>
     * @date 2023/12/05 11:07
     * @since 1.4.0
     */
    public static void decompress(String zipFilePath, String destDir) {
        File zipFile = validateArgusForDecompress(zipFilePath, destDir);
        //创建解压根目录  与压缩文件同名
        String zipFileName = zipFile.getName();
        zipFileName = zipFileName.substring(0, zipFileName.indexOf(SUPPORT_FILE_SUFFIX_NAME));
        destDir = destDir + File.separator + zipFileName;
        File distDirFile = new File(destDir);
        distDirFile.mkdir();

        try (ZipInputStream zis = new ZipInputStream(new FileInputStream(zipFile))) {
            ZipEntry entry = zis.getNextEntry();
            byte[] buffer = new byte[BUFF_SIZE];
            while (entry != null) {
                File file = new File(destDir, entry.getName());
                if (entry.isDirectory()) {
                    file.mkdirs();
                } else {
                    File parent = file.getParentFile();
                    if (!parent.exists()) {
                        parent.mkdirs();
                    }
                    try (FileOutputStream fos = new FileOutputStream(file)) {
                        int len;
                        while ((len = zis.read(buffer)) > 0) {
                            fos.write(buffer, 0, len);
                        }
                    }
                }
                entry = zis.getNextEntry();
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * <p>校验解压方法参数</p>
     *
     * @param zipFilePath 待解压文件地址
     * @param destDir 解压目标文件夹
     * @return java.io.File 待解压文件对象
     * <AUTHOR>
     * @date 2023/12/05 11:41
     * @since 1.4.0
     */
    private static File validateArgusForDecompress(String zipFilePath, String destDir) {
        if (StringUtils.isBlank(zipFilePath)) {
            throw new IllegalArgumentException("The Source Zip File Path is Blank");
        }
        if (StringUtils.isBlank(destDir)) {
            throw new IllegalArgumentException("The Destination Directory is Blank");
        }
        if (!FileTypeChecker.isFileType(zipFilePath,FileType.ZIP)) {
            throw new IllegalArgumentException("The Source File Suffix Name is Error ,Only Support .zip Type File");
        }
        File destDirFile = new File(destDir);
        if (!destDirFile.exists()) {
            throw new IllegalArgumentException("The Destination Directory is Illegal");
        }
        File zipFile = new File(zipFilePath);
        if (!zipFile.exists()) {
            throw new IllegalArgumentException("The Source Zip File Not Exists");
        }
        return zipFile;
    }


    /**
     * <p>将指定目录压缩到指定的文件中</p>
     *
     * @param sourceFilePath 待压缩文件（目录）路径 例如： D:\zipdemo\\unzip\\
     * @param zipFilePath 压缩目标文件路径（含文件名） 例子： D:\\zipdemo\\new.zip
     * @param compressLevel 压缩级别 取值范围为 0--9 如果超出范围，则默认按照最高级【9】处理
     * <AUTHOR>
     * @date 2023/12/05 10:50
     * @since 1.4.0
     */
    public static void compress(String sourceFilePath, String zipFilePath,int compressLevel){
        File sourceFile = validateArgusForCompress(sourceFilePath, zipFilePath);
        //设置压缩级别 如果不在限定级别范围内 按最高级设置
        if (compressLevel<=0 || compressLevel> MAX_COMPRESS_LEVEL) {
            compressLevel = MAX_COMPRESS_LEVEL;
        }
        File zipFile = new File(zipFilePath);
        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipFile))){
            //设置压缩级别
            zos.setLevel(compressLevel);
            addZipFile(sourceFile, "", zos);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    /**
     * <p>校验压缩文件方法入参</p>
     *
     * @param sourceFilePath 待压缩目录 例如： D:\zipdemo\\unzip\\
     * @param zipFilePath 压缩目标文件地址（含文件名） 例子： D:\\zipdemo\\new.zip
     * @return java.io.File 带压缩文件（目录）File对象
     * <AUTHOR>
     * @date 2023/12/05 11:43
     * @since 1.4.0
     */
    private static File validateArgusForCompress(String sourceFilePath, String zipFilePath) {
        if (StringUtils.isBlank(sourceFilePath)) {
            throw new IllegalArgumentException("The Source File or Directory is Blank");
        }
        if (StringUtils.isBlank(zipFilePath)) {
            throw new IllegalArgumentException("The Destination File Path is Blank");
        }
        if (!zipFilePath.endsWith(SUPPORT_FILE_SUFFIX_NAME)) {
            throw new IllegalArgumentException("The Destination File Suffix Name is Error ,Only Support .zip Type File");
        }
        File sourceFile = new File(sourceFilePath);
        if(!sourceFile.exists()){
            throw new IllegalArgumentException("The Source File or Directory is Not Exists");
        }
        File zipFile = new File(zipFilePath);
        File parentFile = zipFile.getParentFile();
        if (!parentFile.exists()) {
            throw new IllegalArgumentException("The Destination File Path is Illegal");
        }
        return sourceFile;
    }

    /**
     * <p>将指定文件加入到压缩包中</p>
     *
     * @param file 待操作文件
     * @param parentDir 父目录
     * @param zos 压缩文件流
     * <AUTHOR>
     * @date 2023/12/05 11:36
     * @since 1.4.0
     */
    private static void addZipFile(File file, String parentDir, ZipOutputStream zos) {
        File[] fs = file.listFiles();
        byte[] bufs = new byte[BUFF_SIZE];

        for(int i=0; i<fs.length; i++){
            String fName =  fs[i].getName();
            if(fs[i].isFile()){
                handleFile(parentDir, zos, bufs, fName, fs[i]);
            }
            if(fs[i].isDirectory()){
                handleDirectory(parentDir, zos, fs, i, fName);
            }
        }

    }

    /**
     * <p>将指定文件内容写入压缩文件中</p>
     *
     * @param parentDir 父目录
     * @param zos 压缩文件输出流
     * @param bufs 缓存数组
     * @param fName 文件名称
     * @param fs 文件对象
     * <AUTHOR>
     * @date 2023/12/12 11:03
     * @since 1.4.0
     */
    private static void handleFile(String parentDir, ZipOutputStream zos, byte[] bufs, String fName, File fs) {
        try(FileInputStream fis = new FileInputStream(fs); BufferedInputStream bis = new BufferedInputStream(fis, BUFF_SIZE)){
            ZipEntry zipEntry = new ZipEntry(parentDir + fName);
            zos.putNextEntry(zipEntry);
            int read;
            while((read=bis.read(bufs, 0, BUFF_SIZE))!=-1){
                zos.write(bufs, 0, read);
            }
        }catch  (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * <p>将目录内容写入压缩文件</p>
     *
     * @param parentDir 父目录
     * @param zos 压缩文件输出流
     * @param fs 子文件列表
     * @param i 层级
     * @param fName 文件名称
     * <AUTHOR>
     * @date 2023/12/12 11:04
     * @since 1.4.0
     */
    private static void handleDirectory(String parentDir, ZipOutputStream zos, File[] fs, int i, String fName) {
        if (fs[i].listFiles().length==0) {
            try {
                ZipEntry zipEntry = new ZipEntry(parentDir + fName + "/");
                zos.putNextEntry(zipEntry);
            }catch (Exception e) {
                throw new RuntimeException(e);
            }
        } else {
            addZipFile(fs[i], parentDir + fName + File.separator, zos);
        }
    }
}
