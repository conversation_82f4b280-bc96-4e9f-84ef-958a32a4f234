/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.validations.utils;

import com.snbc.bbpf.commons.validations.annotation.ValidStringLength;
import org.junit.jupiter.api.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * StringLength注解验证工具类
 *
 * <AUTHOR>
 * @module 注解
 * @date 2023/7/27
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class ValidStringLengthUtilTest {

    //测试类
    private StringLengthTestObjSuccess testObjSuccess;
    private StringLengthTestObjFailure testObjFailure;

    @BeforeEach
    public void init() {
        testObjSuccess = new StringLengthTestObjSuccess();
        testObjSuccess.setField1("12");
        testObjFailure = new StringLengthTestObjFailure();
        testObjFailure.setField1("1234");
        testObjFailure.setField2("12");
    }

    @Test
    @DisplayName("验证成功")
    @Tags({
            @Tag("@id:38"),
            @Tag("@author:liulimin"),
            @Tag("@date:2023/7/27")
    })
    void testValidateSuccess() {
        assertDoesNotThrow(() -> {
            ValidStringLengthUtil.stringFieldsValidate(testObjSuccess);
        });
    }

    @Test
    @DisplayName("验证失败")
    @Tags({
            @Tag("@id:38"),
            @Tag("@author:liulimin"),
            @Tag("@date:2023/7/27")
    })
    void testValidateFailure() {
        assertThrows(IllegalArgumentException.class,() -> {
            ValidStringLengthUtil.stringFieldsValidate(testObjFailure);
        });
    }

    public class StringLengthTestObjSuccess {
        @ValidStringLength(min = 0,max = 3,message = "assertTrueField值长度范围0到3")
        private String field1;

        @ValidStringLength(min = 0,max = 3,message = "assertFalseField值长度范围0到3")
        private String field2;

        public String getField1() {
            return field1;
        }

        public void setField1(String field1) {
            this.field1 = field1;
        }

        public String getField2() {
            return field2;
        }

        public void setField2(String field2) {
            this.field2 = field2;
        }
    }
    public class StringLengthTestObjFailure {
        @ValidStringLength(min = 0,max = 3,message = "assertTrueField值长度范围0到3")
        private String field1;

        @ValidStringLength(min = 0,max = 3,message = "assertFalseField值长度范围0到3")
        private String field2;

        public String getField1() {
            return field1;
        }

        public void setField1(String field1) {
            this.field1 = field1;
        }

        public void setField2(String field2) {
            this.field2 = field2;
        }
    }
}