/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.strings;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

class StringReplaceUtilsTest {

    @Test
    @DisplayName("判断检源操作字符串查参数为空则抛出提示的IllegalArgumentException异常")
    @Tags({
            @Tag("@id:12"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testChecckReplaceAll_srcIsemtyThrowException() {
        assertThrows(IllegalArgumentException.class,
                () -> StringReplaceUtils.checkReplace("",""),
                "Source String is null");
    }
    @Test
    @DisplayName("判断检源操作字符串查参数为空则抛出提示的IllegalArgumentException异常")
    @Tags({
            @Tag("@id:12"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testChecckReplace_srcIsnullThrowException() {
        assertThrows(IllegalArgumentException.class,
                () -> StringReplaceUtils.checkReplace(null,""),
                "Source String is null");
    }
    @Test
    @DisplayName("判断检查要替换的字符串参数为空则抛出提示的IllegalArgumentException异常")
    @Tags({
            @Tag("@id:12"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testChecckReplace_regStringIsnullThrowException() {
        assertThrows(IllegalArgumentException.class,
                () -> StringReplaceUtils.checkReplace("目标",null),
                "Replace reg String is null");
    }
    @Test
    @DisplayName("判断检查要替换的字符串参数为空则抛出提示的IllegalArgumentException异常")
    @Tags({
            @Tag("@id:12"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testChecckReplace_regStringIsemtyThrowException() {
        assertThrows(IllegalArgumentException.class,
                () -> StringReplaceUtils.checkReplace("目标",""),
                "Replace reg String is null");
    }
    @Test
    @DisplayName("判断检查开始位置小于零参数为空则抛出提示的IllegalArgumentException异常")
    @Tags({
            @Tag("@id:12"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testChecckReplace_beginIndexMinzeroThrowException() {
        assertThrows(IllegalArgumentException.class,
                () -> StringReplaceUtils.checkReplaceAll("目标","目",-1,0),
                "Begin Replace Index is Error must pass 0");
    }
    @Test
    @DisplayName("判断检查结束位置小于零参数为空则抛出提示的IllegalArgumentException异常")
    @Tags({
            @Tag("@id:12"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testChecckReplaceAll_endIndexMinzeroThrowException() {
        assertThrows(IllegalArgumentException.class,
                () -> StringReplaceUtils.checkReplaceAll("目标","目",0,-1),
                "end Replace Index is Error must pass 0");
    }

    @Test
    @DisplayName("判断检查结束位置大小等于开始位置参数为空则抛出提示的IllegalArgumentException异常")
    @Tags({
            @Tag("@id:12"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testChecckReplaceAll_beginIndexpassEndIndexThrowException() {
        assertThrows(IllegalArgumentException.class,
                () -> StringReplaceUtils.checkReplaceAll("目标","目",2,1),
                "End Replace Index is Error must pass beginIndex");
    }

    @Test
    @DisplayName("当结束符输入为0则表示为改源字串的最大长度")
    @Tags({
            @Tag("@id:12"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testChecckReplaceAll_endIndexIsZerotMaxLenth() {
        assertEquals( StringReplaceUtils.checkReplaceAll("123456","目",2,0),
                6);
    }
    @Test
    @DisplayName("当结束符输入最大值则表示为改源字串的最大长度")
    @Tags({
            @Tag("@id:12"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testChecckReplaceAll_endIndexPassMaxLenth() {
        assertEquals( StringReplaceUtils.checkReplaceAll("123456","目",2,18),
                6);
    }
    @Test
    @DisplayName("全文替换，将含字符!}变成,字符")
    @Tags({
            @Tag("@id:12"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testreplaceAll_allreplace() {
        assertEquals( StringReplaceUtils.replaceAll("替[换]内容同!目}标","\\[\\]!}",",",0,0),
                "替,换,内容同,目,标");
    }
    @Test
    @DisplayName("全文替换，将含字符!}变成,字符")
    @Tags({
            @Tag("@id:12"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testreplaceAll_maxallreplace() {
        assertEquals( StringReplaceUtils.replaceAll("替[换]内容同!目}标","\\[\\]!}",",",0,100),
                "替,换,内容同,目,标");
    }
    @Test
    @DisplayName("全文替换，将含字符!}变成,字符")
    @Tags({
            @Tag("@id:12"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testreplaceAll_noallreplace() {
        assertEquals( StringReplaceUtils.replaceAll("替[换]内容同!目}标","\\[\\]!}",","),
                "替,换,内容同,目,标");
    }
    @Test
    @DisplayName("按长度替换，将含字符!}变成,字符")
    @Tags({
            @Tag("@id:12"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testreplaceAll_lenthreplace() {
        assertEquals( StringReplaceUtils.replaceAll("1!23!456}78}9","!}",",",3,10),
                "1!23,456,78}9");
    }
    @Test
    @DisplayName("全文替换，将含字符!}变成,字符")
    @Tags({
            @Tag("@id:12"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testreplaceStringAll_allreplace() {
        assertEquals( StringReplaceUtils.replaceStringAll("替换!内容同!}目!}标","!}","，",0,0),
                "替换!内容同，目，标");
    }
    @Test
    @DisplayName("全文替换，将含字符!}变成,字符")
    @Tags({
            @Tag("@id:12"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testreplaceStringAll_maxallreplace() {
        assertEquals( StringReplaceUtils.replaceStringAll("替换!内容同!}目!}标","!}","，",0,100),
                "替换!内容同，目，标");
    }
    @Test
    @DisplayName("全文替换，将含字符!}变成,字符")
    @Tags({
            @Tag("@id:12"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testreplaceStringAll_noallreplace() {
        assertEquals( StringReplaceUtils.replaceStringAll("替换!内容同!}目!}标","!}","，"),
                "替换!内容同，目，标");
    }
    @Test
    @DisplayName("按长度替换，将含字符串!}变成,字符")
    @Tags({
            @Tag("@id:12"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testreplaceStringAll_lenthreplace() {
        assertEquals( StringReplaceUtils.replaceStringAll("1!}23!}456!}8!}9","!}","，",3,12),
                "1!}23，456，8!}9");
    }


    @Test
    @DisplayName("移除字符串中的中文，正常情况")
    @Tags({
            @Tag("@id:108"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/5/22")
    })
    void testremoveChinese_normal() {
        assertEquals( StringReplaceUtils.removeChinese("我爱新北洋SNBCsnbc,萤启snbc!"),
                "SNBCsnbc,snbc!");

    }

    @Test
    @DisplayName("移除字符串中的中文，字符串为空")
    @Tags({
            @Tag("@id:108"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/5/22")
    })
    void testremoveChinese_stringNull() {
        assertThrows(IllegalArgumentException.class,
                () -> StringReplaceUtils.removeChinese(null),
                "Source String is null");
        assertThrows(IllegalArgumentException.class,
                () -> StringReplaceUtils.removeChinese(""),
                "Source String is null");
    }

    @Test
    @DisplayName("移除字符串中的英文，正常情况")
    @Tags({
            @Tag("@id:108"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/5/22")
    })
    void testremoveEnglish_normal() {
        assertEquals( StringReplaceUtils.removeEnglish("我爱新北洋SNBCsnbc,萤启snbc!"),
                "我爱新北洋,萤启!");
    }

    @Test
    @DisplayName("移除字符串中的英文，字符串为空")
    @Tags({
            @Tag("@id:108"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/5/22")
    })
    void testremoveEnglish_stringNull() {
        assertThrows(IllegalArgumentException.class,
                () -> StringReplaceUtils.removeEnglish(null),
                "Source String is null");
        assertThrows(IllegalArgumentException.class,
                () -> StringReplaceUtils.removeEnglish(""),
                "Source String is null");
    }

}