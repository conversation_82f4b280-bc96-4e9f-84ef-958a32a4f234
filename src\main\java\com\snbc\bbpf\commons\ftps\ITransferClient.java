/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.ftps;

import java.nio.file.Path;
import java.util.List;
import java.util.Map;
import java.util.function.Predicate;

/**
 * 【数据传输】 定义统一的连接、上传和下载等接口类
 *
 * <AUTHOR>
 * @module 数据传输
 * @date 2023/9/24 17:50
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public interface ITransferClient {

    /**
     * 客户端连接操作
     * @return boolean
     * @throws
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/9/27
     */
    void connect();

    /**
     * 客户端关闭连接
     * @param
     * @return void
     * @throws
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/9/27
     */
    void disconnect();

    /**
     * 上传文件操作
     * 默认不删除本地文件，会覆盖远端重名文件
     * @param localFilePath 本地文件路径
     * @param remoteDirPath 上传的文件路径
     * @return boolean 上传是否成功
     * @throws
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/9/27
     */
    boolean upload(String localFilePath, String remoteDirPath);

    /**
     * 上传文件操作
     * @param localFilePath 本地文件路径
     * @param remoteDirPath 上传的文件路径
     * @param deleteLocalFileIfUploadSuccess  上传成功后是否删除本地文件
     * @param overwriteIfFileExist 是否覆盖已有文件
     * @return boolean 上传是否成功
     * @throws
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/9/27
     */
    boolean upload(String localFilePath,
                          String remoteDirPath,
                          boolean deleteLocalFileIfUploadSuccess,
                          boolean overwriteIfFileExist);

    /**
     * 下载指定文件
     * 默认不删除远端文件，会覆盖本地重名文件
     * @param remoteFilePath    远端文件路径
     * @param localDirPath   本地文件路径
     * @return boolean  下载是否成功
     * @throws
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/9/27
     */
    boolean download(String remoteFilePath, String localDirPath) ;

    /**
     * 下载指定文件
     * @param remoteFilePath    远端文件路径
     * @param localDirPath      本地文件路径
     * @param deleteRemoteFileIfDownloadSuccess 下载成功后是否删除远端文件
     * @param overwriteIfFileExist  是否覆盖已有文件
     * @return boolean  下载是否成功
     * @throws
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/9/27
     */
    boolean download(String remoteFilePath,
                            String localDirPath,
                            boolean deleteRemoteFileIfDownloadSuccess,
                            boolean overwriteIfFileExist);

    /**
     * 删除远端文件
     * @param remoteFilePath    远端文件路径
     * @return boolean  返回是否成功
     * @throws
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/9/27
     */
    boolean deleteRemoteFile(String remoteFilePath);

    /**
     * 批量删除文件名符合条件的远端文件
     * @param remoteDirPath  远端文件目录
     * @param predicate 文件名过滤器
     * @return java.util.Map<java.lang.String,java.lang.Boolean>  符合条件的远程文件是否删除成功列表
     * @throws
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/8/23
     */
    Map<String,Boolean> batchDeleteRemoteFiles(String remoteDirPath, Predicate<String> predicate);

    /**
     * 删除远端的目录
     * @param remoteDirPath 远端目录路径
     * @return boolean  返回是否成功
     * @throws
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/9/27
     */
    boolean deleteRemoteDir(String remoteDirPath);

    /**
     * 列出远程默认目录的所有文件
     * @return 文件数组
     * <AUTHOR>
     * @date 2023/11/05
     * @since 1.3.0
     */
    List<String> listFiles();

    /**
     * 列出指定远程目录的所有文件，目录不存在或目录下没有文件返回空数组
     * @param remoteDirPath 远程目录
     * @return 文件数组
     * <AUTHOR>
     * @date 2023/11/05
     * @since 1.3.0
     */
    List<String> listFiles(String remoteDirPath);

    /**
     * 列出指定远程目录的符合条件的文件，目录不存在或目录下没有文件返回空数组
     * @param remoteDirPath  远程目录
     * @param fileNamePredicate 文件名过滤器
     * @return java.util.List<java.lang.String> 返回文件名列表
     * @throws
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/8/23
     */
    List<String> listFiles(String remoteDirPath, Predicate<String> fileNamePredicate);

    /**
     * 批量上传文件操作
     * 默认不删除本地文件，会覆盖远端重名文件
     * @param localPath 本地文件路径
     * @param predicate 文件过滤器
     * @param remoteDirPath 远端目录
     * @return java.util.Map<java.lang.String,java.lang.Boolean> 返回文件上传成功列表
     * @throws
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/8/23
     */
    Map<String,Boolean> batchUpload(String localPath, Predicate<Path> predicate, String remoteDirPath);

    /**
     * 批量上传文件操作
     * @param localPath 本地文件路径
     * @param predicate 文件过滤器
     * @param remoteDirPath 远端目录
     * @param deleteLocalFileIfUploadSuccess  上传成功后是否删除本地文件
     * @param overwriteIfFileExist 是否覆盖已有文件
     * @return java.util.Map<java.lang.String,java.lang.Boolean>  返回文件上传成功列表
     * @throws
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/8/23
     */
    Map<String,Boolean> batchUpload(String localPath,
                        Predicate<Path> predicate,
                        String remoteDirPath,
                        boolean deleteLocalFileIfUploadSuccess,
                        boolean overwriteIfFileExist);

    /**
     * 批量下载文件名符合条件的文件
     * 默认不删除远端文件，会覆盖本地重名文件
     * @param remoteFilePath  远端文件路径
     * @param predicate 文件过滤器
     * @param localDirPath  本地文件路径
     * @return java.util.Map<java.lang.String,java.lang.Boolean> 返回文件下载成功列表
     * @throws
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/8/23
     */
    Map<String,Boolean> batchDownload(String remoteFilePath, Predicate<String> predicate,String localDirPath) ;

    /**
     * 批量下载文件名符合条件的文件
     * @param remoteDirPath 远端文件路径
     * @param predicate 文件过滤器
     * @param localDirPath 本地文件路径
     * @param deleteRemoteFileIfDownloadSuccess 下载成功后是否删除远端文件
     * @param overwriteIfFileExist 是否覆盖已有文件
     * @return java.util.Map<java.lang.String,java.lang.Boolean> 返回文件下载成功列表
     * @throws
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/8/23
     */
    Map<String,Boolean> batchDownload(String remoteDirPath,
                                    Predicate<String> predicate,
                                    String localDirPath,
                                    boolean deleteRemoteFileIfDownloadSuccess,
                                    boolean overwriteIfFileExist);

    /**
     * 在远端创建目录
     * @param remoteDirPath 远端目录
     * @param dirName  需要创建的目录名称
     * @return boolean 是否创建成功
     * @throws
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/8/23
     */
    boolean createRemoteDir(String remoteDirPath, String dirName);
}
