package com.snbc.bbpf.commons.randoms;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * 随机数字字符串测试类
 *
 * <AUTHOR>
 * @module 字符串
 * @date 2023/6/13 15:43
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class RandomNumberUtilsTest {

    @DisplayName("指定位数生成随机字符仅数字")
    @Tags({
            @Tag("@id:18"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/4/28")
    })
    @Test
    void testRandom_numeric() {
        final String numericLetters = RandomNumberUtils.randomNumeric(3);
        assertEquals(3, numericLetters.length());
        assertEquals(true, numericLetters.matches("[0-9]{3}"));
    }

    @DisplayName("指定位数生成随机字符仅数字,可指定首位不允许为0")
    @Tags({
            @Tag("@id:32"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/6/13")
    })
    @Test
    void testRandom_numeric_first_letter_no_zero() {
        boolean fisrtLetterNoZero = true;
        for (int i = 0; i < 1000; i++) {
            final String numericLetters = RandomNumberUtils.randomNumeric(3, false);
            if (numericLetters.charAt(0) == '0') {
                fisrtLetterNoZero = false;
                break;
            }
        }
        assertEquals(true, fisrtLetterNoZero);
    }

    @DisplayName("指定位数生成随机字符仅数字,可指定首位允许为0")
    @Tags({
            @Tag("@id:32"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/6/13")
    })
    @Test
    void testRandom_numeric_first_letter_zero() {
        boolean fisrtLetterNoZero = true;
        for (int i = 0; i < 10000; i++) {
            final String numericLetters = RandomNumberUtils.randomNumeric(3, true);
            if (numericLetters.charAt(0) == '0') {
                fisrtLetterNoZero = false;
                break;
            }
        }
        assertEquals(false, fisrtLetterNoZero);
    }


}