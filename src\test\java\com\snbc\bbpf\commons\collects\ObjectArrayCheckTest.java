package com.snbc.bbpf.commons.collects;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalUnit;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTimeout;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 对象数组是否为空
 *
 * <AUTHOR>
 * @module 集合处理
 * @date 2023/4/23 11:32
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class ObjectArrayCheckTest {

    @Test
    @DisplayName("对象数组不为空")
    @Tags({
            @Tag("@id:"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testIsNull_notNull() {
        assertFalse(ObjectArrayCheck.isNull(new Object[]{"objs"}));
    }

    @Test
    @DisplayName("对象数组为空")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testIsNull_isNull() {
        assertTrue(ObjectArrayCheck.isNull(null));
    }

    @Test
    @DisplayName("对象数组不为空集合size为0")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testIsNull_notNullAndSizeIsZero() {
        assertFalse(ObjectArrayCheck.isNull(new Object[]{}));
    }

    @Test
    @DisplayName("空对象数组判断是否存在空对象")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testIsEmpty_isNull() {
        assertTrue(ObjectArrayCheck.isEmpty(null));
    }

    @Test
    @DisplayName("空对象数组判断是否存在空对象")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testIsEmpty_notNullAndSizeIsZero() {
        assertTrue(ObjectArrayCheck.isEmpty(new Object[]{}));
    }
    @Test
    @DisplayName("空对象数组判断是否存在空对象")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testIsEmpty_notNullandChildNotNull() {
        assertFalse(ObjectArrayCheck.isEmpty(new Object[]{"object"}));
    }
    @Test
    @DisplayName("对象数据中包含字符串返回false")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasNullItem_hasString() {
        assertFalse(ObjectArrayCheck.hasNullItem(new Object[]{"objs"}));
    }

    @Test
    @DisplayName("对象数据Null返回true")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasNullItem_isNull() {
        assertTrue(ObjectArrayCheck.hasNullItem(null));
    }

    @Test
    @DisplayName("对象数据中包含NULL字符串返回true")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasNullItem_hasNull() {
        assertTrue(ObjectArrayCheck.hasNullItem(new Object[]{null}));
    }
    @Test
    @DisplayName("对象数据中包含Empty字符串返回false")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasNullItem_hasEmptyString() {
        assertFalse(ObjectArrayCheck.hasNullItem(new Object[]{""}));
    }

    @Test
    @DisplayName("对象数据中包含两个字符串其中一个是Empty字符返回false")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasNullItem_hasTwoStringOfOneEmptyString() {
        assertFalse(ObjectArrayCheck.hasNullItem(new Object[]{"objs",""}));
    }
    @Test
    @DisplayName("对象数据中包含两个字符串其中一个是null字符返回true")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasNullItem_hasTwoStringOfOneNullString() {
        assertTrue(ObjectArrayCheck.hasNullItem(new Object[]{"objs",null}));
    }
    @Test
    @DisplayName("对象数据中包含对象数组大小为0返回false")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasNullItem_hasObjectArraySizeIsZero() {
        assertFalse(ObjectArrayCheck.hasNullItem(
                new Object[]{"objs",new Object[]{}}));
    }

    @Test
    @DisplayName("对象数据中包含对象数组中有NULL返回true")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasNullItem_hasObjectArrayNullString() {
        assertTrue(ObjectArrayCheck.hasNullItem(
                new Object[]{"objs",new Object[]{null}}));
    }

    @Test
    @DisplayName("对象数据中包含对象数组中有2个字符串其中一个为NULL返回true")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasNullItem_hasObjectArrayTwoStringOneOfNullString() {
        assertTrue(ObjectArrayCheck.hasNullItem(
                new Object[]{"objs",new Object[]{"abc",null}}));
    }
    @Test
    @DisplayName("对象数据中包含对象数组中有2个字符串其中一个为Empty返回false")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasNullItem_hasObjectArrayTwoStringOneOfEmptyString() {
        assertFalse(ObjectArrayCheck.hasNullItem(
                new Object[]{"objs",new Object[]{"abc",""}}));
    }

    @Test
    @DisplayName("对象数据中包含对象数组中有2个Object其中一个为Empty返回false")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasNullItem_hasObjectArrayTwoObjectOneOfEmptyString() {
        assertFalse(ObjectArrayCheck.hasNullItem(
                new Object[]{"objs",new Object[]{1,""}}));
    }
    @Test
    @DisplayName("对象数据中包含对象数组中有2个Object其中一个List返回false")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasNullItem_hasObjectArrayTwoObjectOneOfList() {
        assertFalse(ObjectArrayCheck.hasNullItem(
                new Object[]{"objs",new Object[]{1, Arrays.asList("12","23")}}));
    }
    @Test
    @DisplayName("对象数据中包含对象数组中有2个Object其中一个List有一个是null返回true")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasNullItem_hasObjectArrayTwoObjectOneOfListIsNull() {
        assertTrue(ObjectArrayCheck.hasNullItem(
                new Object[]{"objs",new Object[]{1, Arrays.asList("12",null)}}));
    }
    @Test
    @DisplayName("对象数据中包含对象数组中有2个Object其中一个List有一个是empty返回false")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasNullItem_hasObjectArrayTwoObjectOneOfListIsEmpty() {
        assertFalse(ObjectArrayCheck.hasNullItem(
                new Object[]{"objs",new Object[]{1, Arrays.asList("12","")}}));
    }

    @Test
    @DisplayName("对象数据中包含对象数组中有2个Object其中一个Map返回false")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasNullItem_hasObjectArrayTwoObjectOneOfMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("1",1);
        map.put("2",2);
        assertFalse(ObjectArrayCheck.hasNullItem(
                new Object[]{"objs",map}));
    }
    @Test
    @DisplayName("对象数据中包含对象数组中有2个Object其中一个Map中key是null返回true")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasNullItem_hasObjectArrayTwoObjectOneOfMapKeyIsNull() {
        Map<String, Object> map = new HashMap<>();
        map.put(null,1);
        map.put("2",2);
        assertTrue(ObjectArrayCheck.hasNullItem(
                new Object[]{"objs",map}));
    }
    @Test
    @DisplayName("对象数据中包含对象数组中有2个Object其中一个Map值是null返回true")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasNullItem_hasObjectArrayTwoObjectOneOfMapValueIsNull() {
        Map<String, Object> map = new HashMap<>();
        map.put("1",null);
        map.put("2",2);
        assertTrue(ObjectArrayCheck.hasNullItem(
                new Object[]{"objs",map}));
    }

    @Test
    @DisplayName("对象数据中包含对象数组中有2个Object其中一个Map是值是empty返回false")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasNullItem_hasObjectArrayTwoObjectOneOfMapValueIsEmpty() {
        Map<String, Object> map = new HashMap<>();
        map.put("1","");
        map.put("2",2);
        assertFalse(ObjectArrayCheck.hasNullItem(
                new Object[]{"objs",map}));
    }
    @Test
    @DisplayName("对象数据中包含对象数组中有2个Object其中一个Map是key是empty返回false")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasNullItem_hasObjectArrayTwoObjectOneOfMapKeyIsEmpty() {
        Map<String, Object> map = new HashMap<>();
        map.put("","1");
        map.put("2",2);
        assertFalse(ObjectArrayCheck.hasNullItem(
                new Object[]{"objs",map}));
    }
    @Test
    @DisplayName("对象数据中包含对象数组中有2个Object其中一个Map是key是empty返回false")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasNullItem_hasObjectArrayTwoObjectOneOfMapIsEmpty() {
        Map<String, Object> map = new HashMap<>();
        assertFalse(ObjectArrayCheck.hasNullItem(
                new Object[]{"objs",map}));
    }

    @Test
    @DisplayName("对象数据中包含字符串返回false")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasEmptyItem_hasString() {
        assertFalse(ObjectArrayCheck.hasEmptyItem(new Object[]{"objs"}));
    }

    @Test
    @DisplayName("对象数据Null返回true")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasEmptyItem_isNull() {
        assertTrue(ObjectArrayCheck.hasEmptyItem(null));
    }

    @Test
    @DisplayName("对象数据中包含NULL字符串返回true")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasEmptyItem_hasNull() {
        assertTrue(ObjectArrayCheck.hasEmptyItem(new Object[]{null}));
    }
    @Test
    @DisplayName("对象数据中包含Empty字符串返回true")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasEmptyItem_hasEmptyString() {
        assertTrue(ObjectArrayCheck.hasEmptyItem(new Object[]{""}));
    }

    @Test
    @DisplayName("对象数据中包含两个字符串其中一个是Empty字符返回true")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasEmptyItem_hasTwoStringOfOneEmptyString() {
        assertTrue(ObjectArrayCheck.hasEmptyItem(new Object[]{"objs",""}));
    }
    @Test
    @DisplayName("对象数据中包含两个字符串其中一个是null字符返回true")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasEmptyItem_hasTwoStringOfOneNullString() {
        assertTrue(ObjectArrayCheck.hasEmptyItem(new Object[]{"objs",null}));
    }
    @Test
    @DisplayName("对象数据中包含对象数组大小为0返回true")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasEmptyItem_hasObjectArraySizeIsZero() {
        assertTrue(ObjectArrayCheck.hasEmptyItem(
                new Object[]{"objs",new Object[]{}}));
    }

    @Test
    @DisplayName("对象数据中包含对象数组中有NULL返回true")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasEmptyItem_hasObjectArrayNullString() {
        assertTrue(ObjectArrayCheck.hasEmptyItem(
                new Object[]{"objs",new Object[]{null}}));
    }

    @Test
    @DisplayName("对象数据中包含对象数组中有2个字符串其中一个为NULL返回true")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasEmptyItem_hasObjectArrayTwoStringOneOfNullString() {
        assertTrue(ObjectArrayCheck.hasEmptyItem(
                new Object[]{"objs",new Object[]{"abc",null}}));
    }
    @Test
    @DisplayName("对象数据中包含对象数组中有2个字符串其中一个为Empty返回true")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasEmptyItem_hasObjectArrayTwoStringOneOfEmptyString() {
        assertTrue(ObjectArrayCheck.hasEmptyItem(
                new Object[]{"objs",new Object[]{"abc",""}}));
    }

    @Test
    @DisplayName("对象数据中包含对象数组中有2个Object其中一个为Empty返回true")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasEmptyItem_hasObjectArrayTwoObjectOneOfEmptyString() {
        assertTrue(ObjectArrayCheck.hasEmptyItem(
                new Object[]{"objs",new Object[]{1,""}}));
    }
    @Test
    @DisplayName("对象数据中包含对象数组中有2个Object其中一个List返回false")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasEmptyItem_hasObjectArrayTwoObjectOneOfList() {
        assertFalse(ObjectArrayCheck.hasEmptyItem(
                new Object[]{"objs",new Object[]{1, Arrays.asList("12","23")}}));
    }
    @Test
    @DisplayName("对象数据中包含对象数组中有2个Object其中一个List有一个是null返回true")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasEmptyItem_hasObjectArrayTwoObjectOneOfListIsNull() {
        assertTrue(ObjectArrayCheck.hasEmptyItem(
                new Object[]{"objs",new Object[]{1, Arrays.asList("12",null)}}));
    }
    @Test
    @DisplayName("对象数据中包含对象数组中有2个Object其中一个List有一个是empty返回false")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasEmptyItem_hasObjectArrayTwoObjectOneOfListIsEmpty() {
        assertTrue(ObjectArrayCheck.hasEmptyItem(
                new Object[]{"objs",new Object[]{1, Arrays.asList("12","")}}));
    }

    @Test
    @DisplayName("对象数据中包含对象数组中有2个Object其中一个Map返回false")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasEmptyItem_hasObjectArrayTwoObjectOneOfMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("1",1);
        map.put("2",2);
        assertFalse(ObjectArrayCheck.hasEmptyItem(
                new Object[]{"objs",map}));
    }
    @Test
    @DisplayName("对象数据中包含对象数组中有2个Object其中一个Map中key是null返回true")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasEmptyItem_hasObjectArrayTwoObjectOneOfMapKeyIsNull() {
        Map<String, Object> map = new HashMap<>();
        map.put(null,1);
        map.put("2",2);
        assertTrue(ObjectArrayCheck.hasEmptyItem(
                new Object[]{"objs",map}));
    }
    @Test
    @DisplayName("对象数据中包含对象数组中有2个Object其中一个Map值是null返回true")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasEmptyItem_hasObjectArrayTwoObjectOneOfMapValueIsNull() {
        Map<String, Object> map = new HashMap<>();
        map.put("1",null);
        map.put("2",2);
        assertTrue(ObjectArrayCheck.hasEmptyItem(
                new Object[]{"objs",map}));
    }

    @Test
    @DisplayName("对象数据中包含对象数组中有2个Object其中一个Map是值是empty返回false")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasEmptyItem_hasObjectArrayTwoObjectOneOfMapValueIsEmpty() {
        Map<String, Object> map = new HashMap<>();
        map.put("1","");
        map.put("2",2);
        assertTrue(ObjectArrayCheck.hasEmptyItem(
                new Object[]{"objs",map}));
    }
    @Test
    @DisplayName("对象数据中包含对象数组中有2个Object其中一个Map是key是empty返回false")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasEmptyItem_hasObjectArrayTwoObjectOneOfMapKeyIsEmpty() {
        Map<String, Object> map = new HashMap<>();
        map.put("","1");
        map.put("2",2);
        assertTrue(ObjectArrayCheck.hasEmptyItem(
                new Object[]{"objs",map}));
    }
    @Test
    @DisplayName("对象数据中包含对象数组中有2个Object其中一个Map是key是empty返回false")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasEmptyItem_hasObjectArrayTwoObjectOneOfMapIsEmpty() {
        Map<String, Object> map = new HashMap<>();
        assertTrue(ObjectArrayCheck.hasEmptyItem(
                new Object[]{"objs",map}));
    }

    @Test
    @DisplayName("创建包含10000*10个对象数组在200毫秒内")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/23")
    })
    void testHasEmptyItem_checkTime() {
        Object[] objs =  greatObjectArray(10000);
        assertTimeout(Duration.of(400, ChronoUnit.MILLIS),
                () -> ObjectArrayCheck.hasEmptyItem(objs));
    }

    private Object[] greatObjectArray(int t){
        Object[] objs2 = new Object[t];
        for(int m = 0 ;m < t ;m++) {
            Object[] objs = new Object[10];
            for (int i = 0; i < 10; i++) {
                objs[i] = greatObjectArray();
            }
            objs2[m]=objs;
        }
        return objs2;
    }

    private Object[] greatObjectArray(){
        Object[] objs1 = new Object[10];
        for(int i = 0; i < 10; i++) {
            objs1[i] = i;
        }
        return objs1;
    }
}
