/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.collects;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.google.common.collect.Lists;
import com.snbc.bbpf.commons.dates.LocalDateTimeUtils;
import com.snbc.bbpf.commons.validations.annotation.ValidStringType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * List转换成JSON字符串处理单元测试类
 *
 * <AUTHOR>
 * @module 集合处理
 * @date 2023/9/19 20:00
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class ListUtilsTest {
    Container container;
    List<Object> list = new ArrayList<>();
    final LocalDateTime now = LocalDateTime.of(2023, 9, 5, 15, 30, 45, 0);
    HasChildMapObject hasChildMapObject1 = new HasChildMapObject("1", 1);
    HasChildMapObject hasChildMapObject2 = new HasChildMapObject("2", 2);

    @BeforeEach
    void setup() {

        container = new Container(12,
                "新北洋1号售货机",
                LocalDateTimeUtils.toDate(now)
                ,
                now, null, null, LocalDateTimeUtils.toDate(now));
    }


    @Test
    @DisplayName("空list转为json")
    @Tags({
            @Tag("@id:53"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/19")
    })
    void testListToJson_emptyListToJson() {
        assertEquals(null, ListUtils.listToJson(new ArrayList<>()));
    }

    @Test
    @DisplayName("value值为空转为json")
    @Tags({
            @Tag("@id:53"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/19")
    })
    void testListToJson_nullValueListToJson() {
        list.add("");
        assertEquals(null, ListUtils.listToJson(list));
    }

    @Test
    @DisplayName("嵌套list转为json")
    @Tags({
            @Tag("@id:53"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/19")
    })
    void testListToJson_nestedListToJson() {
        List<HasChildMapObject> list1 = new ArrayList<>();
        list1.add(hasChildMapObject1);
        List<HasChildMapObject> list2 = new ArrayList<>();
        list2.add(hasChildMapObject2);
        List<List<HasChildMapObject>> list3 = new ArrayList<>();
        list3.add(list1);
        list3.add(list2);
        assertEquals("[[{\"str\":\"1\",\"itNumber\":1,\"testMapObject\":{\"str\":\"456\",\"itNumber\":789,\"map\":{\"data2\":\"2\",\"data1\":\"1\"}}}],[{\"str\":\"2\",\"itNumber\":2,\"testMapObject\":{\"str\":\"456\",\"itNumber\":789,\"map\":{\"data2\":\"2\",\"data1\":\"1\"}}}]]", ListUtils.listToJson(list3));

    }

    @Test
    @DisplayName("value为enum的list转为json")
    @Tags({
            @Tag("@id:53"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/19")
    })
    void testListToJson_enumListToJson() {
        list.add(ValidStringType.REGEX);
        list.add(ValidStringType.PHONE_NUMBER);
        assertEquals("[\"REGEX\",\"PHONE_NUMBER\"]", ListUtils.listToJson(list));

    }

    @Test
    @DisplayName("value为各种类型的list转为json")
    @Tags({
            @Tag("@id:53"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/19")
    })
    void testListToJson_fixTypeToJson() {
        list.add(LocalDateTimeUtils.toDate(now));
        list.add(hasChildMapObject1);
        list.add(Lists.newArrayList("1", "2"));
        list.add(ValidStringType.REGEX);
        Map<String, String> map1 = new HashMap<>();
        map1.put("key", "value");
        list.add(map1);
        list.add("str");
        list.add(20);
        assertEquals("[1693899045000,{\"str\":\"1\",\"itNumber\":1,\"testMapObject\":{\"str\":\"456\",\"itNumber\":789,\"map\":{\"data2\":\"2\",\"data1\":\"1\"}}},[\"1\",\"2\"],\"REGEX\",{\"key\":\"value\"},\"str\",20]", ListUtils.listToJson(list));

    }

    @Test
    @DisplayName("value为属性未初始化的Bean转为json")
    @Tags({
            @Tag("@id:53"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/19")
    })
    void testListToJson_emptyValueBeanListToJson() {
        list.add(new HasChildMapObject());
        assertEquals("[{\"str\":null,\"itNumber\":0,\"testMapObject\":{\"str\":null,\"itNumber\":0,\"map\":null}}]", ListUtils.listToJson(list));

    }

    @Test
    @DisplayName("含有注解的方式转为json")
    @Tags({
            @Tag("@id:53"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/19")
    })
    void testListToJson_jaksonAnnotion() {
        list.add(container);
        assertEquals("[{\"id\":12,\"name\":\"新北洋1号售货机\",\"createTime\":\"2023-09-05 15:30:45\",\"updateTime\":\"2023-09-05 15:30:45\",\"mark\":null,\"tags\":null,\"markDate\":1693899045000}]", ListUtils.listToJson(list));

    }

    @Test
    @DisplayName("使用自定义ObjectMapper，空list转为json")
    @Tags({
            @Tag("@id:53"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/19")
    })
    void testListToJson_emptyListToJsonSelfMapper() {
        assertEquals(null, ListUtils.listToJson(new ArrayList<>(), null, null));
    }

    @Test
    @DisplayName("使用自定义ObjectMapper，value值为空转为json")
    @Tags({
            @Tag("@id:53"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/19")
    })
    void testListToJson_nullValueListToJsonSelfMapper() {
        list.add("");
        assertEquals(null, ListUtils.listToJson(list, null, null));
    }

    @Test
    @DisplayName("使用自定义ObjectMapper，嵌套list转为json")
    @Tags({
            @Tag("@id:53"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/19")
    })
    void testListToJson_nestedListToJsonSelfMapper() {
        List<HasChildMapObject> list1 = new ArrayList<>();
        list1.add(hasChildMapObject1);
        List<HasChildMapObject> list2 = new ArrayList<>();
        list2.add(hasChildMapObject2);
        List<List<HasChildMapObject>> list3 = new ArrayList<>();
        list3.add(list1);
        list3.add(list2);
        assertEquals("[[{\"str\":\"1\",\"itNumber\":1,\"testMapObject\":{\"str\":\"456\",\"itNumber\":789,\"map\":{\"data2\":\"2\",\"data1\":\"1\"}}}],[{\"str\":\"2\",\"itNumber\":2,\"testMapObject\":{\"str\":\"456\",\"itNumber\":789,\"map\":{\"data2\":\"2\",\"data1\":\"1\"}}}]]", ListUtils.listToJson(list3));

    }

    @Test
    @DisplayName("使用自定义ObjectMapper，value为enum的list转为json，输出索引")
    @Tags({
            @Tag("@id:53"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/19")
    })
    void testListToJson_enumListToJsonSelfMapper() {
        final ArrayList<SerializationFeature> enablesFeatures = Lists.newArrayList(SerializationFeature.WRITE_ENUMS_USING_INDEX);

        list.add(ValidStringType.REGEX);
        list.add(ValidStringType.PHONE_NUMBER);
        assertEquals("[7,5]", ListUtils.listToJson(list, enablesFeatures, null));
    }

    @Test
    @DisplayName("使用自定义ObjectMapper，value为各种类型的list转为json")
    @Tags({
            @Tag("@id:53"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/19")
    })
    void testListToJson_fixTypeToJsonSelfMapper() {
        list.add(LocalDateTimeUtils.toDate(now));
        list.add(hasChildMapObject1);
        list.add(Lists.newArrayList("1", "2"));
        list.add(ValidStringType.REGEX);
        Map<String, String> map1 = new HashMap<>();
        map1.put("key", "value");
        list.add(map1);
        list.add("str");
        list.add(20);
        assertEquals("[1693899045000,{\"str\":\"1\",\"itNumber\":1,\"testMapObject\":{\"str\":\"456\",\"itNumber\":789,\"map\":{\"data2\":\"2\",\"data1\":\"1\"}}},[\"1\",\"2\"],\"REGEX\",{\"key\":\"value\"},\"str\",20]", ListUtils.listToJson(list, null, null));

    }

}

class HasChildMapObject {
    private String str;
    private int itNumber;
    private MapObject testMapObject;

    public MapObject getTestMapObject() {
        return testMapObject;
    }

    public void setTestMapObject(MapObject testMapObject) {
        this.testMapObject = testMapObject;
    }

    public int getItNumber() {
        return itNumber;
    }

    public String getStr() {
        return str;
    }

    public void setItNumber(int itNumber) {
        this.itNumber = itNumber;
    }

    public void setStr(String str) {
        this.str = str;
    }

    public HasChildMapObject() {
        this.testMapObject = new MapObject();
    }

    public HasChildMapObject(String str, int tNumber) {
        this.str = str;
        this.itNumber = tNumber;
        this.testMapObject = new MapObject("456", 789);
        testMapObject.setMap(new HashMap<>());
        testMapObject.getMap().put("data1", "1");
        testMapObject.getMap().put("data2", "2");
    }
}

class MapObject {
    private String str;
    private int itNumber;
    private Map<String, String> map;

    public void setStr(String str) {
        this.str = str;
    }

    public void setItNumber(int itNumber) {
        this.itNumber = itNumber;
    }

    public String getStr() {
        return str;
    }

    public int getItNumber() {
        return itNumber;
    }

    public Map<String, String> getMap() {
        return map;
    }

    public void setMap(Map<String, String> map) {
        this.map = map;
    }

    public MapObject() {
    }

    public MapObject(String str, int tNumber) {
        this.str = str;
        this.itNumber = tNumber;
        map = new HashMap<>();
        map.put("data1", "1");
        map.put("data2", "2");
    }

}

class Container {
    private Integer id;
    /**
     * 售货机名称
     */
    private String name;
    /**
     * 售货机创建时间
     * 如果项目使用 spring.jackson.time-zone=Asia/Shanghai
     * spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createTime;
    /**
     * 售货机更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    private String mark;

    private List<String> tags;

    private Date markDate;

    public Container() {
    }

    public Container(Integer id, String name, Date createTime, LocalDateTime updateTime, String mark, List<String> tags, Date markDate) {
        this.id = id;
        this.name = name;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.mark = mark;
        this.tags = tags;
        this.markDate = markDate;
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getMark() {
        return mark;
    }

    public void setMark(String mark) {
        this.mark = mark;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }


    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public Date getMarkDate() {
        return markDate;
    }

    public void setMarkDate(Date markDate) {
        this.markDate = markDate;
    }

    @Override
    public String toString() {
        return "ContainerInfo{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", mark='" + mark + '\'' +
                ", tags=" + tags +
                ", markDate=" + markDate +
                '}';
    }

}