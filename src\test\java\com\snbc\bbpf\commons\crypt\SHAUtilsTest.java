/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.crypt;



import java.security.NoSuchAlgorithmException;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
/**
 * SH5散列算法工具类单元测试类
 *
 * <AUTHOR>
 * @module 加解密
 * @date 2023-12-08 10:44
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class SHAUtilsTest {
    /**
     * 算法内容
     */
    String content;
    /**
     * SHA256算法
     */
    String contentResult_256;
    /**
     * SHA512算法
     */
    String contentResult_512;
    /**
     * SHA1算法
     */
    String contentResult;

    @BeforeEach
    void setup() {
        content = "12345678啊哈哈<!@#$%^&*()_+=-<>?:asdZXC";
        contentResult_256="9cacbd3eac00575cbff25acc945e9aa461a19902b732fc014e11dd54cbea4b68";
        contentResult_512="319f081a6aae2cd482336ffb5d8d0668835b4c10caa4a1955fb3041c936ede9ba58bcef6fbfed06e84bb1259ce63528c01c41e5126a87959c0a9774de52b6997";
        contentResult="ac6deae372c83a3a2086cded349729bc5fb23b86";
    }
    @DisplayName("使用SHA计算字符串256正常")
    @Tags({
            @Tag("@id:87"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/12/08")
    })
    @Test
    void encrypt() throws Exception {
        assertEquals(contentResult_256,SHAUtils.encrypt(content));
    }
    @DisplayName("使用SHA计算字符串512正常")
    @Tags({
            @Tag("@id:87"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/12/08")
    })
    @Test
    void encryptFive()throws Exception {
        assertEquals(contentResult_512,SHAUtils.encryptFive(content));
    }
    @DisplayName("使用SHA计算字符串SHA1正常")
    @Tags({
            @Tag("@id:87"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/12/08")
    })
    @Test
    void testEncrypt() throws Exception {
        assertEquals(contentResult,SHAUtils.encrypt(content,"SHA1"));
    }
    @DisplayName("SHA算法内容为空")
    @Tags({
            @Tag("@id:87"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/12/08")
    })
    @Test
    void testEncrypt_content_isblank() {
        RuntimeException thrown = assertThrows(RuntimeException.class,
                () -> SHAUtils.encrypt(""));
        assertEquals("[SHAUtils][encrypt] (msg,algorithm) is blank", thrown.getMessage());
    }
    @DisplayName("SHA算法内容为null")
    @Tags({
            @Tag("@id:87"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/12/08")
    })
    @Test
    void testEncrypt_content_isnull()  {
        RuntimeException thrown = assertThrows(RuntimeException.class,
                () -> SHAUtils.encrypt(null));
        assertEquals("[SHAUtils][encrypt] (msg,algorithm) is blank", thrown.getMessage());
    }
    @DisplayName("SHA算法 算法为null")
    @Tags({
            @Tag("@id:87"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/12/08")
    })
    @Test
    void testEncrypt_algorithm_isnull() {
        RuntimeException thrown = assertThrows(RuntimeException.class,
                () -> SHAUtils.encrypt(content, null));
        assertEquals("[SHAUtils][encrypt] (msg,algorithm) is blank", thrown.getMessage());
    }
    @DisplayName("SHA算法 算法为空")
    @Tags({
            @Tag("@id:87"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/12/08")
    })
    @Test
    void testEncrypt_algorithm_isblank()  {
        RuntimeException thrown = assertThrows(RuntimeException.class,
                () -> SHAUtils.encrypt(content, ""));
        assertEquals("[SHAUtils][encrypt] (msg,algorithm) is blank", thrown.getMessage());
    }
    @DisplayName("SHA算法 算法不存在")
    @Tags({
            @Tag("@id:87"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/12/08")
    })
    @Test
    void testEncrypt_algorithm_iserror()  {
        NoSuchAlgorithmException thrown = assertThrows(NoSuchAlgorithmException.class,
                () -> SHAUtils.encrypt(content, "ERROR"));
        assertEquals("ERROR MessageDigest not available", thrown.getMessage());
    }
}