/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.validations.aspect;

import com.snbc.bbpf.commons.validations.annotation.ValidString;
import com.snbc.bbpf.commons.validations.annotation.ValidStringType;
import com.snbc.bbpf.commons.validations.validator.IStringValidator;
import com.snbc.bbpf.commons.validations.validator.PhoneNumberValidator;
import com.snbc.bbpf.commons.validations.validator.StringValidationStrategyFactory;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * 字符串格式验证注解方法参数的测试类
 *
 * <AUTHOR>
 * @module
 * @date 2023/10/30 13:59
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class ValidParameterStringFormatAspectTest {

    @Test
    @DisplayName("未添加注解，不进行参数校验，格式不符合未抛异常")
    @Tags({
            @Tag("@id:67"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/10/30")
    })
    void testNoAnnotation_noAnnotation() {
        assertFalse(noAnnotation("aaa"));
    }

    @Test
    @DisplayName("方法未添加注解，不进行参数校验，格式不符合未抛异常")
    @Tags({
            @Tag("@id:67"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/10/30")
    })
    void testNoMethodAnno_noMethodAnno() {
        assertFalse(noMethodAnno("aaa"));
    }

    @Test
    @DisplayName("方法参数未添加注解，不进行参数校验，格式不符合未抛异常")
    @Tags({
            @Tag("@id:67"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/10/30")
    })
    void testNoParameterAnno_noParameterAnno() {
        assertFalse(noParameterAnno("aaa"));
    }

    @Test
    @DisplayName("方法参数添加注解，参数校验失败")
    @Tags({
            @Tag("@id:67"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/10/30")
    })
    void testAnnoString_annoString() {
        IllegalArgumentException thrown1 = assertThrows(IllegalArgumentException.class,
                () -> annoString("17306311661", "aaa"));
        assertEquals("method[annoString]parameter[arg1] The phone number format is incorrect", thrown1.getMessage());
    }

    @Test
    @DisplayName("方法参数添加注解，参数为空")
    @Tags({
            @Tag("@id:67"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/10/30")
    })
    void testAnnoString_annoStringEmpty() {
        IllegalArgumentException thrown1 = assertThrows(IllegalArgumentException.class,
                () -> annoString(null, null));
        assertEquals("method[annoString]parameter[arg0] Mobile number is empty or null", thrown1.getMessage());
    }

    public Boolean noAnnotation(String str) {
        return PhoneNumberValidator.PHONE_PATTERN.matcher(str).matches();

    }

    public Boolean noMethodAnno(@ValidString String str) {
        return PhoneNumberValidator.PHONE_PATTERN.matcher(str).matches();
    }

    @ValidString
    public Boolean noParameterAnno(String str) {
        return PhoneNumberValidator.PHONE_PATTERN.matcher(str).matches();
    }

    @ValidString
    public void annoString(@ValidString(validStringType = ValidStringType.PHONE_NUMBER) String str, @ValidString(validStringType = ValidStringType.PHONE_NUMBER) String str1) {
        IStringValidator stringValidator = StringValidationStrategyFactory
                .getValidator(ValidStringType.PHONE_NUMBER);
        stringValidator.validate(str, "");
        stringValidator.validate(str1, "");
    }

}
