/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.bytes.protocol;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * 协议消息解码器
 * <p>将二进制数据解码为ProtocolMessage对象</p>
 * <p>使用小端字节序和UTF-8字符串编码</p>
 * 
 * <AUTHOR>
 * @module 字节处理
 * @date 2025/07/25 16:00
 * @copyright 2024 山东新北洋信息技术股份有限公司. All rights reserved
 * @since 1.7.0
 */
public class ProtocolMessageDecoder {
    
    /** 消息标识 */
    private static final byte[] MESSAGE_ID = {'S', 'T', 'U', 'M'};
    
    /**
     * 解码二进制数据为协议消息
     * @param data 要解码的二进制数据
     * @return 解码后的协议消息
     * @throws IllegalArgumentException 如果数据格式无效
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public static ProtocolMessage decode(byte[] data) {
        if (data == null || data.length < ProtocolMessageEncoder.FIXED_HEADER_LENGTH) {
            throw new IllegalArgumentException("Invalid protocol message data: insufficient length");
        }
        
        ByteBuffer buffer = ByteBuffer.wrap(data);
        buffer.order(ByteOrder.LITTLE_ENDIAN);
        
        // 解码固定消息头
        ProtocolMessage message = decodeFixedHeader(buffer);
        
        // 验证数据长度
        int expectedLength = ProtocolMessageEncoder.FIXED_HEADER_LENGTH + 
                           message.getExtendedHeaderLength() + 
                           message.getBodyLength();
        
        if (data.length < expectedLength) {
            throw new IllegalArgumentException("Invalid protocol message data: actual length less than expected");
        }
        
        // 解码扩展消息头
        if (message.getExtendedHeaderLength() > 0) {
            decodeExtendedHeader(buffer, message);
        }
        
        // 解码消息体
        if (message.getBodyLength() > 0) {
            byte[] messageBody = new byte[message.getBodyLength()];
            buffer.get(messageBody);
            message.setMessageBody(messageBody);
        }
        
        // 解码校验码（如果还有剩余数据）
        if (buffer.hasRemaining()) {
            byte[] checksum = new byte[buffer.remaining()];
            buffer.get(checksum);
            message.setChecksum(checksum);
        }
        
        return message;
    }
    
    /**
     * 解码固定消息头
     * @param buffer 字节缓冲区
     * @return 部分填充的协议消息对象
     */
    private static ProtocolMessage decodeFixedHeader(ByteBuffer buffer) {
        // 验证消息标识
        byte[] messageId = new byte[4];
        buffer.get(messageId);
        if (!Arrays.equals(messageId, MESSAGE_ID)) {
            throw new IllegalArgumentException("Invalid message identifier: expected STUM");
        }
        
        // 读取协议版本
        byte protocolVersion = buffer.get();
        
        // 读取消息类型
        short messageType = buffer.getShort();
        
        // 读取消息序号
        long sequenceNumber = buffer.getLong();
        
        // 读取时间戳
        long timestamp = buffer.getLong();
        
        // 读取扩展消息头数据长度
        int extendedHeaderLength = buffer.getInt();
        
        // 读取消息体数据长度
        int bodyLength = buffer.getInt();
        
        // 创建协议消息对象
        ProtocolMessage message = new ProtocolMessage(messageType, sequenceNumber, timestamp);
        message.setProtocolVersion(protocolVersion);
        
        // 设置长度信息（通过反射或添加包级别的setter方法）
        // 这里我们需要在ProtocolMessage中添加包级别的setter方法
        setExtendedHeaderLength(message, extendedHeaderLength);
        setBodyLength(message, bodyLength);
        
        return message;
    }
    
    /**
     * 解码扩展消息头
     * @param buffer 字节缓冲区
     * @param message 协议消息对象
     */
    private static void decodeExtendedHeader(ByteBuffer buffer, ProtocolMessage message) {
        // 解码商户编号
        String tenantId = decodeStringField(buffer);
        message.setTenantId(tenantId);
        
        // 解码产品编号
        String productId = decodeStringField(buffer);
        message.setProductId(productId);
        
        // 解码应用编号
        String appKey = decodeStringField(buffer);
        message.setAppKey(appKey);
        
        // 解码保留信息
        byte[] reservedInfo = decodeByteArrayField(buffer);
        message.setReservedInfo(reservedInfo);
    }
    
    /**
     * 解码字符串字段
     * @param buffer 字节缓冲区
     * @return 解码后的字符串
     */
    private static String decodeStringField(ByteBuffer buffer) {
        // 读取长度
        int length = buffer.getInt();
        
        if (length == 0) {
            return "";
        }
        
        if (length < 0 || length > buffer.remaining()) {
            throw new IllegalArgumentException("Invalid string field length: " + length);
        }
        
        // 读取内容
        byte[] stringBytes = new byte[length];
        buffer.get(stringBytes);
        
        return new String(stringBytes, StandardCharsets.UTF_8);
    }
    
    /**
     * 解码字节数组字段
     * @param buffer 字节缓冲区
     * @return 解码后的字节数组
     */
    private static byte[] decodeByteArrayField(ByteBuffer buffer) {
        // 读取长度
        int length = buffer.getInt();
        
        if (length == 0) {
            return new byte[0];
        }
        
        if (length < 0 || length > buffer.remaining()) {
            throw new IllegalArgumentException("Invalid byte array field length: " + length);
        }
        
        // 读取内容
        byte[] data = new byte[length];
        buffer.get(data);
        
        return data;
    }
    
    /**
     * 验证协议消息格式
     * @param data 要验证的二进制数据
     * @return 如果格式有效返回true，否则返回false
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public static boolean validateFormat(byte[] data) {
        try {
            decode(data);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 从数据中提取消息类型（不完整解码）
     * @param data 二进制数据
     * @return 消息类型
     * @throws IllegalArgumentException 如果数据格式无效
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public static short extractMessageType(byte[] data) {
        if (data == null || data.length < 7) {
            throw new IllegalArgumentException("Invalid data for message type extraction");
        }
        
        ByteBuffer buffer = ByteBuffer.wrap(data, 5, 2);
        buffer.order(ByteOrder.LITTLE_ENDIAN);
        return buffer.getShort();
    }
    
    /**
     * 从数据中提取消息序号（不完整解码）
     * @param data 二进制数据
     * @return 消息序号
     * @throws IllegalArgumentException 如果数据格式无效
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public static long extractSequenceNumber(byte[] data) {
        if (data == null || data.length < 15) {
            throw new IllegalArgumentException("Invalid data for sequence number extraction");
        }
        
        ByteBuffer buffer = ByteBuffer.wrap(data, 7, 8);
        buffer.order(ByteOrder.LITTLE_ENDIAN);
        return buffer.getLong();
    }
    
    // 包级别的辅助方法，用于设置私有字段
    static void setExtendedHeaderLength(ProtocolMessage message, int length) {
        // 这里需要在ProtocolMessage中添加包级别的setter方法
        // 或者使用反射来设置私有字段
        try {
            java.lang.reflect.Field field = ProtocolMessage.class.getDeclaredField("extendedHeaderLength");
            field.setAccessible(true);
            field.setInt(message, length);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set extended header length", e);
        }
    }
    
    static void setBodyLength(ProtocolMessage message, int length) {
        try {
            java.lang.reflect.Field field = ProtocolMessage.class.getDeclaredField("bodyLength");
            field.setAccessible(true);
            field.setInt(message, length);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set body length", e);
        }
    }
}
