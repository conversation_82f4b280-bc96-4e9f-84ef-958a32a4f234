/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.crypt;

/**
 * 算法填充方式
 *
 * <AUTHOR>
 * @module
 * @date 2023/11/26 21:19
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public enum Padding {
    NOPADDING("NoPadding"), // 不对明文进行填充
    PKCS5PADDING("PKCS5Padding"), //PKCS5Padding
    PKCS7PADDING("PKCS7Padding"), //PKCS7Padding
    MD5WITHRSA("MD5withRSA"), //MD5withRSA
    SHA256WITHRSA("SHA256withRSA"), //SHA256withRSA
    OAEPWITHSHA_1ANDMGF1PADDING("OAEPWithSHA-1AndMGF1Padding"), //OAEPWithSHA-1AndMGF1Padding
    OAEPWITHSHA_256ANDMGF1PADDING("OAEPWithSHA-256AndMGF1Padding"); //OAEPWithSHA-256AndMGF1Padding

    private String value;

    /**
     * @param value
     */
    Padding(String value) {
        this.value = value;
    }

    /**
     * 根据值取对应枚举
     *
     * @param type
     * @return Padding
     * @throws
     * <AUTHOR>
     * @date 2023/11/27
     * @since 1.4.0
     */
    public static Padding getByValue(String type) {

        for (Padding val : values()) {

            if (val.getValue().equals(type)) {
                return val;
            }
        }
        return null;
    }

    public String getValue() {
        return value;
    }
}
