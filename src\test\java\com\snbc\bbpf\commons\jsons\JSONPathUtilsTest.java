/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.jsons;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.util.LinkedHashMap;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 类描述:   测试jsonpath的读取 数据，测试异常入参情况 .
 *
 * <AUTHOR>
 * 创建时间:  [2023/9/1 15:02]
 */
class JSONPathUtilsTest {
    @Test
    @DisplayName("测试正常读取path数据")
    @Tags({
            @Tag("@id:59"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/8/23")
    })
    void testFindDataByJSONPath_ok() {

        String jsonData = "{ " +
                "\"employees\": [" +
                "    {\"id\": 1, \"name\": \"John\"}," +
                "    {\"id\": 2, \"name\": \"Jane\"}" +
                "  ]" +
                "}";
        String i = "$.employees[?(@.id == 1)]";
        List<LinkedHashMap<String, Object>> re = JSONPathUtils.findDataByJSONPath(jsonData, i);
        // 查找符合条件的数据
        assertTrue(re.size() == 1);

    }


    @Test
    @DisplayName("检查非法json格式的用例")
    @Tags({
            @Tag("@id:59"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/8/23")
    })
    void testFindDataByJSONPath_ErrorJsonpath() {
        String jsonData = "1111{ " +
                "\"employees\": [" +
                "    {\"id\": 1, \"name\": \"John\"}," +
                "    {\"id\": 2, \"name\": \"Jane\"}" +
                "  ]" +
                "}";
        String i = "$.employees[?(@.id == 1)]";
        assertThrows(IllegalArgumentException.class,
                () -> JSONPathUtils.findDataByJSONPath(jsonData, i),
                "错误的json格式");
    }

    @Test
    @DisplayName("检查非法入参")
    @Tags({
            @Tag("@id:59"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/8/23")
    })
    void testFindDataByJSONPath_ErrorParam() {
        String i = "$.employees[?(@.id == 1)]";
        assertThrows(IllegalArgumentException.class,
                () -> JSONPathUtils.findDataByJSONPath(null, i),
                "错误的json格式");
    }
}
