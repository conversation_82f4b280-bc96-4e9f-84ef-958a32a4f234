/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.validations.aspect;

import com.snbc.bbpf.commons.validations.annotation.ValidStringContains;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 判断字符是否包含指定字符注解方法参数的测试类
 *
 * <AUTHOR>
 * @module
 * @date 2023/10/30 13:59
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class ValidParameterStringContainsAspectTest {

    @Test
    @DisplayName("未添加注解，不进行参数校验")
    @Tags({
            @Tag("@id:67"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/10/30")
    })
    void testNoAnnotation_noAnnotation() {
        assertTrue(noAnnotation("1111"));
    }

    @Test
    @DisplayName("方法未添加注解，不进行参数校验")
    @Tags({
            @Tag("@id:67"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/10/30")
    })
    void testNoMethodAnno_noMethodAnno() {
        assertTrue(noMethodAnno("1111"));
    }

    @Test
    @DisplayName("方法参数未添加注解，不进行参数校验")
    @Tags({
            @Tag("@id:67"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/10/30")
    })
    void testNoParameterAnno_noParameterAnno() {
        assertTrue(noParameterAnno("1111"));
    }

    @Test
    @DisplayName("方法参数添加注解，参数校验失败")
    @Tags({
            @Tag("@id:67"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/10/30")
    })
    void testAnnoString_annoString() {
        IllegalArgumentException thrown1 = assertThrows(IllegalArgumentException.class,
                () -> annoString("1111"));
        assertEquals("method[annoString]parameter[arg0]value contains error", thrown1.getMessage());
    }

    @Test
    @DisplayName("方法参数添加注解，参数为空")
    @Tags({
            @Tag("@id:67"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/10/30")
    })
    void testAnnoString_annoStringEmpty() {
        IllegalArgumentException thrown1 = assertThrows(IllegalArgumentException.class,
                () -> annoString(null));
        assertEquals("method[annoString]parameter[arg0]can't be null", thrown1.getMessage());
    }


    public Boolean noAnnotation(String str) {
        return str.contains("111");
    }

    public Boolean noMethodAnno(@ValidStringContains(value = "aaa") String str) {
        return str.contains("111");
    }

    @ValidStringContains
    public Boolean noParameterAnno(String str) {
        return str.contains("111");
    }

    @ValidStringContains
    public Boolean annoString(@ValidStringContains(value = "aaa") String str) {
        return str.contains("111");
    }

}
