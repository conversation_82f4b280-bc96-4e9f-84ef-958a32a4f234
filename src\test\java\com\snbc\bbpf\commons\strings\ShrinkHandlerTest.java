/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.strings;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * 字符串修剪单元测试类
 *
 * <AUTHOR>
 * @module 字符串处理
 * @date 2023/4/23 19:53
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class ShrinkHandlerTest {

    @Test
    @DisplayName("将[12345678900987654321]字符串保留右长度4，修剪左侧[...4321]")
    @Tags({
            @Tag("@id:17"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testShrinkLeft() {
        assertEquals("...4321", ShrinkHandler.shrinkLeft("12345678900987654321", 4));
    }

    @Test
    @DisplayName("字符串保留右长度>=字符串长度，直接返回原字符串")
    @Tags({
            @Tag("@id:17"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testShrinkLeft_unchanged() {
        assertEquals("12345678900987654321", ShrinkHandler.shrinkLeft("12345678900987654321", 21));
    }

    @Test
    @DisplayName("输入null字符串应该抛出提示参数是空的IllegalArgumentException异常")
    @Tags({
            @Tag("@id:17"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testShrinkLeft_strNullShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> ShrinkHandler.shrinkLeft(null, 4),
				"Input parameter is empty");
    }


    @Test
    @DisplayName("输入空字符串应该抛出提示参数是空的IllegalArgumentException异常")
    @Tags({
            @Tag("@id:17"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testShrinkLeft_strEmptyShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> ShrinkHandler.shrinkLeft("", 4),
				"Input parameter is empty");
    }

    @Test
    @DisplayName("保留长度小于1应该抛出提示参数是空的IllegalArgumentException异常")
    @Tags({
            @Tag("@id:17"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testShrinkLeft_lengthLessOneShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> ShrinkHandler.shrinkLeft("12345678900987654321", 0),
				"Length parameter invalid");
    }


    @Test
    @DisplayName("使用+性能测试将[12345678900987654321]字符串保留左长度4，修剪右侧[1234...]")
    @Tags({
            @Tag("@id:17"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testShrinkRight() {
        assertEquals("1234...", ShrinkHandler.shrinkRight("12345678900987654321", 4));
    }



	
    @Test
    @DisplayName("字符串保留右长度>=字符串长度，直接返回原字符串")
    @Tags({
            @Tag("@id:17"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testShrinkRight_unchanged() {
        assertEquals("12345678900987654321", ShrinkHandler.shrinkRight("12345678900987654321", 21));
    }

	@Test
	@DisplayName("输入null字符串应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:17"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testShrinkRight_strNullShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> ShrinkHandler.shrinkRight(null, 4),
				"Input parameter is empty");
	}


	@Test
	@DisplayName("输入空字符串应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:17"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testShrinkRight_strEmptyShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> ShrinkHandler.shrinkRight("", 4),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("保留长度小于1应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:17"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testShrinkRight_lengthLessOneShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> ShrinkHandler.shrinkRight("12345678900987654321", 0),
				"Length parameter invalid");
	}


    @Test
    @DisplayName("将[12345678900987654321]字符串保留长度4,左偏移量2，修剪右侧[123456...]")
    @Tags({
            @Tag("@id:17"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testShrinkLR_leftOffset_reserveLeft() {
        assertEquals("123456...", ShrinkHandler.shrinkLR("12345678900987654321", 4, 2));
    }

    @Test
    @DisplayName("将[12345678900987654321]字符串左偏移量4[...5678...]")
    @Tags({
            @Tag("@id:17"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testShrinkLR_leftOffset_reserveCentre() {
        assertEquals("...5678...", ShrinkHandler.shrinkLR("12345678900987654321", 4, 4));
    }

    @Test
    @DisplayName("字符串保留右长度>=字符串长度,左偏移量0，直接返回原字符串")
    @Tags({
            @Tag("@id:17"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testShrinkLR_leftOffset_unchanged() {
        assertEquals("12345678900987654321", ShrinkHandler.shrinkLR("12345678900987654321", 22, 0));
    }

    @Test
    @DisplayName("将保留长度小于或等于0传入应该抛出提示保留长度只支持大于0的IllegalArgumentException异常")
    @Tags({
            @Tag("@id:17"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testShrinkLR_leftOffset_shouldThrowException() {
        assertThrows(IllegalArgumentException.class,
                () -> ShrinkHandler.shrinkLR("12345678900987654321", 0, 0),
                "Input parameter invalid");
    }


	@Test
	@DisplayName("输入null字符串应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:17"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testShrinkLR_leftOffset_strNullShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> ShrinkHandler.shrinkLR(null, 4, 1),
				"Input parameter is empty");
	}


	@Test
	@DisplayName("输入空字符串应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:17"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testShrinkLR_leftOffset_strEmptyShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> ShrinkHandler.shrinkLR("", 4, 1),
				"Input parameter is empty");
	}

	@Test
	@DisplayName("偏移太大应该抛出IllegalArgumentException异常")
	@Tags({
			@Tag("@id:17"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testShrinkLR_leftOffset_overFlowShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> ShrinkHandler.shrinkLR("12345678900987654321", 4, 100),
				"offset overflow");
	}


	@Test
	@DisplayName("偏移小于0应该抛出IllegalArgumentException异常")
	@Tags({
			@Tag("@id:17"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/4/23")
	})
	void testShrinkLR_leftOffset_leftOffsetShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> ShrinkHandler.shrinkLR("12345678900987654321", 4, -1),
				"Input parameter invalid");
	}


    @Test
    @DisplayName("将[12345678900987654321]字符串左偏移量4,左缩略符++,右偏移量5,右缩略符===[++56789009876===]")
    @Tags({
            @Tag("@id:17"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testShrinkLR_custom() {
        assertEquals("++56789009876===", ShrinkHandler.shrinkLR("12345678900987654321", 4, "++", 5, "==="));
    }

    @Test
    @DisplayName("将[12345678900987654321]字符串左偏移量24,左缩略符++,右偏移量15,右缩略符===,抛出提示偏移溢出")
    @Tags({
            @Tag("@id:17"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testShrinkLR_overflowThrowException() {
        assertThrows(IllegalArgumentException.class,
                () -> ShrinkHandler.shrinkLR("12345678900987654321", 24, "++", 15, "==="),
                "offset overflow");
    }

    @Test
    @DisplayName("将[12345678900987654321]字符串左偏移量14,左缩略符++,右偏移量15,右缩略符===,抛出提示偏移冲突")
    @Tags({
            @Tag("@id:17"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testShrinkLR_conflictThrowException() {
        assertThrows(IllegalArgumentException.class,
                () -> ShrinkHandler.shrinkLR("12345678900987654321", 14, "++", 15, "==="),
                "offset conflict");
    }

    @Test
    @DisplayName("输入null字符串应该抛出提示参数是空的IllegalArgumentException异常")
    @Tags({
            @Tag("@id:17"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testShrinkLR_strNullShouldThrowException() {
        assertThrows(IllegalArgumentException.class,
                () -> ShrinkHandler.shrinkLR(null, 14, "++", 15, "==="),
                "Input parameter is empty");
    }


    @Test
    @DisplayName("输入null缩略符应该抛出IllegalArgumentException异常")
    @Tags({
            @Tag("@id:17"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testShrinkLR_leftPaddingNullShouldThrowException() {
        assertThrows(IllegalArgumentException.class,
                () -> ShrinkHandler.shrinkLR("12345678900987654321", 14, null, 15, "==="),
                "padding is null");
    }

    @Test
    @DisplayName("输入null缩略符应该抛出IllegalArgumentException异常")
    @Tags({
            @Tag("@id:17"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testShrinkLR_rightPaddingNullShouldThrowException() {
        assertThrows(IllegalArgumentException.class,
                () -> ShrinkHandler.shrinkLR("12345678900987654321", 14, "===", 15, null),
                "padding is null");
    }


    @Test
    @DisplayName("输入空字符串应该抛出提示参数是空的IllegalArgumentException异常")
    @Tags({
            @Tag("@id:17"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/4/23")
    })
    void testShrinkLR_strEmptyShouldThrowException() {
        assertThrows(IllegalArgumentException.class,
                () -> ShrinkHandler.shrinkLR("",14, "++", 15, "==="),
                "Input parameter is empty");
    }


}

