package com.snbc.bbpf.commons.strings;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

/**
 * 版本号比较工具类
 *
 * <AUTHOR>
 * @module 集合处理
 * @date 2023/4/23 11:32
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class VersionUtilTest {


    @Test
    @DisplayName("比较versionLeft和versionRight，如果versionLeft大于versionRight则返回true。")
    @Tags({
            @Tag("@id:2"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/4/27")
    })
    void testGreaterThanGreaterThan_specificSize() {

        Assertions.assertTrue(VersionUtil.build("V1.1.4").greaterThan("V1.1.2"));

    }

    @Test
    @DisplayName("比较versionLeft和versionRight，如果versionLeft大于等于versionRight则返回true。")
    @Tags({
            @Tag("@id:2"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/4/27")
    })
    void testGreaterThanOrEqualTo_specificSize() {
        Assertions.assertTrue(VersionUtil.build("V1.1.2").greaterThanOrEqualTo("V1.1.2"));
        Assertions.assertTrue(VersionUtil.build("V1.1.4").greaterThanOrEqualTo("V1.1.2"));

    }

    @Test
    @DisplayName("检查versionLeft是否小于versionRight,小于返回true")
    @Tags({
            @Tag("@id:2"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/4/27")
    })
    void testLessThan_specificSize() {
        Assertions.assertTrue(VersionUtil.build("V1.1.1").lessThanOrEqualTo("V1.1.2"));
        Assertions.assertTrue(VersionUtil.build("V1.1").lessThanOrEqualTo("V1.1.2"));
        Assertions.assertTrue(VersionUtil.build("V1.1.2").lessThanOrEqualTo("V1.1.2"));

    }

    @Test
    @DisplayName("比较两个版本字符串，如果versionLeft小于或等于versionRight则返回true")
    @Tags({
            @Tag("@id:2"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/4/27")
    })
    void testLessThanOrEqualTo_specificSize() {
        Assertions.assertFalse(VersionUtil.build("V1.1.2").lessThan("V1.1.2"));
        Assertions.assertFalse(VersionUtil.build("V1.1.2").lessThan("V1.1.1"));
        Assertions.assertTrue(VersionUtil.build("V1.1.0").lessThan("V1.1.1"));

    }


    @Test
    @DisplayName("比较表示程序不同版本的两个字符串 ")
    @Tags({
            @Tag("@id:2"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/4/27")
    })
    void testEquals_specificSize() {

        Assertions.assertTrue(VersionUtil.compare("V1.1.2", "V1.2.2") < 0);
        Assertions.assertTrue(VersionUtil.compare("V1.1.2", "V1.2.21") < 0);

    }

    @Test
    @DisplayName("比较表示程序不同版本的两个字符串")
    @Tags({
            @Tag("@id:2"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/4/27")
    })
    void testCompare_specificSize() {
        Assertions.assertTrue(VersionUtil.compare("V1.1.4", "V1.1.3") > 0);
        Assertions.assertTrue(VersionUtil.compare("V1.2.4", "V1.1.1") > 0);
        Assertions.assertTrue(VersionUtil.compare("V1.1", "V1.1.3") < 0);
        Assertions.assertTrue(VersionUtil.compare("V1.1", "V1.2.3") < 0);

    }

    @Test
    @DisplayName("检查两个版本字符串是否相等，等于返回true。")
    @Tags({
            @Tag("@id:2"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/4/27")
    })
    void equalToTest() {
        Assertions.assertTrue(VersionUtil.build("V1.1.2").equalTo("V1.1.2"));
    }
 
}
