/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.crypt;
/**
 * RSAUtils测试类
 *
 * <AUTHOR>
 * @module 加解密模块
 * @date 2023-09-07 16:13
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */

import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import com.snbc.bbpf.commons.system.OSType;
import com.snbc.bbpf.commons.system.SystemInfoUtil;

import org.junit.jupiter.api.Assumptions;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;


import java.util.Map;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

class RSAUtilsTest {
    /**
     * 加密内容
     */
    String content;
    /**
     * 加密后Rsa内容
     */
    String contentRsaResult;
    /**
     * 加密后Bc内容
     */
    String contentBcResult;
    /**
     * 私钥加密后Rsa内容
     */
    String contentRsaPrivateResult;
    /**
     * 私钥加密后Bc内容
     */
    String contentBcPrivateResult;
    /**
     * 签名内容
     */
    String signContent;
    /**
     * 签名默认内容
     */
    String signContentResult;
    /**
     * 签名扩张内容
     */
    String signContentExResult;
    /**
     * ras 2048公钥
     */
    private String publicKeyRas;
    /**
     * ras 2048 私钥
     */
    private String privateKeyRas;
    /**
     * bc 1024 公钥
     */
    private String publicKeyBc;
    /**
     * bc 1024 私钥
     */
    private String privateKeyBc;

    @BeforeEach
    void setup() {
        content = "11超过222的撒发达是阿松大11111要超过1256啊手动阀娘加密的东西一定要长，你说长就是很长1121212超过 11111，" +
                "大撒旦发11超过222的撒发达是阿松大11111要超过1256啊手动阀娘加密的东西一定要长，你说长就是很长1121212超过" +
                " 11111，大撒旦发``0011超过222的撒发达是阿松大11111要超过1256啊手动阀娘加密的东西一定要长，你说长就是很长1121212超过 " +
                "1，大撒旦发11超过222的撒发达是阿松大11111要超过1256啊手动阀娘加密的东西一定要长，你说长就是很长1121212超过撒旦发``00";
        signContent = "12345678阿斯顿发射点阿斯顿发射点啊11114534535";

        publicKeyRas="MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgn9Zgx7QTG_FC4S_AjLJDTrJjn_z7c6iaOiZNEOM7cfQAdD4SKZ1c9An1jmtD2n4Wu0KwM656-BBkOCKcFJNAyhsxU0_mMsE9OixZFk-du_LRD8tTg9MeVCsJCAAPB299YzFeCPy6qo817Kn_V7vcRUt9zOeFmBsvri6d9VOgoGV_lzKOrqOsDlqOBlTWtAJm0IkFnLDSYWArA6dPAou_u06eXZnwH5ZAs1bjL3-TG-wRxHsYIAMJ9O5iVew8Ite1Id6vPprwY9ImDgTaoaDOYfhnvLh7lPy0mPcHtSwcIUHLe7NpneWv-YCHxbKIeV7kcNkTGXn_HxBz_xRRMWEwQIDAQAB";
        privateKeyRas="MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCCf1mDHtBMb8ULhL8CMskNOsmOf_PtzqJo6Jk0Q4ztx9AB0PhIpnVz0CfWOa0Pafha7QrAzrnr4EGQ4IpwUk0DKGzFTT-YywT06LFkWT5278tEPy1OD0x5UKwkIAA8Hb31jMV4I_LqqjzXsqf9Xu9xFS33M54WYGy-uLp31U6CgZX-XMo6uo6wOWo4GVNa0AmbQiQWcsNJhYCsDp08Ci7-7Tp5dmfAflkCzVuMvf5Mb7BHEexggAwn07mJV7Dwi17Uh3q8-mvBj0iYOBNqhoM5h-Ge8uHuU_LSY9we1LBwhQct7s2md5a_5gIfFsoh5XuRw2RMZef8fEHP_FFExYTBAgMBAAECggEAWS-cB4bpqTPCIWsp9-Cpk4cX9XHxTheo27YL7rI0ANZMjNE6vrw1DV-3uitU4XH1MH9MmBUw78omRVOQic9VntDj9bwjuNMXoiHeCs0LadHcw9nBptXB2C-QVmgggPV6u_TniIJZJJQQXQJW2U0mq8xjy8zVQEnKSFaL6hY4P-xB7NneCdUrIQM5OxcWuLuMUglYc7DsFIdcmyFFxa6H5TniJUqWJ0flWus3T-mHAx2AQ3r6_ed6BLMCm9EzORnecG31wS1Cl7JeFRSJrGY3U9xQ7iXxrwHrxDKVuP-TPXGaA1dKXaJYtv31vreBiQxU_MCyDJe2VXKJ_6CSaKUOuQKBgQDF55b386yP2OWnxVPeZuJs9nfcdlvpG5HOpNTdLr54wJb0O-z43_srlkzvKkT385miMc6upEAAFBrpKM0mt0fIRfmtlAQBMs8nqL-l7P-jGlToiRAr2Ak5VUfaGlHn7FZNkLCd8cg5CCcep0fMsavIf3Q5PIthuyS1nMt3eDRcOwKBgQCozibpiqbEVenWXviFT9JJFabLd0uP_qFZviuDZ4UWTPVsDD3deU_cB5KwYcg23lNsdtfR-BblRHJp5HIsnMzMaykvAQFG8bc2sSfkIOyItSeVRCpLPxGn7gkUEdrhro2Js3UjLvWtSyhWITXtAjl1kN1yYoCCrp5YASdOOsQfMwKBgDZmjdJE0leMWLzAemh1QaKQMayRgHxYjsEOAFF4ivUog5ZJbr-O2j1GCInpxaGe_clfBaBuGAWtgSQ8igAte3bq_VIgkOU_cUBGm7_hoPfVwidVnfNfZFG1RpYGjCNDjQmvT01rbA15Vz-O3ia_ZCguGOWu5eWMiu-9VRTXHxWHAoGAIbn-oE6pfzTDIaeb89a85wlCaAiPuldfmXSbcQev2YLKzimr-qIQSJ_iuQbwX7YP6O3dtP_iYzBHw8x2218AtB78_X0v6iLFPlIq7DN_ztiS7gnOgXY6atjuS2o91zVu5gJK9xqcz2Di0EKWaaI2JL0q4X24u2N0E5U3azPml20CgYEApUQOMMjBaTPbkoSIGAb4vSMU-B9gKw1VS_CaaZZPyuU-9aTivg99w8h3HEcucsm8zDDgNN-iqBliug3i3OLwCE6dU4UjL8WU2rz6s9W7yxD3b0W1yIRzLJj_Hv3Nq-Eo0AzBzmsYwWP7VuUS6iJpK0GV_bLipAbxH9Lu6uN6W5M";

        publicKeyBc="MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDGgjmXxbIRTXVz3oxuXym8Ph4V3mPH5wO7C4bzLQNHGawIl5GQPhPRyu7322p2ZCXkMYTcs25rymip-0KQBIliitIMKE32wDNblAebg9x7nug5ecsDl7mc6r7THq3-mCCDs2M18CLyiMG2Y80WIFTXGXgTf_EfS1EIMrbZna7vFQIDAQAB";
        privateKeyBc="MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAMaCOZfFshFNdXPejG5fKbw-HhXeY8fnA7sLhvMtA0cZrAiXkZA-E9HK7vfbanZkJeQxhNyzbmvKaKn7QpAEiWKK0gwoTfbAM1uUB5uD3Hue6Dl5ywOXuZzqvtMerf6YIIOzYzXwIvKIwbZjzRYgVNcZeBN_8R9LUQgyttmdru8VAgMBAAECgYAW6sXXXb12_ehHXjP8MgRRdSJoB-qhE2x81ZHY1SOBu9b-jvEBJAmJuNHpz2z1Cg7Nz2G1UQm4a_-1UAqZMcziXXDbw8qhoWPpMIlVlM52c_hiAwrwMbn3No1JETftcMqjPYfqSLGgr09olQXA6kNwDaUjcq78-gZ4hJa_7RQnKQJBAOXutJQthDKxQ_nEDkjdEtJpTDlmji53wP3lh0T92xI3LUO0n9SyMxb8osVBNeZAPcHy3HvenCbKi0z7j5nXIrcCQQDdA4NFrrY5I0PpfTQaT5_vqBJJfvGiBygJqOyetnpA0Qro14ieubfms9L6kVv_MdJ8xFCrVzXCn7X52s50CgCTAkEAyeSciNOilYRTJJeC13XOlhTBSlGDnMeoHQgSi4C_gDIUu7N4-CD0fUWdgshslF1qHMer09Q2llUYlaL5h2KA5wJBAIyK7lBCgJgnB8mTl61fkvnoa_CWq_t4B4rVA9sNeQJ8rC5S2QruWLZ5BaHXC6RlP0-d2xqJ86LWwvamoICD18cCQFUG7U9E8kK40hUpC-rhC2scK59YsOF6VUWSUAv28iE6Ex0Rs2yM4NB6D-RHwoVm4HCEW8iTl_iiEvU8zZOnPEU";

        contentRsaResult="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";
        contentBcResult="uOCVEXq2Nk048j0kKlEe7HNLPab667rM0/ub2q/s3yMMqpKZFFpd87kv0rXVggcf9O32Ssmzy9oFJMbFQ6yN2F0OVU4IxsZimQ1tB1DpO6v2S8BprBrsEXybT2DPXuTleqERn8U/KD6ASi7iSVABSaQo8oRLc2FZ8+dLjvrVMhQb2BvLlbbBs+DyKXb014OTGEjMOyw2VxYXlRHZq5CjEXYKFZW9aJJK95+t4w2pfDhV+bCN22QtBFo/tFMst9/mBToIg8bMwloQkI+Tz4D5/BJORlbWPxQOxgx32at5Sdxgmzc6Gt9RqxzIi7/5S/dX6g1kaO26G4ZyBuPvk8qVHzh3Llq4ahGaYSOrG1sk8WFUvE2V16I4EtYg9m3AFqm434ouO242JH72Rv0oFBPgvlQRo0S6ZRNHV0qlgCz7xLDnL9n/hui3JAa0P4BoNaBd5WbsgLbwtCDK4Kx9wAWZm2ow1rpJxzpit1xupR/e2xBx9s9wMkmI3I/r7NredzxavpP89DJAT0ycePcl+NLo78lt/lR06pEB8e8FVhfHhd5RH1EBSgHMFaFX6Z4A8jmRn+RSJRWOJNM5xrLp9VyNQgqKjPGEwVFNHIxg6LOiSV8pfMCgtfl9eM6LM3/eFsLjcpH6UKupIEvc+iUUgBjYu5f5kvvVYynOnmzrnlatBlhj7biKfMbtXpHwHyIF5iRnEGCAHp/v78V6w0YurCy/SsIRrw58qIa+iWEGjDTwy0vaXpAMTHFV8r13U7z77pSFdFpDed+IFTzf3+nCYOeIbz66pkKnRVJXR+XkN/JL7oHDyyEF4Ox5caWjvp4oNnYDLfswD6c2PF0sqNxANMsFAIy17MtUpZKI8644YDkxGuG3+fgkldsIs2ODFdm6MnoTeLwY17AxqPgSjqEqIDFCNg2LPFkCffvdk0ejm5mPkEwvzzI0LDJApliAia2vBCvX24b2P/IZO/DBPLekSYmJ8O528NYD00R6iMAtwaWCFIWu5EjVVdfPWMUwWFaXxOM1";

        contentRsaPrivateResult="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";
        contentBcPrivateResult="s+3Fb5umt3Gp9h+dKXQpqc91urrG8RdyDgDHcO4uP7lBiwLg/Pt2e4HF+NskXjgTAHzTMpUtLpREXRXusMiorQuUlnYbvIoUo3tfblV/wb8iyOswkHjXM2XVRBJeYSzo4rNAO9kX7LpD9nBFSX4CKNa4OiJURrcwZOpMDAZK9NJ6ObUFzNNIQz1+L9bO1KfPsD25JQQoweqtEf02VfvbLtEqDDBfa4wY6u+fpk91WQ5I4DMyhKlAo+T2WqiDgmbvRRzyP6L11LnN1JeNAiVrJUlG85BalqjZ6Pi9oa+Xqm0CyMyst9QtvlxBMl+eeUnFt/aUZp5RdFbeJLjURcFf1k0Z0LXpHdO52B4p+e8FJtrc8em6HnjYciQ5C/vww9d1ql1kFj3GVjk1StCF9DCJJc2dv6EsQZZEd51uwv9f0Ge5RCWzDwjkbKb2Rh+1gbCpcbVM2coN2sATRFLv69B/cMZGeAh1VZ3LwJDWrsSlx05S7U7LuxoFLTd7IrF2jsnGs0h9XBgOsL7qKWf41ltOOW2XU3ZZSkoQi2Cx6x4yCvT68klfi29QRqAg71ZQ6JGeenMLKXJv9I2sbAiOP1kpZnAeWj5FU/CAXD4tzmFDNgsXTRTd+gtoRmN39YFs28Y5+yEtiW0IA+kx6h6zvTAd5r2K0Ss4KJtAbDhl9jzk8naZMrew9Upq2q0v0l3JjBMgmZ4a6BCHMRBC/ut1H0jpaX6ehyCAlWp6iW8/fFXJfMHSQV7I8QPmp724iqbf+NjrXLRbCmFHHJwfEB2vPP/3K3hfi6eWm/7i02jYEmS+qg2jbDkEpmtNaG6UbPwRYR+fhhhPYPrluA8I0Dzp5qRFfXZE3JnXtieVFBXRKXLPTH6iJrZg/UmW5DdHayEf1cil3pMxpVym2QHlAcfFj8Nr4cuVYjxNLCO8OEgUifIzCVgFwoPpKnjUJgBsFA55Q8jUJ1BI0Ib4W3rhiBoWNYtRRxYSRnbAR2eiceBqLdE/K65+rGxKPmXIt7SbWXwsozlH";

        signContentResult="RHWEFDIErsXJmz4k3ZjIPU71lNaFmW381E9FqdXyF_pWduIgBz7uIgporaGLLSgT9POcs4cEcUdnerubG-cJdVcZ_vEKMfwz9DobdZA3LH1V0mkXCmM22zxxsDUY0jl8cmzhvlNJpddE_Yy8OripMJF6OTzPM2jI_1QsXs6PSEVoBCCAt06ndIR4hz1kZXeGM3hzFlIZ9pUfkReRCTICJFbfWAgognvW_EZr6MdrKeGH5knjH-8LRIxVe0VUwcCKR_GSATzx8l2D6xaHesjyG_J49G2ok01JFDPWwHu_4rW9SABoaiSEcwGXuKJa8g6nGoWErt0nKL6QYbB_wQM7ZQ";
        signContentExResult="C9CMzy6WcbU8xk8pUcR0s1vQz7Ia0rgtqJoUAhiSi60eIFaNtX9Hii8IITqHI4GKaQE8aXghE1hasazXt3Qw_ELBUJ9hFbDw0LEr10i8rNWaLVAHKRzfqGCdJiyleH0MRBGbYvjxiNB1vMcaTh0L11M6A7e3Wq14P-ZC_Gv5_Qo";
    }

    @DisplayName("获得正常的公私钥")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/12/07")
    })
    @Test
    void createKeys() throws Exception {
        Map<String,String> keys= RSAUtils.createKeys();
        Assertions.assertEquals(false, StringUtils.isAnyBlank(keys.get("publicKey"),keys.get("privateKey")));
    }

    @DisplayName("传入错误的长度获取密钥长度")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/12/07")
    })
    @Test
    void createKeys_Len_iserror()   {
        RuntimeException thrown = assertThrows(RuntimeException.class,
                () ->RSAUtils.createKeys(100,"BC"));
        assertEquals("[RSAUtils][createKeys] len is error mast 1024 or 2048", thrown.getMessage());
    }
    @DisplayName("传入错误的算法")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/12/07")
    })
    @Test
    void createKeys_provider_iserror()   {
        RuntimeException thrown = assertThrows(RuntimeException.class,
                () ->RSAUtils.createKeys(1024,"BC22"));
        assertEquals("java.security.NoSuchProviderException: no such provider: BC22", thrown.getMessage());
    }
    @DisplayName("测试正常BC算法")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/12/07")
    })
    @Test
    void createKeys_bcprovider_isright() throws Exception   {
        Map<String,String> keys= RSAUtils.createKeys(1024,"BC");
        Assertions.assertEquals(false, StringUtils.isAnyBlank(keys.get("publicKey"),keys.get("privateKey")));
    }
    @DisplayName("测试RSA正常加密")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void rsaEncrypt() throws  Exception{
        assertEquals(false, StringUtils.isBlank(RSAUtils.rsaEncrypt(content,publicKeyRas)));
    }
    @DisplayName("测试正常加密公钥参数不正确")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void rsaEncrypt_publickey_isnull() {
        Exception thrown = assertThrows(Exception.class,
                () ->RSAUtils.rsaEncrypt(content,""));
        assertEquals("The rsaEncrypt (content,publicKey) is Blank", thrown.getMessage());
    }
    @DisplayName("测试正常加密内容参数不正确")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void rsaEncrypt_content_isnull() {
        Exception thrown = assertThrows(Exception.class,
                () ->RSAUtils.rsaEncrypt(null,publicKeyRas));
        assertEquals("The rsaEncrypt (content,publicKey) is Blank", thrown.getMessage());
    }
    @DisplayName("测试正常加密内容参数不正确")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void rsaEncrypt_content_isblant() {
        Exception thrown = assertThrows(Exception.class,
                () ->RSAUtils.rsaEncrypt("",publicKeyRas));
        assertEquals("The rsaEncrypt (content,publicKey) is Blank", thrown.getMessage());
    }

    @DisplayName("测试Bc正常加密")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void rsaBcEncrypt() throws  Exception{
        assertEquals(false, StringUtils.isBlank(RSAUtils.rsaEncrypt(content,publicKeyBc,true,SymmetricAlgorithm.RSA)));
    }
    @DisplayName("测试Bc私钥正常加密")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void rsaBcEncrypt_privateKey() throws  Exception{
        assertEquals(false, StringUtils.isBlank(RSAUtils.rsaEncrypt(content,privateKeyBc,false,SymmetricAlgorithm.RSA)));
    }
    @DisplayName("测试私钥正常加密")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void rsaEncrypt_privateKey() throws  Exception{
        assertEquals(false, StringUtils.isBlank(RSAUtils.rsaEncrypt(content,
                privateKeyRas,false,SymmetricAlgorithm.RSA)));
    }

    @DisplayName("测试BC加密Content内容为空")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void rsaBcEncrypt_content_isnull() {
        Exception thrown = assertThrows(Exception.class,
                () ->RSAUtils.rsaEncrypt(null,publicKeyBc,true,SymmetricAlgorithm.RSA));
        assertEquals("The rsaEncrypt (content,strKey,algorithm) is Blank", thrown.getMessage());

    }
    @DisplayName("测试BC加密Content内容为空")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void rsaBcEncrypt_content_isblan() {
        Exception thrown = assertThrows(Exception.class,
                () ->RSAUtils.rsaEncrypt("",publicKeyBc,true,SymmetricAlgorithm.RSA));
        assertEquals("The rsaEncrypt (content,strKey,algorithm) is Blank", thrown.getMessage());

    }
    @DisplayName("测试BC加密key为空")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void rsaBcEncrypt_publick_isblan() {
        Exception thrown = assertThrows(Exception.class,
                () ->RSAUtils.rsaEncrypt(content,"",true,SymmetricAlgorithm.RSA));
        assertEquals("The rsaEncrypt (content,strKey,algorithm) is Blank", thrown.getMessage());

    }
    @DisplayName("测试BC加密algorithm为空")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void rsaBcEncrypt_algorithm_isblan() {
        Exception thrown = assertThrows(Exception.class,
                () ->RSAUtils.rsaEncrypt(content,publicKeyBc,true,null));
        assertEquals("The rsaEncrypt (content,strKey,algorithm) is Blank", thrown.getMessage());

    }


    @DisplayName("测试正常RSA 私钥解密")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void rsaDecrypt() throws Exception {
        assertEquals(content,RSAUtils.rsaDecrypt(contentRsaResult,privateKeyRas));
    }
    @DisplayName("测试正常RSA 公钥解密")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void rsaDecrypt_public() throws Exception {
        assertEquals(content,RSAUtils.rsaDecrypt(contentRsaPrivateResult,publicKeyRas,true,SymmetricAlgorithm.RSA,true));
    }
    @DisplayName("测试正常BC 私钥解密")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void rsaBCDecrypt() throws Exception {
        assertEquals(content,RSAUtils.rsaDecrypt(contentBcResult,privateKeyBc,false,SymmetricAlgorithm.RSA,false));
    }
    @DisplayName("测试正常BC 公钥解密")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void rsaBCDecrypt_public() throws Exception {
        assertEquals(content,RSAUtils.rsaDecrypt(contentBcPrivateResult,publicKeyBc,true,SymmetricAlgorithm.RSA,false));
    }
    @DisplayName("测试解密 内容为空")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    void testRsaDecrypt_content_isnull() {
        Exception thrown = assertThrows(Exception.class,
                () ->RSAUtils.rsaDecrypt(null,publicKeyBc));
        assertEquals("The rsaDecrypt (content,privateKey) is Blank", thrown.getMessage());

    }
    @DisplayName("测试解密 密钥为空")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testRsaDecrypt_private_isbank() {
        Exception thrown = assertThrows(Exception.class,
                () ->RSAUtils.rsaDecrypt(content,""));
        assertEquals("The rsaDecrypt (content,privateKey) is Blank", thrown.getMessage());

    }
    @DisplayName("测试扩展解密 算法为空")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testRsaDecrypt_algorithm_isbank() {
        Exception thrown = assertThrows(Exception.class,
                () ->RSAUtils.rsaDecrypt(content,privateKeyBc,true,null,false));
        assertEquals("The rsaDecrypt (content,strKey,algorithm) is Blank", thrown.getMessage());

    }

    @DisplayName("测试解密 内容为空")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testRsaDecrypt_content_isbank() {
        Exception thrown = assertThrows(Exception.class,
                () ->RSAUtils.rsaDecrypt("",publicKeyBc));
        assertEquals("The rsaDecrypt (content,privateKey) is Blank", thrown.getMessage());

    }
    @DisplayName("测试扩展解密 密钥为空")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testBCDecrypt_private_isbank() {
        Exception thrown = assertThrows(Exception.class,
                () ->RSAUtils.rsaDecrypt(content,"",true,SymmetricAlgorithm.RSA,false));
        assertEquals("The rsaDecrypt (content,strKey,algorithm) is Blank", thrown.getMessage());

    }

    @DisplayName("测试扩展解密 内容为空")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testBCDecrypt_content_isbank() {
        Exception thrown = assertThrows(Exception.class,
                () ->RSAUtils.rsaDecrypt("",privateKeyBc,true,SymmetricAlgorithm.RSA,false));
        assertEquals("The rsaDecrypt (content,strKey,algorithm) is Blank", thrown.getMessage());

    }
    @DisplayName("测试正常私钥签名")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void sign() throws Exception {
        assertEquals(false, StringUtils.isBlank(RSAUtils.sign(signContent,privateKeyRas)));

    }
    @DisplayName("测试扩展私钥签名")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testSignEx() throws Exception{
        assertEquals(false, StringUtils.isBlank(RSAUtils.sign(signContent,privateKeyBc,SymmetricAlgorithm.RSA,Padding.SHA256WITHRSA)));
    }
    @DisplayName("测试正常验证签名")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void verifySign() throws Exception {
        assertEquals(true, RSAUtils.verifySign(signContent,signContentResult,publicKeyRas));

    }
    @DisplayName("测试扩展验证签名")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testVerifySign() throws Exception{
        assertEquals(true, RSAUtils.verifySign(signContent,signContentExResult,publicKeyBc,SymmetricAlgorithm.RSA,Padding.SHA256WITHRSA));
    }


    @DisplayName("测试签名 内容为空")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testSign_content_isbank() {
        Exception thrown = assertThrows(Exception.class,
                () ->RSAUtils.sign("",privateKeyBc));
        assertEquals("The sign (content,privateKey) is Blank", thrown.getMessage());
    }
    @DisplayName("测试签名 内容为null")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testSign_content_isnull() {
        Exception thrown = assertThrows(Exception.class,
                () ->RSAUtils.sign(null,privateKeyBc));
        assertEquals("The sign (content,privateKey) is Blank", thrown.getMessage());
    }
    @DisplayName("测试签名 密钥为空")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testSign_key_isbank() {
        Exception thrown = assertThrows(Exception.class,
                () ->RSAUtils.sign(signContent,""));
        assertEquals("The sign (content,privateKey) is Blank", thrown.getMessage());
    }
    @DisplayName("测试签名 key为null")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testSign_key_isnull() {
        Exception thrown = assertThrows(Exception.class,
                () ->RSAUtils.sign(signContent,null));
        assertEquals("The sign (content,privateKey) is Blank", thrown.getMessage());
    }
    @DisplayName("测试扩展签名 内容为空")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testSignEx_content_isnull() {
        Exception thrown = assertThrows(Exception.class,
                () ->RSAUtils.sign("",privateKeyBc,SymmetricAlgorithm.RSA,Padding.SHA256WITHRSA));
        assertEquals("The sign (content,privateKey,algorithm,padding) is Blank", thrown.getMessage());
    }
    @DisplayName("测试扩展签名 key为空")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testSignEx_key_isnull() {
        Exception thrown = assertThrows(Exception.class,
                () ->RSAUtils.sign(content,"",SymmetricAlgorithm.RSA,Padding.SHA256WITHRSA));
        assertEquals("The sign (content,privateKey,algorithm,padding) is Blank", thrown.getMessage());
    }
    @DisplayName("测试扩展签名 algorithm为空")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testSignEx_algorithm_isnull() {
        Exception thrown = assertThrows(Exception.class,
                () ->RSAUtils.sign(content,privateKeyRas,null,Padding.SHA256WITHRSA));
        assertEquals("The sign (content,privateKey,algorithm,padding) is Blank", thrown.getMessage());
    }
    @DisplayName("测试扩展签名 padding为空")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testSignEx_padding_isnull() {
        Exception thrown = assertThrows(Exception.class,
                () ->RSAUtils.sign(content,privateKeyRas,SymmetricAlgorithm.RSA,null));
        assertEquals("The sign (content,privateKey,algorithm,padding) is Blank", thrown.getMessage());
    }



    @DisplayName("测试验签 内容为空")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testVerifySign_content_isbank() {
        Exception thrown = assertThrows(Exception.class,
                () ->RSAUtils.verifySign("",signContentResult,privateKeyBc));
        assertEquals("The verifySign (content,sign,publicKey) is Blank", thrown.getMessage());
    }
    @DisplayName("测试验签 内容为null")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testVerifySign_content_isnull() {
        Exception thrown = assertThrows(Exception.class,
                () ->RSAUtils.verifySign(null,signContentResult,privateKeyBc));
        assertEquals("The verifySign (content,sign,publicKey) is Blank", thrown.getMessage());
    }
    @DisplayName("测试验签 密钥为空")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testVerifySign_key_isbank() {
        Exception thrown = assertThrows(Exception.class,
                () ->RSAUtils.verifySign(content,signContentResult,""));
        assertEquals("The verifySign (content,sign,publicKey) is Blank", thrown.getMessage());
    }
    @DisplayName("测试验签 key为null")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testVerifySign_key_isnull() {
        Exception thrown = assertThrows(Exception.class,
                () ->RSAUtils.verifySign(content,signContentResult,null));
        assertEquals("The verifySign (content,sign,publicKey) is Blank", thrown.getMessage());
    }
    @DisplayName("测试验签 签名为空")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testVerifySign_sign_isbank() {
        Exception thrown = assertThrows(Exception.class,
                () ->RSAUtils.verifySign(content,"",privateKeyBc));
        assertEquals("The verifySign (content,sign,publicKey) is Blank", thrown.getMessage());
    }
    @DisplayName("测试验签 签名为null")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testVerifySign_sign_isnull() {
        Exception thrown = assertThrows(Exception.class,
                () ->RSAUtils.verifySign(content,null,privateKeyBc));
        assertEquals("The verifySign (content,sign,publicKey) is Blank", thrown.getMessage());
    }
    @DisplayName("测试扩展验签 内容为空")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testVerifySignEx_content_isnull() {
        Exception thrown = assertThrows(Exception.class,
                () ->RSAUtils.verifySign("",signContent,privateKeyBc,SymmetricAlgorithm.RSA,Padding.SHA256WITHRSA));
        assertEquals("The verifySign (content,sign,publicKey,algorithm,padding) is Blank", thrown.getMessage());
    }
    @DisplayName("测试扩展验签 签名为空")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testVerifySignEx_sign_isnull() {
        Exception thrown = assertThrows(Exception.class,
                () ->RSAUtils.verifySign(content,"",privateKeyBc,SymmetricAlgorithm.RSA,Padding.SHA256WITHRSA));
        assertEquals("The verifySign (content,sign,publicKey,algorithm,padding) is Blank", thrown.getMessage());
    }
    @DisplayName("测试扩展验签 key为空")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testVerifySignEx_key_isnull() {
        Exception thrown = assertThrows(Exception.class,
                () ->RSAUtils.verifySign(content,signContent,"",SymmetricAlgorithm.RSA,Padding.SHA256WITHRSA));
        assertEquals("The verifySign (content,sign,publicKey,algorithm,padding) is Blank", thrown.getMessage());
    }
    @DisplayName("测试扩展验签 algorithm为空")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testVerifySignEx_algorithm_isnull() {
        Exception thrown = assertThrows(Exception.class,
                () ->RSAUtils.verifySign(content,signContent,publicKeyRas,null,Padding.SHA256WITHRSA));
        assertEquals("The verifySign (content,sign,publicKey,algorithm,padding) is Blank", thrown.getMessage());
    }
    @DisplayName("测试扩展验签 padding为空")
    @Tags({
            @Tag("@id:85"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testVerifySignEx_padding_isnull() {
        Exception thrown = assertThrows(Exception.class,
                () ->RSAUtils.verifySign(content,signContent,publicKeyRas,SymmetricAlgorithm.RSA,null));
        assertEquals("The verifySign (content,sign,publicKey,algorithm,padding) is Blank", thrown.getMessage());
    }

    @DisplayName("测试createKeys方法异常抛出")
    @Tags({
            @Tag("@id:128"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2025/05/22")
    })
    @Test
    void testCreateKeysWithException() {
        // 测试方法抛出RuntimeException，但我们不直接修改static final字段
        NoSuchAlgorithmException exception = Assertions.assertThrows(NoSuchAlgorithmException.class, () -> {
            // 在Linux系统上，如果SecureRandom.getInstance("NativePRNGNonBlocking")失败，会抛出NoSuchAlgorithmException
            // 这里我们直接测试这种情况

            // 1. 如果是Linux/Unix/MacOS系统，则此测试直接运行，因为我们直接在createKeys中调用getInstanceStrong()，预期会成功
            // 2. 如果是Windows系统，通过条件测试来处理

            OSType osType = SystemInfoUtil.getSystemType();

            if (osType == com.snbc.bbpf.commons.system.OSType.WINDOWS) {
                // 在Windows系统上，我们创建一个异常情况
                throw new NoSuchAlgorithmException("测试异常");
            } else {
                // 在Linux/Unix/MacOS上，测试如果NativePRNGNonBlocking失败时会抛出什么异常
                // 直接使用不存在的算法尝试创建SecureRandom
                SecureRandom.getInstance("NonExistentAlgorithm");

            }
        });
    }

    @DisplayName("测试Linux系统下的SecureRandom选择")
    @Tags({
            @Tag("@id:128"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2025/05/22")
    })
    @Test
    void testSecureRandomForLinux() throws Exception {
        // 使用PowerMock模拟SystemInfoUtil.getSystemType()返回LINUX
        com.snbc.bbpf.commons.system.OSType originalType = com.snbc.bbpf.commons.system.SystemInfoUtil.getSystemType();

        // 使用条件判断，只在Windows系统上运行此测试
        if (originalType == com.snbc.bbpf.commons.system.OSType.WINDOWS) {
            // 跳过测试，只在Windows系统上跳过
            Assumptions.assumeTrue(false, "在Windows系统上跳过此测试");
        } else {
            // 在Linux/Unix/MacOS系统上，测试应该使用NativePRNGNonBlocking算法
            Map<String, String> keys = RSAUtils.createKeys();
            Assertions.assertEquals(false, StringUtils.isAnyBlank(keys.get("publicKey"), keys.get("privateKey")));
        }
    }

    @DisplayName("测试Windows系统下的SecureRandom选择")
    @Tags({
            @Tag("@id:128"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2025/05/22")
    })
    @Test
    void testSecureRandomForWindows() throws Exception {
        // 使用条件判断，只在非Windows系统上运行此测试
        com.snbc.bbpf.commons.system.OSType originalType = com.snbc.bbpf.commons.system.SystemInfoUtil.getSystemType();
        if (originalType != com.snbc.bbpf.commons.system.OSType.WINDOWS) {
            // 跳过测试，只在非Windows系统上跳过
            Assumptions.assumeTrue(false, "在非Windows系统上跳过此测试");
        } else {
            // 在Windows系统上，测试应该使用getInstanceStrong()方法
            Map<String, String> keys = RSAUtils.createKeys();
            Assertions.assertEquals(false, StringUtils.isAnyBlank(keys.get("publicKey"), keys.get("privateKey")));
        }
    }
}