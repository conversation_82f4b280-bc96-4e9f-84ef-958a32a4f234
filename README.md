
# 基础类库

------

## 1. 项目简介

------

    本项目用于新北洋Java项目，将平时项目中的通用实现进行封装，易于大家使用。本项目在新北洋内部进行开源。
    本项目也是基础业务平台进行开源的基础类库模块。
    本项目将包含如下类别的通用实现（其余的类别将随着需求进行逐步增加）：
```
        1. 字节处理
        2. 集合处理
        3. 字符串处理
        4. 时间处理
        5. 对象处理
        6. 验证处理
        7. 精度计算
        8. 随机处理
        9. xml处理       
        10. JSON处理
        11. 文件处理
        12. ftp处理
        13. 反射处理
        14. 表达式处理
        15. 加解密处理
        16. 系统处理
        17. 地理处理
```

## 2. 使用者如何使用

------

提需求和bug步骤:

```
   1. 浏览器打开http://gitlab.xtjc.net/opensource/bbpf/si-bbpf-commons或者 查看 [docs/README.md](docs/README.md) 目录中内容
   2. 在左侧菜单找到【议题】，点击【新建议题】
   3. 填写标题和内容。如果是需求，类型可以选择【议题】，如果是bug，类型可以选择【事件】
```

查看使用文档:

```
   1. 浏览器打开http://gitlab.xtjc.net/opensource/bbpf/si-bbpf-commons或者 查看 [docs/README.md](docs/README.md) 目录中内容
   2. 找到【Wiki】菜单，点击查看。
```
## 3. 如何成为贡献者

------

     1. 通过公司OA申请【系统集成代码权限申请及变更流程】
     2. 填写仓库：SI-BBPF-COMMONS SVN服务器：系统集成研发中心 申请原因：修改开发者或开发代表
     3. 就可以成为本开源项目贡献者了。

## 4. 如何贡献代码

------

   具体流程如下：
```
    1. 首先git clone http://gitlab.xtjc.net/opensource/bbpf/si-bbpf-commons.git
    2. 使用IDEA开发工具打开项目
    3. 浏览器打开http://gitlab.xtjc.net/opensource/bbpf/si-bbpf-commons
    4. 在左侧菜单找到【议题】，点击后选择【看板】
    5. 点击看板中【开放中】列表中的议题。选择自己需要开发的议题（议题由项目负责人指派或自己领取）。
       自己领取可以在右侧指派人里点击分配给自己，同时需要该设定完成时间(不要超过里程碑时间前一周)
    6. 将选择的议题拖拽到【To Do】中，注意需要查看右侧【对版本号进行比较的功能】中【#】后面的数字，该数字为议题编号
    7. 类名，方法名和使用第三方类库等设计相关内容，可以在议题的评论或设计中进行提前说明。如果不合适可以追加评论或者单独进行沟通的方式确认设计。确认后可调整设计说明。
    8. 然后在IDEA开发工具中可以从develop分支创建一个以议题编号的新功能分支版本（feature-#1-V1.0.0-开发人名全拼）
    9. 如果进入开发，则需要将该【议题】在【看板】中拖拽放到【Doing】中。
    10. 则可以进行该功能的开发。在开发完成后（需要编写单元测试，修正sonar问题），然后提交合并到develop分支，需要选择审核人。
    11. 如果其他贡献者提交的代码由你审核，请认真对代码审核。如果代码有问题，需要在对应有问题代码处记录问题。
    12. 如果审核通过，则会合并到该分支。如果确定该功能开发完成，可以删除该功能分支。
    13. 该议题开发完毕，由代码审核人将【议题】拖拽到【Closed】中。
```

**注意 ：**

1. 分支需要根据议题类型创建，功能创建feature分支，bug需要创建bugfix分支等。
2. 单元测试用例必须保证都能通过在提交代码和合并代码。
3. 如果需求需要讨论或有异议，可以直接在议题内进行评论。
4. 在议题中建立任务，最好是明确定义任务，可以按照实现的方法来定义
   例如任务标题：开发区分版本分隔符的版本比较方法  内容：入参是版本字符串1，版本字符串2，版本分隔符，出参返回-1(版本字符串1<版本字符串2) ,0(版本字符串1=版本字符串2),1(版本字符串1>版本字符串2)
   不要用开发，测试，需求分析这样定义。

## 5. 如何进行二次开发

------
    
    当二次开发团队需要在本项目基础上进行扩充时，需要进行如下步骤：

### 5.1 派生本项目

------

    在gitlab中打开http://gitlab.xtjc.net/opensource/bbpf/si-bbpf-commons
    点击项目右上角的派生按钮。可以在二次开发团队的项目中创建派生项目。

### 5.2 找到pom文件

------

```
  将 <artifactId>bbpf-commons</artifactId> 修改为
  <artifactId>{项目名}-commons</artifactId>
```
**注意：**  这步骤必须设置，如果发布到私服可能会将正式的已发布的本开源组件覆盖。

### 5.3 进行二次开发

------

   则可以在派生的项目进行开发，开发也要遵循如下原则：
1. 需要遵循原有包名和类名所对应的功能，不要将不属于当前包或类的功能写进去。
2. 新建的包和类目也要遵循原有的命名原则。

### 5.4 发布

------

   发布时需要修改pom中关于发布的配置，需要将下面{}部分修改成对应项目的Maven仓库地址。
   
```
    <distributionManagement>
        <repository>
            <id>releases</id>
            <url>http://maven.xtjc.net/repository/{正式仓库名}/</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <url>http://maven.xtjc.net/repository/{快照仓库名}/</url>
        </snapshotRepository>
    </distributionManagement>
```

### 5.5 和开源版本同步

------

   该步骤有2个场景：
```
场景一：
   当本开源项目版本升级后。二次开发团队需要使用升级后的功能。
   二次开发团队可以发起合并请求，将最新版本代码合并到派生的项目中。

场景二：
   当二次开发团队开发了一些新功能，这些功能具备通用性。
   可以申请将这部分代码从派生的项目合并回本开源项目中，通过审核后就可以合并回来了。
   这样可以逐步共建我们的开源项目。  
```

## 6. 如何使用

------
    JDK8项目组中引用：
```
    <dependency>
        <groupId>com.snbc.bbpf</groupId>
        <artifactId>bbpf-commons</artifactId>
        <version>1.6.0</version>
    </dependency>
``` 

  具体文档地址参考：
  1. http://gitlab.xtjc.net/opensource/bbpf/si-bbpf-commons/-/wikis/home
  2. 或者 查看 [docs/README.md](docs/README.md) 目录中内容
## 7.开发说明

------

### 7.1 开发原则

------

    1. 所依赖的外部组件必须是成熟组件，不能存在安全问题，同类型组件不要使用多个。
    2. 所有类确保单一职责，相同类别的处理放到一个类中，不要一个类中包含所有类型的处理方法。
    3. 所有方法必须要有单元测试，并要求70%以上覆盖率，所有单元测试用例通过才可以提交代码。具体规范参考单元测试指南。
    4. 必须按照系研规范：开发规范，日志规范，接口规范，安全规范执行。
    5. 所有代码提交后必须经过项目负责人审核后才可提交到远端仓库。
    6. 方法注释必须要有对应功能的示例，如果可以最好都列举一下。
    7. 对于注释要求能通过javac生成对应文档，按照javadoc要求编写注释
    8. 当前版本JDK要求Java8，尽量使用Java8的特性编写代码。

## 7.2 分支说明

------

    master: 主分支，由项目负责人在版本发布后，合并develop分支到master。
    develop: 开发分支,开发提交该分支需要进行合并，并由代码审核人进行代码审核。
    feature-#issue编号-问题产生版本号-人名全拼：功能分支，新功能需要创建功能分支进行开发，开发后提交合并到develop分支。
    bugfix-#issue编号-问题产生版本号-人名全拼：修改问题编号，修改问题需要创建该分支，修复后合并到develop分支，版本号是发生问题时的版本号
    hotfix-#issue编号-问题产生版本号-人名全拼：针对严重问题，需要紧急修复的，修改问题编号，修改问题需要创建该分支，修复后合并到develop分支，版本号是发生问题的版本号，合并后需要变更小版本号进行一次版本发布。
    release-发布的版本号-[JDK版本]： 发布版本分支,如果是针对某个JDK版本的版本，需要带上JDK版本，jdk版本用小写，如jdk11 本项目默认版本jdk8则不需要增加这个

## 8.组成说明

------

### 8.1 代码结构

------
```
    SI-BBPF-COMMONS
    |--src/main/java
       |--com.snbc.bbpf.commons.bytes                       # 字节处理模块
       |--com.snbc.bbpf.commons.calculations                # 精度计算处理模块
       |--com.snbc.bbpf.commons.collects                    # 集合处理模块
       |--com.snbc.bbpf.commons.crypt                       # 加解密模块
       |--com.snbc.bbpf.commons.dates                       # 时间处理模块
       |--com.snbc.bbpf.commons.expressions                 # 表达式处理模块
       |--com.snbc.bbpf.commons.files                       # 文件处理模块
       |--com.snbc.bbpf.commons.ftps                        # ftp处理模块（包括sftp）
       |--com.snbc.bbpf.commons.jsons                       # JSON处理模块
       |--com.snbc.bbpf.commons.geo                         # 地理处理模块
       |--com.snbc.bbpf.commons.objs                        # 对象处理模块
       |--com.snbc.bbpf.commons.randoms                     # 随机处理模块
       |--com.snbc.bbpf.commons.reflects                    # 反射处理模块
       |--com.snbc.bbpf.commons.strings                     # 字符串处理模块
          |--com.snbc.bbpf.commons.strings.security         # 敏感词处理模块
       |--com.snbc.bbpf.commons.systems                     # 系统处理模块   
       |--com.snbc.bbpf.commons.validations                 # 验证处理模块
          |--com.snbc.bbpf.commons.validations.annotation   # 验证注解
          |--com.snbc.bbpf.commons.validations.aspect       # 注解切面类
          |--com.snbc.bbpf.commons.validations.processor    # 验证注解执行类
          |--com.snbc.bbpf.commons.validations.utils        # 用于验证的帮助类
          |--com.snbc.bbpf.commons.validations.validator    # 常用验证类
       |--com.snbc.bbpf.commons.xmls                        # xml处理模块
    |--pom.xml   
```  
### 8.2 依赖组成

| groupId | artifactId     | version      | 作用               | 
|---|----------------|--------------|------------------|
| org.apache.commons | commons-text   | 1.10.0       | 对字符串做转义时使用       |
| org.apache.commons | commons-lang3  | 3.12.0       | 在字符串处理中使用        |
| org.apache.commons | commons-codec  | 1.16.0       | 对文件验证处理中使用       |
| com.google.code.gson | gson | 2.9.0        | 在做深拷贝中使用效率较高     |
| com.github.f4b6a3| ulid-creator | 5.2.0        | 在生成ulid时使用       |
| com.google.auto.service | auto-service | 1.1.1        | 验证注解时使用          |
| org.mapstruct | mapstruct | 1.5.5.Final  | 用于对象转换时使用        |
| commons-net | commons-net | 3.9.0  | 用于ftp客户端使用       |
| com.github.mwiede | jsch | 0.2.11  | 用于sftp客户端使用      |
| com.fasterxml.jackson.core | jackson-databind | 2.15.2  | 用于JSON处理时使用      |
| com.fasterxml.jackson.dataformat | jackson-dataformat-xml | 2.15.2  | 用于转换XML时使用       |
| com.fasterxml.jackson.datatype | jackson-datatype-jsr310 | 2.15.2  | 用于JSON处理时使用      |
| com.jayway.jsonpath | json-path | 2.8.0  | 使用JSON-Path查找JSON |
| org.aspectj | aspectjweaver | 1.8.2  | 使用对注解进行切面        |
| org.aspectj | aspectjrt | 1.8.2  | 使用对注解进行切面的运行时    |
| org.dom4j | dom4j | 2.1.4  | 对XML解析时使用        |
| jaxen | jaxen | 2.0.0  | 使用XPath查找XML时使用  |
| org.bouncycastle | bcprov-jdk18on | 1.77 | 加解密时使用 |
| org.apache.httpcomponents | httpclient | 4.5.13 | 用于http请求时使用 |
| org.jasypt | jasypt | 1.9.3 | 用于加解密时使用 |
| org.apache.httpcomponents | httpclient | 4.5.13 | 用于http请求时使用 |
------

## 9. 注意事项

暂无

------

## 10. 版本记录

```
V1.6.0
   【字符串处理】
      1.对通用字符串进行格式化
      2.可以将字符串指定起始、结束位置进行脱敏
      3.断是否以子字符串开头、判断是否以子字符串结尾
   【文件处理】
      1.检查路径中是否包含非法字符
      2.检查路径是否有效路径
   【地理处理】
      1.将WGS84坐标和BD09坐标相互转换
      2.将GCJ02坐标和BD09坐标相互转换
      3.将WGS84坐标和GCJ02坐标相互转换
   【加解密】
      1.增加使用jasypt的加解密
   【随机处理】
      1. SecureRandom使用非堵塞方式
   【系统资源与诊断】
      1. http方式连通性测试
    【文档】
      1. 生成所有API的使用指南文档。
      2. 注释优化(返回值，时间等)。
V1.5.0
   【字节处理】
     1. Socket编程中常用大小端转换数字 字符串需求
     2. byte数组合并
   【字符串处理】
     1. 字符串判空操作增加对“null”的判断
     2. 将带分隔符的字符串转成List
     3. 去掉字符串中中文或英文字符
   【文件处理】  
     1. 查询指定目录下文件数量
     2. 将指定文件或目录移动或复制到指定目录下
     3. 将指定文件或目录重命名
     4. 对gzip文件进行处理
     5. 对文件权限进行设置
     6. 新增获取jar工作目录的需求
   【加解密】
     1. DES加解密支持8倍位数密钥
   【XML模块】  
     1. 对xml进行修改/插入/删除操作
   【FTP模块】  
     1. 批量下载和删除、创建目录
   【时间处理】  
     1. 返回指定条件的日期或时间字符串列表
   【系统资源与诊断】  
     1. 连通性测试
     2. 获取系统信息
   【集合操作】  
     1. 去除重复数据
   【随机处理】
     1. 雪花算法增加19位算法  
V1.4.2 
  【bug】
     1. 脱敏校验注解验证不通过问题
V1.4.0  2023-12-13
  【文件处理模块】
     1. 对文件路径的操作
     2. 对ini文件的查找操作
     3. 对ini文件的增删改查操作
     4. properties文件的查找操作
     5. 对zip文件进行处理
  【加解密】
     1. sm4对称加密算法
     2. sm3散列算法
     3. 3DES对称加密算法
     4. Base64算法
     5. RSA非对称加密算法
     6. MD5散列算法
     7. SHA散列算法
     8. AES对称加密算法
     9. token的生成和解析
  【集合处理】
     1. 对集合进行交集/并集/排除操作
     2. 查找集合中符合条件的元素
  【对象模块】
     1. 通过反射调用方法
  【FTP模块】
     1. 对远程目录的操作
  【字符串处理】
     1. 对多个字符串进行判空处理
---------------------------------------------------------------------  
V1.3.0  2023-11-10
  【验证模块】
      1. 优化身份证的验证
      2. 对非空验证注解的字段类型/方法返回值/参数类型的校验
  【对象模块】
      1. 反射获取对象信息
  【XML模块】
      1. 验证XML 
      2. 获取指定名称的节点数据
      3. 获取指定名称和属性
  【文件处理模块】
      1. properties文件的查找操作  
  【Bug】
      1. DateUtil parseToDate问题
---------------------------------------------------------------------      
V1.2.0  2023-09-27
 【字符串处理】
   1. 对字符串进行压缩和解压 
 【集合模块】
   1. 集合转换成JSON字符串处理。
 【敏感词】
   1. 敏感词注解优化。
 【验证模块】
   1. 常用字符串格式验证的优化需求。
   2. 非空验证的优化需求。
   3. 判断字符是否包含指定字符注解功能。
   4. 判断字符串是否为整数或浮点数的注解功能。
 【表达式模块】
   1. 解析带有表达式的字符串,并进行运算。
 【XML模块】
   1. XML字符串和对象之间的转换处理。
   2. XML和JSON字符串之间的转换处理。
   3. 查找XML字符串中符合条件的数据。
 【JSON模块】
   1. JSON字符串和对象之间的转换处理。
   2. JSON字符串转换成集合处理。
   3. 查找JSON字符串符合条件的数据。
 【FTP模块】
   1. 实现FTP客户端及相关处理方法。
   2. 实现SFTP客户端及相关处理方法。
 【文件处理模块】
   1. 对文件类型校验。
   2. 使用Java8的API处理文件。
   3. 对文件完整性处理。
---------------------------------------------------------------------    
V1.1.2  2023-09-11
   [bug]
      1. 修复雪花算法在docker环境下出现异常的问题
---------------------------------------------------------------------      
V1.1.1  2023-08-31
   [bug]
      1. 修复雪花算法异常和重复问题
---------------------------------------------------------------------        
V1.1.0  2023-07-31
   [对象处理]
      1. 检查对象是否为空
      2. 深拷贝对象
      3. 对两个属性雷同但类名不同对象进行互相转换
      4. 将Map和对象进行互相转换
   [随机处理]
      1. 指定格式随机字符串
      2. 随机数字的生成
   [精度计算]
      1. 加减乘除法精度运算
      2. 大小写金额互相转换
      3. 计算一个数字占另外一个数字的百分比字符串
   [验证处理]
      1. 对字符串长度验证
      2. 非空验证
      3. 常用字符串格式验证     
   [字符串处理]
      1. 查找字符串中指定字符的出现次数和指定次数的索引
      2. 对版本号进行比较
   [字节处理]
      1. 将byte数组和十六进制字符串互相转换
   [时间处理]
      1. 将Date和LocalDate、LocalDateTime、ZonedDateTime的互相转换    
---------------------------------------------------------------------          
V1.0.0  2023-05-30 
   [字节处理]
      1. BCD码和byte数组互相转换
      2. ASCII码和BCD码互相转换
      3. 将byte数组变成大小端方式
   [集合处理]
      1. 对象数组是否为空
      2. 将对象集合和带有指定分隔符的字符串互相转换
   [时间处理]
      1. 将Date,LocalDate,LocalDateTime,ZonedDateTime和指定时间格式字符串互相转换
      2. 对Date,LocalDate,LocalDateTime,ZonedDateTime进行各种时间计算
      3. 判断指定时间是否在开始时间和结束时间之间
   [字符串处理]
      1. 去除字符串中指定字符或字符串。
      2. 对常用的字符串格式进行校验。
      3. 对字符串或空白判断进行封装。
      4. 判断字符串是否为整数或浮点数。
      5. 封装对字符串的转义
      6. 判断字符是否包含指定字符
      7. 将字符串进行指定长度的缩略
      8. 根据分隔符获取字符串
      9. 字符串补全处理
      10. 生成随机字符串
      11. 敏感信息脱敏处理
```
------
