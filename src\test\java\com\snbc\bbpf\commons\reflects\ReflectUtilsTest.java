/*
 * 版权所有 2009-2023 山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.reflects;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.util.Map;

/**
 * 反射工具类单元测试
 *
 * <AUTHOR>
 * @date 2023/11/2 10:08
 */
class ReflectUtilsTest {

    class Dog{
        private String name;

        private Integer age;

        private Color color;

        public Dog(String name, Integer age, Color color) {
            this.name = name;
            this.age = age;
            this.color = color;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Integer getAge() {
            return age;
        }

        public void setAge(Integer age) {
            this.age = age;
        }

        public Color getColor() {
            return color;
        }

        public void setColor(Color color) {
            this.color = color;
        }
    }

    class Color{
        private String red;

        private String green;

        private String blue;

        public Color(String red, String green, String blue) {
            this.red = red;
            this.green = green;
            this.blue = blue;
        }

        public String getRed() {
            return red;
        }

        public String getGreen() {
            return green;
        }

        public String getBlue() {
            return blue;
        }

        public void setRed(String red) {
            this.red = red;
        }

        public void setGreen(String green) {
            this.green = green;
        }

        public void setBlue(String blue) {
            this.blue = blue;
        }
    }

    private Color color;

    private Dog dog;

    @BeforeEach
    void setup() {
        color = new Color("1001","2002","3003");
        dog = new Dog("大黄",5,color);
    }

    @Test
    @DisplayName("参数异常校验")
    @Tags({
            @Tag("@id:66"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/11/2")
    })
    void testValidateArgs_argsError() {
        RuntimeException runtimeException = Assertions.assertThrows(RuntimeException.class, () -> ReflectUtils.getPropertyValue(null, "aa", String.class));
        Assertions.assertEquals("The Target Object is Null",runtimeException.getMessage());
        runtimeException = Assertions.assertThrows(RuntimeException.class,()->ReflectUtils.getPropertyValue(dog,"",String.class));
        Assertions.assertEquals("PropertyName is Blank",runtimeException.getMessage());
        runtimeException = Assertions.assertThrows(RuntimeException.class,()->ReflectUtils.getPropertyValue(dog,"name",null));
        Assertions.assertEquals("Property Value Class is Null",runtimeException.getMessage());
    }

    @Test
    @DisplayName("获取对象指定属性取值 参数名不存在异常")
    @Tags({
            @Tag("@id:66"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/11/2")
    })
    void testValidateArgs_propertyNameNotExist() {
        Assertions.assertEquals("大黄",ReflectUtils.getPropertyValue(dog, "name", String.class));
    }

    @Test
    @DisplayName("获取对象指定属性取值 获取String类型属性取值成功")
    @Tags({
            @Tag("@id:66"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/11/2")
    })
    void testGetPropertyValue_StringValueSuccess() {
        Assertions.assertEquals("大黄",ReflectUtils.getPropertyValue(dog,"name",String.class));
    }


    @Test
    @DisplayName("获取对象指定属性取值 获取Integer类型属性取值成功")
    @Tags({
            @Tag("@id:66"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/11/2")
    })
    void testGetPropertyValue_IntegerValueSuccess() {
        Assertions.assertEquals(5,ReflectUtils.getPropertyValue(dog,"age",Integer.class));
    }

    @Test
    @DisplayName("获取对象指定属性取值 获取自定义类型属性取值成功")
    @Tags({
            @Tag("@id:66"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/11/2")
    })
    void testGetPropertyValue_ObjectValueSuccess() {
        Assertions.assertEquals(color,ReflectUtils.getPropertyValue(dog, "color", Color.class));
        Assertions.assertEquals("1001",ReflectUtils.getPropertyValue(dog, "color", Color.class).red);
        Assertions.assertEquals("2002",ReflectUtils.getPropertyValue(dog, "color", Color.class).green);
        Assertions.assertEquals("3003",ReflectUtils.getPropertyValue(dog, "color", Color.class).blue);

    }

    /****************************************************************************************************************************************************/


    @Test
    @DisplayName("获取对象指定属性取值 获取自定义类型属性取值成功")
    @Tags({
            @Tag("@id:66"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/11/2")
    })
    void testGetAllPropertiesValue_Success() {
        Map<String, Object> allPropertiesValue = ReflectUtils.getAllPropertiesValue(dog);
        Assertions.assertEquals("大黄", allPropertiesValue.get("name"));
        Assertions.assertEquals(5,allPropertiesValue.get("age"));
        Assertions.assertEquals(color,allPropertiesValue.get("color"));
    }

}
