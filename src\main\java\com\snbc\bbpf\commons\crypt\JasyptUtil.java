/*
 * 版权所有 2009-2024山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.crypt;

import org.jasypt.encryption.pbe.StandardPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.SimpleStringPBEConfig;

/**
 * Jasypt加解密工具类，提供基于Jasypt库的字符串加解密功能
 *
 * <AUTHOR>
 * @module bbpf-commons
 * @date 2025-05-14
 * @version 1.6.0
 */
public class JasyptUtil {

    /**
     * 默认加密算法
     */
    public static final String DEFAULT_ALGORITHM = "PBEWithMD5AndDES";

    /**
     * 默认密钥
     */
    private static final String DEFAULT_PASSWORD = "SNBC_DEFAULT_PASSWORD";

    /**
     * 默认迭代次数
     */
    private static final int DEFAULT_ITERATIONS = 1000;

    /**
     * 默认线程池大小
     */
    private static final String DEFAULT_POOL_SIZE = "1";
    
    /**
     * 默认加密提供者名称
     */
    private static final String DEFAULT_PROVIDER_NAME = "SunJCE";
    
    /**
     * 默认盐生成器
     */
    private static final String DEFAULT_SALT_GENERATOR = "org.jasypt.salt.RandomSaltGenerator";
    
    /**
     * 默认初始化向量生成器
     */
    private static final String DEFAULT_IV_GENERATOR = "org.jasypt.iv.NoIvGenerator";
    
    /**
     * 默认输出类型
     */
    private static final String DEFAULT_OUTPUT_TYPE = "base64";

    /**
     * 默认加密解密工具
     */
    private static final StandardPBEStringEncryptor DEFAULT_ENCRYPTOR = createEncryptor(DEFAULT_ALGORITHM, DEFAULT_PASSWORD, DEFAULT_ITERATIONS);

    /**
     * 使用默认参数加密字符串
     *
     * @param plainText 待加密的字符串
     * @return 加密后的字符串
     * @throws IllegalArgumentException 当输入参数为null时抛出
     * <AUTHOR>
     * @date 2025-05-14
     * @since 1.6.0
     */
    public static String encrypt(String plainText) {
        if (plainText == null) {
            throw new IllegalArgumentException("加密字符串不能为null");
        }
        return DEFAULT_ENCRYPTOR.encrypt(plainText);
    }

    /**
     * 使用指定参数加密字符串
     * 注意：
     *  1. 如果密码为空，则使用默认密码
     *  2. 如果迭代次数为0，则使用默认迭代次数
     *  3. 如果算法为空，则使用默认算法PBEWithMD5AndDES
     *  4. 算法这里使用字符串，该算法必须是对应系统下支持的算法，否则会报错。
     *
     * @param plainText  待加密的字符串
     * @param password   加密密钥
     * @param algorithm  加密算法
     * @param iterations 迭代次数
     * @return 加密后的字符串
     * @throws IllegalArgumentException 当输入参数为null时抛出
     * <AUTHOR>
     * @date 2025-05-14
     * @since 1.6.0
     */
    public static String encrypt(String plainText, String password, String algorithm, int iterations) {
        if (plainText == null) {
            throw new IllegalArgumentException("加密字符串不能为null");
        }
        StandardPBEStringEncryptor encryptor = createEncryptor(algorithm, password, iterations);
        return encryptor.encrypt(plainText);
    }

    /**
     * 使用默认参数解密字符串
     *
     * @param encryptedText 加密的字符串
     * @return 解密后的字符串
     * @throws IllegalArgumentException 当输入参数为null时抛出
     * <AUTHOR>
     * @date 2025-05-14
     * @since 1.6.0
     */
    public static String decrypt(String encryptedText) {
        if (encryptedText == null) {
            throw new IllegalArgumentException("解密字符串不能为null");
        }
        return DEFAULT_ENCRYPTOR.decrypt(encryptedText);
    }

    /**
     * 使用指定参数解密字符串
     * 注意：
     *  1. 如果密码为空，则使用默认密码
     *  2. 如果迭代次数为0，则使用默认迭代次数
     *  3. 如果算法为空，则使用默认算法PBEWithMD5AndDES
     *  4. 算法这里使用字符串，该算法必须是对应系统下支持的算法，否则会报错。
     *
     * @param encryptedText 加密的字符串
     * @param password      加密密钥
     * @param algorithm     加密算法
     * @param iterations    迭代次数
     * @return 解密后的字符串
     * @throws IllegalArgumentException 当输入参数为null时抛出
     * <AUTHOR>
     * @date 2025-05-14
     * @since 1.6.0
     */
    public static String decrypt(String encryptedText, String password, String algorithm, int iterations) {
        if (encryptedText == null) {
            throw new IllegalArgumentException("解密字符串不能为null");
        }
        StandardPBEStringEncryptor encryptor = createEncryptor(algorithm, password, iterations);
        return encryptor.decrypt(encryptedText);
    }

    /**
     * 创建并配置加密解密器
     *
     * @param algorithm  加密算法
     * @param password   加密密钥
     * @param iterations 迭代次数
     * @return 配置好的加密解密器
     * @throws IllegalArgumentException 当输入参数为null时抛出
     * <AUTHOR>
     * @date 2025-05-14
     * @since 1.6.0
     */
    private static StandardPBEStringEncryptor createEncryptor(String algorithm, String password, int iterations) {
        if (algorithm == null) {
            algorithm = DEFAULT_ALGORITHM;
        }
        if (password == null) {
            password = DEFAULT_PASSWORD;
        }
        if (iterations <= 0) {
            iterations = DEFAULT_ITERATIONS;
        }

        StandardPBEStringEncryptor encryptor = new StandardPBEStringEncryptor();
        SimpleStringPBEConfig config = new SimpleStringPBEConfig();
        config.setAlgorithm(algorithm);
        config.setPassword(password);
        config.setKeyObtentionIterations(String.valueOf(iterations));
        // 定义常量替代魔法字符串
        
        config.setPoolSize(DEFAULT_POOL_SIZE);
        config.setProviderName(DEFAULT_PROVIDER_NAME);
        config.setSaltGeneratorClassName(DEFAULT_SALT_GENERATOR);
        config.setIvGeneratorClassName(DEFAULT_IV_GENERATOR);
        config.setStringOutputType(DEFAULT_OUTPUT_TYPE);
        encryptor.setConfig(config);
        return encryptor;
    }
} 