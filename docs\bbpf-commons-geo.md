# 地理坐标转换 (com.snbc.bbpf.commons.geo)

## 类概览

| 类名 | 功能描述 |
|------|----------|
| GeoCoordinateConverter | 提供WGS84、GCJ02和BD09坐标系之间的转换功能 |

## GeoCoordinateConverter - 坐标转换工具

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| wgs84ToGcj02(double lng, double lat) | 将WGS84坐标转换为GCJ02坐标 | lng: 经度<br>lat: 纬度 | double[]: 包含GCJ02经纬度的数组 |
| gcj02ToWgs84(double lng, double lat) | 将GCJ02坐标转换为WGS84坐标 | lng: 经度<br>lat: 纬度 | double[]: 包含WGS84经纬度的数组 |
| bd09ToGcj02(double lng, double lat) | 将BD09坐标转换为GCJ02坐标 | lng: 经度<br>lat: 纬度 | double[]: 包含GCJ02经纬度的数组 |
| gcj02ToBd09(double lng, double lat) | 将GCJ02坐标转换为BD09坐标 | lng: 经度<br>lat: 纬度 | double[]: 包含BD09经纬度的数组 |

### 注意事项

- **坐标系说明**: 
  - WGS84: 国际标准GPS坐标系
  - GCJ02: 中国国家测绘局制定的坐标系，俗称火星坐标系
  - BD09: 百度地图使用的坐标系
- **精度**: 转换算法存在一定误差，适用于一般应用场景
- **适用范围**: 仅适用于中国大陆地区坐标转换
- **边界检查**: 自动检查坐标是否在中国范围内，不在范围内的坐标不进行转换
- **线程安全**: 所有方法均为静态方法，线程安全
- **异常处理**: 输入参数为非法坐标值时可能抛出IllegalArgumentException

### 使用示例

```java
// WGS84转GCJ02
double[] gcj02 = GeoCoordinateConverter.wgs84ToGcj02(116.404, 39.915);
System.out.println("GCJ02坐标: " + gcj02[0] + ", " + gcj02[1]);

// GCJ02转WGS84
double[] wgs84 = GeoCoordinateConverter.gcj02ToWgs84(116.404, 39.915);
System.out.println("WGS84坐标: " + wgs84[0] + ", " + wgs84[1]);

// BD09转GCJ02
double[] gcj02FromBd = GeoCoordinateConverter.bd09ToGcj02(116.404, 39.915);
System.out.println("从BD09转换的GCJ02坐标: " + gcj02FromBd[0] + ", " + gcj02FromBd[1]);

// GCJ02转BD09
double[] bd09 = GeoCoordinateConverter.gcj02ToBd09(116.404, 39.915);
System.out.println("BD09坐标: " + bd09[0] + ", " + bd09[1]);
```

### 坐标系说明

1. **WGS84坐标系**
   - 国际通用的GPS坐标系
   - 谷歌地图(国际版)、苹果地图等使用此坐标系

2. **GCJ02坐标系**
   - 中国国家测绘局制定的加密坐标系
   - 高德地图、腾讯地图等使用此坐标系
   - 在中国大陆地区，从GPS设备获取的WGS84坐标需要转换为GCJ02才能正确显示

3. **BD09坐标系**
   - 百度地图使用的坐标系
   - 在GCJ02基础上做了二次加密
   - 百度地图API返回的坐标是BD09坐标系