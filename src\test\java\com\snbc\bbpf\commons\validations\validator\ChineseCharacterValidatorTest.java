package com.snbc.bbpf.commons.validations.validator;

import com.snbc.bbpf.commons.validations.ValidException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;
@DisplayName("ChineseCharacterValidator 类单元测试")
class ChineseCharacterValidatorTest {
    @Test
    @DisplayName("输入非空，验证通过")
    @Tags({
        @Tag("@id:36"),
        @Tag("@author:yangweipeng"),
        @Tag("@date:2023/6/28")
    })
    void testValidateWithValidChineseString() {
        ChineseCharacterValidator validator = new ChineseCharacterValidator();
        String validChineseString = "你好世界";
        try {
            validator.validate(validChineseString, "测试消息");
        } catch (IllegalArgumentException e) {
            fail("验证失败");
        }
    }
    @Test
    @DisplayName("输入为空，抛出异常")
    @Tags({
        @Tag("@id:36"),
        @Tag("@author:yangweipeng"),
        @Tag("@date:2023/6/28")
    })
    void testValidateWithNullString() {
        ChineseCharacterValidator validator = new ChineseCharacterValidator();
        String nullString = null;
        assertThrows(IllegalArgumentException.class, () -> {
            validator.validate(nullString, "测试消息");
        });
    }
    @Test
    @DisplayName("输入非汉字字符串，抛出异常")
    @Tags({
        @Tag("@id:36"),
        @Tag("@author:yangweipeng"),
        @Tag("@date:2023/6/28")
    })
    void testValidateWithNonChineseString() {
        ChineseCharacterValidator validator = new ChineseCharacterValidator();
        String nonChineseString = "Hello World";
        assertThrows(IllegalArgumentException.class, () -> {
            validator.validate(nonChineseString, "测试消息");
        });
    }
}
