package com.snbc.bbpf.commons.bytes;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.math.BigInteger;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;


/**
 * bcd码转二进制字节数组单元测试类
 *
 * <AUTHOR>
 * @module 字节处理
 * @date 2023/4/20 18:18
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class BcdHandlerTest {

    @Test
    @DisplayName("将31字符串转换BCD码字节数组0x31")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testEncode_shouldEncodeStringEven() {
        assertArrayEquals(new byte[] { 0x31 }, BcdHandler.encode("31"));
    }

    @Test
    @DisplayName("将231字符串转换BCD码字节数组0x02 0x31")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testEncode_shouldEncodeStringOdd() {
        assertArrayEquals(new byte[] { 0x02, 0x31 }, BcdHandler.encode("231"));
    }

    @Test
    @DisplayName("将0字符串转换BCD码字节数组0x00")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testEncode_shouldEncodeStringZero() {
        assertArrayEquals(new byte[] { 0x00 }, BcdHandler.encode("0"));
    }

    @Test
    @DisplayName("将031字符串转换BCD码字节数组0x00 0x31")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testEncode_shouldEncodeStringAndKeepExplicitZero() {
        assertArrayEquals(new byte[] { 0x00, 0x31 }, BcdHandler.encode("031"));
    }

    @Test
    @DisplayName("将空串传入应该抛出提示必须是数字的IllegalArgumentException异常")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testEncode_encodeStringShouldThrowExceptionForEmptyString() {
        assertThrows(IllegalArgumentException.class,
                () -> BcdHandler.encode(""),
                "Only numeric strings can be encoded");
    }

    @Test
    @DisplayName("将字母字符串传入应该抛出必须是数字的IllegalArgumentException异常")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testEncode_encodeStringShouldThrowExceptionForNonDecimalString() {
        assertThrows(IllegalArgumentException.class,
                () -> BcdHandler.encode("A"),
                "Only numeric strings can be encoded");
    }

    @Test
    @DisplayName("将数字31转换BCD码字节数组0x31")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testEncode_shouldEncodeLongEven() {
        assertArrayEquals(new byte[] { 0x31 }, BcdHandler.encode(31));
    }

    @Test
    @DisplayName("将数字231转换BCD码字节数组0x02,0x31")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testEncode_shouldEncodeLongOdd() {
        assertArrayEquals(new byte[] { 0x02, 0x31 }, BcdHandler.encode(231));
    }

    @Test
    @DisplayName("将数字0转换BCD码字节数组0x00")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testEncode_shouldEncodeLongZero() {
        assertArrayEquals(new byte[] { 0x00 }, BcdHandler.encode(0));
    }

    @Test
    @DisplayName("将数字-1转换BCD码应该抛出提示必须是非负数的IllegalArgumentException异常")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testEncode_encodeLongShouldThrowExceptionForNegative() {
        assertThrows(IllegalArgumentException.class,
                () -> BcdHandler.encode(-1),
                "Only supports non negative numbers");
    }

    @Test
    @DisplayName("将数字31转换为长度2的BCD码字节数组为0x00 0x31")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testEncode_shouldEncodeLongWithLengthEven() {
        assertArrayEquals(new byte[] { 0x00, 0x31 }, BcdHandler.encode(31, 2));
    }

    @Test
    @DisplayName("将数字231转换为长度4的BCD码字节数组为0x00 0x00 0x02 0x31")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testEncode_shouldEncodeLongWithLengthOdd() {
        assertArrayEquals(new byte[] { 0x00, 0x00, 0x02, 0x31 }, BcdHandler.encode(231, 4));
    }

    @Test
    @DisplayName("将数字0转换为长度2的BCD码字节数组为0x00 0x00")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testEncode_shouldEncodeLongWithLengthZero() {
        assertArrayEquals(new byte[] { 0x00, 0x00 }, BcdHandler.encode(0, 2));
    }

    @Test
    @DisplayName("将数字-1转换为长度2的BCD码字节数组抛出提示必须是非负数的IllegalArgumentException异常")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testEncode_encodeLongWithLengthShouldThrowExceptionForNegative() {
        assertThrows(IllegalArgumentException.class,
                () -> BcdHandler.encode(-1,2),
                "Only supports non negative numbers");
    }

    @Test
    @DisplayName("将数字-1转换为长度2的BCD码字节数组抛出提示必须是非负数的IllegalArgumentException异常")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testEncode_encodeLongWithLengthShouldThrowExceptionIfLengthIsTooSmall() {
        assertThrows(IllegalArgumentException.class,
                () -> BcdHandler.encode(100,1),
                "Value does not fit in byte array of length 1");
    }

    @Test
    @DisplayName("将数字最大值转换为BCD码字节数组0x09 0x22 0x33 0x72 0x03 0x68 0x54 0x77 0x58 0x07")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testEncode_shouldEncodeBigIntegerSmall() {
        assertArrayEquals(new byte[] { 0x09, 0x22, 0x33, 0x72, 0x03, 0x68, 0x54, 0x77, 0x58, 0x07 },
                BcdHandler.encode(BigInteger.valueOf(Long.MAX_VALUE)));
    }

    @Test
    @DisplayName("将数字最大值+1转换为BCD码字节数组0x09 0x22 0x33 0x72 0x03 0x68 0x54 0x77 0x58 0x08")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testEncode_shouldEncodeBigIntegerBig() {
        assertArrayEquals(new byte[] { 0x09, 0x22, 0x33, 0x72, 0x03, 0x68, 0x54, 0x77, 0x58, 0x08 },
                BcdHandler.encode(BigInteger.valueOf(Long.MAX_VALUE).add(BigInteger.ONE)));
    }

    @Test
    @DisplayName("将数字0转换为BCD码字节数组0x00")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testEncode_shouldEncodeBigIntegerZero() {
        assertArrayEquals(new byte[] { 0x00 }, BcdHandler.encode(BigInteger.ZERO));
    }

    @Test
    @DisplayName("将数字-1转换为BCD码字节数组抛出提示必须是非负数的IllegalArgumentException异常")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testEncode_encodeBigIntegerShouldThrowExceptionForNegative() {
        assertThrows(IllegalArgumentException.class,
                () -> BcdHandler.encode(BigInteger.ONE.negate()),
                "Only supports non negative numbers");
    }

    @Test
    @DisplayName("将数字最大值转换为长度11位的BCD码字节数组0x00 0x09 0x22 0x33 0x72 0x03 0x68 0x54 0x77 0x58 0x07")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testEncode_shouldEncodeBigIntegerWithLengthSmall() {
        assertArrayEquals(new byte[] { 0x00, 0x09, 0x22, 0x33, 0x72, 0x03, 0x68, 0x54, 0x77, 0x58, 0x07 },
                BcdHandler.encode(BigInteger.valueOf(Long.MAX_VALUE), 11));
    }

    @Test
    @DisplayName("将数字最大值+1转换为长度11位的BCD码字节数组0x00 0x09 0x22 0x33 0x72 0x03 0x68 0x54 0x77 0x58 0x08")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testEncode_shouldEncodeBigIntegerWithLengthBig() {
        assertArrayEquals(new byte[] { 0x00, 0x09, 0x22, 0x33, 0x72, 0x03, 0x68, 0x54, 0x77, 0x58, 0x08 },
                BcdHandler.encode(BigInteger.valueOf(Long.MAX_VALUE).add(BigInteger.ONE), 11));
    }

    @Test
    @DisplayName("将数字0转换为长度2位的BCD码字节数组0x00 0x00")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testEncode_shouldEncodeBigIntegerWithLengthZero() {
        assertArrayEquals(new byte[] { 0x00, 0x00 }, BcdHandler.encode(BigInteger.ZERO, 2));
    }

    @Test
    @DisplayName("将数字-1转换为长度2位的BCD码字节数组0x00 0x00")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testEncode_encodeBigIntegerWithLengthShouldThrowExceptionForNegative() {
        assertThrows(IllegalArgumentException.class,
                () -> BcdHandler.encode(BigInteger.ONE.negate(),2),
                "Only supports non negative numbers");
    }

    @Test
    @DisplayName("将数字-1转换为长度2位的BCD码字节数组0x00 0x00")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testEncode_encodeBigIntegerWithLengthShouldThrowExceptionIfLengthIsTooSmall() {
        assertThrows(IllegalArgumentException.class,
                () -> BcdHandler.encode(BigInteger.valueOf(100), 1),
                "Value does not fit in byte array of length 1");
    }

    @Test
    @DisplayName("将字节数组0x31转换为BCD码数字31")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testDecode_shouldDecodeEven() {
        assertEquals(31, BcdHandler.decode(new byte[] { 0x31}).intValue());
    }

    @Test
    @DisplayName("将字节数组0x02 0x31转换为BCD码数字231")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testDecode_shouldDecodeOdd() {
        assertEquals(231, BcdHandler.decode(new byte[] { 0x02, 0x31}).intValue());
    }

    @Test
    @DisplayName("将字节数组0x00转换为BCD码数字0")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testDecode_shouldDecodeZero() {
        assertEquals(0, BcdHandler.decode(new byte[] { 0x0 }).intValue());
    }

    @Test
    @DisplayName("将字节数组-48转换为BCD码时抛出非法字符异常Illegal byte d0 at 0")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testDecode_decodeShouldThrowExceptionOnHighNibble() {
        assertThrows(IllegalArgumentException.class,
                () -> BcdHandler.decode(new byte[] { -48 }),
                "Illegal byte d0 at 0");
    }

    @Test
    @DisplayName("将字节数组0x0d转换为BCD码时抛出非法字符异常Illegal byte d0 at 0")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testDecode_decodeShouldThrowExceptionOnLowNibble() {
        assertThrows(IllegalArgumentException.class,
                () -> BcdHandler.decode(new byte[] { 0x0d }),
                "Illegal byte d0 at 0");
    }

    @Test
    @DisplayName("将字节数组0x31并设置奇数时带前导0转换为十进制BCD码字符串为31")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testDecode_shouldDecodeAsStringEven() {
        assertEquals("31", BcdHandler.decodeAsString(new byte[] { 0x31 }, true));
    }

    @Test
    @DisplayName("将字节数组0x02 0x31并设置奇数时带前导0转换为十进制BCD码字符串为231")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testDecode_shouldDecodeAsStringOddStripLeadingZero() {
        assertEquals("231", BcdHandler.decodeAsString(new byte[] { 0x02, 0x31 }, true));
    }

    @Test
    @DisplayName("将字节数组0x02 0x31并不设置奇数时带前导0转换为十进制BCD码字符串为0231")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testDecode_shouldDecodeAsStringOddKeepLeadingZero() {
        assertEquals("0231", BcdHandler.decodeAsString(new byte[] { 0x02, 0x31 }, false));
    }

    @Test
    @DisplayName("将字节数组0x00设置奇数时带前导0转换为十进制BCD码字符串为0")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testDecode_shouldDecodeAsStringZeroStripLeadingZero() {
        assertEquals("0", BcdHandler.decodeAsString(new byte[] { 0x00 }, true));
    }

    @Test
    @DisplayName("将字节数组0x00不设置奇数时带前导0转换为十进制BCD码字符串为00")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testDecode_shouldDecodeAsStringZeroKeepLeadingZero() {
        assertEquals("00", BcdHandler.decodeAsString(new byte[] { 0x00 }, false));
    }

    @Test
    @DisplayName("将字节数组-48设置奇数时带前导0转换为十进制BCD码时抛出非法字符异常")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testDecode_decodeAsStringShouldThrowExceptionOnHighNibble() {
        assertThrows(IllegalArgumentException.class,
                () -> BcdHandler.decodeAsString(new byte[] { -48 }, true),
                "Illegal byte d0 at 0");
    }

    @Test
    @DisplayName("将字节数组0x0d设置奇数时带前导0转换为十进制BCD码时抛出非法字符异常")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/20")
    })
    void testDecode_decodeAsStringShouldThrowExceptionOnLowNibble() {
        assertThrows(IllegalArgumentException.class,
                () -> BcdHandler.decodeAsString(new byte[] { 0x0d }, true),
                "Illegal byte d0 at 0");
    }

    @Test
    @DisplayName("将byte=96转换为bcd码为48")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/22")
    })
    void testSingleAsciiToBcd_shouldToBcd(){
        assertEquals((byte)48, BcdHandler.singleAsciiToBcd((byte)96));
    }

    @Test
    @DisplayName("将byte=48,48,49,49转换为bcd码为0,17")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/22")
    })
    void testAsciiToBcd_shouldToBcd(){
        assertArrayEquals(new byte[]{0,17},BcdHandler.asciiToBcd(new byte[] {48,48,49,49}));
    }

    @Test
    @DisplayName("将bcd码为0,17转换为byte=0,0,1,1")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/22")
    })
    void testBcdToAscii_shouldToAscii(){
        assertArrayEquals(new byte[]{0,0,1,1},BcdHandler.bcdToAscii(new byte[] {0,17}));
    }

    @Test
    @DisplayName("将bcd码为0x12, 0x34, 0x56转换为0x01, 0x02, 0x03, 0x04, 0x05, 0x06")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/22")
    })
    void testBcdToAscii_Positive() {
        byte[] input = {0x12, 0x34, 0x56};
        byte[] expectedOutput = {0x01, 0x02, 0x03, 0x04, 0x05, 0x06};
        assertArrayEquals(expectedOutput, BcdHandler.bcdToAscii(input));
    }
    @Test
    @DisplayName("将bcd码为0x12, 0x34, 0x56转换为0x01, 0x02, 0x03, 0x04, 0x05")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/22")
    })
    void testBcdToAscii_Negative() {
        byte[] input = {0x12, 0x34, 0x56};
        byte[] expectedOutput = {0x01, 0x02, 0x03, 0x04, 0x05};
        assertNotEquals(expectedOutput, BcdHandler.bcdToAscii(input));
    }
    @Test
    @DisplayName("将bcd码为null抛出参数异常")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/22")
    })
    void testBcdToAscii_NullInput() {
        byte[] input = null;
        assertThrows(NullPointerException.class,()->BcdHandler.bcdToAscii(input));
    }
    @Test
    @DisplayName("将bcd码为Empty返回也是Empty")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/22")
    })
    void testBcdToAscii_EmptyInput() {
        byte[] input = {};
        byte[] expectedOutput = {};
        assertArrayEquals(expectedOutput, BcdHandler.bcdToAscii(input));
    }

    @Test
    @DisplayName("将bcd码为0x00返回0x00, 0x00")
    @Tags({
            @Tag("@id:5"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/4/22")
    })
    void testBcdToAscii_MinBoundary() {
        byte[] input = {0x00};
        byte[] expectedOutput = {0x00, 0x00};
        assertArrayEquals(expectedOutput, BcdHandler.bcdToAscii(input));
    }
}