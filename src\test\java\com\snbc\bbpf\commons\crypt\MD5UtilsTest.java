/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.crypt;


import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * MD5散列算法工具类单元测试类
 *
 * <AUTHOR>
 * @module 加解密
 * @date 2023-12-01 10:44
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class MD5UtilsTest {

    private static Map<String, String> md5Map = new HashMap<>();

    @BeforeEach
    void setUp() {

        md5Map.put("admin", "21232f297a57a5a743894a0e4a801fc3");
        md5Map.put("123456", "e10adc3949ba59abbe56e057f20f883e");

    }

    @DisplayName("使用BouncyCastle计算字符串MD5正常")
    @Tags({
            @Tag("@id:86"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/12/01")
    })
    @Test
    void testMd5WithBouncyCastleWithString() {
        assertEquals(md5Map.get("admin"), MD5Utils.md5WithBouncyCastle("admin"));
        assertEquals(md5Map.get("123456"), MD5Utils.md5WithBouncyCastle("123456"));
    }

    @DisplayName("使用BouncyCastle计算字节数组MD5正常")
    @Tags({
            @Tag("@id:86"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/12/01")
    })
    @Test
    void testMd5WithBouncyCastleWithBytes() {
        assertEquals(md5Map.get("admin"), MD5Utils.md5WithBouncyCastle("admin".getBytes(StandardCharsets.UTF_8)));
        assertEquals(md5Map.get("123456"), MD5Utils.md5WithBouncyCastle("123456".getBytes(StandardCharsets.UTF_8)));
    }

    @DisplayName("使用JDK计算字符串MD5正常")
    @Tags({
            @Tag("@id:86"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/12/01")
    })
    @Test
    void testMd5WithString() {
        assertEquals(md5Map.get("admin"), MD5Utils.md5("admin"));
        assertEquals(md5Map.get("123456"), MD5Utils.md5("123456"));
    }

    @DisplayName("使用JDK计算字节数组MD5正常")
    @Tags({
            @Tag("@id:86"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/12/01")
    })
    @Test
    void testMd5WithBytes() {
        assertEquals(md5Map.get("admin"), MD5Utils.md5("admin".getBytes(StandardCharsets.UTF_8)));
        assertEquals(md5Map.get("123456"), MD5Utils.md5("123456".getBytes(StandardCharsets.UTF_8)));
    }

    @DisplayName("测试md5计算入参为空的异常情况")
    @Tags({
            @Tag("@id:86"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/12/01")
    })
    @Test
    void testMd5Utils_param_is_null() {
        assertThrows(NullPointerException.class,
                () -> MD5Utils.md5WithBouncyCastle((String) null),
                "parameter is null");
        assertThrows(NullPointerException.class,
                () -> MD5Utils.md5WithBouncyCastle((byte[]) null),
                "parameter is null");
        assertThrows(NullPointerException.class,
                () -> MD5Utils.md5((String) null),
                "parameter is null");
        assertThrows(NullPointerException.class,
                () -> MD5Utils.md5((byte[]) null),
                "parameter is null");
    }


}
