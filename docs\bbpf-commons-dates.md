# 日期时间处理 (com.snbc.bbpf.commons.dates)

## 类概览  

| 类名 | 功能描述 |
|------|----------|
| BaseDateDiffUtil | 提供日期时间计算的基础功能，作为其他日期工具类的基类 |
| DateFormatUtil | 提供日期和时间的格式化和解析功能，支持多种日期格式 |
| DateUtils | 提供对传统Date对象的操作，包括日期计算、比较和转换等功能 |
| LocalDateUtils | 提供对Java 8中引入的LocalDate类的操作，处理不带时间部分的日期 |
| LocalDateTimeUtils | 提供对Java 8中引入的LocalDateTime类的操作，处理不带时区的日期时间 |
| ZoneDateTimeUtils | 提供对Java 8中引入的ZonedDateTime类的操作，处理带时区的日期时间 |

## DateFormatUtil - 日期格式化工具

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| format(Date date, DateTimeFormatter dateTimeFormatter) | 将日期格式化为指定格式的字符串 | date: 日期对象<br>dateTimeFormatter: 格式模式 | String: 格式化后的日期字符串 |
| format(LocalDate localDate, DateTimeFormatter dateTimeFormatter) | 将本地日期格式化为指定格式的字符串 | localDate: 本地日期对象<br>dateTimeFormatter: 格式模式 | String: 格式化后的日期字符串 |
| format(LocalTime localTime, DateTimeFormatter dateTimeFormatter) | 将本地时间格式化为指定格式的字符串 | localTime: 本地时间对象<br>dateTimeFormatter: 格式模式 | String: 格式化后的时间字符串 |
| format(LocalDateTime localDateTime, DateTimeFormatter dateTimeFormatter) | 将本地日期时间格式化为指定格式的字符串 | localDateTime: 本地日期时间对象<br>dateTimeFormatter: 格式模式 | String: 格式化后的日期时间字符串 |
| format(ZonedDateTime zonedDateTime, DateTimeFormatter dateTimeFormatter) | 将带时区的日期时间格式化为指定格式的字符串 | zonedDateTime: 带时区的日期时间对象<br>dateTimeFormatter: 格式模式 | String: 格式化后的日期时间字符串 |
| parseToLocalDateTime(String dateStr, DateTimeFormatter dateTimeFormatter) | 将字符串解析为本地日期时间 | dateStr: 日期字符串<br>dateTimeFormatter: 格式模式 | LocalDateTime: 解析后的本地日期时间对象 |
| parseToLocalDate(String dateStr, DateTimeFormatter dateTimeFormatter) | 将字符串解析为本地日期 | dateStr: 日期字符串<br>dateTimeFormatter: 格式模式 | LocalDate: 解析后的本地日期对象 |
| parseToZonedDateTime(String dateStr, DateTimeFormatter dateTimeFormatter) | 将字符串解析为带时区的日期时间 | dateStr: 日期字符串<br>dateTimeFormatter: 格式模式 | ZonedDateTime: 解析后的带时区的日期时间对象 |
| parseToDate(String dateStr, DateTimeFormatter dateTimeFormatter) | 将字符串解析为日期 | dateStr: 日期字符串<br>dateTimeFormatter: 格式模式 | Date: 解析后的日期对象 |
| createFormatter(String pattern) | 创建日期时间格式化器 | pattern: 格式模式字符串 | DateTimeFormatter: 日期时间格式化器 |

### 预定义的格式化器

| 格式化器 | 格式 | 说明 |
|---------|------|------|
| DATE_FORMATTER | yyyy-MM-dd | 日期格式 |
| TIME_FORMATTER | HH:mm:ss | 时间格式 |
| DATETIME_MINUTE_FORMATTER | yyyy-MM-dd HH:mm | 日期时间格式(精确到分钟) |
| DATETIME_FORMATTER | yyyy-MM-dd HH:mm:ss | 日期时间格式(精确到秒) |
| DATETIME_MS_FORMATTER | yyyy-MM-dd HH:mm:ss.SSS | 日期时间格式(精确到毫秒) |
| CHINESE_DATE_FORMATTER | yyyy年MM月dd日 | 中文日期格式 |
| CHINESE_DATE_TIME_FORMATTER | yyyy年MM月dd日HH时mm分ss秒 | 中文日期时间格式 |
| PURE_DATE_FORMATTER | yyyyMMdd | 纯数字日期格式 |
| PURE_TIME_FORMATTER | HHmmss | 纯数字时间格式 |
| ACCOUNTANT_TIME_FORMATTER | yyyy/M/d HH:mm | 会计时间格式 |
| PURE_DATETIME_NO_SECOND_FORMATTER | yyyyMMddHHmm | 纯数字日期时间格式(不含秒) |
| PURE_DATETIME_FORMATTER | yyyyMMddHHmmss | 纯数字日期时间格式 |
| PURE_DATETIME_MS_FORMATTER | yyyyMMddHHmmssSSS | 纯数字日期时间格式(含毫秒) |
| UTC_MS_FORMATTER | yyyy-MM-dd'T'HH:mm:ss.SSS'Z' | UTC日期时间格式 |

### 注意事项

- **线程安全**: DateTimeFormatter是线程安全的，但使用SimpleDateFormat时需注意线程安全问题
- **异常处理**: 解析日期字符串时可能抛出DateTimeParseException，应妥善处理异常
- **格式兼容性**: 确保字符串格式与解析格式匹配，否则会解析失败
- **区域差异**: 不同区域的日期格式可能不同，使用withLocale()方法设置特定区域
- **时区处理**: 在解析或格式化带时区的日期时，注意时区的正确设置
- **格式常量**: 优先使用类中预定义的格式化器常量，以保证格式统一
- **null值处理**: 传入null值会导致NullPointerException，调用前应检查

### 使用示例

```java
// 格式化日期为指定格式
Date now = new Date();
String formatted = DateFormatUtil.format(now, DateFormatUtil.DATETIME_FORMATTER);  // 例如："2023-01-15 14:30:45"
String dateOnly = DateFormatUtil.format(now, DateFormatUtil.DATE_FORMATTER);      // 例如："2023-01-15"
String chineseDate = DateFormatUtil.format(now, DateFormatUtil.CHINESE_DATE_FORMATTER); // 例如："2023年01月15日"

// 自定义格式
DateTimeFormatter customFormatter = DateFormatUtil.createFormatter("yyyy-MM-dd | HH:mm");
String customFormatted = DateFormatUtil.format(now, customFormatter);  // 例如："2023-01-15 | 14:30"

// 解析字符串为日期
LocalDateTime localDateTime = DateFormatUtil.parseToLocalDateTime("2023-01-15 14:30:45", DateFormatUtil.DATETIME_FORMATTER);
LocalDate localDate = DateFormatUtil.parseToLocalDate("2023-01-15", DateFormatUtil.DATE_FORMATTER);
ZonedDateTime zonedDateTime = DateFormatUtil.parseToZonedDateTime("2023-01-15T14:30:45.000Z", DateFormatUtil.UTC_MS_FORMATTER);
Date date = DateFormatUtil.parseToDate("2023-01-15", DateFormatUtil.DATE_FORMATTER);

// 不同日期类型的格式化
LocalDate today = LocalDate.now();
String todayStr = DateFormatUtil.format(today, DateFormatUtil.DATE_FORMATTER);

LocalDateTime now2 = LocalDateTime.now();
String now2Str = DateFormatUtil.format(now2, DateFormatUtil.DATETIME_FORMATTER);

ZonedDateTime nowWithZone = ZonedDateTime.now();
String nowWithZoneStr = DateFormatUtil.format(nowWithZone, DateFormatUtil.DATETIME_FORMATTER);
```

## DateUtils - 日期工具类

DateUtils提供对传统Java Date对象的全面操作，包括日期计算、比较、转换等功能。

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| plus(Date date, int years, int months, int days, int hours, long minutes, long seconds, long milliseconds) | 日期加上指定的年、月、日、时、分、秒、毫秒 | date: 原始日期<br>years: 年数<br>months: 月数<br>days: 天数<br>hours: 小时数<br>minutes: 分钟数<br>seconds: 秒数<br>milliseconds: 毫秒数 | Date: 计算后的日期 |
| diff(Date start, Date end, TimeUnit timeUnit) | 计算两个日期之间的时间差 | start: 起始日期<br>end: 结束日期<br>timeUnit: 时间单位 | long: 时间差 |
| plusYears(Date date, int years) | 日期加上指定年数 | date: 原始日期<br>years: 要增加的年数 | Date: 计算后的日期 |
| plusMonths(Date date, int months) | 日期加上指定月数 | date: 原始日期<br>months: 要增加的月数 | Date: 计算后的日期 |
| plusDays(Date date, int days) | 日期加上指定天数 | date: 原始日期<br>days: 要增加的天数 | Date: 计算后的日期 |
| plusHours(Date date, int hours) | 日期加上指定小时数 | date: 原始日期<br>hours: 要增加的小时数 | Date: 计算后的日期 |
| plusMinutes(Date date, long minutes) | 日期加上指定分钟数 | date: 原始日期<br>minutes: 要增加的分钟数 | Date: 计算后的日期 |
| plusSeconds(Date date, long seconds) | 日期加上指定秒数 | date: 原始日期<br>seconds: 要增加的秒数 | Date: 计算后的日期 |
| plusMilliseconds(Date date, long milliseconds) | 日期加上指定毫秒数 | date: 原始日期<br>milliseconds: 要增加的毫秒数 | Date: 计算后的日期 |
| year(Date date) | 获取日期的年 | date: 日期 | int: 年数 |
| month(Date date) | 获取日期的月 | date: 日期 | int: 月数 |
| day(Date date) | 获取日期的日 | date: 日期 | int: 日数 |
| hour(Date date) | 获取日期的时 | date: 日期 | int: 时数 |
| minute(Date date) | 获取日期的分 | date: 日期 | int: 分数 |
| second(Date date) | 获取日期的秒 | date: 日期 | int: 秒数 |
| millisecond(Date date) | 获取日期的毫秒 | date: 日期 | int: 毫秒数 |
| starOfDay(Date date) | 获取当天的起始时间(00:00:00) | date: 日期 | Date: 当天的起始时间 |
| endOfDay(Date date) | 获取当天的结束时间(23:59:59) | date: 日期 | Date: 当天的结束时间 |
| firstDayOfYear(Date date) | 获取年份的第一天 | date: 日期 | Date: 年份的第一天(00:00:00) |
| firstDayOfYear(Date date, boolean isMorning) | 获取年份的第一天 | date: 日期<br>isMorning: 是否为晨时间(true=00:00:00, false=23:59:59) | Date: 年份的第一天 |
| lastDayOfYear(Date date) | 获取年份的最后一天 | date: 日期 | Date: 年份的最后一天(00:00:00) |
| lastDayOfYear(Date date, boolean isMorning) | 获取年份的最后一天 | date: 日期<br>isMorning: 是否为晨时间(true=00:00:00, false=23:59:59) | Date: 年份的最后一天 |
| firstDayOfMonth(Date date) | 获取月份的第一天 | date: 日期 | Date: 月份的第一天(00:00:00) |
| firstDayOfMonth(Date date, boolean isMorning) | 获取月份的第一天 | date: 日期<br>isMorning: 是否为晨时间(true=00:00:00, false=23:59:59) | Date: 月份的第一天 |
| lastDayOfMonth(Date date) | 获取月份的最后一天 | date: 日期 | Date: 月份的最后一天(00:00:00) |
| lastDayOfMonth(Date date, boolean isMorning) | 获取月份的最后一天 | date: 日期<br>isMorning: 是否为晨时间(true=00:00:00, false=23:59:59) | Date: 月份的最后一天 |
| ofDate(int years, int months, int days, int hours, int minutes, int seconds, int milliseconds) | 创建一个日期 | years: 年<br>months: 月<br>days: 日<br>hours: 时<br>minutes: 分<br>seconds: 秒<br>milliseconds: 毫秒 | Date: 创建的日期 |
| isEffectiveDate(Date now, Date start, Date end) | 判断日期是否在指定范围内 | now: 待判断的日期<br>start: 开始日期<br>end: 结束日期 | boolean: 是否在范围内 |
| toLoacalDate(Date date) | 将Date转换为LocalDate | date: 日期 | LocalDate: 本地日期 |
| toLoacalDateTime(Date date) | 将Date转换为LocalDateTime | date: 日期 | LocalDateTime: 本地日期时间 |
| toZonedDateTime(Date date) | 将Date转换为ZonedDateTime | date: 日期 | ZonedDateTime: 带时区的日期时间 |
| toZonedDateTime(Date date, ZoneId zoneId) | 将Date转换为指定时区的ZonedDateTime | date: 日期<br>zoneId: 时区 | ZonedDateTime: 带时区的日期时间 |
| getBetweenDateByYear(Date start, Date end, int val) | 获取两个日期之间按年间隔的日期列表 | start: 起始日期<br>end: 结束日期<br>val: 年间隔 | List<Date>: 日期列表 |
| getBetweenDateByQuarter(Date start, Date end, int val) | 获取两个日期之间按季度间隔的日期列表 | start: 起始日期<br>end: 结束日期<br>val: 季度间隔 | List<Date>: 日期列表 |
| getBetweenDateByMonths(Date start, Date end, int val) | 获取两个日期之间按月间隔的日期列表 | start: 起始日期<br>end: 结束日期<br>val: 月间隔 | List<Date>: 日期列表 |
| getBetweenDateByDays(Date start, Date end, int val) | 获取两个日期之间按天间隔的日期列表 | start: 起始日期<br>end: 结束日期<br>val: 天间隔 | List<Date>: 日期列表 |
| getBetweenDateByHours(Date start, Date end, int val) | 获取两个日期之间按小时间隔的日期列表 | start: 起始日期<br>end: 结束日期<br>val: 小时间隔 | List<Date>: 日期列表 |
| getBetweenDateByMinutes(Date start, Date end, int val) | 获取两个日期之间按分钟间隔的日期列表 | start: 起始日期<br>end: 结束日期<br>val: 分钟间隔 | List<Date>: 日期列表 |
| getBetweenDate(Date start, Date end, ChronoUnit field, int val) | 获取两个日期之间按指定时间单位间隔的日期列表 | start: 起始日期<br>end: 结束日期<br>field: 时间单位<br>val: 间隔值 | List<Date>: 日期列表 |
| getBetweenDate(Date start, Date end, ChronoUnit field, int val, boolean isIncludeStart, boolean isIncludeEnd) | 获取两个日期之间按指定时间单位间隔的日期列表 | start: 起始日期<br>end: 结束日期<br>field: 时间单位<br>val: 间隔值<br>isIncludeStart: 是否包含起始日期<br>isIncludeEnd: 是否包含结束日期 | List<Date>: 日期列表 |
| getAfterDates(Date start, ChronoUnit field, int val) | 获取指定日期之后的日期列表 | start: 起始日期<br>field: 时间单位<br>val: 间隔值 | List<Date>: 日期列表 |
| getBeforeDates(Date start, ChronoUnit field, int val) | 获取指定日期之前的日期列表 | start: 起始日期<br>field: 时间单位<br>val: 间隔值 | List<Date>: 日期列表 |
| getNextDates(Date start, ChronoUnit field, int val, boolean isIncludeStart) | 获取指定日期之后的日期列表 | start: 起始日期<br>field: 时间单位<br>val: 间隔值<br>isIncludeStart: 是否包含起始日期 | List<Date>: 日期列表 |

### 注意事项

- **时区处理**: 传统Date对象不直接包含时区信息，但在显示和计算时受系统默认时区影响
- **日期计算**: 日期加减操作可能导致月份或年份溢出，例如1月31日加一个月可能导致非预期结果
- **临界值**: 在处理月底日期时需特别注意（如2月28/29日）
- **性能考虑**: 频繁的Date转换操作可能影响性能，大量计算时应优化处理逻辑
- **日期比较**: 使用isEffectiveDate方法比较日期范围时，包含边界时间点
- **日期序列生成**: 生成日期序列可能消耗大量内存，注意控制列表大小
- **DST(夏令时)**: 在夏令时切换期间，小时计算可能出现异常，如一天不是24小时
- **UTC转换**: 需要UTC时间时，建议使用toZonedDateTime方法并指定ZoneId.of("UTC")

### 使用示例

```java
// 日期加减
Date now = new Date();
Date nextYear = DateUtils.plusYears(now, 1);
Date nextMonth = DateUtils.plusMonths(now, 1);
Date tomorrow = DateUtils.plusDays(now, 1);
Date nextHour = DateUtils.plusHours(now, 1);
Date nextMinute = DateUtils.plusMinutes(now, 1);
Date nextSecond = DateUtils.plusSeconds(now, 1);
Date nextMs = DateUtils.plusMilliseconds(now, 1);

// 日期差值计算
Date start = DateFormatUtil.parseToDate("2023-01-01", DateFormatUtil.DATE_FORMATTER);
Date end = DateFormatUtil.parseToDate("2023-01-10", DateFormatUtil.DATE_FORMATTER);
long daysDiff = DateUtils.diff(start, end, TimeUnit.DAYS);  // 返回9

// 日期属性获取
int year = DateUtils.year(now);       // 获取年份
int month = DateUtils.month(now);     // 获取月份(1-12)
int day = DateUtils.day(now);         // 获取日期(1-31)
int hour = DateUtils.hour(now);       // 获取小时(0-23)
int minute = DateUtils.minute(now);   // 获取分钟(0-59)
int second = DateUtils.second(now);   // 获取秒(0-59)
int ms = DateUtils.millisecond(now);  // 获取毫秒(0-999)

// 获取日期的起始和结束时间
Date startOfDay = DateUtils.starOfDay(now);     // 当天00:00:00
Date endOfDay = DateUtils.endOfDay(now);        // 当天23:59:59

// 获取月份/年份的第一天和最后一天
Date firstDayOfMonth = DateUtils.firstDayOfMonth(now);      // 当月第一天 00:00:00
Date lastDayOfMonth = DateUtils.lastDayOfMonth(now);        // 当月最后一天 00:00:00
Date lastDayOfMonthEnd = DateUtils.lastDayOfMonth(now, false); // 当月最后一天 23:59:59

Date firstDayOfYear = DateUtils.firstDayOfYear(now);        // 当年第一天 00:00:00
Date lastDayOfYear = DateUtils.lastDayOfYear(now);          // 当年最后一天 00:00:00
Date lastDayOfYearEnd = DateUtils.lastDayOfYear(now, false);  // 当年最后一天 23:59:59

// 创建自定义日期
Date customDate = DateUtils.ofDate(2023, 1, 15, 14, 30, 0, 0);  // 2023-01-15 14:30:00

// 判断日期是否在某个范围内
boolean isWithinRange = DateUtils.isEffectiveDate(
    now, 
    DateUtils.plusDays(now, -5), 
    DateUtils.plusDays(now, 5)
);  // 判断当前日期是否在前后5天范围内

// 日期类型转换
LocalDate localDate = DateUtils.toLoacalDate(now);
LocalDateTime localDateTime = DateUtils.toLoacalDateTime(now);
ZonedDateTime zonedDateTime = DateUtils.toZonedDateTime(now);
ZonedDateTime zonedDateTimeUTC = DateUtils.toZonedDateTime(now, ZoneId.of("UTC"));

// 获取日期序列
List<Date> monthlyDates = DateUtils.getBetweenDateByMonths(
    DateFormatUtil.parseToDate("2023-01-01", DateFormatUtil.DATE_FORMATTER),
    DateFormatUtil.parseToDate("2023-12-31", DateFormatUtil.DATE_FORMATTER),
    1
);  // 获取2023年每月的第一天的列表

List<Date> dailyDates = DateUtils.getBetweenDateByDays(
    DateFormatUtil.parseToDate("2023-01-01", DateFormatUtil.DATE_FORMATTER),
    DateFormatUtil.parseToDate("2023-01-10", DateFormatUtil.DATE_FORMATTER),
    1
);  // 获取2023-01-01至2023-01-10每天的列表

List<Date> nextWeekDays = DateUtils.getNextDates(now, ChronoUnit.DAYS, 7, false);  // 获取未来一周的日期列表(不含今天)
```

## BaseDateDiffUtil - 日期计算基础工具

BaseDateDiffUtil是其他日期工具类的基类，提供了基础的日期时间操作功能，主要负责内部实现，通常不直接使用。

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| commonPlus(Temporal temporal, int years, int months, int days, int hours, long minutes, long seconds, long milliseconds) | 为日期时间添加指定的年、月、日、时、分、秒、毫秒 | temporal: 日期时间<br>years: 年数<br>months: 月数<br>days: 天数<br>hours: 小时数<br>minutes: 分钟数<br>seconds: 秒数<br>milliseconds: 毫秒数 | Temporal: 计算后的日期时间 |
| commondDiff(Date start, Date end, TimeUnit timeUnit) | 计算两个日期之间的时间差 | start: 起始日期<br>end: 结束日期<br>timeUnit: 时间单位 | long: 时间差 |
| yearTemporal(Temporal temporal) | 获取日期时间的年 | temporal: 日期时间 | int: 年数 |
| monthTemporal(Temporal temporal) | 获取日期时间的月 | temporal: 日期时间 | int: 月数 |
| dayTemporal(Temporal temporal) | 获取日期时间的日 | temporal: 日期时间 | int: 日数 |
| hourTemporal(Temporal temporal) | 获取日期时间的时 | temporal: 日期时间 | int: 时数 |
| minuteTemporal(Temporal temporal) | 获取日期时间的分 | temporal: 日期时间 | int: 分数 |
| secondTemporal(Temporal temporal) | 获取日期时间的秒 | temporal: 日期时间 | int: 秒数 |
| millisecondTemporal(Temporal temporal) | 获取日期时间的毫秒 | temporal: 日期时间 | int: 毫秒数 |
| morning(LocalDate localDate) | 获取当天的起始时间(00:00:00) | localDate: 日期 | LocalDateTime: 当天的起始时间 |
| evening(LocalDate localDate) | 获取当天的结束时间(23:59:59) | localDate: 日期 | LocalDateTime: 当天的结束时间 |

### 说明
BaseDateDiffUtil主要为内部使用而设计，作为日期工具类的基础，通常不会直接调用。其他日期工具类(如DateUtils、LocalDateUtils、LocalDateTimeUtils和ZoneDateTimeUtils)都继承自这个基类，扩展了更多实用功能。

### 注意事项

- **内部使用**: 此类主要作为其他日期工具类的基类，通常不直接调用
- **通用性**: 设计用于处理多种日期时间类型，使用Java 8时间API的Temporal接口
- **扩展性**: 如需扩展其他日期计算功能，可以继承此类
- **异常处理**: 处理不兼容的时间单位或操作时会抛出异常
- **类型安全**: 传入与返回值类型必须匹配，否则可能发生ClassCastException

## LocalDateTimeUtils - 本地日期时间工具类

LocalDateTimeUtils提供对Java 8中引入的LocalDateTime类的操作，包括日期计算、比较、转换等功能。

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| plus(LocalDateTime localDateTime, int years, int months, int days, int hours, long minutes, long seconds, long milliseconds) | 日期时间加上指定的年、月、日、时、分、秒、毫秒 | localDateTime: 原始日期时间<br>years: 年数<br>months: 月数<br>days: 天数<br>hours: 小时数<br>minutes: 分钟数<br>seconds: 秒数<br>milliseconds: 毫秒数 | LocalDateTime: 计算后的日期时间 |
| diff(LocalDateTime start, LocalDateTime end, TimeUnit timeUnit) | 计算两个日期时间之间的时间差 | start: 起始日期时间<br>end: 结束日期时间<br>timeUnit: 时间单位 | long: 时间差 |
| plusYears(LocalDateTime localDateTime, int years) | 日期时间加上指定年数 | localDateTime: 原始日期时间<br>years: 要增加的年数 | LocalDateTime: 计算后的日期时间 |
| plusMonths(LocalDateTime localDateTime, int months) | 日期时间加上指定月数 | localDateTime: 原始日期时间<br>months: 要增加的月数 | LocalDateTime: 计算后的日期时间 |
| plusDays(LocalDateTime localDateTime, int days) | 日期时间加上指定天数 | localDateTime: 原始日期时间<br>days: 要增加的天数 | LocalDateTime: 计算后的日期时间 |
| plusHours(LocalDateTime localDateTime, int hours) | 日期时间加上指定小时数 | localDateTime: 原始日期时间<br>hours: 要增加的小时数 | LocalDateTime: 计算后的日期时间 |
| plusMinutes(LocalDateTime localDateTime, long minutes) | 日期时间加上指定分钟数 | localDateTime: 原始日期时间<br>minutes: 要增加的分钟数 | LocalDateTime: 计算后的日期时间 |
| plusSeconds(LocalDateTime localDateTime, long seconds) | 日期时间加上指定秒数 | localDateTime: 原始日期时间<br>seconds: 要增加的秒数 | LocalDateTime: 计算后的日期时间 |
| plusMilliseconds(LocalDateTime localDateTime, long milliseconds) | 日期时间加上指定毫秒数 | localDateTime: 原始日期时间<br>milliseconds: 要增加的毫秒数 | LocalDateTime: 计算后的日期时间 |
| year(LocalDateTime localDateTime) | 获取日期时间的年 | localDateTime: 日期时间 | int: 年数 |
| month(LocalDateTime localDateTime) | 获取日期时间的月 | localDateTime: 日期时间 | int: 月数 |
| day(LocalDateTime localDateTime) | 获取日期时间的日 | localDateTime: 日期时间 | int: 日数 |
| hour(LocalDateTime localDateTime) | 获取日期时间的时 | localDateTime: 日期时间 | int: 时数 |
| minute(LocalDateTime localDateTime) | 获取日期时间的分 | localDateTime: 日期时间 | int: 分数 |
| second(LocalDateTime localDateTime) | 获取日期时间的秒 | localDateTime: 日期时间 | int: 秒数 |
| millisecond(LocalDateTime localDateTime) | 获取日期时间的毫秒 | localDateTime: 日期时间 | int: 毫秒数 |
| starOfDay(LocalDateTime localDateTime) | 获取当天的起始时间(00:00:00) | localDateTime: 日期时间 | LocalDateTime: 当天的起始时间 |
| endOfDay(LocalDateTime localDateTime) | 获取当天的结束时间(23:59:59) | localDateTime: 日期时间 | LocalDateTime: 当天的结束时间 |
| firstDayOfYear(LocalDateTime localDateTime) | 获取年份的第一天 | localDateTime: 日期时间 | LocalDateTime: 年份的第一天(00:00:00) |
| firstDayOfYear(LocalDateTime localDateTime, boolean isMorning) | 获取年份的第一天 | localDateTime: 日期时间<br>isMorning: 是否为晨时间(true=00:00:00, false=23:59:59) | LocalDateTime: 年份的第一天 |
| lastDayOfYear(LocalDateTime localDateTime) | 获取年份的最后一天 | localDateTime: 日期时间 | LocalDateTime: 年份的最后一天(00:00:00) |
| lastDayOfYear(LocalDateTime localDateTime, boolean isMorning) | 获取年份的最后一天 | localDateTime: 日期时间<br>isMorning: 是否为晨时间(true=00:00:00, false=23:59:59) | LocalDateTime: 年份的最后一天 |
| firstDayOfMonth(LocalDateTime localDateTime) | 获取月份的第一天 | localDateTime: 日期时间 | LocalDateTime: 月份的第一天(00:00:00) |
| firstDayOfMonth(LocalDateTime localDateTime, boolean isMorning) | 获取月份的第一天 | localDateTime: 日期时间<br>isMorning: 是否为晨时间(true=00:00:00, false=23:59:59) | LocalDateTime: 月份的第一天 |
| lastDayOfMonth(LocalDateTime localDateTime) | 获取月份的最后一天 | localDateTime: 日期时间 | LocalDateTime: 月份的最后一天(00:00:00) |
| lastDayOfMonth(LocalDateTime localDateTime, boolean isMorning) | 获取月份的最后一天 | localDateTime: 日期时间<br>isMorning: 是否为晨时间(true=00:00:00, false=23:59:59) | LocalDateTime: 月份的最后一天 |
| ofLocalDateTime(int years, int months, int days, int hours, int minutes, int seconds, int milliseconds) | 创建一个日期时间 | years: 年<br>months: 月<br>days: 日<br>hours: 时<br>minutes: 分<br>seconds: 秒<br>milliseconds: 毫秒 | LocalDateTime: 创建的日期时间 |
| isEffectiveDate(LocalDateTime now, LocalDateTime start, LocalDateTime end) | 判断日期时间是否在指定范围内 | now: 待判断的日期时间<br>start: 开始日期时间<br>end: 结束日期时间 | boolean: 是否在范围内 |
| toDate(LocalDateTime localDateTime) | 将LocalDateTime转换为Date | localDateTime: 本地日期时间 | Date: 日期 |
| toDate(LocalDateTime localDateTime, ZoneId zoneId) | 将LocalDateTime转换为指定时区的Date | localDateTime: 本地日期时间<br>zoneId: 时区 | Date: 日期 |
| toLocalDate(LocalDateTime localDateTime) | 将LocalDateTime转换为LocalDate | localDateTime: 本地日期时间 | LocalDate: 本地日期 |
| toZonedDateTime(LocalDateTime localDateTime) | 将LocalDateTime转换为ZonedDateTime | localDateTime: 本地日期时间 | ZonedDateTime: 带时区的日期时间 |
| toZonedDateTime(LocalDateTime localDateTime, ZoneId zoneId) | 将LocalDateTime转换为指定时区的ZonedDateTime | localDateTime: 本地日期时间<br>zoneId: 时区 | ZonedDateTime: 带时区的日期时间 |
| getBetweenDateByYear(LocalDateTime start, LocalDateTime end, int val) | 获取两个日期时间之间按年间隔的日期时间列表 | start: 起始日期时间<br>end: 结束日期时间<br>val: 年间隔 | List<LocalDateTime>: 日期时间列表 |
| getBetweenDateByQuarter(LocalDateTime start, LocalDateTime end, int val) | 获取两个日期时间之间按季度间隔的日期时间列表 | start: 起始日期时间<br>end: 结束日期时间<br>val: 季度间隔 | List<LocalDateTime>: 日期时间列表 |
| getBetweenDateByMonths(LocalDateTime start, LocalDateTime end, int val) | 获取两个日期时间之间按月间隔的日期时间列表 | start: 起始日期时间<br>end: 结束日期时间<br>val: 月间隔 | List<LocalDateTime>: 日期时间列表 |
| getBetweenDateByDays(LocalDateTime start, LocalDateTime end, int val) | 获取两个日期时间之间按天间隔的日期时间列表 | start: 起始日期时间<br>end: 结束日期时间<br>val: 天间隔 | List<LocalDateTime>: 日期时间列表 |
| getBetweenDateByHours(LocalDateTime start, LocalDateTime end, int val) | 获取两个日期时间之间按小时间隔的日期时间列表 | start: 起始日期时间<br>end: 结束日期时间<br>val: 小时间隔 | List<LocalDateTime>: 日期时间列表 |
| getBetweenDateByMinutes(LocalDateTime start, LocalDateTime end, int val) | 获取两个日期时间之间按分钟间隔的日期时间列表 | start: 起始日期时间<br>end: 结束日期时间<br>val: 分钟间隔 | List<LocalDateTime>: 日期时间列表 |
| getBetweenDate(LocalDateTime start, LocalDateTime end, ChronoUnit field, int val) | 获取两个日期时间之间按指定时间单位间隔的日期时间列表 | start: 起始日期时间<br>end: 结束日期时间<br>field: 时间单位<br>val: 间隔值 | List<LocalDateTime>: 日期时间列表 |
| getBetweenDate(LocalDateTime start, LocalDateTime end, ChronoUnit field, int val, boolean isIncludeStart, boolean isIncludeEnd) | 获取两个日期时间之间按指定时间单位间隔的日期时间列表 | start: 起始日期时间<br>end: 结束日期时间<br>field: 时间单位<br>val: 间隔值<br>isIncludeStart: 是否包含起始日期时间<br>isIncludeEnd: 是否包含结束日期时间 | List<LocalDateTime>: 日期时间列表 |
| getAfterDates(LocalDateTime start, ChronoUnit field, int val) | 获取指定日期时间之后的日期时间列表 | start: 起始日期时间<br>field: 时间单位<br>val: 间隔值 | List<LocalDateTime>: 日期时间列表 |
| getBeforeDates(LocalDateTime start, ChronoUnit field, int val) | 获取指定日期时间之前的日期时间列表 | start: 起始日期时间<br>field: 时间单位<br>val: 间隔值 | List<LocalDateTime>: 日期时间列表 |
| getNextDates(LocalDateTime start, ChronoUnit field, int val, boolean isIncludeStart) | 获取指定日期时间之后的日期时间列表 | start: 起始日期时间<br>field: 时间单位<br>val: 间隔值<br>isIncludeStart: 是否包含起始日期时间 | List<LocalDateTime>: 日期时间列表 |

### 注意事项

- **无时区信息**: LocalDateTime不包含时区信息，不能表示特定时区的瞬时时间点
- **类型转换**: 转换为Date或ZonedDateTime时，需注意时区设置
- **日期计算**: 处理跨月、跨年计算时注意月份天数变化
- **时间点比较**: 跨时区比较时需先转换为相同时区再比较
- **序列化**: 在序列化时确保接收方使用相同的Java版本或兼容库
- **与Date区别**: 相比传统Date，LocalDateTime是不可变且线程安全的
- **精度**: LocalDateTime支持纳秒级精度，但转换为Date时会丢失纳秒信息
- **计算效率**: 时间计算比传统Date更高效，但转换操作会带来额外开销

### 使用示例

```java
// 日期时间加减
LocalDateTime now = LocalDateTime.now();
LocalDateTime nextYear = LocalDateTimeUtils.plusYears(now, 1);
LocalDateTime nextMonth = LocalDateTimeUtils.plusMonths(now, 1);
LocalDateTime tomorrow = LocalDateTimeUtils.plusDays(now, 1);
LocalDateTime nextHour = LocalDateTimeUtils.plusHours(now, 1);
LocalDateTime nextMinute = LocalDateTimeUtils.plusMinutes(now, 1);
LocalDateTime nextSecond = LocalDateTimeUtils.plusSeconds(now, 1);
LocalDateTime nextMs = LocalDateTimeUtils.plusMilliseconds(now, 1);

// 日期时间差值计算
LocalDateTime start = LocalDateTime.of(2023, 1, 1, 0, 0);
LocalDateTime end = LocalDateTime.of(2023, 1, 10, 0, 0);
long daysDiff = LocalDateTimeUtils.diff(start, end, TimeUnit.DAYS);  // 返回9

// 日期时间属性获取
int year = LocalDateTimeUtils.year(now);       // 获取年份
int month = LocalDateTimeUtils.month(now);     // 获取月份(1-12)
int day = LocalDateTimeUtils.day(now);         // 获取日期(1-31)
int hour = LocalDateTimeUtils.hour(now);       // 获取小时(0-23)
int minute = LocalDateTimeUtils.minute(now);   // 获取分钟(0-59)
int second = LocalDateTimeUtils.second(now);   // 获取秒(0-59)
int ms = LocalDateTimeUtils.millisecond(now);  // 获取毫秒(0-999)

// 获取日期时间的起始和结束时间
LocalDateTime startOfDay = LocalDateTimeUtils.starOfDay(now);     // 当天00:00:00
LocalDateTime endOfDay = LocalDateTimeUtils.endOfDay(now);        // 当天23:59:59

// 获取月份/年份的第一天和最后一天
LocalDateTime firstDayOfMonth = LocalDateTimeUtils.firstDayOfMonth(now);      // 当月第一天 00:00:00
LocalDateTime lastDayOfMonth = LocalDateTimeUtils.lastDayOfMonth(now);        // 当月最后一天 00:00:00
LocalDateTime lastDayOfMonthEnd = LocalDateTimeUtils.lastDayOfMonth(now, false); // 当月最后一天 23:59:59

LocalDateTime firstDayOfYear = LocalDateTimeUtils.firstDayOfYear(now);        // 当年第一天 00:00:00
LocalDateTime lastDayOfYear = LocalDateTimeUtils.lastDayOfYear(now);          // 当年最后一天 00:00:00
LocalDateTime lastDayOfYearEnd = LocalDateTimeUtils.lastDayOfYear(now, false);  // 当年最后一天 23:59:59

// 创建自定义日期时间
LocalDateTime customDateTime = LocalDateTimeUtils.ofLocalDateTime(2023, 1, 15, 14, 30, 0, 0);  // 2023-01-15 14:30:00

// 判断日期时间是否在某个范围内
boolean isWithinRange = LocalDateTimeUtils.isEffectiveDate(
    now, 
    LocalDateTimeUtils.plusDays(now, -5), 
    LocalDateTimeUtils.plusDays(now, 5)
);  // 判断当前日期时间是否在前后5天范围内

// 日期时间类型转换
Date date = LocalDateTimeUtils.toDate(now);
Date dateUTC = LocalDateTimeUtils.toDate(now, ZoneId.of("UTC"));
LocalDate localDate = LocalDateTimeUtils.toLocalDate(now);
ZonedDateTime zonedDateTime = LocalDateTimeUtils.toZonedDateTime(now);
ZonedDateTime zonedDateTimeUTC = LocalDateTimeUtils.toZonedDateTime(now, ZoneId.of("UTC"));

// 获取日期时间序列
List<LocalDateTime> monthlyDates = LocalDateTimeUtils.getBetweenDateByMonths(
    LocalDateTime.of(2023, 1, 1, 0, 0),
    LocalDateTime.of(2023, 12, 31, 0, 0),
    1
);  // 获取2023年每月的第一天的列表

List<LocalDateTime> hourlyDates = LocalDateTimeUtils.getBetweenDateByHours(
    LocalDateTime.of(2023, 1, 1, 0, 0),
    LocalDateTime.of(2023, 1, 1, 23, 0),
    1
);  // 获取2023-01-01每小时的列表

List<LocalDateTime> nextWeekDates = LocalDateTimeUtils.getNextDates(now, ChronoUnit.DAYS, 7, false);  // 获取未来一周的日期时间列表(不含现在)
```

## LocalDateUtils - 本地日期工具类

LocalDateUtils提供对Java 8中引入的LocalDate类的操作，包括日期计算、比较、转换等功能。

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| plus(LocalDate localDate, int years, int months, int days) | 日期加上指定的年、月、日 | localDate: 原始日期<br>years: 年数<br>months: 月数<br>days: 天数 | LocalDate: 计算后的日期 |
| diff(LocalDate start, LocalDate end, TimeUnit timeUnit) | 计算两个日期之间的时间差 | start: 起始日期<br>end: 结束日期<br>timeUnit: 时间单位 | long: 时间差 |
| plusYears(LocalDate localDate, int years) | 日期加上指定年数 | localDate: 原始日期<br>years: 要增加的年数 | LocalDate: 计算后的日期 |
| plusMonths(LocalDate localDate, int months) | 日期加上指定月数 | localDate: 原始日期<br>months: 要增加的月数 | LocalDate: 计算后的日期 |
| plusDays(LocalDate localDate, int days) | 日期加上指定天数 | localDate: 原始日期<br>days: 要增加的天数 | LocalDate: 计算后的日期 |
| firstDayOfYear(LocalDate localDate) | 获取年份的第一天 | localDate: 日期 | LocalDate: 年份的第一天 |
| lastDayOfYear(LocalDate localDate) | 获取年份的最后一天 | localDate: 日期 | LocalDate: 年份的最后一天 |
| firstDayOfMonth(LocalDate localDate) | 获取月份的第一天 | localDate: 日期 | LocalDate: 月份的第一天 |
| lastDayOfMonth(LocalDate localDate) | 获取月份的最后一天 | localDate: 日期 | LocalDate: 月份的最后一天 |
| year(LocalDate localDate) | 获取日期的年 | localDate: 日期 | int: 年数 |
| month(LocalDate localDate) | 获取日期的月 | localDate: 日期 | int: 月数 |
| day(LocalDate localDate) | 获取日期的日 | localDate: 日期 | int: 日数 |
| ofLocalDate(int years, int months, int days) | 创建一个日期 | years: 年<br>months: 月<br>days: 日 | LocalDate: 创建的日期 |
| isEffectiveDate(LocalDate now, LocalDate start, LocalDate end) | 判断日期是否在指定范围内 | now: 待判断的日期<br>start: 开始日期<br>end: 结束日期 | boolean: 是否在范围内 |
| toDate(LocalDate localDate) | 将LocalDate转换为Date | localDate: 本地日期 | Date: 日期 |
| toDate(LocalDate localDate, ZoneId zoneId) | 将LocalDate转换为指定时区的Date | localDate: 本地日期<br>zoneId: 时区 | Date: 日期 |
| toLoacalDateTime(LocalDate localDate) | 将LocalDate转换为LocalDateTime | localDate: 本地日期 | LocalDateTime: 本地日期时间(00:00:00) |
| toZonedDateTime(LocalDate localDate) | 将LocalDate转换为ZonedDateTime | localDate: 本地日期 | ZonedDateTime: 带时区的日期时间 |
| toZonedDateTime(LocalDate localDate, ZoneId zoneId) | 将LocalDate转换为指定时区的ZonedDateTime | localDate: 本地日期<br>zoneId: 时区 | ZonedDateTime: 带时区的日期时间 |
| getBetweenDateByYear(LocalDate start, LocalDate end, int val) | 获取两个日期之间按年间隔的日期列表 | start: 起始日期<br>end: 结束日期<br>val: 年间隔 | List<LocalDate>: 日期列表 |
| getBetweenDateByQuarter(LocalDate start, LocalDate end, int val) | 获取两个日期之间按季度间隔的日期列表 | start: 起始日期<br>end: 结束日期<br>val: 季度间隔 | List<LocalDate>: 日期列表 |
| getBetweenDateByMonths(LocalDate start, LocalDate end, int val) | 获取两个日期之间按月间隔的日期列表 | start: 起始日期<br>end: 结束日期<br>val: 月间隔 | List<LocalDate>: 日期列表 |
| getBetweenDateByDays(LocalDate start, LocalDate end, int val) | 获取两个日期之间按天间隔的日期列表 | start: 起始日期<br>end: 结束日期<br>val: 天间隔 | List<LocalDate>: 日期列表 |
| getBetweenDate(LocalDate start, LocalDate end, ChronoUnit field, int val) | 获取两个日期之间按指定时间单位间隔的日期列表 | start: 起始日期<br>end: 结束日期<br>field: 时间单位<br>val: 间隔值 | List<LocalDate>: 日期列表 |
| getBetweenDate(LocalDate start, LocalDate end, ChronoUnit field, int val, boolean isIncludeStart, boolean isIncludeEnd) | 获取两个日期之间按指定时间单位间隔的日期列表 | start: 起始日期<br>end: 结束日期<br>field: 时间单位<br>val: 间隔值<br>isIncludeStart: 是否包含起始日期<br>isIncludeEnd: 是否包含结束日期 | List<LocalDate>: 日期列表 |
| getAfterDates(LocalDate start, ChronoUnit field, int val) | 获取指定日期之后的日期列表 | start: 起始日期<br>field: 时间单位<br>val: 间隔值 | List<LocalDate>: 日期列表 |
| getBeforeDates(LocalDate start, ChronoUnit field, int val) | 获取指定日期之前的日期列表 | start: 起始日期<br>field: 时间单位<br>val: 间隔值 | List<LocalDate>: 日期列表 |
| getNextDates(LocalDate start, ChronoUnit field, int val, boolean isIncludeStart) | 获取指定日期之后的日期列表 | start: 起始日期<br>field: 时间单位<br>val: 间隔值<br>isIncludeStart: 是否包含起始日期 | List<LocalDate>: 日期列表 |

### 注意事项

- **仅日期**: LocalDate只包含日期信息(年月日)，不含时间和时区
- **日期操作**: 月份和日期操作时需注意月末日期问题(如2月29日)
- **转换至DateTime**: 转换为包含时间的类型时，会设置时间为当日起始(00:00:00)
- **与Date互转**: 转换为传统Date时，默认设置时间为当日起始(00:00:00)
- **不可变性**: LocalDate对象是不可变的，所有修改操作都会返回新实例
- **线程安全**: 因不可变性，LocalDate天然线程安全
- **与字符串转换**: 使用DateFormatUtil进行格式化和解析，避免直接使用toString()
- **边界计算**: 使用firstDayOfMonth和lastDayOfMonth确保正确获取月份边界

### 使用示例

```java
// 日期加减
LocalDate today = LocalDate.now();
LocalDate nextYear = LocalDateUtils.plusYears(today, 1);
LocalDate nextMonth = LocalDateUtils.plusMonths(today, 1);
LocalDate tomorrow = LocalDateUtils.plusDays(today, 1);
LocalDate customDate = LocalDateUtils.plus(today, 1, 2, 3);  // 增加1年2月3天

// 日期差值计算
LocalDate start = LocalDate.of(2023, 1, 1);
LocalDate end = LocalDate.of(2023, 1, 10);
long daysDiff = LocalDateUtils.diff(start, end, TimeUnit.DAYS);  // 返回9

// 日期属性获取
int year = LocalDateUtils.year(today);  // 获取年份
int month = LocalDateUtils.month(today);  // 获取月份(1-12)
int day = LocalDateUtils.day(today);  // 获取日期(1-31)

// 获取月份/年份的第一天和最后一天
LocalDate firstDayOfMonth = LocalDateUtils.firstDayOfMonth(today);  // 当月第一天
LocalDate lastDayOfMonth = LocalDateUtils.lastDayOfMonth(today);  // 当月最后一天

LocalDate firstDayOfYear = LocalDateUtils.firstDayOfYear(today);  // 当年第一天
LocalDate lastDayOfYear = LocalDateUtils.lastDayOfYear(today);  // 当年最后一天

// 创建自定义日期
LocalDate customDate2 = LocalDateUtils.ofLocalDate(2023, 1, 15);  // 2023-01-15

// 判断日期是否在某个范围内
boolean isWithinRange = LocalDateUtils.isEffectiveDate(
    today, 
    LocalDateUtils.plusDays(today, -5), 
    LocalDateUtils.plusDays(today, 5)
);  // 判断当前日期是否在前后5天范围内

// 日期类型转换
Date date = LocalDateUtils.toDate(today);
Date dateUTC = LocalDateUtils.toDate(today, ZoneId.of("UTC"));
LocalDateTime localDateTime = LocalDateUtils.toLoacalDateTime(today);  // 转为当天00:00:00的LocalDateTime
ZonedDateTime zonedDateTime = LocalDateUtils.toZonedDateTime(today);
ZonedDateTime zonedDateTimeUTC = LocalDateUtils.toZonedDateTime(today, ZoneId.of("UTC"));

// 获取日期序列
List<LocalDate> monthlyDates = LocalDateUtils.getBetweenDateByMonths(
    LocalDate.of(2023, 1, 1),
    LocalDate.of(2023, 12, 31),
    1
);  // 获取2023年每月的第一天的列表

List<LocalDate> dailyDates = LocalDateUtils.getBetweenDateByDays(
    LocalDate.of(2023, 1, 1),
    LocalDate.of(2023, 1, 10),
    1
);  // 获取2023-01-01至2023-01-10每天的列表

List<LocalDate> nextWeekDays = LocalDateUtils.getNextDates(today, ChronoUnit.DAYS, 7, false);  // 获取未来一周的日期列表(不含今天)
```

## ZoneDateTimeUtils - 带时区的日期时间工具类

ZoneDateTimeUtils提供对Java 8中引入的ZonedDateTime类的操作，包括日期计算、比较、转换等功能，特别适合处理跨时区的日期时间。

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| plus(ZonedDateTime zonedDateTime, int years, int month, int days, int hours, long minutes, long seconds, long milliseconds) | 日期时间加上指定的年、月、日、时、分、秒、毫秒 | zonedDateTime: 原始日期时间<br>years: 年数<br>month: 月数<br>days: 天数<br>hours: 小时数<br>minutes: 分钟数<br>seconds: 秒数<br>milliseconds: 毫秒数 | ZonedDateTime: 计算后的日期时间 |
| diff(ZonedDateTime start, ZonedDateTime end, TimeUnit timeUnit) | 计算两个日期时间之间的时间差 | start: 起始日期时间<br>end: 结束日期时间<br>timeUnit: 时间单位 | long: 时间差 |
| plusYears(ZonedDateTime zonedDateTime, int years) | 日期时间加上指定年数 | zonedDateTime: 原始日期时间<br>years: 要增加的年数 | ZonedDateTime: 计算后的日期时间 |
| plusMonths(ZonedDateTime zonedDateTime, int months) | 日期时间加上指定月数 | zonedDateTime: 原始日期时间<br>months: 要增加的月数 | ZonedDateTime: 计算后的日期时间 |
| plusDays(ZonedDateTime zonedDateTime, int days) | 日期时间加上指定天数 | zonedDateTime: 原始日期时间<br>days: 要增加的天数 | ZonedDateTime: 计算后的日期时间 |
| plusHours(ZonedDateTime zonedDateTime, int hours) | 日期时间加上指定小时数 | zonedDateTime: 原始日期时间<br>hours: 要增加的小时数 | ZonedDateTime: 计算后的日期时间 |
| plusMinutes(ZonedDateTime zonedDateTime, long minutes) | 日期时间加上指定分钟数 | zonedDateTime: 原始日期时间<br>minutes: 要增加的分钟数 | ZonedDateTime: 计算后的日期时间 |
| plusSeconds(ZonedDateTime zonedDateTime, long seconds) | 日期时间加上指定秒数 | zonedDateTime: 原始日期时间<br>seconds: 要增加的秒数 | ZonedDateTime: 计算后的日期时间 |
| plusMilliseconds(ZonedDateTime zonedDateTime, long milliseconds) | 日期时间加上指定毫秒数 | zonedDateTime: 原始日期时间<br>milliseconds: 要增加的毫秒数 | ZonedDateTime: 计算后的日期时间 |
| starOfDay(ZonedDateTime zonedDateTime) | 获取当天的起始时间(00:00:00) | zonedDateTime: 日期时间 | ZonedDateTime: 当天的起始时间 |
| endOfDay(ZonedDateTime zonedDateTime) | 获取当天的结束时间(23:59:59) | zonedDateTime: 日期时间 | ZonedDateTime: 当天的结束时间 |
| firstDayOfYear(ZonedDateTime zonedDateTime) | 获取年份的第一天 | zonedDateTime: 日期时间 | ZonedDateTime: 年份的第一天(00:00:00) |
| firstDayOfYear(ZonedDateTime zonedDateTime, boolean isMorning) | 获取年份的第一天 | zonedDateTime: 日期时间<br>isMorning: 是否为晨时间(true=00:00:00, false=23:59:59) | ZonedDateTime: 年份的第一天 |
| lastDayOfYear(ZonedDateTime zonedDateTime) | 获取年份的最后一天 | zonedDateTime: 日期时间 | ZonedDateTime: 年份的最后一天(00:00:00) |
| lastDayOfYear(ZonedDateTime zonedDateTime, boolean isMorning) | 获取年份的最后一天 | zonedDateTime: 日期时间<br>isMorning: 是否为晨时间(true=00:00:00, false=23:59:59) | ZonedDateTime: 年份的最后一天 |
| firstDayOfMonth(ZonedDateTime zonedDateTime) | 获取月份的第一天 | zonedDateTime: 日期时间 | ZonedDateTime: 月份的第一天(00:00:00) |
| firstDayOfMonth(ZonedDateTime zonedDateTime, boolean isMorning) | 获取月份的第一天 | zonedDateTime: 日期时间<br>isMorning: 是否为晨时间(true=00:00:00, false=23:59:59) | ZonedDateTime: 月份的第一天 |
| lastDayOfMonth(ZonedDateTime zonedDateTime) | 获取月份的最后一天 | zonedDateTime: 日期时间 | ZonedDateTime: 月份的最后一天(00:00:00) |
| lastDayOfMonth(ZonedDateTime zonedDateTime, boolean isMorning) | 获取月份的最后一天 | zonedDateTime: 日期时间<br>isMorning: 是否为晨时间(true=00:00:00, false=23:59:59) | ZonedDateTime: 月份的最后一天 |
| year(ZonedDateTime zonedDateTime) | 获取日期时间的年 | zonedDateTime: 日期时间 | int: 年数 |
| month(ZonedDateTime zonedDateTime) | 获取日期时间的月 | zonedDateTime: 日期时间 | int: 月数 |
| day(ZonedDateTime zonedDateTime) | 获取日期时间的日 | zonedDateTime: 日期时间 | int: 日数 |
| hour(ZonedDateTime zonedDateTime) | 获取日期时间的时 | zonedDateTime: 日期时间 | int: 时数 |
| minute(ZonedDateTime zonedDateTime) | 获取日期时间的分 | zonedDateTime: 日期时间 | int: 分数 |
| second(ZonedDateTime zonedDateTime) | 获取日期时间的秒 | zonedDateTime: 日期时间 | int: 秒数 |
| millisecond(ZonedDateTime zonedDateTime) | 获取日期时间的毫秒 | zonedDateTime: 日期时间 | int: 毫秒数 |
| ofZonedDateTime(int years, int months, int days, int hours, int minutes, int seconds, int milliseconds) | 创建一个带系统默认时区的日期时间 | years: 年<br>months: 月<br>days: 日<br>hours: 时<br>minutes: 分<br>seconds: 秒<br>milliseconds: 毫秒 | ZonedDateTime: 创建的日期时间 |
| isEffectiveDate(ZonedDateTime now, ZonedDateTime start, ZonedDateTime end) | 判断日期时间是否在指定范围内 | now: 待判断的日期时间<br>start: 开始日期时间<br>end: 结束日期时间 | boolean: 是否在范围内 |
| toDate(ZonedDateTime zonedDateTime) | 将ZonedDateTime转换为Date | zonedDateTime: 带时区的日期时间 | Date: 日期 |
| toDate(ZonedDateTime zonedDateTime, ZoneId zoneId) | 将ZonedDateTime转换为指定时区的Date | zonedDateTime: 带时区的日期时间<br>zoneId: 时区 | Date: 日期 |
| toLoacalDateTime(ZonedDateTime zonedDateTime) | 将ZonedDateTime转换为LocalDateTime | zonedDateTime: 带时区的日期时间 | LocalDateTime: 本地日期时间 |
| toLoacalDate(ZonedDateTime zonedDateTime) | 将ZonedDateTime转换为LocalDate | zonedDateTime: 带时区的日期时间 | LocalDate: 本地日期 |
| getBetweenDateByYear(ZonedDateTime start, ZonedDateTime end, int val) | 获取两个日期时间之间按年间隔的日期时间列表 | start: 起始日期时间<br>end: 结束日期时间<br>val: 年间隔 | List<ZonedDateTime>: 日期时间列表 |
| getBetweenDateByQuarter(ZonedDateTime start, ZonedDateTime end, int val) | 获取两个日期时间之间按季度间隔的日期时间列表 | start: 起始日期时间<br>end: 结束日期时间<br>val: 季度间隔 | List<ZonedDateTime>: 日期时间列表 |
| getBetweenDateByMonths(ZonedDateTime start, ZonedDateTime end, int val) | 获取两个日期时间之间按月间隔的日期时间列表 | start: 起始日期时间<br>end: 结束日期时间<br>val: 月间隔 | List<ZonedDateTime>: 日期时间列表 |
| getBetweenDateByDays(ZonedDateTime start, ZonedDateTime end, int val) | 获取两个日期时间之间按天间隔的日期时间列表 | start: 起始日期时间<br>end: 结束日期时间<br>val: 天间隔 | List<ZonedDateTime>: 日期时间列表 |
| getBetweenDateByHours(ZonedDateTime start, ZonedDateTime end, int val) | 获取两个日期时间之间按小时间隔的日期时间列表 | start: 起始日期时间<br>end: 结束日期时间<br>val: 小时间隔 | List<ZonedDateTime>: 日期时间列表 |
| getBetweenDateByMinutes(ZonedDateTime start, ZonedDateTime end, int val) | 获取两个日期时间之间按分钟间隔的日期时间列表 | start: 起始日期时间<br>end: 结束日期时间<br>val: 分钟间隔 | List<ZonedDateTime>: 日期时间列表 |
| getBetweenDate(ZonedDateTime start, ZonedDateTime end, ChronoUnit field, int val) | 获取两个日期时间之间按指定时间单位间隔的日期时间列表 | start: 起始日期时间<br>end: 结束日期时间<br>field: 时间单位<br>val: 间隔值 | List<ZonedDateTime>: 日期时间列表 |
| getBetweenDate(ZonedDateTime start, ZonedDateTime end, ChronoUnit field, int val, boolean isIncludeStart, boolean isIncludeEnd) | 获取两个日期时间之间按指定时间单位间隔的日期时间列表 | start: 起始日期时间<br>end: 结束日期时间<br>field: 时间单位<br>val: 间隔值<br>isIncludeStart: 是否包含起始日期时间<br>isIncludeEnd: 是否包含结束日期时间 | List<ZonedDateTime>: 日期时间列表 |
| getAfterDates(ZonedDateTime start, ChronoUnit field, int val) | 获取指定日期时间之后的日期时间列表 | start: 起始日期时间<br>field: 时间单位<br>val: 间隔值 | List<ZonedDateTime>: 日期时间列表 |
| getBeforeDates(ZonedDateTime start, ChronoUnit field, int val) | 获取指定日期时间之前的日期时间列表 | start: 起始日期时间<br>field: 时间单位<br>val: 间隔值 | List<ZonedDateTime>: 日期时间列表 |
| getNextDates(ZonedDateTime start, ChronoUnit field, int val, boolean isIncludeStart) | 获取指定日期时间之后的日期时间列表 | start: 起始日期时间<br>field: 时间单位<br>val: 间隔值<br>isIncludeStart: 是否包含起始日期时间 | List<ZonedDateTime>: 日期时间列表 |

### 注意事项

- **时区处理**: 明确指定操作的时区，避免依赖系统默认时区
- **时区转换**: 在不同时区间转换时，需注意时间点保持不变而显示时间会改变
- **夏令时**: 在夏令时切换期间进行加减操作可能出现异常
- **时间点识别**: ZonedDateTime能精确标识全球时间点，适合跨时区业务
- **与其他类型转换**: 转换为不带时区的类型时会丢失时区信息
- **序列化考虑**: 在网络传输或持久化时需考虑时区信息的保留
- **UTC操作**: 对于全球化应用，建议统一使用UTC时区进行存储和计算
- **格式化**: 格式化字符串时需决定是否包含时区信息

### 使用示例

```java
// 创建带时区的日期时间
ZonedDateTime now = ZonedDateTime.now();
ZonedDateTime nowUTC = ZonedDateTime.now(ZoneId.of("UTC"));
ZonedDateTime custom = ZonedDateTime.of(2023, 1, 15, 14, 30, 0, 0, ZoneId.systemDefault());

// 日期时间加减
ZonedDateTime nextYear = ZoneDateTimeUtils.plusYears(now, 1);
ZonedDateTime nextMonth = ZoneDateTimeUtils.plusMonths(now, 1);
ZonedDateTime tomorrow = ZoneDateTimeUtils.plusDays(now, 1);
ZonedDateTime nextHour = ZoneDateTimeUtils.plusHours(now, 1);
ZonedDateTime nextMinute = ZoneDateTimeUtils.plusMinutes(now, 1);
ZonedDateTime nextSecond = ZoneDateTimeUtils.plusSeconds(now, 1);
ZonedDateTime nextMs = ZoneDateTimeUtils.plusMilliseconds(now, 1);

// 日期时间差值计算
ZonedDateTime start = ZonedDateTime.of(2023, 1, 1, 0, 0, 0, 0, ZoneId.systemDefault());
ZonedDateTime end = ZonedDateTime.of(2023, 1, 10, 0, 0, 0, 0, ZoneId.systemDefault());
long daysDiff = ZoneDateTimeUtils.diff(start, end, TimeUnit.DAYS);  // 返回9

// 日期时间属性获取
int year = ZoneDateTimeUtils.year(now);       // 获取年份
int month = ZoneDateTimeUtils.month(now);     // 获取月份(1-12)
int day = ZoneDateTimeUtils.day(now);         // 获取日期(1-31)
int hour = ZoneDateTimeUtils.hour(now);       // 获取小时(0-23)
int minute = ZoneDateTimeUtils.minute(now);   // 获取分钟(0-59)
int second = ZoneDateTimeUtils.second(now);   // 获取秒(0-59)
int ms = ZoneDateTimeUtils.millisecond(now);  // 获取毫秒(0-999)

// 获取日期时间的起始和结束时间
ZonedDateTime startOfDay = ZoneDateTimeUtils.starOfDay(now);     // 当天00:00:00
ZonedDateTime endOfDay = ZoneDateTimeUtils.endOfDay(now);        // 当天23:59:59

// 获取月份/年份的第一天和最后一天
ZonedDateTime firstDayOfMonth = ZoneDateTimeUtils.firstDayOfMonth(now);      // 当月第一天 00:00:00
ZonedDateTime lastDayOfMonth = ZoneDateTimeUtils.lastDayOfMonth(now);        // 当月最后一天 00:00:00
ZonedDateTime lastDayOfMonthEnd = ZoneDateTimeUtils.lastDayOfMonth(now, false); // 当月最后一天 23:59:59

ZonedDateTime firstDayOfYear = ZoneDateTimeUtils.firstDayOfYear(now);        // 当年第一天 00:00:00
ZonedDateTime lastDayOfYear = ZoneDateTimeUtils.lastDayOfYear(now);          // 当年最后一天 00:00:00
ZonedDateTime lastDayOfYearEnd = ZoneDateTimeUtils.lastDayOfYear(now, false);  // 当年最后一天 23:59:59

// 创建自定义日期时间(默认系统时区)
ZonedDateTime customDateTime = ZoneDateTimeUtils.ofZonedDateTime(2023, 1, 15, 14, 30, 0, 0);  // 2023-01-15 14:30:00

// 判断日期时间是否在某个范围内
boolean isWithinRange = ZoneDateTimeUtils.isEffectiveDate(
    now, 
    ZoneDateTimeUtils.plusDays(now, -5), 
    ZoneDateTimeUtils.plusDays(now, 5)
);  // 判断当前日期时间是否在前后5天范围内

// 日期时间类型转换
Date date = ZoneDateTimeUtils.toDate(now);
Date dateUTC = ZoneDateTimeUtils.toDate(now, ZoneId.of("UTC"));
LocalDate localDate = ZoneDateTimeUtils.toLoacalDate(now);
LocalDateTime localDateTime = ZoneDateTimeUtils.toLoacalDateTime(now);

// 获取日期时间序列
List<ZonedDateTime> monthlyDates = ZoneDateTimeUtils.getBetweenDateByMonths(
    ZonedDateTime.of(2023, 1, 1, 0, 0, 0, 0, ZoneId.systemDefault()),
    ZonedDateTime.of(2023, 12, 31, 0, 0, 0, 0, ZoneId.systemDefault()),
    1
);  // 获取2023年每月的第一天的列表

List<ZonedDateTime> hourlyDates = ZoneDateTimeUtils.getBetweenDateByHours(
    ZonedDateTime.of(2023, 1, 1, 0, 0, 0, 0, ZoneId.systemDefault()),
    ZonedDateTime.of(2023, 1, 1, 23, 0, 0, 0, ZoneId.systemDefault()),
    1
);  // 获取2023-01-01每小时的列表

List<ZonedDateTime> nextWeekDates = ZoneDateTimeUtils.getNextDates(now, ChronoUnit.DAYS, 7, false);  // 获取未来一周的日期时间列表(不含现在)
``` 