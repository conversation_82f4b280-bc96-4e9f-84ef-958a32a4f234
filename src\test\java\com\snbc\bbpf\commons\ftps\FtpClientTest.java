package com.snbc.bbpf.commons.ftps;

import com.snbc.bbpf.commons.ftps.config.ConnectConfig;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * ftp工具类单元测试类
 *
 * <AUTHOR>
 * @module ftp模块
 * @date 2023/11/01 20:30
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
@ExtendWith(MockitoExtension.class)
public class FtpClientTest {

    @Mock
    private FTPClient client;
    private ConnectConfig connectConfig;
    @InjectMocks
    private FtpClient underTest;
    @Mock
    private AtomicBoolean isConnect;
    private final String filePath = "root";

    @BeforeEach
    @DisplayName("初始化配置，方便后续测试")
    @Tags({
            @Tag("@id:71"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/11/05")
    })
    void init(){
        connectConfig = new ConnectConfig.Builder()
                .host("127.0.0.1")
                .port(21)
                .userName("tester")
                .password("password")
                .build();
        underTest.init(connectConfig);
    }

    @Test
    @DisplayName("测试远程下载文件不存在，抛出异常")
    @Tags({
            @Tag("@id:71"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/8/23")
    })
    public void testPathNoExist_download(){
        try {
            when(underTest.isConnected()).thenReturn(true);
            when(client.listFiles(filePath)).thenReturn(null);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        Assertions.assertThrows(IllegalArgumentException.class,() -> underTest.download(filePath,filePath));
    }

    @Test
    @DisplayName("测试文件是否存在，存在")
    @Tags({
            @Tag("@id:71"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/11/05")
    })
    public void testPathNoExist_existFileInRemote(){
        try {
            FTPFile[] res = new FTPFile[]{Mockito.mock(FTPFile.class)};
            when(client.listFiles(filePath)).thenReturn(res);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        assertTrue(underTest.existFileInRemote(filePath));
    }

    @Test
    @DisplayName("列出远程默认目录的所有文件")
    @Tags({
            @Tag("@id:71"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/11/05")
    })
    public void testlistAll_listFiles(){
        try {
            FTPFile[] res = new FTPFile[]{Mockito.mock(FTPFile.class),Mockito.mock(FTPFile.class)};
            when(client.listFiles()).thenReturn(res);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        List<String>  ftpFiles = underTest.listFiles();
        Assertions.assertNotNull(ftpFiles);
        Assertions.assertEquals(ftpFiles.size(),2);
    }

    @Test
    @DisplayName("列出指定远程目录的所有文件，目录不存在")
    @Tags({
            @Tag("@id:71"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/11/05")
    })
    public void testPathNoExist_listFiles(){
        try {
            when(client.listFiles(filePath)).thenReturn(null);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        List<String> ftpFiles = underTest.listFiles(filePath);
        Assertions.assertNotNull(ftpFiles);
    }

    @DisplayName("创建远程目录成功")
    @Tags({
            @Tag("@id:102"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2024/08/23")
    })
    @Test
    void testCreateRemoteDir_Success() throws IOException {
        when(client.makeDirectory(anyString())).thenReturn(true);

        boolean result = underTest.createRemoteDir("remote/path", "dirName");

        assertTrue(result);
        verify(client).makeDirectory(anyString());
    }

    @Test
    @DisplayName("创建远程目录失败")
    @Tags({
            @Tag("@id:102"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2024/08/23")
    })
    void testCreateRemoteDir_Failure() throws IOException {
        when(client.makeDirectory(anyString())).thenReturn(false);

        boolean result = underTest.createRemoteDir("remote/path", "dirName");

        assertFalse(result);
        verify(client).makeDirectory(anyString());
    }
}
