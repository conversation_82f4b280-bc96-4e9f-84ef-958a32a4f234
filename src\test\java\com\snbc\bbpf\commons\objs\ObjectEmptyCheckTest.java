package com.snbc.bbpf.commons.objs;

import com.snbc.bbpf.commons.collects.ObjectArrayCheck;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static com.snbc.bbpf.commons.objs.ObjectEmptyCheck.isEmpty;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * ObjectEmptyCheck的单元测试类
 *
 * <AUTHOR>
 * @module
 * @date 2023/6/4 21:28
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class ObjectEmptyCheckTest {

    @Test
    @DisplayName("对象为空返回true")
    @Tags({
            @Tag("@id:33"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/6/4")
    })
    void testIsEmpty_Null() {
        assertTrue(isEmpty(null));
    }

    @Test
    @DisplayName("Optional为空返回true")
    @Tags({
            @Tag("@id:33"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/6/4")
    })
    void testIsEmpty_OptionalNotPresent() {
        Optional<String> optional = Optional.empty();
        assertTrue(isEmpty(optional));
    }
    @Test
    @DisplayName("Optional不为空返回false")
    @Tags({
            @Tag("@id:33"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/6/4")
    })
    void testIsEmpty_OptionalPresent() {
        Optional<String> optional = Optional.of("test");
        assertFalse(isEmpty(optional));
    }
    @Test
    @DisplayName("空字符串返回true")
    @Tags({
            @Tag("@id:33"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/6/4")
    })
    void testIsEmpty_CharSequenceEmpty() {
        assertTrue(isEmpty(""));
    }
    @Test
    @DisplayName("字符串不为空返回false")
    @Tags({
            @Tag("@id:33"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/6/4")
    })
    void testIsEmpty_CharSequenceNotEmpty() {
        assertFalse(isEmpty("test"));
    }
    @Test
    @DisplayName("空数组返回true")
    @Tags({
            @Tag("@id:33"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/6/4")
    })
    void testIsEmpty_ArrayEmpty() {
        Integer[] array = new Integer[0];
        assertTrue(isEmpty(array));
    }
    @Test
    @DisplayName("非空数组返回false")
    @Tags({
            @Tag("@id:33"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/6/4")
    })
    void testIsEmpty_ArrayNotEmpty() {
        Integer[] array = new Integer[]{1, 2, 3};
        assertFalse(isEmpty(array));
    }
    @Test
    @DisplayName("数组有空元素返回true")
    @Tags({
            @Tag("@id:33"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/6/4")
    })
    void testIsEmpty_ArrayWithEmptyItem() {
        String[] array = new String[]{"test", "", "test2"};
        assertTrue(isEmpty(array));
    }
    @Test
    @DisplayName("Map有value为空返回true")
    @Tags({
            @Tag("@id:33"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/6/4")
    })
    void testIsEmpty_mapValueIsNull() {
        Map<String, Object> map = new HashMap<>();
        map.put("1",null);
        map.put("2",2);
        assertTrue(isEmpty(map));
    }

    @Test
    @DisplayName("Map是值是empty的字符串返回true")
    @Tags({
            @Tag("@id:33"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/6/3")
    })
    void testIsEmpty_mapValueIsEmpty() {
        Map<String, Object> map = new HashMap<>();
        map.put("1","");
        map.put("2",2);
        assertTrue(isEmpty(map));
    }
    @Test
    @DisplayName("Map是key是empty字符串返回true")
    @Tags({
            @Tag("@id:7"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/6/3")
    })
    void testIsEmpty_mapKeyIsEmpty() {
        Map<String, Object> map = new HashMap<>();
        map.put("","1");
        map.put("2",2);
        assertTrue(isEmpty(map));
    }
}
