/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.crypt;

import javax.crypto.Cipher;

/**
 * 【加解密】AES对称加密算法
 * <p>1.实现AES的加密和解密算法</p>
 * <p>2.使用BouncyCastleProvider（简称BC）扩展可用算法</p>
 *
 * <AUTHOR>
 * @module
 * @date 2023/11/26 20:49
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class AesUtils {

    private AesUtils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * AES加密CBC模式
     *
     * @param key     加密密钥 密码长度为128 位、192 位或 256 位
     * @param keyIv   初始向量 keyIv长度必须等于16字节
     * @param data    加密数据
     * @param padding Padding枚举
     *                NoPadding待加密原文长度必须是 16字节的倍数
     *                PKCS5Padding/PKCS7Padding待加密原文长度不限制
     * @return byte
     * @throws
     * <AUTHOR>
     * @date 2023/11/27
     * @since 1.4.0
     */
    public static byte[] encryptCbc(byte[] key, byte[] keyIv, byte[] data, Padding padding) {
        return CipherUtils.doFinal(Cipher.ENCRYPT_MODE, SymmetricAlgorithm.AES, WorkingMode.CBC, padding, key, keyIv, data);
    }

    /**
     * AES解密CBC模式
     *
     * @param key     加密密钥 密码长度为128 位、192 位或 256 位
     * @param keyIv   初始向量 keyIv长度必须等于16字节
     * @param data    解密数据
     * @param padding Padding枚举
     * @return byte
     * @throws
     * <AUTHOR>
     * @date 2023/11/27
     * @since 1.4.0
     */
    public static byte[] decryptCbc(byte[] key, byte[] keyIv, byte[] data, Padding padding) {
        return CipherUtils.doFinal(Cipher.DECRYPT_MODE, SymmetricAlgorithm.AES, WorkingMode.CBC, padding, key, keyIv, data);
    }

    /**
     * AES加密ECB模式
     *
     * @param key     加密密钥 密码长度为128 位、192 位或 256 位
     * @param data    加密数据
     * @param padding Padding枚举
     *                NoPadding待加密原文长度必须是 16字节的倍数
     *                PKCS5Padding/PKCS7Padding待加密原文长度不限制
     * @return byte
     * @throws
     * <AUTHOR>
     * @date 2023/11/27
     * @since 1.4.0
     */
    public static byte[] encryptEcb(byte[] key, byte[] data, Padding padding) {
        return CipherUtils.doFinal(Cipher.ENCRYPT_MODE, SymmetricAlgorithm.AES, WorkingMode.ECB, padding, key, data);
    }

    /**
     * AES解密ECB模式
     *
     * @param key     加密密钥 密码长度为128 位、192 位或 256 位
     * @param data    解密数据
     * @param padding Padding枚举（NoPadding或PKCS5Padding、PKCS7Padding）
     * @return byte
     * @throws
     * <AUTHOR>
     * @date 2023/11/27
     * @since 1.4.0
     */
    public static byte[] decryptEcb(byte[] key, byte[] data, Padding padding) {
        return CipherUtils.doFinal(Cipher.DECRYPT_MODE, SymmetricAlgorithm.AES, WorkingMode.ECB, padding, key, data);
    }


}
