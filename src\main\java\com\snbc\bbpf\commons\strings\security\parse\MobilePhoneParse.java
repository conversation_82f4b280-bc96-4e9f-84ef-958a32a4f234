/*
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.commons.strings.security.parse;

import org.apache.commons.lang3.StringUtils;

/**
 * @ClassName:  MobilePhoneParse
 *   移动电话脱敏
 * @module:    字符脱敏
 * @Author:    yangweipeng
 * @date:      2023/05/06
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class MobilePhoneParse implements IDesensitizedParse {

    /**
     * 11位手机显示字符数
     */
    private static final Integer MINLENGTH=3;
    /**
     * 13位手机显示字符数
     */
    private static final Integer MAXLENGTH=5;
    /**
     * 右边显示字符数
     */
    private static final Integer RIGHT_LENGTH =4;

    /**
     * 11位号码
     */
    private static final Integer COMSTANTTENON=11;
    /**
     * 13位号码
     */
    private static final Integer COMSTANTTENTHREE=13;
    /**
     * 【手机号码】前三位，后四位，其他隐藏，比如135****6810
     * 当满足11时采用上面的算法
     * 当满足13位的时候才用86135****6810
     * 其它的时候直接显示
     * @param num
     * @return
     */
    @Override
    public String parseString(String num) {
        if(StringUtils.isEmpty(num)){
            return num;
        }
        int lenth=StringUtils.length(num);
        if(COMSTANTTENON==lenth) {
            return StringUtils.left(num , MINLENGTH).
                    concat(StringUtils.removeStart(StringUtils.leftPad(StringUtils.right(num , RIGHT_LENGTH) ,
                            lenth , "*") , "***"));
        }else if(COMSTANTTENTHREE==lenth) {
            return StringUtils.left(num , MAXLENGTH).
                    concat(StringUtils.removeStart(StringUtils.leftPad(StringUtils.right(num , RIGHT_LENGTH) ,
                            lenth , "*") , "*****"));
        }else{
            return num;
        }
    }
}

