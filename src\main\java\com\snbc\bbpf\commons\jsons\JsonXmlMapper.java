/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.jsons;

import com.snbc.bbpf.commons.xmls.XmlObjectMapper;


/**
 * #JSON和XML转换
 *
 * <p>兼融各种日期格式、空属性</p>
 * <p>使用注意：默认的配置已经可以完成95%以上的序列化需求
 * 使用@JsonFormat可以完成98%以上的需求
 * 更多需求请参考Jackson下的其他annatation
 * </p>
 *
 * <AUTHOR>
 * @module JSON模块
 * @date 2023-09-07 16:04
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class JsonXmlMapper {

    private JsonXmlMapper() {

    }
    /**
     * # 将JOSN对象转化为xml
     * 先将JSON 转成对象，再将对象装成XML
     * @param jsonStrStr 要转化的JSON字符串
     * @param clazz 要转化为JSON的对象
     * @return String json字符串
     * @throws RuntimeException
     * <AUTHOR>
     * @date 2023/9/7
     * @since 1.2.0
     */
    public static <T> String marshall(String jsonStrStr, Class<T> clazz) {
        try {
            return XmlObjectMapper.marshall(JsonObjectMapper.unmarshall(jsonStrStr, clazz));
        } catch (RuntimeException e) {
            throw new RuntimeException(e);
        }
    }

}
