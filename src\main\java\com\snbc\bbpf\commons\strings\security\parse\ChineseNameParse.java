/*
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.commons.strings.security.parse;

import org.apache.commons.lang3.StringUtils;

/**
 * @ClassName:     ChineseNameParse
 *   脱敏工具类
    脱敏的具体抽象类
 * @module:         ${PROJECT_NAME}
 * @Author:         yangweipeng
 * @date:           ${DATE}
 * copyright 2020 barm Inc. All rights reserver
 */
public class ChineseNameParse implements IDesensitizedParse {
    /**
     * 显示字符数
     */
    private static final Integer LEFT_LENGTH =1;
    /**
     * 【中文姓名】脱敏处理：只显示第一个汉字，其他隐藏为星号，比如：李**
 
     * @param srcStr 原始姓名字符串
     * @return 脱敏后的姓名字符串
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/6
     */
    @Override
    public String parseString(String srcStr) {
        if(StringUtils.isEmpty(srcStr)){
            return srcStr;
        }
        return StringUtils.rightPad(StringUtils.left(srcStr, LEFT_LENGTH),
                StringUtils.length(srcStr), "*");
    }
}

