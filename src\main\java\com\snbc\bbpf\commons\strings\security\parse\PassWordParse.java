/*
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.commons.strings.security.parse;

import org.apache.commons.lang3.StringUtils;

/**
 * @ClassName:  PassWordParse
 * 密码脱敏
 * @module:    字符脱敏
 * @Author:    yangweipeng
 * @date:      2023/05/06
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class PassWordParse implements IDesensitizedParse {
    /**
     * 密码全隐藏，比如：*************
     *
     * @param password
     * @return
     */
    @Override
    public String parseString(String password) {
        if(StringUtils.isEmpty(password)){
            return password;
        }
        return StringUtils.rightPad("", StringUtils.length(password), "*");
    }
}

