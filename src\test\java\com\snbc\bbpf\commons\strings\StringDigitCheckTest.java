/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.strings;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 判断字符串是否为整数或浮点数测试类
 *
 * <AUTHOR>
 * @module
 * @date 2023/5/5 22:12
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class StringDigitCheckTest {
    @Test
    @DisplayName("正号返回false")
    @Tags({
            @Tag("@id:13"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/24")
    })
    void testIsNumber_false() {
        assertFalse(StringDigitCheck.isNumber("+"));
    }
    @Test
    @DisplayName("数字返回true")
    @Tags({
            @Tag("@id:13"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testIsNumber_true() {
        assertTrue(StringDigitCheck.isNumber("123"));
    }

    @Test
    @DisplayName("0返回true")
    @Tags({
            @Tag("@id:13"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testIsNumber_trueForZero() {
        assertTrue(StringDigitCheck.isNumber("0"));
    }

    @Test
    @DisplayName("负整数返回true")
    @Tags({
            @Tag("@id:13"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testIsNumber_trueForNegative() {
        assertTrue(StringDigitCheck.isNumber("-156888888888856666666"));
    }

    @Test
    @DisplayName("空返回false")
    @Tags({
            @Tag("@id:13"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testIsNumber_falseForEmpty() {
        assertFalse(StringDigitCheck.isNumber(""));
    }

    @Test
    @DisplayName("空格返回false")
    @Tags({
            @Tag("@id:13"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testIsNumber_falseForBlank() {
        assertFalse(StringDigitCheck.isNumber(" "));
    }

    @Test
    @DisplayName("null返回false")
    @Tags({
            @Tag("@id:13"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testIsNumber_falseForNull() {
        assertFalse(StringDigitCheck.isNumber(null));
    }

    @Test
    @DisplayName("小数返回false")
    @Tags({
            @Tag("@id:13"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testIsNumber_falseForDouble() {
        assertFalse(StringDigitCheck.isNumber("0.22"));
    }

    @Test
    @DisplayName("浮点数返回false")
    @Tags({
            @Tag("@id:13"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testIsNumber_falseForFloat() {
        assertFalse(StringDigitCheck.isNumber("1e3"));
    }

    @Test
    @DisplayName("小数返回true")
    @Tags({
            @Tag("@id:13"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testIsFloat_true() {
        assertTrue(StringDigitCheck.isFloat("123.10"));
    }

    @Test
    @DisplayName("0开头小数返回true")
    @Tags({
            @Tag("@id:13"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testIsFloat_zeroTopDouble() {
        assertTrue(StringDigitCheck.isFloat("0.10"));
    }

    @Test
    @DisplayName("0.00返回true")
    @Tags({
            @Tag("@id:13"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testIsFloat_zeroPointZero() {
        assertTrue(StringDigitCheck.isFloat("0.00"));
    }

    @Test
    @DisplayName("0返回true")
    @Tags({
            @Tag("@id:13"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testIsFloat_trueForZero() {
        assertTrue(StringDigitCheck.isFloat("0"));
    }

    @Test
    @DisplayName("负小数返回true")
    @Tags({
            @Tag("@id:13"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testIsFloat_trueForNegative() {
        assertTrue(StringDigitCheck.isFloat("-456.1"));
    }

    @Test
    @DisplayName("空返回false")
    @Tags({
            @Tag("@id:13"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testIsFloat_falseForEmpty() {
        assertFalse(StringDigitCheck.isFloat(""));
    }

    @Test
    @DisplayName("null返回false")
    @Tags({
            @Tag("@id:13"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testIsFloat_falseForNull() {
        assertFalse(StringDigitCheck.isFloat(null));
    }

    @Test
    @DisplayName("空格返回false")
    @Tags({
            @Tag("@id:13"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testIsFloat_falseForBlank() {
        assertFalse(StringDigitCheck.isFloat(" "));
    }

    @Test
    @DisplayName("整数返回true")
    @Tags({
            @Tag("@id:13"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testIsFloat_falseForDouble() {
        assertTrue(StringDigitCheck.isFloat("232222222222222222222222122"));
    }

    @Test
    @DisplayName("e科学计数法返回true")
    @Tags({
            @Tag("@id:13"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testIsFloat_trueForScientific_e() {
        assertTrue(StringDigitCheck.isFloat("-1e3"));
    }

    @Test
    @DisplayName("E科学计数法返回true")
    @Tags({
            @Tag("@id:13"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testIsFloat_trueForScientific_bigE() {
        assertTrue(StringDigitCheck.isFloat("11E13"));
    }

    @Test
    @DisplayName("错误的科学计数法返回false")
    @Tags({
            @Tag("@id:13"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testIsFloat_trueForScientific_Ee() {
        assertFalse(StringDigitCheck.isFloat("11Ee13"));
    }

    @Test
    @DisplayName("错误的科学计数法返回false")
    @Tags({
            @Tag("@id:13"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testIsFloat_trueForScientific_errorTwoE() {
        assertFalse(StringDigitCheck.isFloat("1e13e1"));
    }
    @Test
    @DisplayName("负号返回false")
    @Tags({
            @Tag("@id:13"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/24")
    })
    void testIsFloat_false() {
        assertFalse(StringDigitCheck.isFloat("-"));
    }
}
