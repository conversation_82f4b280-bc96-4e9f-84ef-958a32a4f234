/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.randoms;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 类描述:    指定格式随机字符串.
 *
 * <AUTHOR>
 * 创建时间:  [2023/6/8 13:41]
 */
class IdUtilsTest {


    @DisplayName("返回前缀+年月日+n位随机数的字符串")
    @Tags({
            @Tag("@id:35"),
            @Tag("@author:xuping"),
            @Tag("@date:2023/6/8")
    })
    @Test
    void generateString() {

        String prefix = "PREFIX";
        int randomLength = 6;
        IdUtils.generateString(prefix, randomLength);
        assertDoesNotThrow(() -> {
            IdUtils.generateString(null, randomLength);
        });
        String code = IdUtils.generateString(prefix, 13);
        System.out.println(code);
        Assertions.assertNotNull(code);

    }

    @DisplayName("生成带有前缀和UUID的字符串  ")
    @Tags({
            @Tag("@id:35"),
            @Tag("@author:xuping"),
            @Tag("@date:2023/6/8")
    })
    @Test
    void testGenerateStringWithoutHyphen() {
        String prefix = "PREFIX";

        String result = IdUtils.generateStringWithoutHyphen(prefix);
        System.out.println(result);
        // 根据具体前缀和UUID长度，修改期望的字符串格式
        String expectedFormat = "^PREFIX[A-Za-z0-9]{32}$";
        assertDoesNotThrow(() -> {
            IdUtils.generateStringWithoutHyphen(null);
        });
        assertTrue(result.matches(expectedFormat));

    }

    @DisplayName("生成带有前缀和UUID的字符串  empty")
    @Tags({
            @Tag("@id:35"),
            @Tag("@author:xuping"),
            @Tag("@date:2023/6/8")
    })
    @Test
    void testGenerateStringWithoutHyphen_empty() {
        String prefix = "PREFIX";

        String result = IdUtils.generateStringWithoutHyphen(prefix);
        // 根据具体前缀和UUID长度，修改期望的字符串格式
        String expectedFormat = "^PREFIX[A-Za-z0-9]{32}$";
        assertDoesNotThrow(() -> {
            IdUtils.generateStringWithoutHyphen("");
        });
        assertTrue(result.matches(expectedFormat));

    }

    @DisplayName("生成带有前缀和UUID的字符串（保留-）")
    @Tags({
            @Tag("@id:35"),
            @Tag("@author:xuping"),
            @Tag("@date:2023/6/8")
    })
    @Test
    void testGenerateStringWithHyphen() {
        String prefix = "PREFIX";

        String result = IdUtils.generateStringWithHyphen(prefix);

        // 根据具体前缀和UUID长度，修改期望的字符串格式
        String expectedFormat = "^PREFIX[A-Za-z0-9-]{36}$";

        assertTrue(result.matches(expectedFormat));
        assertDoesNotThrow(() -> {
            IdUtils.generateStringWithHyphen(null);
        });
        assertDoesNotThrow(() -> {
            IdUtils.generateStringWithHyphen("");
        });
    }

    @DisplayName("生成UUID字符串")
    @Tags({
            @Tag("@id:35"),
            @Tag("@author:xuping"),
            @Tag("@date:2023/6/8")
    })
    @Test
    void testGenerateUUIDWithHyphens() {
        String uuidString = IdUtils.generateUUID(false);
        assertEquals(36, uuidString.length());
        assertEquals(5, uuidString.split("-").length);
    }

    @DisplayName("生成UUID字符串")
    @Tags({
            @Tag("@id:35"),
            @Tag("@author:xuping"),
            @Tag("@date:2023/6/8")
    })
    @Test
    void testGenerateUUIDWithoutHyphens() {
        String uuidString = IdUtils.generateUUID(true);
        assertEquals(32, uuidString.length());
    }

    @DisplayName("生成UUID字符串")
    @Tags({
            @Tag("@id:35"),
            @Tag("@author:xuping"),
            @Tag("@date:2023/6/8")
    })
    @Test
    void testGenerateUUIDsWithHyphens() {
        int count = 5;
        String[] uuids = IdUtils.generateUUIDsWithHyphens(count);

        assertEquals(count, uuids.length);
        for (String uuid : uuids) {
            assertEquals(36, uuid.length());
            assertEquals(5, uuid.split("-").length);
        }
    }

    @DisplayName("生成UUID字符串")
    @Tags({
            @Tag("@id:35"),
            @Tag("@author:xuping"),
            @Tag("@date:2023/6/8")
    })
    @Test
    void testGenerateUUIDsWithoutHyphens() {
        int count = 5;
        String[] uuids = IdUtils.generateUUIDsWithoutHyphens(count);

        assertEquals(count, uuids.length);
        for (String uuid : uuids) {
            assertEquals(32, uuid.length());
        }
    }

    @DisplayName("使用ULID生成有序的ID")
    @Tags({
            @Tag("@id:35"),
            @Tag("@author:xuping"),
            @Tag("@date:2023/6/8")
    })
    @Test
    void testGenerateULID() {
        for (int i = 0; i < 100; i++) {
            String uuids1 = IdUtils.generateULID();
            String uuids2 = IdUtils.generateULID();
            assertTrue(uuids2.compareTo(uuids1) > 0);
        }
    }




}
