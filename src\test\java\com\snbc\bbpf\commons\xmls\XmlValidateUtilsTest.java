/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.xmls;

import com.snbc.bbpf.commons.files.FileUtils;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.AfterAll;

import java.io.File;
import java.nio.charset.StandardCharsets;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * 基于DTD验证XML单元测试类
 *
 * <AUTHOR>
 * @module XML模块
 * @date 2023/10/15 20:13
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class XmlValidateUtilsTest {

    public static String FILE_NORMAL_XML = "./normal.xml";
    public static String FILE_NORMAL_DTDFILEXML = "./dtdfilenormal.xml";
    public static String FILE_NORMAL_DTDXML = "./dtdnormal.xml";
    public static String FILE_UNNORMAL_XML = "./unnormal.xml";
    public static String FILE_NORMAL_DTD = "./normal.dtd";
    public static String FILE_UNNORMAL_DTD = "./unnormal.dtd";
    public static File testFile = null;
    @BeforeAll
    @DisplayName("生成用于测试的XML、DTD文件")
    @Tags({
            @Tag("@id:68"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/10/15")
    })
    static void beforeAll_creatFile() throws Exception {
		String xmlContent =
			"<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n" +
			"<班级>\r\n" +
			    "<学生>\r\n" +
			        "<名字>周小星</名字>\r\n"  +
			        "<年龄>23</年龄>\r\n" +
			        "<介绍>学习刻苦</介绍>\r\n" +
			    "</学生>\r\n" +
			    "<学生>\r\n" +
			        "<名字>林晓</名字>\r\n" +
			        "<年龄>25</年龄>\r\n" +
			        "<介绍>是一个好学生</介绍>\r\n" +
			    "</学生>\r\n" +
			"</班级>";

		FileUtils.write(FILE_NORMAL_XML, xmlContent, StandardCharsets.UTF_8);

		String xmlDtdFileContent =
			"<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n" +
			"<!DOCTYPE 班级 SYSTEM \"myClass.dtd\">\r\n" +
			"<班级>\r\n" +
			    "<学生>\r\n" +
			        "<名字>周小星</名字>\r\n"  +
			        "<年龄>23</年龄>\r\n" +
			        "<介绍>学习刻苦</介绍>\r\n" +
			    "</学生>\r\n" +
			    "<学生>\r\n" +
			        "<名字>林晓</名字>\r\n" +
			        "<年龄>25</年龄>\r\n" +
			        "<介绍>是一个好学生</介绍>\r\n" +
			    "</学生>\r\n" +
			"</班级>";

		FileUtils.write(FILE_NORMAL_DTDFILEXML, xmlDtdFileContent,StandardCharsets.UTF_8);

		String xmlDtdContent =
			"<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n" +
			"<!DOCTYPE 班级 [" +
			"<!ELEMENT 班级 (学生+)>\r\n" +
			"<!ELEMENT 学生 (名字,年龄,介绍)>\r\n" +
			"<!ELEMENT 名字 (#PCDATA)>\r\n" +
			"<!ELEMENT 年龄 (#PCDATA)>\r\n" +
			"<!ELEMENT 介绍 (#PCDATA)>" +
			"]>\r\n" +
			"<班级>\r\n" +
			    "<学生>\r\n" +
			        "<名字>周小星</名字>\r\n"  +
			        "<年龄>23</年龄>\r\n" +
			        "<介绍>学习刻苦</介绍>\r\n" +
			    "</学生>\r\n" +
			    "<学生>\r\n" +
			        "<名字>林晓</名字>\r\n" +
			        "<年龄>25</年龄>\r\n" +
			        "<介绍>是一个好学生</介绍>\r\n" +
			    "</学生>\r\n" +
			"</班级>";

		FileUtils.write(FILE_NORMAL_DTDXML, xmlDtdContent,StandardCharsets.UTF_8);

		String xmlUnnormalContent =
			"<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n" +
			"<!DOCTYPE 班级 SYSTEM \"myClass.dtd\">\r\n" +
			"<班级>\r\n" +
			    "<学生>" +
			        "<名字>周小星</名字>\r\n"  +
			        "<年龄>23</年龄>\r\n" +
			        "<介绍>学习刻苦</介绍>\r\n" +
			    "</学生>\r\n" +
			    "<学生>\r\n" +
			        "<名字>林晓</名字>\r\n" +
			        "<年龄>25</年龄>\r\n" +
			        "<介绍>是一个好学生</介绍>\r\n" +
			    "</学生>\r\n";

		FileUtils.write(FILE_UNNORMAL_XML, xmlUnnormalContent,StandardCharsets.UTF_8);


		String dtdContent =
			"<!ELEMENT 班级 (学生+)>\r\n" +
			"<!ELEMENT 学生 (名字,年龄,介绍)>\r\n" +
			"<!ELEMENT 名字 (#PCDATA)>\r\n" +
			"<!ELEMENT 年龄 (#PCDATA)>\r\n" +
			"<!ELEMENT 介绍 (#PCDATA)>";

		FileUtils.write(FILE_NORMAL_DTD, dtdContent,StandardCharsets.UTF_8);

		String dtdUnnormalContent =
			"<!ELEMENT 班级2 (学生+)>\r\n" +
			"<!ELEMENT 学生 (名字,年龄,介绍)>\r\n" +
			"<!ELEMENT 名字 (#PCDATA)>\r\n" +
			"<!ELEMENT 年龄 (#PCDATA)>\r\n" +
			"<!ELEMENT 介绍 (#PCDATA)>";

		FileUtils.write(FILE_UNNORMAL_DTD, dtdUnnormalContent,StandardCharsets.UTF_8);
    }

    @AfterAll
    @DisplayName("释放资源")
    @Tags({
            @Tag("@id:68"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/10/15")
    })
    static void afterAll_deleteFile() throws Exception {
		FileUtils.delete(FILE_NORMAL_XML);
		FileUtils.delete(FILE_NORMAL_DTDFILEXML);
		FileUtils.delete(FILE_NORMAL_DTDXML);
		FileUtils.delete(FILE_UNNORMAL_XML);
		FileUtils.delete(FILE_NORMAL_DTD);
		FileUtils.delete(FILE_UNNORMAL_DTD);
    }

    @Test
	@DisplayName("对xml文件、传入DTD文件内容符合格式要求")
	@Tags({
			@Tag("@id:68"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/10/15")
	})
    void testValidateDTDContent() {
        assertTrue(XmlValidateUtils.validateDTD(FILE_NORMAL_XML, FileUtils.readString(FILE_NORMAL_DTD,StandardCharsets.UTF_8)));
    }

    @Test
	@DisplayName("对xml文件、传入DTD文件内容符合格式要求")
	@Tags({
			@Tag("@id:68"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/10/15")
	})
    void testValidateDTDContent_inside() {
        assertTrue(XmlValidateUtils.validateDTD(FILE_NORMAL_DTDXML, FileUtils.readString(FILE_NORMAL_DTD,StandardCharsets.UTF_8)));
    }

    @Test
	@DisplayName("对xml文件、传入DTD文件内容符合格式要求")
	@Tags({
			@Tag("@id:68"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/10/15")
	})
    void testValidateDTDContent_file() {
        assertTrue(XmlValidateUtils.validateDTD(FILE_NORMAL_DTDFILEXML, FileUtils.readString(FILE_NORMAL_DTD,StandardCharsets.UTF_8)));
    }

    @Test
	@DisplayName("xml文件路径空应该抛出IllegalArgumentException异常")
	@Tags({
			@Tag("@id:68"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/10/15")
	})
    void testValidateDTDContent_xmlEmptyShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> XmlValidateUtils.validateDTD("", FileUtils.readString(FILE_NORMAL_DTD,StandardCharsets.UTF_8)),
				"Input parameter is empty");
    }

    @Test
	@DisplayName("xml文件路径null应该抛出IllegalArgumentException异常")
	@Tags({
			@Tag("@id:68"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/10/15")
	})
    void testValidateDTDContent_xmlNullShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> XmlValidateUtils.validateDTD(null, FileUtils.readString(FILE_NORMAL_DTD,StandardCharsets.UTF_8)),
				"Input parameter is null");
    }

    @Test
	@DisplayName("DTD文件内容空应该抛出IllegalArgumentException异常")
	@Tags({
			@Tag("@id:68"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/10/15")
	})
    void testValidateDTDContent_dtdEmptyShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> XmlValidateUtils.validateDTD(FILE_NORMAL_XML, ""),
				"Input parameter is empty");
    }

    @Test
	@DisplayName("DTD文件内容null应该抛出IllegalArgumentException异常")
	@Tags({
			@Tag("@id:68"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/10/15")
	})
    void testValidateDTDContent_dtdNullShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> XmlValidateUtils.validateDTD(FILE_NORMAL_XML, null),
				"Input parameter is null");
    }

    @Test
	@DisplayName("对xml文件、传入DTD文件符合格式要求")
	@Tags({
			@Tag("@id:68"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/10/15")
	})
    void testValidateDTD() {
        assertTrue(XmlValidateUtils.validate(FILE_NORMAL_XML, FILE_NORMAL_DTD));
    }

    @Test
	@DisplayName("xml文件不符合格式要求")
	@Tags({
			@Tag("@id:68"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/10/15")
	})
    void testValidateDTD_xmlUnnormal() {
        assertFalse(XmlValidateUtils.validate(FILE_UNNORMAL_XML, FILE_NORMAL_DTD));
    }

    @Test
	@DisplayName("DTD文件不符合格式要求")
	@Tags({
			@Tag("@id:68"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/10/15")
	})
    void testValidateDTD_dtdUnnormal() {
        assertFalse(XmlValidateUtils.validate(FILE_NORMAL_XML, FILE_UNNORMAL_DTD));
    }

    @Test
	@DisplayName("xml文件符合格式要求")
	@Tags({
			@Tag("@id:68"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/10/15")
	})
    void testValidate() {
        assertTrue(XmlValidateUtils.validate(FILE_NORMAL_XML));
    }

    @Test
	@DisplayName("xml文件不符合格式要求")
	@Tags({
			@Tag("@id:68"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/10/15")
	})
    void testValidate_xmlUnnormal() {
        assertFalse(XmlValidateUtils.validate(FILE_UNNORMAL_XML));
    }

}

