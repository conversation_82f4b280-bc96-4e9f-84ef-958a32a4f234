/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.validations.annotation;


import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 字符串长度注解
 *
 * <AUTHOR>
 * @module 注解
 * @date 2023/7/26
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
@Target(value = {ElementType.FIELD,ElementType.METHOD,ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ValidStringLength {
    int min() default 0;
    int max() default 0;
    String message() default "";
}
