# 反射处理 (com.snbc.bbpf.commons.reflects)

## 类概览 

| 类名 | 功能描述 |
|------|----------|
| ReflectUtils | 提供反射操作功能，包括获取对象属性值等 |
| ReflectCallUtils | 提供方法调用功能，支持通过反射调用对象方法和类方法 |

## ReflectUtils - 反射工具类

### API列表 

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| getPropertyValue(Object targetObject, String propertyName, Class<T> clazz) | 获取对象指定属性的值 | targetObject: 目标对象<br>propertyName: 属性名<br>clazz: 属性值类型 | <T>: 属性值 |
| getAllPropertiesValue(Object targetObject) | 获取对象所有属性的值 | targetObject: 目标对象 | Map<String, Object>: 属性名和值的映射 |

### 注意事项

1. **参数校验**：所有参数将进行非空校验，如果传入null或空字符串将抛出IllegalArgumentException异常
2. **属性访问**：方法会尝试访问private属性，如果无法访问可能会抛出反射相关异常
3. **属性不存在**：如果请求的属性在对象中不存在，将抛出NoSuchFieldException异常
4. **类型转换**：如果指定的Class类型与实际属性类型不匹配，可能会发生ClassCastException
5. **性能考虑**：反射操作相比直接访问字段性能较低，不建议在性能敏感场景频繁使用
6. **继承关系**：方法会获取父类中定义的属性，但不保证获取所有层级的父类属性
7. **getAllPropertiesValue**：该方法不会获取静态字段的值

### 使用示例

```java
// 创建测试对象
Dog dog = new Dog();
dog.setName("大黄");
dog.setAge(5);

// 创建颜色对象
Color color = new Color();
color.red = "1001";
color.green = "2002";
color.blue = "3003";
dog.setColor(color);

// 获取对象指定属性的值
String name = ReflectUtils.getPropertyValue(dog, "name", String.class);
System.out.println("Name: " + name);  // 输出: Name: 大黄

// 获取整数类型属性值
Integer age = ReflectUtils.getPropertyValue(dog, "age", Integer.class);
System.out.println("Age: " + age);  // 输出: Age: 5

// 获取复杂对象类型属性值
Color dogColor = ReflectUtils.getPropertyValue(dog, "color", Color.class);
System.out.println("Color red: " + dogColor.red);    // 输出: Color red: 1001
System.out.println("Color green: " + dogColor.green);  // 输出: Color green: 2002
System.out.println("Color blue: " + dogColor.blue);   // 输出: Color blue: 3003

// 获取对象所有属性的值
Map<String, Object> allProperties = ReflectUtils.getAllPropertiesValue(dog);
System.out.println("All properties: " + allProperties);
// 输出类似: All properties: {name=大黄, age=5, color=Color@123456}

// 异常情况示例
try {
    // 对象为null时抛出异常
    ReflectUtils.getPropertyValue(null, "name", String.class);
} catch (IllegalArgumentException e) {
    System.out.println("Exception: " + e.getMessage());  // 输出: Exception: The Target Object is Null
}

try {
    // 属性名为空时抛出异常
    ReflectUtils.getPropertyValue(dog, "", String.class);
} catch (IllegalArgumentException e) {
    System.out.println("Exception: " + e.getMessage());  // 输出: Exception: PropertyName is Blank
}

try {
    // 类型为null时抛出异常
    ReflectUtils.getPropertyValue(dog, "name", null);
} catch (IllegalArgumentException e) {
    System.out.println("Exception: " + e.getMessage());  // 输出: Exception: Property Value Class is Null
}
```

## ReflectCallUtils - 方法调用工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| call(Object targetObject, String methodName, Class<?>[] paramclasses, Object... args) | 调用指定对象方法 | targetObject: 目标对象<br>methodName: 方法名<br>paramclasses: 参数类型数组<br>args: 参数数组 | <T>: 方法返回值 |
| call(String className, String methodName, Class<?>[] paramclasses, Object... args) | 调用指定类方法 | className: 类全名<br>methodName: 方法名<br>paramclasses: 参数类型数组<br>args: 参数数组 | <T>: 方法返回值 |
| call(String className, String methodName) | 调用指定类无参数方法 | className: 类全名<br>methodName: 方法名 | <T>: 方法返回值 |
| call(Object targetObject, String methodName) | 调用指定对象无参数方法 | targetObject: 目标对象<br>methodName: 方法名 | <T>: 方法返回值 |

### 注意事项

1. **参数校验**：所有关键参数（目标对象、类名、方法名）不能为null或空字符串，否则将抛出IllegalArgumentException
2. **类加载**：通过类名调用方法时，如果类不存在会抛出ClassNotFoundException
3. **方法查找**：如果指定的方法不存在，将抛出NoSuchMethodException异常
4. **参数匹配**：paramclasses参数数组必须与args参数数组长度一致，且类型匹配，否则可能导致方法调用失败
5. **访问控制**：方法会尝试调用private方法，但可能受到Java安全管理器限制
6. **返回值处理**：方法使用泛型返回值，需要正确指定返回类型，如`ReflectCallUtils.<String>call(...)`
7. **静态方法**：通过类名调用的方法通常是静态方法，通过对象调用的是实例方法
8. **性能考虑**：反射调用比直接方法调用性能低，不建议在性能敏感场景频繁使用
9. **异常处理**：方法调用过程中的异常会被包装成RuntimeException抛出，需要妥善处理

### 使用示例

```java
// 通过类名调用无参方法
String name = ReflectCallUtils.<String>call("com.snbc.bbpf.commons.reflects.Dog", "getName");
System.out.println("Dog name: " + name);  // 输出: Dog name: 小黑 (默认名)

// 通过类名调用有参方法
Class<?>[] paramclasses = new Class<?>[]{String.class};
ReflectCallUtils.call("com.snbc.bbpf.commons.reflects.Dog", "setName", paramclasses, "大白");
String newName = ReflectCallUtils.<String>call("com.snbc.bbpf.commons.reflects.Dog", "getName");
System.out.println("New dog name: " + newName);  // 输出: New dog name: 大白

// 获取其他属性
Integer age = ReflectCallUtils.<Integer>call("com.snbc.bbpf.commons.reflects.Dog", "getAge");
System.out.println("Dog age: " + age);  // 输出: Dog age: 7 (默认年龄)

// 调用有返回值的方法
Class<?>[] colorParamClasses = new Class<?>[]{String.class};
String result = ReflectCallUtils.<String>call("com.snbc.bbpf.commons.reflects.Dog", "setColor", colorParamClasses, "有颜色的狗狗");
System.out.println("Set color result: " + result);  // 输出: Set color result: 有颜色的狗狗

// 基于对象实例调用方法
Dog dog = new Dog();
Integer dogAge = ReflectCallUtils.<Integer>call(dog, "getAge");
System.out.println("Instance dog age: " + dogAge);  // 输出: Instance dog age: 3

// 基于对象实例调用有参方法
Class<?>[] dogColorParamClasses = new Class<?>[]{String.class};
String dogColorResult = ReflectCallUtils.<String>call(dog, "setColor", dogColorParamClasses, "有颜色的狗狗");
System.out.println("Instance set color result: " + dogColorResult);  // 输出: Instance set color result: 有颜色的狗狗

// 基于对象实例调用多参数方法
Class<?>[] nameAgeParamClasses = new Class<?>[]{String.class, int.class};
String nameAgeResult = ReflectCallUtils.<String>call(dog, "setNameAge", nameAgeParamClasses, "花花", 15);
System.out.println("Set name and age result: " + nameAgeResult);  // 输出: Set name and age result: 一条名叫花花狗狗已经15岁了!

// 异常情况示例
try {
    // 类名为空时抛出异常
    ReflectCallUtils.call("", "getName");
} catch (IllegalArgumentException e) {
    System.out.println("Exception: " + e.getMessage());  // 输出: Exception: Input parameter is empty
}

try {
    // 方法名为空时抛出异常
    ReflectCallUtils.call("Dog", "");
} catch (IllegalArgumentException e) {
    System.out.println("Exception: " + e.getMessage());  // 输出: Exception: Input parameter is empty
}

try {
    // 无有效参数时抛出异常
    ReflectCallUtils.call("Dog", "setSize");
} catch (RuntimeException e) {
    System.out.println("Exception: " + e.getMessage());  // 输出相关异常信息
}

try {
    // 对象为null时抛出异常
    ReflectCallUtils.<Integer>call((Object)null, "getAge");
} catch (IllegalArgumentException e) {
    System.out.println("Exception: " + e.getMessage());  // 输出: Exception: Input parameter is empty
}
``` 