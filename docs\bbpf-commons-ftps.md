# FTP处理 (com.snbc.bbpf.commons.ftps)

## 类概览 

| 类名 | 功能描述 |
|------|----------|
| ITransferClient | 定义统一的连接、上传和下载等接口 |
| BaseFtpClient | 抽象的FTP客户端类，包含通用方法 |
| FtpClient | FTP协议客户端实现类 |
| SFTPClient | SFTP协议客户端实现类 |
| ConnectConfig | 连接配置类，提供连接参数的构建器 |
| FileTransferType | 文件传输类型枚举（二进制/ASCII） |
| FtpMode | FTP模式枚举（主动/被动） |
| SFTPAuthType | SFTP认证方式枚举 |

## ITransferClient - 通用传输接口

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| connect() | 连接到服务器 | 无 | void |
| disconnect() | 断开连接 | 无 | void |
| upload(String localFilePath, String remoteDirPath) | 上传文件（默认不删除本地文件，覆盖远程文件） | localFilePath: 本地文件路径<br>remoteDirPath: 远程目录路径 | boolean: 上传是否成功 |
| upload(String localFilePath, String remoteDirPath, boolean deleteLocalFileIfUploadSuccess, boolean overwriteIfFileExist) | 上传文件（可配置本地文件处理和远程文件覆盖选项） | localFilePath: 本地文件路径<br>remoteDirPath: 远程目录路径<br>deleteLocalFileIfUploadSuccess: 上传成功是否删除本地文件<br>overwriteIfFileExist: 是否覆盖远程文件 | boolean: 上传是否成功 |
| download(String remoteFilePath, String localDirPath) | 下载文件（默认不删除远程文件，覆盖本地文件） | remoteFilePath: 远程文件路径<br>localDirPath: 本地目录路径 | boolean: 下载是否成功 |
| download(String remoteFilePath, String localDirPath, boolean deleteRemoteFileIfDownloadSuccess, boolean overwriteIfFileExist) | 下载文件（可配置远程文件处理和本地文件覆盖选项） | remoteFilePath: 远程文件路径<br>localDirPath: 本地目录路径<br>deleteRemoteFileIfDownloadSuccess: 下载成功是否删除远程文件<br>overwriteIfFileExist: 是否覆盖本地文件 | boolean: 下载是否成功 |
| deleteRemoteFile(String remoteFilePath) | 删除远程文件 | remoteFilePath: 远程文件路径 | boolean: 删除是否成功 |
| batchDeleteRemoteFiles(String remoteDirPath, Predicate<String> predicate) | 批量删除符合条件的远程文件 | remoteDirPath: 远程目录路径<br>predicate: 文件名过滤器 | Map<String, Boolean>: 文件删除结果映射 |
| deleteRemoteDir(String remoteDirPath) | 删除远程目录 | remoteDirPath: 远程目录路径 | boolean: 删除是否成功 |
| listFiles() | 列出远程默认目录的所有文件 | 无 | List<String>: 文件名列表 |
| listFiles(String remoteDirPath) | 列出指定远程目录的所有文件 | remoteDirPath: 远程目录路径 | List<String>: 文件名列表 |
| listFiles(String remoteDirPath, Predicate<String> fileNamePredicate) | 列出指定远程目录的符合条件的文件 | remoteDirPath: 远程目录路径<br>fileNamePredicate: 文件名过滤器 | List<String>: 符合条件的文件名列表 |
| batchUpload(String localPath, Predicate<Path> predicate, String remoteDirPath) | 批量上传符合条件的文件 | localPath: 本地目录路径<br>predicate: 文件过滤器<br>remoteDirPath: 远程目录路径 | Map<String, Boolean>: 文件上传结果映射 |
| batchUpload(String localPath, Predicate<Path> predicate, String remoteDirPath, boolean deleteLocalFileIfUploadSuccess, boolean overwriteIfFileExist) | 批量上传符合条件的文件（可配置） | localPath: 本地目录路径<br>predicate: 文件过滤器<br>remoteDirPath: 远程目录路径<br>deleteLocalFileIfUploadSuccess: 上传成功是否删除本地文件<br>overwriteIfFileExist: 是否覆盖远程文件 | Map<String, Boolean>: 文件上传结果映射 |
| batchDownload(String remoteDirPath, Predicate<String> predicate, String localDirPath) | 批量下载符合条件的文件 | remoteDirPath: 远程目录路径<br>predicate: 文件名过滤器<br>localDirPath: 本地目录路径 | Map<String, Boolean>: 文件下载结果映射 |
| batchDownload(String remoteDirPath, Predicate<String> predicate, String localDirPath, boolean deleteRemoteFileIfDownloadSuccess, boolean overwriteIfFileExist) | 批量下载符合条件的文件（可配置） | remoteDirPath: 远程目录路径<br>predicate: 文件名过滤器<br>localDirPath: 本地目录路径<br>deleteRemoteFileIfDownloadSuccess: 下载成功是否删除远程文件<br>overwriteIfFileExist: 是否覆盖本地文件 | Map<String, Boolean>: 文件下载结果映射 |
| createRemoteDir(String remoteDirPath, String dirName) | 在远程创建目录 | remoteDirPath: 远程目录路径<br>dirName: 要创建的目录名 | boolean: 创建是否成功 |

### 接口注意事项

1. **异常处理**: 接口方法可能抛出`RuntimeException`或其子类异常，调用方需自行捕获处理。
2. **路径格式统一**: 路径参数应统一格式，建议使用`File.separator`或斜杠`/`作为分隔符。
3. **传输依赖有效连接**: 所有传输操作前必须先调用`connect()`方法建立连接，否则操作将失败。
4. **接口实现差异**: FtpClient和SFTPClient对接口的实现存在细微差异，使用时需注意具体实现类的行为。
5. **批量操作风险**: 批量操作可能导致部分文件成功部分失败的情况，需检查返回的Map结果。

## BaseFtpClient - 基础FTP客户端

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| init(ConnectConfig connectConfig) | 初始化客户端 | connectConfig: 连接配置 | boolean: 初始化是否成功 |
| isConnected() | 检查客户端是否已连接 | 无 | boolean: 是否已连接 |
| getFileNameByFilePath(String filePath) | 从文件路径获取文件名 | filePath: 文件路径 | String: 文件名 |
| getDirPathWithoutLastSeparator(String dirPath) | 去除路径末尾的分隔符 | dirPath: 目录路径 | String: 处理后的目录路径 |

### 基础类注意事项

1. **初始化顺序**: 必须先执行`init()`方法后才能调用`connect()`方法，否则连接将失败。
2. **状态检查**: `isConnected()`方法只反映客户端内部连接状态，不一定反映实际网络连接状态，特别是在网络异常中断的情况下。
3. **路径处理方法**: 静态工具方法`getFileNameByFilePath`和`getDirPathWithoutLastSeparator`处理路径时同时支持Windows和Linux风格的路径分隔符。
4. **多线程安全性**: 此基础类没有实现线程安全机制，在多线程环境中使用时需要额外同步处理。
5. **子类扩展**: 继承此类的子类必须实现ITransferClient接口中定义的所有方法。

## ConnectConfig - 连接配置类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| Builder.host(String host) | 设置主机地址 | host: 主机地址 | Builder |
| Builder.port(int port) | 设置端口号 | port: 端口号 | Builder |
| Builder.userName(String userName) | 设置用户名 | userName: 用户名 | Builder |
| Builder.password(String password) | 设置密码 | password: 密码 | Builder |
| Builder.connectTimeout(long connectTimeout) | 设置连接超时时间 | connectTimeout: 超时时间(ms) | Builder |
| Builder.transferTimeout(long transferTimeout) | 设置传输超时时间 | transferTimeout: 超时时间(ms) | Builder |
| Builder.encoding(String charset) | 设置编码格式 | charset: 编码格式 | Builder |
| Builder.transferFileType(String transferFileType) | 设置文件传输类型 | transferFileType: 文件传输类型 | Builder |
| Builder.mode(String mode) | 设置FTP模式 | mode: FTP模式 | Builder |
| Builder.privateKeyFilePath(String privateKeyFilePath) | 设置私钥文件路径 | privateKeyFilePath: 私钥文件路径 | Builder |
| Builder.authType(SFTPAuthType authType) | 设置SFTP认证类型 | authType: SFTP认证类型 | Builder |
| Builder.build() | 构建连接配置 | 无 | ConnectConfig |

### 配置类注意事项

1. **必填字段**: 主机地址(host)、端口号(port)、用户名(userName)和密码(password)是必填字段，缺少这些字段将导致连接失败。
2. **默认值设置**: 
   - 连接超时时间默认为3000毫秒(3秒)
   - 传输超时时间默认为-1(永不超时)
   - 编码格式默认为UTF-8
   - 文件传输类型默认为二进制(BINARY)
   - FTP模式默认为被动模式(POSITIVE_MODE) 
3. **配置不可变性**: 一旦通过Builder创建了ConnectConfig实例，其配置不可再修改。如需更改配置，必须创建新的实例。
4. **SFTP专用配置**: privateKeyFilePath和authType参数仅在使用SFTPClient时有效，对FtpClient无效。
5. **配置有效性验证**: 使用`isValid()`方法可检查配置的主要参数是否有效，建议在初始化客户端前进行验证。

## 枚举类型

### FileTransferType
- FILE_TRANSFER_TYPE_BINARY: 二进制格式
- FILE_TRANSFER_TYPE_ASCII: ASCII码格式

### FtpMode
- ACTIVE_MODE: 主动模式
- POSITIVE_MODE: 被动模式

### SFTPAuthType
- SFTP_AUTH_TYPE_PASSWORD: 密码认证
- SFTP_AUTH_TYPE_PRIVATE_KEY: 私钥认证
- SFTP_AUTH_TYPE_KEYBOARD_INTERACTIVE: 键盘交互认证

### 枚举类型注意事项

1. **FileTransferType使用场景**: 二进制模式(BINARY)适用于所有类型文件，尤其是非文本文件；ASCII模式仅适用于纯文本文件，且可能在不同操作系统间自动转换换行符。
2. **FtpMode选择建议**: 在大多数情况下，建议使用被动模式(POSITIVE_MODE)，特别是当客户端处于防火墙或NAT后面时；主动模式(ACTIVE_MODE)通常在特定网络环境下使用。
3. **SFTPAuthType兼容性**: 不同SFTP服务器可能支持不同的认证方式，在使用前应确认服务器支持的认证类型。
4. **私钥文件格式**: 使用SFTP_AUTH_TYPE_PRIVATE_KEY认证时，私钥文件必须是服务器能识别的格式，通常为OpenSSH格式。
5. **键盘交互认证限制**: SFTP_AUTH_TYPE_KEYBOARD_INTERACTIVE认证方式在当前实现中可能支持有限，需谨慎使用。

## 注意事项

1. **无自动重连功能**：当前的FTP和SFTP客户端实现没有提供自动重连功能。在网络不稳定的环境中，如果连接断开，需要应用代码自行处理重连逻辑。

2. **手动关闭连接**：使用完毕后必须手动调用`disconnect()`方法关闭连接，否则可能导致资源泄漏。建议在finally块中进行关闭操作，确保即使发生异常也能正确释放资源。

3. **连接状态管理**：在进行任何文件操作前，应先检查连接状态，可以使用`isConnected()`方法判断当前是否已连接。

4. **异常处理机制**：大多数方法会将底层异常包装为RuntimeException抛出，使用时应妥善捕获并处理异常。

5. **线程安全性**：FtpClient和SFTPClient的方法都使用了synchronized关键字确保方法级别的线程安全，但不保证实例级别的线程安全性。

6. **高频操作建议**：对于高频操作，建议保持连接状态而不是频繁地连接和断开，以提高性能。

7. **字符编码兼容性**：在处理包含非ASCII字符的文件名时，需确保设置正确的字符编码(encoding)以避免乱码问题。

8. **防火墙与NAT影响**：在企业网络环境中，防火墙和NAT设置可能影响FTP/SFTP连接，特别是主动模式的FTP连接。

## 使用示例

```java
// FTP客户端使用示例
// 创建连接配置
ConnectConfig ftpConfig = new ConnectConfig.Builder()
    .host("ftp.example.com")
    .port(21)
    .userName("username")
    .password("password")
    .connectTimeout(5000)
    .transferTimeout(30000)
    .transferFileType(FileTransferType.FILE_TRANSFER_TYPE_BINARY.getType())
    .mode(FtpMode.POSITIVE_MODE.getMode())
    .build();

// 创建FTP客户端
FtpClient ftpClient = new FtpClient();
ftpClient.init(ftpConfig);

try {
    // 连接到FTP服务器
    ftpClient.connect();
    
    // 上传文件
    boolean uploadSuccess = ftpClient.upload(
        "C:\\local\\path\\file.txt", 
        "/remote/directory/",
        false,  // 上传后不删除本地文件
        true    // 覆盖远程文件
    );
    System.out.println("Upload success: " + uploadSuccess);
    
    // 下载文件
    boolean downloadSuccess = ftpClient.download(
        "/remote/directory/file.txt", 
        "C:\\local\\download\\",
        false,  // 下载后不删除远程文件
        true    // 覆盖本地文件
    );
    System.out.println("Download success: " + downloadSuccess);
    
    // 列出目录文件
    List<String> files = ftpClient.listFiles("/remote/directory/");
    System.out.println("Files in directory: " + files);
    
    // 批量下载以.txt结尾的文件
    Map<String, Boolean> downloadResults = ftpClient.batchDownload(
        "/remote/directory/",
        fileName -> fileName.endsWith(".txt"),
        "C:\\local\\download\\"
    );
    System.out.println("Batch download results: " + downloadResults);
    
    // 删除远程文件
    boolean deleteSuccess = ftpClient.deleteRemoteFile("/remote/directory/file.txt");
    System.out.println("Delete success: " + deleteSuccess);
    
} catch (Exception e) {
    e.printStackTrace();
} finally {
    // 断开连接
    ftpClient.disconnect();
}

// SFTP客户端使用示例
// 创建连接配置
ConnectConfig sftpConfig = new ConnectConfig.Builder()
    .host("sftp.example.com")
    .port(22)
    .userName("username")
    .password("password")
    .connectTimeout(5000)
    .transferTimeout(30000)
    .authType(SFTPAuthType.SFTP_AUTH_TYPE_PASSWORD)
    .build();

// 使用私钥认证的配置
ConnectConfig sftpWithKeyConfig = new ConnectConfig.Builder()
    .host("sftp.example.com")
    .port(22)
    .userName("username")
    .privateKeyFilePath("/path/to/private/key")
    .authType(SFTPAuthType.SFTP_AUTH_TYPE_PRIVATE_KEY)
    .build();

// 创建SFTP客户端
SFTPClient sftpClient = new SFTPClient();
sftpClient.init(sftpConfig);

try {
    // 连接到SFTP服务器
    sftpClient.connect();
    
    // 创建远程目录
    boolean createDirSuccess = sftpClient.createRemoteDir("/remote/parent/", "newdir");
    System.out.println("Create directory success: " + createDirSuccess);
    
    // 批量上传所有非隐藏文件
    Map<String, Boolean> uploadResults = sftpClient.batchUpload(
        "C:\\local\\files\\",
        path -> !path.getFileName().toString().startsWith("."),
        "/remote/directory/"
    );
    System.out.println("Batch upload results: " + uploadResults);
    
    // 删除远程目录
    boolean deleteDirSuccess = sftpClient.deleteRemoteDir("/remote/directory/to/delete");
    System.out.println("Delete directory success: " + deleteDirSuccess);
    
} catch (Exception e) {
    e.printStackTrace();
} finally {
    // 断开连接
    sftpClient.disconnect();
}
```

## 异常处理

```java
// 异常情况处理示例
FtpClient ftpClient = new FtpClient();
ConnectConfig ftpConfig = new ConnectConfig.Builder()
    .host("ftp.example.com")
    .port(21)
    .userName("username")
    .password("password")
    .build();

ftpClient.init(ftpConfig);

try {
    ftpClient.connect();
    
    // 处理不存在的远程文件
    try {
        ftpClient.download("/non/existing/file.txt", "C:\\local\\path\\");
    } catch (IllegalArgumentException e) {
        System.out.println("Error: " + e.getMessage());  // 输出: Error: remote file not exist!
    }
    
    // 处理无效的本地文件路径
    try {
        ftpClient.upload("C:\\non\\existing\\file.txt", "/remote/directory/");
    } catch (IllegalArgumentException e) {
        System.out.println("Error: " + e.getMessage());  // 输出: Error: local localFile is not exist or is not a localFile!
    }
    
    // 处理远程文件已存在且不允许覆盖的情况
    try {
        ftpClient.upload("C:\\local\\file.txt", "/remote/directory/", false, false);
    } catch (IllegalArgumentException e) {
        System.out.println("Error: " + e.getMessage());  // 输出: Error: remote file is exist!
    }
    
} catch (RuntimeException e) {
    System.out.println("Connection error: " + e.getMessage());
} finally {
    ftpClient.disconnect();
}
``` 