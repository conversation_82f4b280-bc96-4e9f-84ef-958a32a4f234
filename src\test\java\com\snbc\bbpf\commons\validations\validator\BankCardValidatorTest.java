package com.snbc.bbpf.commons.validations.validator;

import com.snbc.bbpf.commons.validations.ValidException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
 import static org.junit.jupiter.api.Assertions.*;
@DisplayName("BankCardValidator测试")
class BankCardValidatorTest {
    private BankCardValidator validator;
     @BeforeEach
    public void setUp() {
        validator = new BankCardValidator();
    }
    @Test
    @DisplayName("输入非空")
    @Tags({
            @Tag("@id:36"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/7/16")
    })
    void testValidate_ValidBankCard() {
        try {
            validator.validate("****************", null);
        } catch (IllegalArgumentException e) {
            fail("验证应该通过对于一个有效的银行卡号");
        }
    }
    @Test
    @DisplayName("输入为空")
    @Tags({
            @Tag("@id:36"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/7/16")
    })
    void testValidate_NullBankCard() {
        assertThrows(IllegalArgumentException.class, () ->
                validator.validate(null, null));
    }
    @Test
    @DisplayName("输入为空字符串")
    @Tags({
            @Tag("@id:36"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/7/16")
    })
    void testValidate_EmptyBankCard() {
        assertThrows(IllegalArgumentException.class, () ->
                validator.validate("", null));
    }
    @Test
    @DisplayName("输入非数字")
    @Tags({
            @Tag("@id:36"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/7/16")
    })
    void testValidate_NonNumericBankCard() {
        assertThrows(IllegalArgumentException.class, () ->
                validator.validate("1234abcd5678", null));
    }
    @Test
    @DisplayName("输入不符合Luhm校验算法")
    @Tags({
            @Tag("@id:36"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/7/16")
    })
    void testValidate_InvalidBankCard() {
        assertThrows(IllegalArgumentException.class, () ->
                validator.validate("****************", null));
    }
}
