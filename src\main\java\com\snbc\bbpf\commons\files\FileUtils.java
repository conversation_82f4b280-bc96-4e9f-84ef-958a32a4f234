package com.snbc.bbpf.commons.files;

import org.apache.commons.lang3.StringUtils;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.file.FileVisitOption;
import java.nio.file.FileVisitResult;
import java.nio.file.Files;
import java.nio.file.OpenOption;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.SimpleFileVisitor;
import java.nio.file.StandardCopyOption;
import java.nio.file.StandardOpenOption;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.ArrayList;
import java.util.Collection;
import java.util.EnumSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;

/**
 * 文件处理工具类
 * <p>功能列表</p>
 * <p>1.读取指定文件</p>
 * <p>2.写入指定目录的文件</p>
 * <p>3.删除指定目录下符合条件的目录和文件</p>
 * <p>4.查找指定目录下符合条件的文件或目录列表</p>
 * <p>5.查询指定目录下文件数量，支持遍历查询子目录</p>
 * <p>6.重命名文件或目录</p>
 * <p>6.移动文件或路径下的文件或目录</p> 
 *
 * <p> {@link java.nio.file.Files} 中存在的方法，请直接使用</p>
 *
 * <AUTHOR>
 * @module 文件处理模块
 * @date 2023/8/25 20:10
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class FileUtils {


    private FileUtils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 读取指定文件返回byte数组
     *
     * @param filePath 文件路径
     * @return byte[]
     * @throws
     * <AUTHOR>
     * @date 2023/9/20
     * @since 1.2.0
     */
    public static byte[] read(String filePath) {
        File file = new File(filePath);
        if (!file.isFile()) {
            throw new IllegalArgumentException(filePath + " not a file");
        }
        byte[] buff = new byte[(int) file.length()];
        try (FileInputStream in = new FileInputStream(file)) {
            in.read(buff, 0, buff.length);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return buff;
    }

    /**
     * 读取指定文件返回String
     * <p>字符集取系统默认</p>
     *
     * @param filePath 文件路径
     * @return byte[]
     * @throws
     * <AUTHOR>
     * @date 2023/9/20
     * @since 1.2.0
     */
    public static String readString(String filePath) {
        return readString(filePath, Charset.defaultCharset());
    }

    /**
     * 读取指定文件返回String
     *
     * @param filePath 文件路径
     * @param charset  字符集
     * @return byte[]
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.2.0
     */
    public static String readString(String filePath, Charset charset) {
        return new String(read(filePath), charset);
    }


    /**
     * 向filePath中写入字节
     *
     * @param filePath 文件路径
     * @param context  写入的内容
     * @param isCreate 如果为true,文件不存在则创建文件，如果为false,不创建文件
     * @param isAppend 是否追加
     * @return
     * <AUTHOR>
     * @date 2023/9/20
     * @since 1.2.0
     */
    public static void write(String filePath, byte[] context, boolean isCreate, boolean isAppend) {
        try {
            List<OpenOption> options = new ArrayList<>();
            options.add(StandardOpenOption.WRITE);
            if (isCreate) {
                options.add(StandardOpenOption.CREATE);
                File file = new File(filePath);
                if (!file.getParentFile().exists()) {
                    file.getParentFile().mkdirs();
                }
                if (!file.exists() && !file.createNewFile()) {
                    throw new RuntimeException("file is create fail");
                }
            }
            if (isAppend) {
                options.add(StandardOpenOption.APPEND);
            }
            OpenOption[] openOptions = new OpenOption[options.size()];
            Files.write(Paths.get(filePath), context, options.toArray(openOptions));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 向filePath中写入字符串
     * <p>默认从文本开始位置复制内容，如果文件不存在，则创建文件</p>
     * <p>字符集采用系统默认字符集</p>
     *
     * @param filePath 文件路径
     * @param context  写入的内容
     * @return
     * <AUTHOR>
     * @date 2023/9/20
     * @since 1.2.0
     */
    public static void write(String filePath, String context) {
        write(filePath, context, true, false, Charset.defaultCharset());
    }

    /**
     * 向filePath中写入字符串
     * <p>默认从文本开始位置复制内容，如果文件不存在，则创建文件</p>
     *
     * @param filePath 文件路径
     * @param context  写入的内容
     * @param charset  字符集
     * @return
     * <AUTHOR>
     * @date 2023/9/20
     * @since 1.2.0
     */
    public static void write(String filePath, String context, Charset charset) {
        write(filePath, context, true, false, charset);
    }

    /**
     * 向filePath中写入字节
     * <p>默认从文本开始位置复制内容，如果文件不存在，则创建文件</p>
     *
     * @param filePath 文件路径
     * @param context  写入的内容
     * @return
     * <AUTHOR>
     * @date 2023/9/20
     * @since 1.2.0
     */
    public static void write(String filePath, byte[] context) {
        write(filePath, context, true, false);
    }

    /**
     * 向filePath中写入字节
     * <p>默认不创建文件，只追加</p>
     *
     * @param filePath 文件路径
     * @param context  写入的内容
     * @return
     * <AUTHOR>
     * @date 2023/9/20
     * @since 1.2.0
     */
    public static void writeAppend(String filePath, byte[] context) {
        write(filePath, context, false, true);
    }

    /**
     * 向filePath中追加字符串
     * <p>默认不创建文件，只追加</p>
     *
     * @param filePath 文件路径
     * @param context  写入的内容
     * @return
     * <AUTHOR>
     * @date 2023/9/20
     * @since 1.2.0
     */
    public static void writeAppend(String filePath, String context) {
        write(filePath, context, false, true, Charset.defaultCharset());
    }

    /**
     * 向filePath中写入字符串
     * <p>向filePath中写入字符串</p>
     * <p>编码取系统默认编码</p>
     *
     * @param filePath 文件路径
     * @param context  写入的内容
     * @param isCreate 如果为true,文件不存在则创建文件，如果为false,不创建文件
     * @param isAppend 是否追加，如果不追加，则会从0还是覆盖
     * @return
     * <AUTHOR>
     * @date 2023/9/20
     * @since 1.2.0
     */
    public static void write(String filePath, String context, boolean isCreate, boolean isAppend) {
        write(filePath, context, isCreate, isAppend, Charset.defaultCharset());
    }

    /**
     * 向filePath中写入字符串
     *
     * @param filePath 文件路径
     * @param context  写入的内容
     * @param isCreate 如果为true,文件不存在则创建文件，如果为false,不创建文件
     * @param isAppend 是否追加
     * @param charset  筛选目录条件
     * @return
     * <AUTHOR>
     * @date 2023/9/20
     * @since 1.2.0
     */
    public static void write(String filePath, String context, boolean isCreate, boolean isAppend, Charset charset) {
        write(filePath, context.getBytes(charset), isCreate, isAppend);
    }

    /**
     * 删除文件路径下的符合条件所有目录或文件
     *
     * @param filePath      文件路径
     * @param filePredicate 筛选文件条件
     * @param dirPredicate  筛选目录条件
     * @return
     * <AUTHOR>
     * @date 2023/9/20
     * @since 1.2.0
     */
    public static void delete(String filePath, Predicate<Path> filePredicate, Predicate<Path> dirPredicate) {
        try {
            Files.walkFileTree(Paths.get(filePath), new SimpleFileVisitor<Path>() {
                @Override
                public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                    if (filePredicate.test(file)) {
                        Files.delete(file);
                    }
                    return FileVisitResult.CONTINUE;
                }

                @Override
                public FileVisitResult postVisitDirectory(Path dir, IOException exc) throws IOException {
                    if (exc == null) {
                        if (dirPredicate.test(dir)) {
                            Files.delete(dir);
                        }
                        return FileVisitResult.CONTINUE;
                    } else {
                        throw exc;
                    }
                }
            });
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 删除文件路径下的符合条件所有目录或文件
     *
     * @param filePath  文件路径
     * @param predicate 筛选的条件,文件和目录自行判断如何处理
     * @return
     * <AUTHOR>
     * @date 2023/9/20
     * @since 1.2.0
     */
    public static void delete(String filePath, Predicate<Path> predicate) {
        delete(filePath, predicate, predicate);
    }

    /**
     * 删除文件路径下的所有目录或文件
     *
     * @param filePath 文件路径
     * @return
     * <AUTHOR>
     * @date 2023/9/20
     * @since 1.2.0
     */
    public static void delete(String filePath) {
        delete(filePath, file -> true, dir -> true);
    }

    /**
     * 查找指定目录下符合条件的文件或目录列表
     *
     * @param filePath  文件路径
     * @param predicate 筛选的条件 如果为空则返回所有文件
     * @return 筛选出来的文件或目录
     * <AUTHOR>
     * @date 2023/9/20
     * @since 1.2.0
     */
    public static List<Path> filter(String filePath, Predicate<Path> predicate) {
        List<Path> paths = new LinkedList<>();
        try {
            Files.walkFileTree(Paths.get(filePath), new SimpleFileVisitor<Path>() {
                @Override
                public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                    if (predicate==null||predicate.test(file)) {
                        paths.add(file);
                    }
                    return FileVisitResult.CONTINUE;
                }

                @Override
                public FileVisitResult postVisitDirectory(Path dir, IOException exc) throws IOException {
                    if (exc == null) {
                        if (predicate==null||predicate.test(dir)) {
                            paths.add(dir);
                        }
                        return FileVisitResult.CONTINUE;
                    } else {
                        throw exc;
                    }
                }
            });
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return paths;
    }


    /**
     * 把字符串list写入到文件中
     *
     * @param file
     * @param lines
     * @throws IOException
     * <AUTHOR>
     * @date 2023/11/1
     * @since 1.3.0
     */
    public static void writeLines(File file, Collection<String> lines) {
        try (BufferedWriter writer = Files.newBufferedWriter(file.toPath())) {
            if (lines != null) {
                for (String line : lines) {
                    writer.write(line);
                    writer.newLine();
                }
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 读取文件到字符串list
     *
     * @param file
     * @throws IOException
     * <AUTHOR>
     * @date 2023/11/1
     * @since 1.3.0
     */
    public static List<String> readLines(File file) {
        List<String> lines = new ArrayList<>();
        try (BufferedReader reader = Files.newBufferedReader(file.toPath())) {
            String line = null;
            while ((line = reader.readLine()) != null) {
                lines.add(line);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return lines;
    }

    /**
     * 查询指定目录下文件数量，遍历所有子目录
     *
     * @param fileDirectory 文件路径
     * @return int
     * @throws
     * <AUTHOR>
     * @date 2024/6/11
     * @since 1.5.0
     */
    public static int countFiles(String fileDirectory) {

        return countFiles(fileDirectory, Integer.MAX_VALUE);
    }

    /**
     * 查询指定目录下文件数量，遍历指定深度的文件目录
     *
     * @param fileDirectory 文件路径
     * @param maxDepth      允许访问的目录最大深度 0-主目录 n-读取到指定深度 n>=0
     * @return int
     * @throws
     * <AUTHOR>
     * @date 2024/6/11
     * @since 1.5.0
     */
    public static int countFiles(String fileDirectory, int maxDepth) {

        //文件数量
        final int[] count = {0};
        Path path = Paths.get(fileDirectory);
        try {

            //遍历文件树 若为文件则计数+1
            Files.walkFileTree(path, EnumSet.noneOf(FileVisitOption.class), maxDepth,
                    new SimpleFileVisitor<Path>() {
                        @Override
                        public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) {
                            count[0]++;
                            return FileVisitResult.CONTINUE;
                        }

                    });
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return count[0];
    }

    /**
     * 移动文件或目录
     *  默认覆盖同名文件
     * @param source 文件或目录路径
     * @param destination    目标文件路径，如果为文件，则将原目标重命名为此文件名
     * @throws IOException
     * <AUTHOR>
     * @date 2024/6/11
     * @since 1.5.0
     */
    public static void move(String source, String destination) throws IOException {
        move(source, destination, true);
    }

    /**
     * 移动文件或路径下的文件或目录 移动
     *
     * @param source 文件或目录路径
     * @param destination    目标文件路径，如果为文件，则将原目标重命名为此文件名
     * @param isOverwrite   如果遇到文件名相同的文件，是否覆盖
     * @throws IOException
     * <AUTHOR>
     * @date 2024/6/11
     * @since 1.5.0
     */
    public static void move(String source, String destination, boolean isOverwrite) throws IOException {
        move(source, destination, isOverwrite, false);
    }

    /**
     * 移动文件或路径下的文件或目录 移动
     *
     * @param source 文件或目录路径
     * @param destination    目标文件路径，如果为文件，则将原目标重命名为此文件名
     * @param isOverwrite   如果遇到文件名相同的文件，是否覆盖
     * @param isUnwind  isUnwind为true时，当source和destination同时为目录，则将source下的文件移动到destination
     * @throws IOException
     * <AUTHOR>
     * @date 2024/6/11
     * @since 1.5.0
     */
    public static void move(String source, String destination, boolean isOverwrite, boolean isUnwind) throws IOException {
        File sourceFile = new File(source);
        File destinationFile = new File(destination);
        if(!isUnwind && destinationFile.isDirectory()){
            destinationFile = new File(destination,sourceFile.getName());
        }
        if(isUnwind && sourceFile.isDirectory() && destinationFile.isDirectory() &&  sourceFile.listFiles()!=null){
            // 把source目录下的所有文件移动到destination目录下
            for (File file : Objects.requireNonNull(sourceFile.listFiles())) {
                moveOverwrite(file.toPath(), new File(destination,file.getName()).toPath(), isOverwrite);
            }
            // 同时删除原来的目录
            Files.delete(sourceFile.toPath());
        }else{
            // 把source文件移动到destination目录下
            moveOverwrite(sourceFile.toPath(), destinationFile.toPath(), isOverwrite);
        }

    }

    /**
     * 移动文件或路径下的文件或目录 移动
     * 通过添加StandardCopyOption.REPLACE_EXISTING属性实现覆盖
     * @param source 源目录路径
     * @param destination    目标文件路径
     * @param isOverwrite   如果遇到文件名相同的文件，是否覆盖
     * @throws IOException 当移动出错时，抛出IOException
     * <AUTHOR>
     * @date 2024/6/11
     * @since 1.5.0
     */
    private static void moveOverwrite(Path source, Path destination, boolean isOverwrite) throws IOException {
        if(isOverwrite){
            Files.move(source, destination, StandardCopyOption.REPLACE_EXISTING);
        }else{
            Files.move(source, destination);
        }
    }
    
    /**
     * 重命名文件或目录
     * 如果源文件soruce不存在或者修改的名称updateName为空，抛出IllegalArgumentException异常
     * @param soruce 需要修改的文件或文件路径
     * @param updateName   新的目录名或文件名
     * @return boolean true-重命名成功 false-重命名失败
     * @throws IllegalArgumentException
     * <AUTHOR>
     * @date 2024/6/17
     * @since 1.5.0
     */
    public static boolean rename(String soruce, String updateName) {
        File source = new File(soruce);
        if(!source.exists()){
            throw new IllegalArgumentException("source is not exist");
        }
        if(StringUtils.isBlank(updateName)){
            throw new IllegalArgumentException("updateName is null or empty");
        }
        File update = new File(source.getParent(), updateName);
        return source.renameTo(update);
    }
}
