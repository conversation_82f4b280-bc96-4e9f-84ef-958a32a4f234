/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.validations.validator;

import com.snbc.bbpf.commons.strings.StringEmptyCheck;
import com.snbc.bbpf.commons.validations.ValidException;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

/**
 * 邮箱格式校验
 *
 * <AUTHOR>
 * @module 字符串校验模块
 * @date 2023/7/16
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class EmailValidator implements IStringValidator {
    public static final Pattern EMAIL_PATTERN = Pattern.compile("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}");

    /**
     * 验证邮箱格式
     *
     * @param email   邮箱字符串
     * @param message 错误消息
     * @throws ValidException 如果邮箱为空或格式错误
     * <AUTHOR>
     * @date 2023/7/16
     * @since 1.1.0
     */
    @Override
    public void validate(String email, String message) throws IllegalArgumentException {
        if (StringUtils.isEmpty(email)) {
            throw new IllegalArgumentException("Email is empty or null");
        }
        // 验证邮箱格式
        if (!EMAIL_PATTERN.matcher(email).matches()) {
            throw new IllegalArgumentException(!StringEmptyCheck.isEmpty(message) ? message : "Email format error");
        }
    }
}
