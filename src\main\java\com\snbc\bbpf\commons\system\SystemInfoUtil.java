/*
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.commons.system;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.lang.management.RuntimeMXBean;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Enumeration;
import java.util.List;

import com.sun.management.OperatingSystemMXBean;

import java.lang.management.ManagementFactory;
import java.util.Locale;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * SystemInfoUtil 类提供了获取系统信息的各种静态方法，
 * 包括操作系统信息、硬件资源（如内存和CPU）状态、网络配置详情及系统运行时长等。
 * 这些方法旨在帮助进行系统监控、资源管理和故障排查。
 *
 * <AUTHOR>
 * @version 1.5.0
 * @since 2024-06-11
 */
public class SystemInfoUtil {

    public static final int BYTE_LENGHT = 1024;
    private static final int EXECUTOR_SERVICE_SIZE = 1; // 增加线程池大小常量
    private static final ExecutorService executorService = Executors.newFixedThreadPool(EXECUTOR_SERVICE_SIZE);
    public static final int TIMEOUT = 5;

    /**
     * 获取操作系统名称。
     *
     * @return 操作系统名称
     * <AUTHOR>
     * @date 2024/06/11
     * @since 1.5.0
     */
    public static String getOsName() {
        return System.getProperty("os.name");
    }

    /**
     * 获取操作系统版本。
     *
     * @return 操作系统版本
     * <AUTHOR>
     * @date 2024/06/11
     * @since 1.5.0
     */
    public static String getOsVersion() {
        return System.getProperty("os.version");
    }

    /**
     * 获取系统架构。
     *
     * @return 系统架构
     * <AUTHOR>
     * @date 2024/06/11
     * @since 1.5.0
     */
    public static String getOsArchitecture() {
        return System.getProperty("os.arch");
    }

    /**
     * 获取Java虚拟机总内存（MB）。
     *
     * @return 总内存大小（MB）
     * <AUTHOR>
     * @date 2024/06/11
     * @since 1.5.0
     */
    public static long getTotalMemoryInMB() {
        return Runtime.getRuntime().totalMemory() / (BYTE_LENGHT * BYTE_LENGHT);
    }

    /**
     * 获取Java虚拟机可用内存（MB）。
     *
     * @return 可用内存大小（MB）
     * <AUTHOR>
     * @date 2024/06/11
     * @since 1.5.0
     */
    public static long getFreeMemoryInMB() {
        return Runtime.getRuntime().freeMemory() / (BYTE_LENGHT * BYTE_LENGHT);
    }

    /**
     * 获取Java虚拟机最大可分配内存（MB）。
     *
     * @return 最大内存大小（MB）
     * <AUTHOR>
     * @date 2024/06/11
     * @since 1.5.0
     */
    public static long getMaxMemoryInMB() {
        return Runtime.getRuntime().maxMemory() / (BYTE_LENGHT * BYTE_LENGHT);
    }

    /**
     * 获取CPU核心数。
     *
     * @return CPU核心数
     * <AUTHOR>
     * @date 2024/06/11
     * @since 1.5.0
     */
    public static int getCpuCoreCount() {
        OperatingSystemMXBean osBean = (OperatingSystemMXBean) ManagementFactory.getOperatingSystemMXBean();
        return osBean.getAvailableProcessors();
    }

    /**
     * 获取CPU使用率。
     * 注意：此功能需要Java 1.7及以上版本，并且JVM需支持com.sun.management.OperatingSystemMXBean。
     *
     * @return CPU使用率（0.0到1.0之间的小数）
     * <AUTHOR>
     * @date 2024/06/11
     * @since 1.5.0
     */
    public static double getCpuUsage() {
        OperatingSystemMXBean osBean = (OperatingSystemMXBean) ManagementFactory.getOperatingSystemMXBean();
        if (osBean != null) {
            return osBean.getSystemCpuLoad();
        } else {
            throw new UnsupportedOperationException("Unsupported get cpu usage ");
        }
    }

    /**
     * 获取主机名
     *
     * @return 主机名字符串
     * @throws UnknownHostException 如果获取主机名失败
     * <AUTHOR>
     * @date 2024/06/11
     * @since 1.5.0
     */
    public static String getHostName() throws UnknownHostException {
        try {
            return InetAddress.getLocalHost().getHostName();
        } catch (UnknownHostException e) {
            // 当无法解析主机名时，返回IP地址或默认值
            try {
                return InetAddress.getLocalHost().getHostAddress();
            } catch (UnknownHostException ex) {
                return "localhost";
            }
        }
    }

    /**
     * 获取所有网络接口的MAC地址和IP地址
     *
     * @return 包含网络接口MAC地址和IP地址的列表
     * @throws SocketException 如果获取网络接口失败
     * <AUTHOR>
     * @date 2024/06/11
     * @since 1.5.0
     */
    public static List<NetworkInterfaceInfo> getNetworkInterfaces() throws SocketException {
        List<NetworkInterfaceInfo> interfaceInfos = new ArrayList<>();
        Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
        for (NetworkInterface networkInterface : Collections.list(interfaces)) {
            byte[] mac = networkInterface.getHardwareAddress();
            if (mac != null) {
                StringBuilder macAddress = new StringBuilder();
                for (int i = 0; i < mac.length; i++) {
                    macAddress.append(String.format("%02X%s", mac[i], (i < mac.length - 1) ? "-" : ""));
                }

                List<String> ipAddresses = new ArrayList<>();
                Enumeration<InetAddress> inetAddresses = networkInterface.getInetAddresses();
                for (InetAddress inetAddress : Collections.list(inetAddresses)) {
                    ipAddresses.add(inetAddress.getHostAddress());
                }

                interfaceInfos.add(new NetworkInterfaceInfo(networkInterface.getName(), macAddress.toString(), ipAddresses));
            }
        }
        return interfaceInfos;
    }

    /**
     * 获取系统信息方法。
     *
     * @return 系统信息字符串，若发生错误则返回错误信息。
     * <AUTHOR>
     * @date 2024/06/11
     * @since 1.5.0
     */
    public static String getSystemInfo() {
        OSType osName = getSystemType();
        try {
            Optional<Process> processOpt = getProcess(osName);
            Process process = processOpt.orElseThrow(() -> new RuntimeException("Failed to start system information retrieval process."));
            if (!process.waitFor(TIMEOUT, TimeUnit.SECONDS)) {
                process.destroyForcibly();
                throw new RuntimeException("Command timed out.");
            }
            int exitCode = process.exitValue();
            if (exitCode != 0) {
                throw new RuntimeException("Command execution failed with exit code: " + exitCode);
            }
            StringBuilder output = readAndProcessOutput(process);
            return output.toString();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // 重置中断状态
            throw new RuntimeException("Thread interrupted while waiting for process.", e);
        } catch (IOException e) {
            throw new RuntimeException("Error reading process output.", e);
        }
    }

    private static StringBuilder readAndProcessOutput(Process process) throws IOException {
        StringBuilder output = new StringBuilder();
        String encoding = System.getProperty("sun.jnu.encoding");
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), encoding))) {
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }
        }
        return output;
    }


    /**
     * 根据操作系统类型获取系统信息进程。
     * 本方法通过ProcessBuilder创建一个进程，用于执行特定于操作系统的命令，以获取系统信息。
     * 支持的操作系统类型包括Windows和Linux。对于不支持的操作系统，将抛出UnsupportedOperationException异常。
     *
     * @param osName 操作系统类型，使用OSType枚举定义。
     * @return 返回一个Process对象，代表执行系统命令的进程。
     * <AUTHOR>
     * @date 2024/06/11
     * @since 1.5.0
     */
    private static Optional<Process> getProcess(OSType osName) throws IOException {
        ProcessBuilder processBuilder;
        // 根据操作系统类型选择合适的命令
        if (osName == OSType.WINDOWS) {
            // 对于Windows系统，使用systeminfo命令获取系统信息
            processBuilder = new ProcessBuilder("systeminfo");
        } else if (osName == OSType.LINUX) {
            // 对于Linux系统，使用uname -a命令获取系统信息
            processBuilder = new ProcessBuilder("uname", "-a");
        } else {
            // 对于不支持的操作系统，直接返回空Optional
            return Optional.empty();
        }
        // 启动进程并返回
        return Optional.of(processBuilder.start());
    }


    /**
     * 获取系统运行时间（秒）
     *
     * @return 系统运行时间（秒）
     * <AUTHOR>
     * @date 2024/06/11
     * @since 1.5.0
     */
    public static long getUptime() {
        RuntimeMXBean runtimeBean = ManagementFactory.getRuntimeMXBean();
        return runtimeBean.getUptime();
    }

    /**
     * 获取系统的开机时间
     *
     * @return 系统的开机时间（Date对象）
     * <AUTHOR>
     * @date 2024/06/11
     * @since 1.5.0
     */
    public static LocalDateTime getBootTime() {
        long uptimeInSeconds = getUptime();
        Instant now = Instant.now();
        Instant bootInstant = now.minus(Duration.ofSeconds(uptimeInSeconds));
        return LocalDateTime.ofInstant(bootInstant, ZoneId.systemDefault());
    }

    /**
     * 获取系统类型（Windows或Unix/Linux/MacOS）
     *
     * @return 系统类型字符串
     * <AUTHOR>
     * @date 2024/06/11
     * @since 1.5.0
     */
    public static OSType getSystemType() {
        String os = getOsName().toLowerCase(Locale.getDefault());
        if (os.contains("win")) {
            return OSType.WINDOWS;
        } else if (os.contains("mac")) {
            return OSType.MACOS;
        } else if (os.contains("nix") || os.contains("nux")) {
            if (os.contains("linux")) {
                return OSType.LINUX;
            } else {
                return OSType.UNIX;
            }
        } else {
            return OSType.OTHER;
        }
    }


    /**
     * 获取指定的系统环境变量
     *
     * @param envVar 环境变量名称
     * @return 环境变量值，如果不存在则返回null
     * <AUTHOR>
     * @date 2024/06/11
     * @since 1.5.0
     */
    public static String getEnvironmentVariable(String envVar) {
        return System.getenv(envVar);
    }
}
