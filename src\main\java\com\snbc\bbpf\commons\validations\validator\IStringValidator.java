/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.validations.validator;

import com.snbc.bbpf.commons.validations.ValidException;

/**
 * 字符串验证接口
 * 该接口定义了字符串验证的方法。
 *
 * <AUTHOR>
 * @module 字符串校验模块
 * @date 2023/7/16
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public interface IStringValidator {
    /**
     * 验证字符串
     *
     * @param str 待验证的字符串
     * @param message 验证不通过时的提示信息
     * @throws ValidException 当验证不通过时抛出该异常
     * <AUTHOR>
     * @date 2023/7/16
     * @since 1.1.0
     */
    void validate(String str, String message) throws IllegalArgumentException;
}
