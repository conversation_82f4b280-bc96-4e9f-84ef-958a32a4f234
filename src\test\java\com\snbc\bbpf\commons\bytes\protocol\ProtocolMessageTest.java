package com.snbc.bbpf.commons.bytes.protocol;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.nio.charset.StandardCharsets;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 协议消息编解码测试类
 *
 * <AUTHOR>
 * @module 字节处理模块
 * @date 2025/07/25 16:30
 * @copyright 2024 山东新北洋信息技术股份有限公司. All rights reserved
 */
class ProtocolMessageTest {

    @Test
    @DisplayName("测试协议消息基本编解码")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testBasicProtocolMessage() {
        // 创建协议消息
        ProtocolMessage message = new ProtocolMessage((short) 101, 10000L, 1734058149000L);
        message.setTenantId("tenant_fjlweh");
        message.setProductId("");
        message.setAppKey("");
        message.setReservedInfo(new byte[0]);
        message.setMessageBody(new byte[0]);
        
        // 编码
        byte[] encoded = ProtocolMessageEncoder.encode(message);
        
        // 验证编码结果不为空
        assertNotNull(encoded);
        assertTrue(encoded.length >= ProtocolMessageEncoder.FIXED_HEADER_LENGTH);
        
        // 解码
        ProtocolMessage decoded = ProtocolMessageDecoder.decode(encoded);
        
        // 验证解码结果
        assertNotNull(decoded);
        assertEquals(message.getProtocolVersion(), decoded.getProtocolVersion());
        assertEquals(message.getMessageType(), decoded.getMessageType());
        assertEquals(message.getSequenceNumber(), decoded.getSequenceNumber());
        assertEquals(message.getTimestamp(), decoded.getTimestamp());
        assertEquals(message.getTenantId(), decoded.getTenantId());
        assertEquals(message.getProductId(), decoded.getProductId());
        assertEquals(message.getAppKey(), decoded.getAppKey());
        assertArrayEquals(message.getReservedInfo(), decoded.getReservedInfo());
        assertArrayEquals(message.getMessageBody(), decoded.getMessageBody());
    }

    @Test
    @DisplayName("测试完整协议消息编解码")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testCompleteProtocolMessage() {
        // 创建完整的协议消息
        ProtocolMessage message = new ProtocolMessage((short) 200, 12345L, System.currentTimeMillis());
        message.setTenantId("tenant_test_123");
        message.setProductId("product_abc");
        message.setAppKey("app_key_xyz");
        message.setReservedInfo("reserved".getBytes(StandardCharsets.UTF_8));
        message.setMessageBody("Hello World".getBytes(StandardCharsets.UTF_8));
        message.setChecksum("checksum".getBytes(StandardCharsets.UTF_8));
        
        // 编码
        byte[] encoded = ProtocolMessageEncoder.encode(message);
        
        // 解码
        ProtocolMessage decoded = ProtocolMessageDecoder.decode(encoded);
        
        // 验证所有字段
        assertEquals(message.getProtocolVersion(), decoded.getProtocolVersion());
        assertEquals(message.getMessageType(), decoded.getMessageType());
        assertEquals(message.getSequenceNumber(), decoded.getSequenceNumber());
        assertEquals(message.getTimestamp(), decoded.getTimestamp());
        assertEquals(message.getTenantId(), decoded.getTenantId());
        assertEquals(message.getProductId(), decoded.getProductId());
        assertEquals(message.getAppKey(), decoded.getAppKey());
        assertArrayEquals(message.getReservedInfo(), decoded.getReservedInfo());
        assertArrayEquals(message.getMessageBody(), decoded.getMessageBody());
        assertArrayEquals(message.getChecksum(), decoded.getChecksum());
    }

    @Test
    @DisplayName("测试空字段处理")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testEmptyFields() {
        // 创建只有必要字段的消息
        ProtocolMessage message = new ProtocolMessage((short) 300, 1L, 0L);
        
        // 编码
        byte[] encoded = ProtocolMessageEncoder.encode(message);
        
        // 解码
        ProtocolMessage decoded = ProtocolMessageDecoder.decode(encoded);
        
        // 验证空字段
        assertEquals("", decoded.getTenantId());
        assertEquals("", decoded.getProductId());
        assertEquals("", decoded.getAppKey());
        assertEquals(0, decoded.getReservedInfo().length);
        assertEquals(0, decoded.getMessageBody().length);
        assertEquals(0, decoded.getChecksum().length);
    }

    @Test
    @DisplayName("测试中文字符串处理")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testChineseCharacters() {
        // 创建包含中文的消息
        ProtocolMessage message = new ProtocolMessage((short) 400, 999L, System.currentTimeMillis());
        message.setTenantId("商户编号_测试");
        message.setProductId("产品编号_测试");
        message.setAppKey("应用编号_测试");
        message.setMessageBody("消息体内容：你好世界！".getBytes(StandardCharsets.UTF_8));
        
        // 编码
        byte[] encoded = ProtocolMessageEncoder.encode(message);
        
        // 解码
        ProtocolMessage decoded = ProtocolMessageDecoder.decode(encoded);
        
        // 验证中文字符串
        assertEquals(message.getTenantId(), decoded.getTenantId());
        assertEquals(message.getProductId(), decoded.getProductId());
        assertEquals(message.getAppKey(), decoded.getAppKey());
        assertArrayEquals(message.getMessageBody(), decoded.getMessageBody());
    }

    @Test
    @DisplayName("测试长度计算")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testLengthCalculation() {
        ProtocolMessage message = new ProtocolMessage((short) 500, 1000L, System.currentTimeMillis());
        message.setTenantId("test");
        message.setProductId("product");
        message.setAppKey("app");
        message.setReservedInfo(new byte[]{1, 2, 3});
        message.setMessageBody("body".getBytes(StandardCharsets.UTF_8));
        
        // 计算扩展消息头长度
        int extendedHeaderLength = message.calculateExtendedHeaderLength();
        
        // 验证长度计算
        int expectedLength = 0;
        expectedLength += 4 + "test".getBytes(StandardCharsets.UTF_8).length;      // tenantId
        expectedLength += 4 + "product".getBytes(StandardCharsets.UTF_8).length;  // productId
        expectedLength += 4 + "app".getBytes(StandardCharsets.UTF_8).length;      // appKey
        expectedLength += 4 + 3;                                                  // reservedInfo
        
        assertEquals(expectedLength, extendedHeaderLength);
        
        // 验证总长度
        int totalLength = message.getTotalLength();
        int expectedTotalLength = 31 + extendedHeaderLength + 4; // 固定头 + 扩展头 + 消息体
        assertEquals(expectedTotalLength, totalLength);
    }

    @Test
    @DisplayName("测试消息类型和序号提取")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testMessageTypeAndSequenceExtraction() {
        ProtocolMessage message = new ProtocolMessage((short) 600, 88888L, System.currentTimeMillis());
        message.setTenantId("test_tenant");
        
        // 编码
        byte[] encoded = ProtocolMessageEncoder.encode(message);
        
        // 提取消息类型和序号
        short extractedType = ProtocolMessageDecoder.extractMessageType(encoded);
        long extractedSequence = ProtocolMessageDecoder.extractSequenceNumber(encoded);
        
        assertEquals(600, extractedType);
        assertEquals(88888L, extractedSequence);
    }

    @Test
    @DisplayName("测试格式验证")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testFormatValidation() {
        // 有效消息
        ProtocolMessage message = new ProtocolMessage((short) 700, 1L, System.currentTimeMillis());
        byte[] validData = ProtocolMessageEncoder.encode(message);
        assertTrue(ProtocolMessageDecoder.validateFormat(validData));
        
        // 无效消息（长度不足）
        byte[] invalidData = new byte[10];
        assertFalse(ProtocolMessageDecoder.validateFormat(invalidData));
        
        // null数据
        assertFalse(ProtocolMessageDecoder.validateFormat(null));
    }

    @Test
    @DisplayName("测试异常情况")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testExceptionCases() {
        // 测试null消息编码
        assertThrows(IllegalArgumentException.class, () -> 
            ProtocolMessageEncoder.encode(null));
        
        // 测试无效数据解码
        assertThrows(IllegalArgumentException.class, () -> 
            ProtocolMessageDecoder.decode(new byte[10]));
        
        // 测试错误的消息标识
        byte[] invalidHeader = new byte[31];
        invalidHeader[0] = 'X'; // 错误的消息标识
        assertThrows(IllegalArgumentException.class, () -> 
            ProtocolMessageDecoder.decode(invalidHeader));
    }

    @Test
    @DisplayName("测试示例数据匹配")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testExampleDataMatch() {
        // 根据文档示例创建消息
        ProtocolMessage message = new ProtocolMessage((short) 101, 10000L, 1734058149000L);
        message.setTenantId("tenant_fjlweh");
        message.setProductId("");
        message.setAppKey("");
        message.setReservedInfo(new byte[0]);
        message.setMessageBody(new byte[0]);
        
        // 编码
        byte[] encoded = ProtocolMessageEncoder.encode(message);
        
        // 验证固定消息头的关键字节
        assertEquals('S', encoded[0]);  // 消息标识
        assertEquals('T', encoded[1]);
        assertEquals('U', encoded[2]);
        assertEquals('M', encoded[3]);
        assertEquals(1, encoded[4]);    // 协议版本
        
        // 验证扩展消息头长度（应该是29字节）
        int extendedHeaderLength = message.calculateExtendedHeaderLength();
        assertEquals(29, extendedHeaderLength);
    }

    @Test
    @DisplayName("测试实际消息数据解析 - 消息1")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testRealMessageData1() {
        // 第一个实际消息的十六进制数据
        String hexData = "53 54 55 4D 01 2F 01 0D 00 00 00 00 00 00 00 8A 58 BC 80 96 01 00 00 2F 00 00 00 6B 00 00 00 0D 00 00 00 74 65 6E 61 6E 74 5F 7A 73 69 74 76 71 00 00 00 00 12 00 00 00 30 35 35 64 64 62 66 64 64 63 39 34 34 38 63 39 62 64 00 00 00 00 0C 14 00 00 00 42 54 50 2D 4E 4C 35 38 32 30 31 31 30 30 31 30 30 30 30 31 04 08 00 00 00 00 B0 A2 C2 9C 7F F6 09 0C 2A 00 00 00 31 37 34 35 39 31 36 35 34 39 32 35 37 2D 2D E5 8D 87 E7 BA A7 E4 BF A1 E6 81 AF EF BC 9A E8 8E B7 E5 8F 96 E6 88 90 E5 8A 9F 03 04 00 00 00 00 00 00 00 04 08 00 00 00 89 58 BC 80 96 01 00 00";
        byte[] data = hexStringToByteArray(hexData);

        // 解码消息
        ProtocolMessage message = ProtocolMessageDecoder.decode(data);

        // 验证解码结果
        assertNotNull(message);
        assertEquals(1, message.getProtocolVersion());
        assertEquals(303, message.getMessageType()); // 0x012F = 303
        assertEquals(13, message.getSequenceNumber());
        assertEquals("tenant_zsitvq", message.getTenantId());
        assertEquals("", message.getProductId()); // 产品编号为空
        assertEquals("055ddbfddc9448c9bd", message.getAppKey()); // 这个字段实际是产品编号

        // 验证消息体内容包含中文
        String messageBodyStr = new String(message.getMessageBody(), StandardCharsets.UTF_8);
        assertTrue(messageBodyStr.contains("升级信息"));
        assertTrue(messageBodyStr.contains("获取成功"));
    }

    @Test
    @DisplayName("测试实际消息数据解析 - 消息2")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testRealMessageData2() {
        // 第二个实际消息的十六进制数据
        String hexData = "53 54 55 4D 01 2F 01 0E 00 00 00 00 00 00 00 DA DD BC 80 96 01 00 00 2F 00 00 00 74 00 00 00 0D 00 00 00 74 65 6E 61 6E 74 5F 7A 73 69 74 76 71 00 00 00 00 12 00 00 00 30 35 35 64 64 62 66 64 64 63 39 34 34 38 63 39 62 64 00 00 00 00 0C 14 00 00 00 42 54 50 2D 4E 4C 35 38 32 30 31 31 30 30 31 30 30 30 30 31 04 08 00 00 00 00 B0 A2 C2 9C 7F F6 09 0C 33 00 00 00 31 37 34 35 39 31 36 35 38 33 33 38 36 2D 2D E4 B8 8B E8 BD BD EF BC 9A E4 BA 91 E6 89 93 E5 8D B0 E5 8D 87 E7 BA A7 E5 8C 85 EF BC 9A E5 AE 8C E6 88 90 03 04 00 00 00 00 00 00 00 04 08 00 00 00 DA DD BC 80 96 01 00 00";
        byte[] data = hexStringToByteArray(hexData);

        // 解码消息
        ProtocolMessage message = ProtocolMessageDecoder.decode(data);

        // 验证解码结果
        assertNotNull(message);
        assertEquals(1, message.getProtocolVersion());
        assertEquals(303, message.getMessageType()); // 0x012F = 303
        assertEquals(14, message.getSequenceNumber());
        assertEquals("tenant_zsitvq", message.getTenantId());
        assertEquals("", message.getProductId()); // 产品编号为空
        assertEquals("055ddbfddc9448c9bd", message.getAppKey());

        // 验证消息体内容包含中文
        String messageBodyStr = new String(message.getMessageBody(), StandardCharsets.UTF_8);
        assertTrue(messageBodyStr.contains("下载"));
        assertTrue(messageBodyStr.contains("云打印升级包"));
        assertTrue(messageBodyStr.contains("完成"));
    }

    @Test
    @DisplayName("测试实际消息数据解析 - 消息3")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testRealMessageData3() {
        // 第三个实际消息的十六进制数据
        String hexData = "53 54 55 4D 01 2F 01 0F 00 00 00 00 00 00 00 4E DE BC 80 96 01 00 00 2F 00 00 00 74 00 00 00 0D 00 00 00 74 65 6E 61 6E 74 5F 7A 73 69 74 76 71 00 00 00 00 12 00 00 00 30 35 35 64 64 62 66 64 64 63 39 34 34 38 63 39 62 64 00 00 00 00 0C 14 00 00 00 42 54 50 2D 4E 4C 35 38 32 30 31 31 30 30 31 30 30 30 30 31 04 08 00 00 00 00 B0 A2 C2 9C 7F F6 09 0C 33 00 00 00 31 37 34 35 39 31 36 35 38 33 35 30 32 2D 2D E5 AE 89 E8 A3 85 EF BC 9A E4 BA 91 E6 89 93 E5 8D B0 E5 8D 87 E7 BA A7 E5 8C 85 EF BC 9A E6 88 90 E5 8A 9F 03 04 00 00 00 00 00 00 00 04 08 00 00 00 4E DE BC 80 96 01 00 00";
        byte[] data = hexStringToByteArray(hexData);

        // 解码消息
        ProtocolMessage message = ProtocolMessageDecoder.decode(data);

        // 验证解码结果
        assertNotNull(message);
        assertEquals(1, message.getProtocolVersion());
        assertEquals(303, message.getMessageType()); // 0x012F = 303
        assertEquals(15, message.getSequenceNumber());
        assertEquals("tenant_zsitvq", message.getTenantId());
        assertEquals("", message.getProductId()); // 产品编号为空
        assertEquals("055ddbfddc9448c9bd", message.getAppKey());

        // 验证消息体内容包含中文
        String messageBodyStr = new String(message.getMessageBody(), StandardCharsets.UTF_8);
        assertTrue(messageBodyStr.contains("安装"));
        assertTrue(messageBodyStr.contains("云打印升级包"));
        assertTrue(messageBodyStr.contains("成功"));
    }

    @Test
    @DisplayName("测试实际消息数据解析 - 消息4（复杂TLV消息体）")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testRealMessageData4() {
        // 第四个实际消息的十六进制数据（包含复杂的TLV结构消息体）
        String hexData = "53 54 55 4D 01 30 01 10 00 00 00 00 00 00 00 57 DE BC 80 96 01 00 00 2F 00 00 00 CA 00 00 00 0D 00 00 00 74 65 6E 61 6E 74 5F 7A 73 69 74 76 71 00 00 00 00 12 00 00 00 30 35 35 64 64 62 66 64 64 63 39 34 34 38 63 39 62 64 00 00 00 00 0C 14 00 00 00 42 54 50 2D 4E 4C 35 38 32 30 31 31 30 30 31 30 30 30 30 31 04 08 00 00 00 00 B0 A2 C2 9C 7F F6 09 03 04 00 00 00 01 00 00 00 03 04 00 00 00 01 00 00 00 04 08 00 00 00 B6 4E BC 80 96 01 00 00 04 08 00 00 00 57 DE BC 80 96 01 00 00 03 04 00 00 00 03 00 00 00 0C 0C 00 00 00 E5 8D 87 E7 BA A7 E6 88 90 E5 8A 9F 10 59 00 00 00 0F 54 00 00 00 04 08 00 00 00 00 D0 5A 5E 27 E8 F4 09 0C 1F 00 00 00 E4 BA 91 E6 89 93 E5 8D B0 E5 8D 87 E7 BA A7 E5 8C 85 20 E5 8D 87 E7 BA A7 E6 88 90 E5 8A 9F 03 04 00 00 00 00 00 00 00 04 08 00 00 00 D4 58 BC 80 96 01 00 00 04 08 00 00 00 53 DE BC 80 96 01 00 00";
        byte[] data = hexStringToByteArray(hexData);

        // 解码消息
        ProtocolMessage message = ProtocolMessageDecoder.decode(data);

        // 验证解码结果
        assertNotNull(message);
        assertEquals(1, message.getProtocolVersion());
        assertEquals(304, message.getMessageType()); // 0x0130 = 304
        assertEquals(16, message.getSequenceNumber());
        assertEquals("tenant_zsitvq", message.getTenantId());
        assertEquals("", message.getProductId()); // 产品编号为空
        assertEquals("055ddbfddc9448c9bd", message.getAppKey());

        // 验证消息体长度
        assertEquals(202, message.getBodyLength()); // 0xCA = 202
        assertTrue(message.getMessageBody().length > 0);

        // 这个消息体包含TLV结构，可以进一步解析
        byte[] messageBody = message.getMessageBody();
        assertTrue(messageBody.length >= 202);
    }

    /**
     * 将十六进制字符串转换为字节数组
     * @param hexString 十六进制字符串（可以包含空格）
     * @return 字节数组
     */
    private byte[] hexStringToByteArray(String hexString) {
        // 移除空格和换行符
        String cleanHex = hexString.replaceAll("\\s+", "");

        int len = cleanHex.length();
        byte[] data = new byte[len / 2];

        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(cleanHex.charAt(i), 16) << 4)
                                + Character.digit(cleanHex.charAt(i + 1), 16));
        }

        return data;
    }
}
