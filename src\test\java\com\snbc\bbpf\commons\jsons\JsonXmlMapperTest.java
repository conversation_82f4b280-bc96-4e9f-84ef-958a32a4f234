/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.jsons;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;


import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * JsonXml测试类
 *
 * <AUTHOR>
 * @module JSON模块
 * @date 2023-09-07 16:13
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */

class JsonXmlMapperTest {

    @DisplayName("测试json字符串为空")
    @Tags({
            @Tag("@id:54"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testUnmarshall_is_empty() {
        RuntimeException thrown = Assertions.assertThrows(RuntimeException.class,
                () -> JsonXmlMapper.marshall("", ContainerInfo.class));
        Assertions.assertNotNull(thrown);
    }
    @DisplayName("测试json字符串为null")
    @Tags({
            @Tag("@id:54"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testUnmarshall_is_null() {
        RuntimeException thrown = Assertions.assertThrows(RuntimeException.class,
                () -> JsonXmlMapper.marshall(null, ContainerInfo.class));
        Assertions.assertNotNull(thrown);
    }
    @DisplayName("测试json字符串错误")
    @Tags({
            @Tag("@id:54"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testUnmarshall_is_error() {
        final String jsonStr = "{\"id\":12,\"name\":\"新北洋1号售货机\",\"createTime\":\"2023-09-05 15:30:45\",\"updateTime\":\"2023-444409-05 15:30:45\",\"mark\":null,\"tags\":null,\"markDate\":1693899045000,\"other\":\"otherInfo\"}";
        RuntimeException thrown = Assertions.assertThrows(RuntimeException.class,
                () -> JsonXmlMapper.marshall(jsonStr, ContainerInfo.class));
        Assertions.assertNotNull(thrown);
    }
    @DisplayName("测试json字符串反序列化为XML字符串")
    @Tags({
            @Tag("@id:54"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testUnmarshall() {
        String jsonStr = "{\"id\":12,\"name\":\"新北洋1号售货机\",\"createTime\":\"2023-09-05 15:30:45\",\"updateTime\":\"2023-09-05 15:30:45\",\"mark\":null,\"tags\":null,\"markDate\":1693899045000,\"other\":\"otherInfo\"}";
        String xmlStr = "<ContainerInfo><id>12</id><name>新北洋1号售货机</name><createTime>2023-09-05 15:30:45</createTime><updateTime>2023-09-05 15:30:45</updateTime><mark/><markDate>1693899045000</markDate></ContainerInfo>";
        assertEquals(xmlStr, JsonXmlMapper.marshall(jsonStr, ContainerInfo.class));

    }


}

