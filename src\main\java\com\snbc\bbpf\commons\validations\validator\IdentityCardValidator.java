/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.validations.validator;

import com.snbc.bbpf.commons.dates.DateFormatUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.Locale;

/**
 * 验证身份证号码格式
 *
 * <AUTHOR>
 * @module 字符串校验模块
 * @date 2023/7/16
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class IdentityCardValidator implements IStringValidator {

    private static final int[] WEIGHTS = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
    private static final char[] CHECKBOOK = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};
    private static final int ID_CARD_LENGTH = 18;
    private static final int MIN_NUMERIC_VALUE = 1;
    private static final int MAX_NUMERIC_VALUE = 3;
    private static final int MODULUS_VALUE = 11;
    private static final int BIRTH_DATE_START_INDEX = 6;
    private static final int BIRTH_DATE_END_INDEX = 14;
    private static final int VALIDATION_START_INDEX = 0;
    private static final int VALIDATION_END_INDEX = 17;
    public static final int VALID_WEIGHT = 48;

    /*
    /**
     * 验证身份证号码格式
     *
     * @param idNum   身份证号码字符串
     * @param message 错误消息
     * @throws IllegalArgumentException 如果身份证号码为空或格式错误
     * <AUTHOR>
     * @date 2023/7/16
     * @since 1.1.0
     */
    @Override
    public void validate(String idNum, String message) throws IllegalArgumentException {
        if (StringUtils.isEmpty(idNum)) {
            throw new IllegalArgumentException("ID card number is empty or in wrong format");
        }
        if (!isIdCardValid(idNum)) {
            throw new IllegalArgumentException("ID card number format error");
        }
    }

    /**
     * 验证身份证号码
     *
     * @param idCard 身份证号
     * @throws IllegalArgumentException 如果身份证号码为空或格式错误
     * @return 返回是否满足要求
     * <AUTHOR>
     * @date 2023/11/7
     * @since 1.3.0
     */
    private static boolean isIdCardValid(String idCard) {
        idCard = idCard.toUpperCase(Locale.ROOT); // 明确指定字符集为大写
        if (idCard.length() != ID_CARD_LENGTH) {
            return false;
        }
        String dateStr = idCard.substring(BIRTH_DATE_START_INDEX, BIRTH_DATE_END_INDEX);
        try {
            DateFormatUtil.PURE_DATE_FORMATTER.parse(dateStr);
        } catch (Exception e) {
            throw new IllegalArgumentException("ID card number format error",e);
        }
        char[] number = idCard.toCharArray();
        for (int i = VALIDATION_START_INDEX; i < VALIDATION_END_INDEX; i++) {
            if (number[i] > CHECKBOOK[MAX_NUMERIC_VALUE] || number[i] < CHECKBOOK[MIN_NUMERIC_VALUE]) {
                return false;
            }
        }
        int sum = 0;
        for (int j = VALIDATION_START_INDEX; j < VALIDATION_END_INDEX; j++) {
            sum+=(number[j] - VALID_WEIGHT) * WEIGHTS[j];
        }

        int a = sum % MODULUS_VALUE;
        return number[VALIDATION_END_INDEX] == CHECKBOOK[a];
    }
}
