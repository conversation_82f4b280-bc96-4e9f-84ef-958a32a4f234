/*
 * 版权所有 2009-2025山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.strings;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.util.Locale;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * 字符串空或空白判断测试类
 *
 * <AUTHOR>
 * @module 字符串非空判断
 * @date 2025/6/5 19:09
 * @copyright 2025 山东新北洋信息技术股份有限公司. All rights reserved
 */
class StringFormatUtilsTest {

    // region formatBankCard 测试
    @Test
    @DisplayName("格式化银行卡号-空输入")
    @Tags({
            @Tag("@id:113"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/6/5")
    })
    void testFormatBankCard_emptyInput() {
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> StringFormatUtils.formatBankCard(""));
        assertEquals("Bank card number is empty or formatted incorrectly", thrown.getMessage());
        IllegalArgumentException thrown1 = assertThrows(IllegalArgumentException.class,
                () -> StringFormatUtils.formatBankCard(null));
        assertEquals("Bank card number is empty or formatted incorrectly", thrown1.getMessage());
    }

    @Test
    @DisplayName("格式化银行卡号-卡号格式错误")
    @Tags({
            @Tag("@id:113"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/6/5")
    })
    void testFormatBankCard_wrongFormat() {
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> StringFormatUtils.formatBankCard("****************"));
        assertEquals("The bank card format is incorrect", thrown.getMessage());
    }

    @Test
    @DisplayName("格式化银行卡号-自定义占位符（横线）")
    @Tags({
            @Tag("@id:113"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/6/5")
    })
    void formatBankCard_customPlaceholder() {
        assertEquals("6222--5210--3515--3430", StringFormatUtils.formatBankCard("****************", "--"));
        assertEquals("6217--8560--0007--5405--935", StringFormatUtils.formatBankCard("6217856000075405935", "--"));
    }
    // endregion

    // region formatIDCard 测试
    @Test
    @DisplayName("格式化身份证号-空输入")
    @Tags({
            @Tag("@id:113"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/6/5")
    })
    void testFormatIDCard_emptyInput() {
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> StringFormatUtils.formatIDCard(""));
        assertEquals("ID card number is empty or in wrong format", thrown.getMessage());
        IllegalArgumentException thrown1 = assertThrows(IllegalArgumentException.class,
                () -> StringFormatUtils.formatIDCard(null));
        assertEquals("ID card number is empty or in wrong format", thrown1.getMessage());
    }

    @Test
    @DisplayName("格式化身份证号-18位正常值使用默认空格")
    @Tags({
            @Tag("@id:113"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/6/5")
    })
    void testFormatIDCard_normalWithDefaultPlaceholder() {
        assertEquals("3710 8119 9303 0196 6X", StringFormatUtils.formatIDCard("37108119930301966X"));
    }

    @Test
    @DisplayName("格式化证件号（20位超长）")
    @Tags({
            @Tag("@id:113"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/6/5")
    })
    void testFormatIDCard_overLengthInput() {
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> StringFormatUtils.formatIDCard("****************7890"));
        assertEquals("ID card number format error", thrown.getMessage());
    }

    @Test
    @DisplayName("格式化身份证号-自定义占位符（点）")
    @Tags({
            @Tag("@id:113"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/6/5")
    })
    void formatIDCard_customPlaceholder() {
        assertEquals("3710.8119.9307.3000.10", StringFormatUtils.formatIDCard("371081199307300010", "."));
    }
    // endregion

    // region formatPhoneNumber 测试
    @Test
    @DisplayName("格式化手机号-空输入")
    @Tags({
            @Tag("@id:113"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/6/5")
    })
    void testFormatPhoneNumber_emptyInput() {
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> StringFormatUtils.formatPhoneNumber(""));
        assertEquals("Mobile number is empty or null", thrown.getMessage());
        IllegalArgumentException thrown1 = assertThrows(IllegalArgumentException.class,
                () -> StringFormatUtils.formatPhoneNumber(null));
        assertEquals("Mobile number is empty or null", thrown1.getMessage());
    }

    @Test
    @DisplayName("格式化手机号-11位正常值使用默认空格")
    @Tags({
            @Tag("@id:113"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/6/5")
    })
    void testFormatPhoneNumber_normalWithDefaultPlaceholder() {
        assertEquals("178 6272 4771", StringFormatUtils.formatPhoneNumber("17862724771"));
    }

    @Test
    @DisplayName("格式化手机号-超长输入（12位）")
    @Tags({
            @Tag("@id:113"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/6/5")
    })
    void testFormatPhoneNumber_overLengthInput() {
        IllegalArgumentException thrown1 = assertThrows(IllegalArgumentException.class,
                () -> StringFormatUtils.formatPhoneNumber("173063116611"));
        assertEquals("The phone number format is incorrect", thrown1.getMessage());
    }

    @Test
    @DisplayName("格式化手机号-过短输入（10位）")
    @Tags({
            @Tag("@id:113"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/6/5")
    })
    void testFormatPhoneNumber_underLengthInput() {
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> StringFormatUtils.formatPhoneNumber("1886311563"));
        assertEquals("The phone number format is incorrect", thrown.getMessage());
    }

    @Test
    @DisplayName("格式化手机号-自定义占位符（斜线）")
    @Tags({
            @Tag("@id:113"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/6/5")
    })
    void formatPhoneNumber_customPlaceholder() {
        assertEquals("188/6311/5639", StringFormatUtils.formatPhoneNumber("18863115639", "/"));
    }
    // endregion

    // region formatAmount 测试
    @Test
    @DisplayName("格式化金额-默认参数（默认中文环境）")
    @Tags({
            @Tag("@id:113"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/6/5")
    })
    void testFormatAmount_defaultParams() {
        assertEquals("1,234,567.89", StringFormatUtils.formatAmount(1234567.89));
    }

    @Test
    @DisplayName("格式化金额-显示货币符号+自定义单位")
    @Tags({
            @Tag("@id:113"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/6/5")
    })
    void testFormatAmount_showSymbolWithUnit() {
        String result = StringFormatUtils.formatAmount(Locale.US, 1234567.89, true, 0, false, ",", "美元");
        assertEquals("$1,234,567美元", result);
    }

    @Test
    @DisplayName("格式化金额-0值+默认参数")
    @Tags({
            @Tag("@id:113"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/6/5")
    })
    void testFormatAmount_zeroValue() {
        assertEquals("0.00", StringFormatUtils.formatAmount(0.0));
    }

    @Test
    @DisplayName("格式化金额-负数+四舍五入（保留2位）")
    @Tags({
            @Tag("@id:113"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/6/5")
    })
    void testFormatAmount_negativeWithRounding() {
        String result = StringFormatUtils.formatAmount(Locale.CHINA, -1234.567, false, 2, true, ",", "");
        assertEquals("-1,234.57", result);
    }

    @Test
    @DisplayName("格式化金额-保留1位小数")
    @Tags({
            @Tag("@id:113"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/6/5")
    })
    void testFormatAmount_oneDecimalPlace() {
        String result = StringFormatUtils.formatAmount(Locale.CHINA, 123.456, false, 1, true, ",", "");
        assertEquals("123.5", result);
    }

    @Test
    @DisplayName("格式化金额-保留3位小数")
    @Tags({
            @Tag("@id:113"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/6/5")
    })
    void testFormatAmount_threeDecimalPlaces() {
        String result = StringFormatUtils.formatAmount(Locale.CHINA, 123.4567, true, 3, true, ",", "");
        assertEquals("￥123.457", result);
    }

    @Test
    @DisplayName("格式化金额-负数+不四舍五入")
    @Tags({
            @Tag("@id:113"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/6/5")
    })
    void formatAmount_negativeNoRounding() {
        String result = StringFormatUtils.formatAmount(Locale.CHINA, -1234.567, false, 2, false, ",", "");
        assertEquals("-1,234.56", result);
    }
    // endregion

    // region formatFixedLine 测试
    @Test
    @DisplayName("格式化固话-空输入")
    @Tags({
            @Tag("@id:113"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/6/5")
    })
    void testFormatFixedLine_emptyInput() {
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> StringFormatUtils.formatFixedLine(""));
        assertEquals("Fixed line number is empty", thrown.getMessage());
        IllegalArgumentException thrown1 = assertThrows(IllegalArgumentException.class,
                () -> StringFormatUtils.formatFixedLine(null));
        assertEquals("Fixed line number is empty", thrown1.getMessage());
    }

    @Test
    @DisplayName("格式化固话-三位区号正常值")
    @Tags({
            @Tag("@id:113"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/6/5")
    })
    void testFormatFixedLine_threeDigitAreaCode() {
        assertEquals("010-12345678", StringFormatUtils.formatFixedLine("01012345678"));
    }

    @Test
    @DisplayName("格式化固话-四位区号但号码过短")
    @Tags({
            @Tag("@id:113"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/6/5")
    })
    void testFormatFixedLine_fourDigitAreaCodeShortNumber() {
        assertEquals("05318765", StringFormatUtils.formatFixedLineWithPlaceholder("05318765", "-"));
    }

    @Test
    @DisplayName("格式化固话-非标准区号格式保留原输入")
    @Tags({
            @Tag("@id:113"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/6/5")
    })
    void testFormatFixedLine_invalidAreaCode() {
        assertEquals("02012345", StringFormatUtils.formatFixedLine("02012345"));  // 020是三位区号但号码长度不足
    }

    @Test
    @DisplayName("格式化固话-四位区号自定义占位符")
    @Tags({
            @Tag("@id:113"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/6/5")
    })
    void formatFixedLine_fourDigitAreaCodeWithPlaceholder() {
        assertEquals("0531*8765432", StringFormatUtils.formatFixedLineWithPlaceholder("05318765432", "*"));
    }
    // endregion
}