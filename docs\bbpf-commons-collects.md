# 集合处理 (com.snbc.bbpf.commons.collects)

## 类概览 

| 类名 | 功能描述 |
|------|----------|
| ListUtils | 提供List集合操作功能，包括排序、筛选、转换等 |
| MapUtils | 提供Map集合操作功能，包括合并、转换、筛选等 |
| CollectFinder | 提供集合元素查找功能，包括完全匹配、部分匹配、正则匹配等 |
| CollectsUtils | 提供多个集合操作功能，包括交集、并集、差集、去重等 |
| ObjectArrayCheck | 提供对象数组判空功能，包括判断数组本身、元素、递归判断等 |
| ObjectArraySeparator | 提供对象集合和分隔符字符串互相转换功能 |

## ListUtils - List集合工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| listToJson(List<?> list) | 将List集合转换为JSON字符串 | list: 要转换的List集合 | String: 转换后的JSON字符串 |
| listToJson(List<?> list, List<SerializationFeature> enableFeatures, List<SerializationFeature> disableFeatures) | 按自定义特性将List集合转换为JSON字符串 | list: 要转换的List集合<br>enableFeatures: 允许的特性集合<br>disableFeatures: 禁止的特性集合 | String: 转换后的JSON字符串 |

### 注意事项

- 当传入的List为null时，listToJson方法将返回"null"字符串
- 内部使用Jackson处理序列化，需确保对象可被正确序列化
- 对于包含循环引用的对象需特别注意，可能导致StackOverflowError
- 自定义序列化特性时，需了解SerializationFeature枚举的含义，避免特性之间冲突

### 使用示例

```java
// 将List集合转换为JSON字符串
List<Person> people = Arrays.asList(
    new Person("Alice", 20),
    new Person("Bob", 30),
    new Person("Charlie", 20)
);
String json = ListUtils.listToJson(people);
System.out.println(json);  // 输出：[{"name":"Alice","age":20},{"name":"Bob","age":30},{"name":"Charlie","age":20}]

// 将List集合按自定义特性转换为JSON字符串
List<SerializationFeature> enableFeatures = Arrays.asList(SerializationFeature.INDENT_OUTPUT);
List<SerializationFeature> disableFeatures = Arrays.asList(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
String prettyJson = ListUtils.listToJson(people, enableFeatures, disableFeatures);
System.out.println(prettyJson);  // 输出格式化的JSON
```

## MapUtils - Map集合工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| objectToMap(Object object) | 将对象转成MAP对象 | object: 要转的对象 | Map<String, Object>: 返回MAP对象 |
| mapToClass(Map<String, Object> map, Class bean) | 将MAP转成对象(非严格模式) | map: 要转的Map对象<br>bean: 要转成的对象类 | <T> T: 返回转换后的对象 |
| mapToClassStrict(Map<String, Object> map, Class bean) | 将MAP转成对象(严格模式) | map: 要转的Map对象<br>bean: 要转成的对象类 | <T> T: 返回转换后的对象 |
| mapToJson(Map<String, ?> map) | Map转为JSON字符串 | map: 要转换的map | String: 转换后的JSON字符串 |
| mapToJson(Map<String, ?> map, List<SerializationFeature> enableFeatures, List<SerializationFeature> disableFeatures) | 按自定义特性将Map转为JSON字符串 | map: 要转换的map<br>enableFeatures: 允许的特性集合<br>disableFeatures: 禁止的特性集合 | String: 转换后的JSON字符串 |

### 注意事项

- objectToMap方法只转换对象的直接属性，不处理继承的属性
- objectToMap转换过程基于反射，性能可能受影响
- mapToClass方法采用非严格模式，允许Map中存在目标类没有的属性
- mapToClassStrict方法采用严格模式，要求Map的键必须与目标类的属性完全匹配
- 当转换复杂对象时可能需要自定义TypeReference

### 使用示例

```java
// 将对象转成MAP对象
Person person = new Person("Alice", 25);
Map<String, Object> personMap = MapUtils.objectToMap(person);
System.out.println(personMap);  // 输出：{name=Alice, age=25}

// 将MAP转成对象(非严格模式)
Map<String, Object> map = new HashMap<>();
map.put("name", "Bob");
map.put("age", 30);
map.put("extraField", "这个字段在Person类中不存在");
Person bob = MapUtils.mapToClass(map, Person.class);
System.out.println(bob.getName());  // 输出：Bob
System.out.println(bob.getAge());   // 输出：30

// 将MAP转成对象(严格模式)
Map<String, Object> strictMap = new HashMap<>();
strictMap.put("name", "Charlie");
strictMap.put("age", 35);
// 下面这行代码会抛出异常，因为strictMap的键与Person类的属性不完全匹配
// Person charlie = MapUtils.mapToClassStrict(strictMap, Person.class);

// Map转为JSON字符串
Map<String, Object> jsonMap = new HashMap<>();
jsonMap.put("name", "David");
jsonMap.put("age", 40);
String json = MapUtils.mapToJson(jsonMap);
System.out.println(json);  // 输出：{"name":"David","age":40}

// 按自定义特性将Map转为JSON字符串
List<SerializationFeature> enableFeatures = Arrays.asList(SerializationFeature.INDENT_OUTPUT);
List<SerializationFeature> disableFeatures = Arrays.asList(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
String prettyJson = MapUtils.mapToJson(jsonMap, enableFeatures, disableFeatures);
System.out.println(prettyJson);  // 输出格式化的JSON
```

## CollectFinder - 集合查找工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| existsExactMatch(Collection<T> collection, T match) | 查找集合中是否存在完全匹配的元素 | collection: 集合<br>match: 要匹配的元素 | boolean: 是否存在匹配 |
| existsRegexMatch(Collection<String> collection, String regex) | 对于String集合，使用正则表达式匹配 | collection: 字符串集合<br>regex: 正则表达式 | boolean: 是否存在匹配 |
| findPartialMatches(Collection<T> collection, Predicate<T> predicate) | 查找集合中符合条件的元素 | collection: 集合<br>predicate: 匹配条件 | List<T>: 匹配的元素列表 |

### 注意事项

- 使用existsExactMatch时，依赖于元素的equals方法实现
- existsRegexMatch方法对于大集合可能影响性能，因为每个元素都需要进行正则匹配
- 传入的正则表达式格式错误可能导致PatternSyntaxException
- findPartialMatches返回的是符合条件的所有元素的新列表，不会修改原集合
- 处理空集合时会返回空结果，不会抛出异常

### 使用示例

```java
// 完全匹配查找
List<Integer> numbers = Arrays.asList(1, 2, 3, 4, 5);
boolean hasThree = CollectFinder.existsExactMatch(numbers, 3);
System.out.println(hasThree);  // 输出：true

boolean hasSix = CollectFinder.existsExactMatch(numbers, 6);
System.out.println(hasSix);  // 输出：false

// 正则匹配查找
List<String> words = Arrays.asList("apple", "banana", "cherry");
boolean hasWordStartingWithA = CollectFinder.existsRegexMatch(words, "a.*");
System.out.println(hasWordStartingWithA);  // 输出：true

boolean hasWordEndingWithZ = CollectFinder.existsRegexMatch(words, ".*z");
System.out.println(hasWordEndingWithZ);  // 输出：false

// 部分匹配查找
List<Person> people = Arrays.asList(
    new Person("Alice", 25),
    new Person("Bob", 30),
    new Person("Charlie", 35)
);

// 查找年龄大于30的人
List<Person> olderPeople = CollectFinder.findPartialMatches(people, person -> person.getAge() > 30);
System.out.println(olderPeople.size());  // 输出：1
System.out.println(olderPeople.get(0).getName());  // 输出：Charlie

// 查找名字包含'e'的人
List<Person> peopleWithE = CollectFinder.findPartialMatches(people, person -> person.getName().contains("e"));
System.out.println(peopleWithE.size());  // 输出：2
```

## CollectsUtils - 多集合操作工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| intersection(List<T>... collections) | 对多个集合进行交集操作 | collections: 多个集合 | List<T>: 交集结果 |
| union(List<T>... collections) | 对多个集合进行并集操作 | collections: 多个集合 | List<T>: 并集结果 |
| removeElements(List<T> listA, List<T> listB) | 从集合A中移除集合B中的元素 | listA: 集合A<br>listB: 集合B | List<T>: 移除后的集合 |
| distinct(List<T> list) | 去掉列表中重复数据 | list: 原始集合 | List<T>: 去重后的集合 |

### 注意事项

- 交集和并集操作基于元素的equals和hashCode方法，确保这些方法正确实现
- 对于null值的处理：方法参数可以包含null集合，会自动忽略
- 返回的集合是新创建的，不会修改原集合
- 集合操作可能改变元素的顺序，如果顺序很重要，可能需要额外排序
- distinct方法保持元素的原有顺序

### 使用示例

```java
// 多集合交集
List<Integer> list1 = Arrays.asList(1, 2, 3, 4);
List<Integer> list2 = Arrays.asList(3, 4, 5, 6);
List<Integer> list3 = Arrays.asList(3, 4, 7, 8);

List<Integer> intersection = CollectsUtils.intersection(list1, list2, list3);
System.out.println(intersection);  // 输出：[3, 4]

// 处理空集合
List<Integer> intersection2 = CollectsUtils.intersection(list1, null, list3);
System.out.println(intersection2);  // 输出：[3, 4] (忽略null集合)

// 多集合并集
List<Integer> union = CollectsUtils.union(list1, list2, list3);
System.out.println(union);  // 输出：[1, 2, 3, 4, 5, 6, 7, 8]

// 集合差集操作
List<Integer> listA = Arrays.asList(1, 2, 3, 4, 5);
List<Integer> listB = Arrays.asList(3, 4, 5);
List<Integer> difference = CollectsUtils.removeElements(listA, listB);
System.out.println(difference);  // 输出：[1, 2]

// 处理空集合
List<Integer> difference2 = CollectsUtils.removeElements(null, listB);
System.out.println(difference2);  // 输出：[] (空集合)

List<Integer> difference3 = CollectsUtils.removeElements(listA, null);
System.out.println(difference3);  // 输出：[1, 2, 3, 4, 5] (返回原集合)

// 集合去重
List<Integer> duplicates = Arrays.asList(1, 1, 2, 2, 3, 3);
List<Integer> distinct = CollectsUtils.distinct(duplicates);
System.out.println(distinct);  // 输出：[1, 2, 3]
```

## ObjectArrayCheck - 对象数组判空工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| isNull(Object[] objs) | 判断对象数组本身是否为空 | objs: 对象数组 | boolean: 是否为空 |
| isEmpty(Object[] objs) | 判断对象数组是否为空或长度为0 | objs: 对象数组 | boolean: 是否为空或长度为0 |
| hasNullItem(Object[] objs) | 判断对象数组是否包含空元素 | objs: 对象数组 | boolean: 是否包含空元素 |
| hasEmptyItem(Object[] objs) | 判断对象数组是否包含空元素(包括空字符串、空集合等) | objs: 对象数组 | boolean: 是否包含空元素 |
| isNull(Object obj) | 递归判断对象是否为空(递归判断数组、集合、Map类型) | obj: 对象 | boolean: 是否为空 |

### 注意事项

- isNull和isEmpty方法的区别：isNull检查对象数组引用是否为null，isEmpty同时检查长度是否为0
- hasNullItem和hasEmptyItem方法的区别：前者只检查null元素，后者还检查空字符串、空集合等
- hasEmptyItem递归检查嵌套数组/集合，可能导致性能开销
- 针对大型嵌套结构使用递归判断方法时要注意潜在的堆栈溢出风险
- isNull(Object obj)方法对于自定义对象，依赖类型判断和递归检查

### 使用示例

```java
// 判断对象数组本身是否为空
Object[] array1 = new Object[]{"test"};
boolean isNull1 = ObjectArrayCheck.isNull(array1);
System.out.println(isNull1);  // 输出：false

Object[] array2 = null;
boolean isNull2 = ObjectArrayCheck.isNull(array2);
System.out.println(isNull2);  // 输出：true

// 判断对象数组是否为空或长度为0
Object[] array3 = new Object[]{};
boolean isEmpty1 = ObjectArrayCheck.isEmpty(array3);
System.out.println(isEmpty1);  // 输出：true

Object[] array4 = new Object[]{"test"};
boolean isEmpty2 = ObjectArrayCheck.isEmpty(array4);
System.out.println(isEmpty2);  // 输出：false

// 判断对象数组是否包含空元素
Object[] array5 = new Object[]{"test", null, "example"};
boolean hasNull = ObjectArrayCheck.hasNullItem(array5);
System.out.println(hasNull);  // 输出：true

Object[] array6 = new Object[]{"test", "example"};
boolean hasNull2 = ObjectArrayCheck.hasNullItem(array6);
System.out.println(hasNull2);  // 输出：false

// 递归判断
Object[] nested = new Object[]{"test", new Object[]{null}};
boolean hasNull3 = ObjectArrayCheck.hasNullItem(nested);
System.out.println(hasNull3);  // 输出：true

// 判断对象数组是否包含空元素(包括空字符串、空集合等)
Object[] array7 = new Object[]{"test", ""};
boolean hasEmpty = ObjectArrayCheck.hasEmptyItem(array7);
System.out.println(hasEmpty);  // 输出：true

Object[] array8 = new Object[]{"test", new ArrayList<>()};
boolean hasEmpty2 = ObjectArrayCheck.hasEmptyItem(array8);
System.out.println(hasEmpty2);  // 输出：true
```

## ObjectArraySeparator - 对象集合与分隔符字符串转换工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| join(String[] objs, String separator) | 将字符串数组转换为带分隔符的字符串 | objs: 字符串数组<br>separator: 分隔符 | String: 转换后的字符串 |
| splitToString(String str, String separator) | 将带分隔符的字符串转换为字符串数组 | str: 带分隔符的字符串<br>separator: 分隔符 | String[]: 转换后的字符串数组 |
| join(List<String> lists, String separator) | 将字符串列表转换为带分隔符的字符串 | lists: 字符串列表<br>separator: 分隔符 | String: 转换后的字符串 |
| splitToList(String str, String separator) | 将带分隔符的字符串转换为字符串列表 | str: 带分隔符的字符串<br>separator: 分隔符 | List<String>: 转换后的字符串列表 |
| join(Map<String,String> maps, String separator) | 将字符串Map转换为带分隔符的字符串 | maps: 字符串Map<br>separator: 分隔符 | String: 转换后的字符串 |
| join(Map<String,String> maps, String itemSeparator, String kvSeparator) | 将字符串Map转换为带双重分隔符的字符串 | maps: 字符串Map<br>itemSeparator: 条目分隔符<br>kvSeparator: 键值分隔符 | String: 转换后的字符串 |
| splitToMap(String str, String separator) | 将带分隔符的字符串转换为字符串Map | str: 带分隔符的字符串<br>separator: 分隔符 | Map<String,String>: 转换后的字符串Map |
| splitToMap(String str, String itemSeparator, String kvSeparator) | 将带双重分隔符的字符串转换为字符串Map | str: 带分隔符的字符串<br>itemSeparator: 条目分隔符<br>kvSeparator: 键值分隔符 | Map<String,String>: 转换后的字符串Map |

### 注意事项

- 如果字符串数组或集合中包含分隔符字符，可能导致转换后的结果解析错误
- splitToString和splitToList使用正则表达式分割字符串，对于特殊字符(如|、.、*)需使用转义(\\|、\\.、\\*)
- 使用splitToMap时，默认键值分隔符为"="，如果字符串中的键值对格式不符合预期，会导致解析异常
- join方法对null值的处理：如果集合或元素为null，可能返回空字符串或包含"null"的字符串
- 对于Map的处理不保证键值对的顺序

### 使用示例

```java
// 字符串数组与分隔符字符串互转
String[] arr = {"apple", "banana", "cherry"};
String joined = ObjectArraySeparator.join(arr, ",");
System.out.println(joined);  // 输出：apple,banana,cherry

String[] split = ObjectArraySeparator.splitToString("apple,banana,cherry", ",");
System.out.println(Arrays.toString(split));  // 输出：[apple, banana, cherry]

// 处理空元素
String[] splitWithEmpty = ObjectArraySeparator.splitToString("apple,,cherry", ",");
System.out.println(Arrays.toString(splitWithEmpty));  // 输出：[apple, , cherry]

// 字符串列表与分隔符字符串互转
List<String> list = Arrays.asList("apple", "banana", "cherry");
String joinedList = ObjectArraySeparator.join(list, "|");
System.out.println(joinedList);  // 输出：apple|banana|cherry

List<String> splitList = ObjectArraySeparator.splitToList("apple|banana|cherry", "\\|");
System.out.println(splitList);  // 输出：[apple, banana, cherry]

// 字符串Map与分隔符字符串互转
Map<String, String> map = new HashMap<>();
map.put("name", "John");
map.put("age", "30");
map.put("city", "New York");

// 使用默认键值分隔符(=)
String joinedMap = ObjectArraySeparator.join(map, ";");
System.out.println(joinedMap);  // 输出类似：name=John;age=30;city=New York

// 自定义键值分隔符
String joinedMapCustom = ObjectArraySeparator.join(map, ";", ":");
System.out.println(joinedMapCustom);  // 输出类似：name:John;age:30;city:New York

// 字符串转Map
Map<String, String> splitMap = ObjectArraySeparator.splitToMap("name=John;age=30;city=New York", ";");
System.out.println(splitMap);  // 输出类似：{name=John, age=30, city=New York}

// 自定义分隔符
Map<String, String> splitMapCustom = ObjectArraySeparator.splitToMap("name:John;age:30;city:New York", ";", ":");
System.out.println(splitMapCustom);  // 输出类似：{name=John, age=30, city=New York}
``` 