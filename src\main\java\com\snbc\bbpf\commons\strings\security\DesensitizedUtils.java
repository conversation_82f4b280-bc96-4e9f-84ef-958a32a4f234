/*
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.commons.strings.security;

import com.snbc.bbpf.commons.strings.security.annotation.Desensitized;
import com.snbc.bbpf.commons.strings.security.annotation.SensitiveType;
import com.snbc.bbpf.commons.strings.security.parse.CardParse;
import com.snbc.bbpf.commons.strings.security.parse.AddressParse;
import com.snbc.bbpf.commons.strings.security.parse.BankCardParse;
import com.snbc.bbpf.commons.strings.security.parse.ChineseNameParse;
import com.snbc.bbpf.commons.strings.security.parse.CustomParse;
import com.snbc.bbpf.commons.strings.security.parse.EmailParse;
import com.snbc.bbpf.commons.strings.security.parse.FixedPhoneParse;
import com.snbc.bbpf.commons.strings.security.parse.MobilePhoneParse;
import com.snbc.bbpf.commons.strings.security.parse.PassWordParse;
import com.snbc.bbpf.commons.strings.security.parse.IDesensitizedParse;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.EnumMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @ClassName: DesensitizedUtils
 *             脱敏工具类
 *             <p>
 *             定义要 脱敏的字段 @Desensitized(type = SensitiveTypeEnum.ID_CARD)
 *             private String idCard;
 *             调用具体的方法DesensitizedUtils
 *             请采用
 * @module: 字符脱敏
 * @Author: yangweipeng
 * @date: 2023/05/06
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class DesensitizedUtils {
    public static final String JAVAX = "javax.";
    public static final String JAVA = "java.";
    private static EnumMap<SensitiveType, IDesensitizedParse> parseMap = initParseMap();

    private static Map<Class<?>, Field[]> cachedFields = new ConcurrentHashMap<>();

    private DesensitizedUtils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 初始化所有脱敏策略
     * <p>
     *     示例：初始化后parseMap包含CHINESE_NAME、MOBILE_PHONE等脱敏类型对应的解析器
     * </p>
     * @return 脱敏策略映射表（键为脱敏类型，值为对应的解析器）
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/6
     */
    private static EnumMap<SensitiveType, IDesensitizedParse> initParseMap() {
        EnumMap<SensitiveType, IDesensitizedParse> parseMap = new EnumMap(SensitiveType.class);
        if (parseMap.isEmpty()) {
            parseMap.put(SensitiveType.CHINESE_NAME, new ChineseNameParse());
            parseMap.put(SensitiveType.MOBILE_PHONE, new MobilePhoneParse());
            parseMap.put(SensitiveType.ADDRESS, new AddressParse());
            parseMap.put(SensitiveType.EMAIL, new EmailParse());
            parseMap.put(SensitiveType.BANK_CARD, new BankCardParse());
            parseMap.put(SensitiveType.ID_CARD, new CardParse());
            parseMap.put(SensitiveType.PASSWORD, new PassWordParse());
            parseMap.put(SensitiveType.FIXED_PHONE, new FixedPhoneParse());
        }
        return parseMap;
    }

    /**
     * 获取脱敏json串(递归引用会导致java.lang.StackOverflowError)
     *
     * @param javaBean 需要转换的对象
     * @return 返回脱敏后对象
     * @throws IllegalArgumentException 如果输入不是数字
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/21
     */
    public static Object getJson(Object javaBean) throws Exception {
        if (null != javaBean) {
            /* 定义一个计数器，用于避免重复循环自定义对象类型的字段 */
            Set<Integer> referenceCounter = new HashSet<>();
            /* 对实体进行脱敏操作 */
            DesensitizedUtils.replace(getAllFields(javaBean), javaBean, referenceCounter);
            /* 清空计数器 */
            referenceCounter.clear();
        }
        return javaBean;
    }

    /**
     * 脱敏操作（按照规则转化需要脱敏的字段并设置新值）
     * 目前只支持String类型的字段，如需要其他类型如BigDecimal、Date等类型，可以添加
     *
     * @param javaBean 需要转换对象
     * @param field    转换的字段
     * @param value    字段值
     * @throws IllegalArgumentException 如果输入不是数字
     * <AUTHOR>
     * @date 2023/5/3
     * @since 1.0.0
     */
    public static void setNewValueForField(Object javaBean, Field field, Object value)
            throws Exception {
        // 处理自身的属性
        Desensitized annotation = field.getAnnotation(Desensitized.class);
        if (field.getType().equals(String.class) && null != annotation
                && executeIsEffictiveMethod(javaBean, annotation)) {
            String valueStr = (String) value;
            if (StringUtils.isNotBlank(valueStr)) {
                // 如果是自定义脱敏类型，创建自定义解析器
                if (annotation.type() == SensitiveType.CUSTOM) {
                    CustomParse customParse = new CustomParse(
                        annotation.prefixKeep(),
                        annotation.suffixKeep(),
                        annotation.maskSymbol()
                    );
                    field.set(javaBean, customParse.parseString(valueStr));
                } else {
                    field.set(javaBean, parseMap.get(annotation.type()).parseString(valueStr));
                }
            }
        }
    }

    /**
     * 脱敏操作（按照规则转化需要脱敏的字段并设置新值）
     * 目前只支持String类型的字段，如需要其他类型如BigDecimal、Date等类型，可以添加
     *
     * @param valueStr 需要转换的字符
     * @param type     脱敏类型
     * @return 脱敏后的字符
     * <AUTHOR>
     * @date 2023/4/21
     * @since 1.0.0
     */
    public static String desensitize(String valueStr, SensitiveType type) {
        // 处理自身的属性
        return parseMap.get(type).parseString(valueStr);
    }

    /**
     * 自定义脱敏操作
     * 支持指定保留前几位和后几位，其余用指定符号替换
     *
     * @param valueStr 需要转换的字符串
     * @param prefixKeep 保留前几位
     * @param suffixKeep 保留后几位
     * @return 脱敏后的字符串
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/28
     */
    public static String desensitizeCustom(String valueStr, int prefixKeep, int suffixKeep) {
        return desensitizeCustom(valueStr, prefixKeep, suffixKeep, "*");
    }

    /**
     * 自定义脱敏操作
     * 支持指定保留前几位和后几位，其余用指定符号替换
     *
     * @param valueStr 需要转换的字符串
     * @param prefixKeep 保留前几位
     * @param suffixKeep 保留后几位
     * @param maskSymbol 脱敏符号
     * @return 脱敏后的字符串
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/28
     */
    public static String desensitizeCustom(String valueStr, int prefixKeep, int suffixKeep, String maskSymbol) {
        CustomParse customParse = new CustomParse(prefixKeep, suffixKeep, maskSymbol);
        return customParse.parseString(valueStr);
    }

    /**
     * 遍历List脱敏数据
     *
     * @param content 需要转换的数字
     * @return 返回脱敏后的数据
     * <AUTHOR>
     * @date 2023/4/21
     * @since 1.0.0
     */
    public static <T> List desensitizedList(List<T> content) throws Exception {
        if (content.isEmpty()) {
            return content;
        }
        List list = new ArrayList<>();
        for (T t : content) {
            list.add(desensitizedObject(t));
        }
        return list;
    }

    /**
     * 对象脱敏
     *
     * @param content 需要脱敏的对象
     * @return 脱敏后的对象
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/21
     */
    public static <T> Object desensitizedObject(T content) throws Exception {
        if (content != null) {
            return getJson(content);
        }
        return null;
    }

    /**
     * 获取包括父类所有的属性
     *
     * @param objSource
     * @return BCD编码字节数组
     * <AUTHOR>
     * @date 2023/4/21
     * @since 1.0.0
     */
    private static Field[] getAllFields(Object objSource) {
        Class<?> clazz = objSource.getClass();
        if (cachedFields.containsKey(clazz)) {
            return cachedFields.get(clazz);
        }
        List<Field> fieldList = new ArrayList<>();
        do {
            Field[] fields = clazz.getDeclaredFields();
            fieldList.addAll(Arrays.stream(fields).collect(Collectors.toList()));
            clazz = clazz.getSuperclass();
        } while (clazz != null && clazz != Object.class);
        Field[] fields = new Field[fieldList.size()];
        System.arraycopy(fieldList.toArray(), 0, fields, 0, fieldList.size());
        cachedFields.put(clazz, fields);
        return fields;
    }

    /**
     * 对需要脱敏的字段进行转化
     *
     * @param fields
     * @param javaBean
     * @param referenceCounter
     * @throws IllegalAccessException
     */
    private static void replace(Field[] fields, Object javaBean, Set<Integer> referenceCounter) throws Exception {
        if (null != fields && fields.length > 0) {
            for (Field field : fields) {
                if (null != field && null != javaBean) {
                    field.setAccessible(true);
                    Object value = field.get(javaBean);
                    if (!(null != value && filter(referenceCounter, field, value))) {
                        // 脱敏操作
                        setNewValueForField(javaBean, field, value);
                    }
                }
            }
        }
    }

    /**
     * 过滤敏感字段的总方法
     *
     * @param
     * @return
     * @Author: yangweipeng
     * @CreateDate: 2021/7/6 11:35
     * @UpdateDate: 2021/7/6 11:35
     */
    private static boolean filter(Set<Integer> referenceCounter, Field field, Object value) throws Exception {
        Class<?> type = value.getClass();
        // 处理子属性，包括集合中的
        if (type.isArray()) {// 对数组类型的字段进行递归过滤
            filteArray(referenceCounter, value);
        } else if (value instanceof Collection<?>) {// 对集合类型的字段进行递归过滤
            filterCollection(referenceCounter, (Collection<?>) value);
        } else if (value instanceof Map<?, ?>) {// 对Map类型的字段进行递归过滤
            filterMap(referenceCounter, (Map<?, ?>) value);
        } else if (value instanceof Enum<?>) {
            return true;
        } else {
            /* 除基础类型、jdk类型的字段之外，对其他类型的字段进行递归过滤 */
            filterOtherType(referenceCounter, field, value, type);
        }
        return false;
    }

    /**
     * 除基础类型、jdk类型的字段之外，对其他类型的字段进行递归过滤
     *
     * @param referenceCounter 引用计数器
     * @param field            字段
     * @param value            值
     * @param type             类型是
     * @throws IllegalAccessException
     * @Author: yangweipeng
     * @CreateDate: 2021/7/6 14:53
     * @UpdateDate: 2021/7/6 14:53
     */
    private static void filterOtherType(Set<Integer> referenceCounter, Field field, Object value, Class<?> type)
            throws Exception {
        boolean result = !type.isPrimitive()
                && type.getPackage() != null
                && !StringUtils.startsWithAny(type.getPackage().getName(), JAVAX, JAVA)
                && !StringUtils.startsWithAny(field.getType().getName(), JAVAX, JAVA);
        if (result && !StringUtils.startsWithAny(field.getName(), JAVAX, JAVA)
                && referenceCounter.add(value.hashCode())) {
            replace(getAllFields(value), value, referenceCounter);
        }
    }

    /**
     * 对Map类型的字段进行递归过滤
     *
     * @param referenceCounter 引用计数器
     * @param value            值
     * @throws IllegalAccessException
     * @Author: yangweipeng
     * @CreateDate: 2021/7/6 14:55
     * @UpdateDate: 2021/7/6 14:55
     *              Copyright© Shandong New Beiyang Information Technology Co., Ltd.
     *              . all rights reserved.
     */
    private static void filterMap(Set<Integer> referenceCounter, Map<?, ?> value) throws Exception {
        Map<?, ?> m = value;
        Set<?> set = m.entrySet();
        for (Object o : set) {
            Map.Entry<?, ?> entry = (Map.Entry<?, ?>) o;
            Object mapVal = entry.getValue();
            if (isNotGeneralType(mapVal.getClass(), mapVal, referenceCounter)) {
                replace(getAllFields(mapVal), mapVal, referenceCounter);
            }
        }
    }

    /**
     * 对集合类型的字段进行递归过滤
     *
     * @param referenceCounter 引用计数器
     * @param value            值
     * @throws IllegalAccessException
     * @Author: yangweipeng
     * @CreateDate: 2021/7/6 14:56
     * @UpdateDate: 2021/7/6 14:56
     *              Copyright© Shandong New Beiyang Information Technology Co., Ltd.
     *              . all rights reserved.
     */
    private static void filterCollection(Set<Integer> referenceCounter, Collection<?> value) throws Exception {
        Collection<?> c = value;
        Iterator<?> it = c.iterator();
        // 待优化
        while (it.hasNext()) {
            Object collectionObj = it.next();
            if (isNotGeneralType(collectionObj.getClass(), collectionObj, referenceCounter)) {
                replace(getAllFields(collectionObj), collectionObj, referenceCounter);
            }
        }
    }

    /**
     * 对数组类型的字段进行递归过滤
     *
     * @param referenceCounter
     * @param value            值
     * @throws IllegalAccessException
     * @Author: yangweipeng
     * @CreateDate: 2021/7/6 14:57
     * @UpdateDate: 2021/7/6 14:57
     *              Copyright© Shandong New Beiyang Information Technology Co., Ltd.
     *              . all rights reserved.
     */
    private static void filteArray(Set<Integer> referenceCounter, Object value) throws Exception {
        int len = Array.getLength(value);
        for (int i = 0; i < len; i++) {
            Object arrayObject = Array.get(value, i);
            if (isNotGeneralType(arrayObject.getClass(), arrayObject, referenceCounter)) {
                replace(getAllFields(arrayObject), arrayObject, referenceCounter);
            }
        }
    }

    /**
     * 排除基础类型、jdk类型、枚举类型的字段
     *
     * @param clazz
     * @param value
     * @param referenceCounter
     * @return
     */
    private static boolean isNotGeneralType(Class<?> clazz, Object value, Set<Integer> referenceCounter) {
        boolean result = !clazz.isPrimitive()
                && clazz.getPackage() != null
                && !StringUtils.startsWithAny(clazz.getPackage().getName(), JAVAX, JAVA)
                && !StringUtils.startsWithAny(clazz.getName(), JAVAX, JAVA);
        return !clazz.isEnum()
                && result
                && referenceCounter.add(value.hashCode());
    }

    /**
     * 执行某个对象中指定的方法
     *
     * @param javaBean     对象
     * @param desensitized
     * @return
     */
    private static boolean executeIsEffictiveMethod(Object javaBean, Desensitized desensitized)
            throws NoSuchMethodException, SecurityException, IllegalAccessException, IllegalArgumentException,
            InvocationTargetException {
        boolean isAnnotationEffictive = true;// 注解默认生效
        if (desensitized != null) {
            String isEffictiveMethod = desensitized.isEffictiveMethod();
            if (!StringUtils.isEmpty(isEffictiveMethod)) {
                Method method = javaBean.getClass().getMethod(isEffictiveMethod);
                method.setAccessible(true);
                isAnnotationEffictive = (Boolean) method.invoke(javaBean);
            }
        }
        return isAnnotationEffictive;
    }

    /**
	 * 脱敏方法一：指定开始和结束位置，使用默认脱敏符号进行脱敏。
	 *
	 * @param src   待脱敏字符串
	 * @param start 脱敏开始位置（从0开始）
	 * @param end   脱敏结束位置（不包含）
	 * @return 脱敏后的字符串
	 * @since 1.7.0
	 * <AUTHOR>
	 * @date 2025/07/28
	 */
    public static String desensitizeByPosition(String src, int start, int end) {
        return desensitizeByRangeInternal(src, start, end, null, null);
    }

    /**
	 * 脱敏方法二：指定开始位置和脱敏长度，使用默认脱敏符号进行脱敏。
	 *
	 * @param src        待脱敏字符串
	 * @param start      脱敏开始位置（从0开始）
	 * @param maskLength 脱敏长度
	 * @return 脱敏后的字符串
	 * @since 1.7.0
	 * <AUTHOR>
	 * @date 2025/07/28
	 */
    public static String desensitizeByLength(String src, int start, int maskLength) {
        return desensitizeByRangeInternal(src, start, start + maskLength, null, maskLength);
    }

    /**
	 * 脱敏方法三：指定开始和结束，指定脱敏符号
	 *
	 * @param src        待脱敏字符串
	 * @param start      脱敏开始位置（从0开始）
	 * @param maskSymbol 脱敏符号
	 * @return 脱敏后的字符串
	 * @since 1.7.0
	 * <AUTHOR>
	 * @date 2025/07/28
	 */
    public static String desensitizeByRange(String src, int start, int end,String maskSymbol) {
        return desensitizeByRangeInternal(src, start, end, maskSymbol,null);
    }

    /**
	 * 脱敏方法四：指定开始位置和脱敏长度，使用指定脱敏符号进行脱敏。
	 *
	 * @param src        待脱敏字符串
	 * @param start      脱敏开始位置（从0开始）
	 * @param maskLength 脱敏长度
	 * @param maskSymbol 脱敏符号
	 * @return 脱敏后的字符串
	 * @since 1.7.0
	 * <AUTHOR>
	 * @date 2025/07/28
	 */
    public static String desensitizeByLength(String src, int start, int maskLength, String maskSymbol) {
        return desensitizeByRangeInternal(src, start, start + maskLength, maskSymbol, maskLength);
    }

    /**
	 * 按指定起始、结束位置进行字符串脱敏，支持自定义脱敏符号和脱敏长度。
	 * <p>
	 * 该方法会对原始字符串src的[start, end)区间进行脱敏处理，区间之外的内容保持不变。
	 * 脱敏符号可自定义，脱敏长度可指定，若为null则与区间长度一致。
	 * <p>
	 * 边界情况：
	 * <ul>
	 * <li>src为null或空字符串时，直接返回原值；</li>
	 * <li>start小于0时自动归零，end大于字符串长度时自动归为字符串长度；</li>
	 * <li>start>=end或start>=字符串长度时，返回原值；</li>
	 * <li>maskSymbol为null或空时，默认使用"*"；</li>
	 * <li>maskLength为null时，等于脱敏区间长度，若小于0则不脱敏；</li>
	 * </ul>
	 *
	 * @param src        原始字符串，允许为null或空
	 * @param start      脱敏起始位置（包含），若小于0自动归零
	 * @param end        脱敏结束位置（不包含），若大于字符串长度自动归为字符串长度
	 * @param maskSymbol 脱敏符号，允许为null或空，默认"*"
	 * @param maskLength 脱敏符号显示长度，允许为null，null时等于脱敏区间长度
	 * @return 脱敏后的字符串，若无需脱敏则返回原值
	 * @throws IllegalArgumentException 若start或end为负数且无法自动归零时
	 * @since 1.7.0
	 * <AUTHOR>
	 * @date 2025/07/28
	 */
    private static String desensitizeByRangeInternal(String src, int start, int end, String maskSymbol,
            Integer maskLength) {
        if (src == null || src.isEmpty()) {
            return src;
        }
        int len = src.length();
        if (start < 0){
            start = 0;
        }
        if (end > len){
            end = len;
        }
        if (start >= end || start >= len) {
            return src;
        }
        if (maskSymbol == null || maskSymbol.isEmpty()) {
            maskSymbol = "*";
        }
        int maskCount = maskLength != null ? maskLength : (end - start);
        if (maskCount < 0){
            maskCount = 0;
        }
        StringBuilder sb = new StringBuilder();
        sb.append(src, 0, start);
        for (int i = 0; i < maskCount; i++) {
            sb.append(maskSymbol);
        }
        sb.append(src.substring(end));
        return sb.toString();
    }
}
