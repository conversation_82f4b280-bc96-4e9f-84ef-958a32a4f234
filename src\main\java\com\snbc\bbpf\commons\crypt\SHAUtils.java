/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.crypt;

import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * SHA散列算法
 * 1. 实现SHA-256和SHA-512算法
 * 2. 使用BouncyCastleProvider（简称BC）扩展可用算法
 *
 * <AUTHOR>
 * @module 加解密
 * @date 2023-12-08 20:10
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class SHAUtils {
    /**
     * 256 算法
     */
    private static final String SHA_256 = "SHA-256";
    /**
     * 512算法
     */
    private static final String SHA_512 = "SHA-512";
    /**
     * SHA 加密 SHA-256模式
     *
     * @param msg    加密内容
     * @return string
     * @throws
     * <AUTHOR>
     * @date 2023/11/27
     * @since 1.4.0
     */
    public static String encrypt(String msg) throws NoSuchAlgorithmException {
        return encrypt(msg,SHA_256);
    }
    /**
     * SHA 加密 SHA-512模式
     *
     * @param msg    加密内容
     * @return string
     * @throws
     * <AUTHOR>
     * @date 2023/11/27
     * @since 1.4.0
     */
    public static String encryptFive(String msg) throws NoSuchAlgorithmException {
        return encrypt(msg,SHA_512);
    }
    /**
     * SHA 加密 扩展模式
     *
     * @param msg    加密内容 为UTF-8 格式
     * @param algorithm    算法
     * @return string
     * @throws
     * <AUTHOR>
     * @date 2023/11/27
     * @since 1.4.0
     */
    public static String encrypt(String msg,String algorithm) throws NoSuchAlgorithmException {
        if(StringUtils.isAnyBlank(msg,algorithm)){
            throw new IllegalArgumentException("[SHAUtils][encrypt] (msg,algorithm) is blank");
        }
        MessageDigest digest = MessageDigest.getInstance(algorithm); 
        byte[] hash = digest.digest(msg.getBytes(StandardCharsets.UTF_8));
        StringBuilder hexString = new StringBuilder();
        for (byte b : hash) {
            hexString.append(String.format("%02x", b));
        }
        return hexString.toString();
    }
}
