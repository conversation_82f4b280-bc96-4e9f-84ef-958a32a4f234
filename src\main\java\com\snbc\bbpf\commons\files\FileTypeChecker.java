/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.files;

import com.snbc.bbpf.commons.strings.HexUtils;

import java.io.FileInputStream;
import java.io.IOException;

/**
 * 需要实现常用的文件格式的文件头的校验。
 * 常用文件格式（图片（jpg，bmp，png，gif，svg）
 * 音频（wav，mp3，wma，amr）
 * 视频（mp4，avi）
 * 文档（doc/docx，xls/xlsx，ppt/pptx，pdf，txt）
 * 压缩（zip，rar，gz）
 * 镜像（iso）
 * 可执行文件(exe，bat，cmd)，
 * 安装文件(apk)）
 * 以上类型可根据情况进行支持，需要注释中说明支持的文件类型
 * <p> 使用。
 * String filePath = "/Users/<USER>/Desktop/a.txt"; // 替换为要检查的文件路径
 * boolean isMatching = FileTypeChecker.isFileType(filePath, FileType.TXT);
 *
 * <AUTHOR>
 * @module 文件
 * @date 2023/4/29 9:58
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 * @since 1.2.0
 */
public class FileTypeChecker {

    public static final int INT = 2;

    /**
     * 检查是否是文件
     *
     * @param filePath .
     * @param fileType .
     * @return .
     */
    public static boolean isFileType(String filePath, FileType fileType) {
        try (FileInputStream fis = new FileInputStream(filePath)) {
            byte[] fileHeader = new byte[fileType.getHexHeader().length() / INT];
            if (fis.read(fileHeader) != fileHeader.length) {
                // 文件太短，无法确定文件类型
                return false;
            }
            String fileHeaderHex = HexUtils.byteArrayToHexString(fileHeader);
            return fileHeaderHex.equalsIgnoreCase(fileType.getHexHeader());
        } catch (IOException e) {
            // 检查文件类型时出现错误
            throw new IllegalArgumentException("file error ", e);

        }
    }
 
}
