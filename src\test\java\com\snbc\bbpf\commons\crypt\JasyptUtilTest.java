/*
 * 版权所有 2009-2024山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.crypt;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JasyptUtil单元测试类
 *
 * <AUTHOR>
 * @module bbpf-commons
 * @date 2025-05-14
 * @version 1.6.0
 */
class JasyptUtilTest {

    /**
     * 测试使用默认参数加密和解密功能
     *
     * <AUTHOR>
     * @date 2025-05-14
     * @since 1.6.0
     */
    @Test
    @DisplayName("测试使用默认参数加密和解密")
    @Tags({
            @Tag("@id:127"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2025-05-14")
    })
    void testEncryptAndDecryptWithDefaultParams() {
        // 准备测试数据
        String plainText = "测试明文123456!@#$%^";

        // 执行加密操作
        String encryptedText = JasyptUtil.encrypt(plainText);
        
        // 验证加密结果不为空且不等于原文
        assertNotNull(encryptedText);
        assertNotEquals(plainText, encryptedText);
        
        // 执行解密操作
        String decryptedText = JasyptUtil.decrypt(encryptedText);
        
        // 验证解密结果与原文一致
        assertEquals(plainText, decryptedText);
    }

    /**
     * 测试使用自定义参数加密和解密功能
     *
     * <AUTHOR>
     * @date 2025-05-14
     * @since 1.6.0
     */
    @Test
    @DisplayName("测试使用自定义参数加密和解密")
    @Tags({
            @Tag("@id:127"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2025-05-14")
    })
    void testEncryptAndDecryptWithCustomParams() {
        // 准备测试数据
        String plainText = "测试明文123456!@#$%^";
        String password = "customPassword";
        String algorithm = "PBEWithMD5AndDES";
        int iterations = 2000;

        // 执行加密操作
        String encryptedText = JasyptUtil.encrypt(plainText, password, algorithm, iterations);
        
        // 验证加密结果不为空且不等于原文
        assertNotNull(encryptedText);
        assertNotEquals(plainText, encryptedText);
        
        // 执行解密操作
        String decryptedText = JasyptUtil.decrypt(encryptedText, password, algorithm, iterations);
        
        // 验证解密结果与原文一致
        assertEquals(plainText, decryptedText);
    }

    /**
     * 测试使用错误密钥解密
     *
     * <AUTHOR>
     * @date 2025-05-14
     * @since 1.6.0
     */
    @Test
    @DisplayName("测试使用错误密钥解密")
    @Tags({
            @Tag("@id:127"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2025-05-14")
    })
    void testDecryptWithWrongPassword() {
        // 准备测试数据
        String plainText = "测试明文123456!@#$%^";
        String correctPassword = "correctPassword";
        String wrongPassword = "wrongPassword";
        String algorithm = "PBEWithMD5AndDES";
        int iterations = 1000;

        // 使用正确密钥加密
        String encryptedText = JasyptUtil.encrypt(plainText, correctPassword, algorithm, iterations);
        
        // 使用错误密钥解密，预期会抛出异常
        Exception exception = assertThrows(Exception.class, () -> {
            JasyptUtil.decrypt(encryptedText, wrongPassword, algorithm, iterations);
        });
        
        // 验证异常信息
        assertNotNull(exception);
    }

    /**
     * 测试使用不同算法加密和解密
     *
     * <AUTHOR>
     * @date 2025-05-14
     * @since 1.6.0
     */
    @Test
    @DisplayName("测试使用不同算法加密和解密")
    @Tags({
            @Tag("@id:127"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2025-05-14")
    })
    void testEncryptAndDecryptWithDifferentAlgorithms() {
        // 准备测试数据
        String plainText = "测试明文123456!@#$%^";
        String password = "testPassword";
        String algorithm = "PBEWithMD5AndTripleDES";
        int iterations = 1000;

        // 执行加密操作
        String encryptedText = JasyptUtil.encrypt(plainText, password, algorithm, iterations);
        
        // 验证加密结果不为空且不等于原文
        assertNotNull(encryptedText);
        assertNotEquals(plainText, encryptedText);
        
        // 执行解密操作
        String decryptedText = JasyptUtil.decrypt(encryptedText, password, algorithm, iterations);
        
        // 验证解密结果与原文一致
        assertEquals(plainText, decryptedText);
    }

    /**
     * 测试加密null值
     *
     * @param plainText 测试输入
     * <AUTHOR>
     * @date 2025-05-14
     * @since 1.6.0
     */
    @ParameterizedTest
    @NullAndEmptySource
    @DisplayName("测试加密null或空字符串")
    @Tags({
            @Tag("@id:127"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2025-05-14")
    })
    void testEncryptNullOrEmpty(String plainText) {
        if (plainText == null) {
            // 预期处理null值时会抛出异常
            Exception exception = assertThrows(IllegalArgumentException.class, () -> {
                JasyptUtil.encrypt(plainText);
            });
            
            assertEquals("加密字符串不能为null", exception.getMessage());
        } else {
            // 空字符串应该能正常加密
            String encryptedText = JasyptUtil.encrypt(plainText);
            assertNotNull(encryptedText);
            
            // 解密后应该仍然是空字符串
            String decryptedText = JasyptUtil.decrypt(encryptedText);
            assertEquals(plainText, decryptedText);
        }
    }

    /**
     * 测试解密null值
     *
     * @param encryptedText 测试输入
     * <AUTHOR>
     * @date 2025-05-14
     * @since 1.6.0
     */
    @ParameterizedTest
    @NullAndEmptySource
    @DisplayName("测试解密null或空字符串")
    @Tags({
            @Tag("@id:127"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2025-05-14")
    })
    void testDecryptNullOrEmpty(String encryptedText) {
        if (encryptedText == null) {
            // 预期处理null值时会抛出异常
            Exception exception = assertThrows(IllegalArgumentException.class, () -> {
                JasyptUtil.decrypt(encryptedText);
            });
            
            assertEquals("解密字符串不能为null", exception.getMessage());
        } else {
            // 空字符串不是有效的加密文本，预期会抛出异常
            assertThrows(Exception.class, () -> {
                JasyptUtil.decrypt(encryptedText);
            });
        }
    }

    /**
     * 测试加密和解密各种特殊字符
     *
     * @param text 测试输入
     * <AUTHOR>
     * @date 2025-05-14
     * @since 1.6.0
     */
    @ParameterizedTest
    @ValueSource(strings = {
        "Hello World!",
        "1234567890",
        "!@#$%^&*()_+",
        "中文测试",
        "English and 中文 mixed",
        "Lines with\nnew line\rcharacters",
        "Unicode characters: \u00A9 \u00AE \u2122"
    })
    @DisplayName("测试加密和解密各种特殊字符")
    @Tags({
            @Tag("@id:127"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2025-05-14")
    })
    void testEncryptAndDecryptSpecialCharacters(String text) {
        // 使用默认参数加密
        String encryptedText = JasyptUtil.encrypt(text);
        
        // 验证加密结果不为空且不等于原文
        assertNotNull(encryptedText);
        assertNotEquals(text, encryptedText);
        
        // 使用默认参数解密
        String decryptedText = JasyptUtil.decrypt(encryptedText);
        
        // 验证解密结果与原文一致
        assertEquals(text, decryptedText);
        
        // 使用自定义参数加密
        String customEncryptedText = JasyptUtil.encrypt(text, "customPassword", "PBEWithMD5AndDES", 2000);
        
        // 验证加密结果不为空且不等于原文
        assertNotNull(customEncryptedText);
        assertNotEquals(text, customEncryptedText);
        
        // 使用自定义参数解密
        String customDecryptedText = JasyptUtil.decrypt(customEncryptedText, "customPassword", "PBEWithMD5AndDES", 2000);
        
        // 验证解密结果与原文一致
        assertEquals(text, customDecryptedText);
    }

    /**
     * 测试迭代次数对加解密速度和结果的影响
     *
     * <AUTHOR>
     * @date 2025-05-14
     * @since 1.6.0
     */
    @Test
    @DisplayName("测试迭代次数对加解密的影响")
    @Tags({
            @Tag("@id:127"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2025-05-14")
    })
    void testIterationsEffect() {
        // 准备测试数据
        String plainText = "测试明文123456!@#$%^";
        String password = "testPassword";
        String algorithm = "PBEWithMD5AndDES";
        
        // 使用不同迭代次数加密解密
        int[] iterations = {100, 1000, 10000};
        
        for (int iteration : iterations) {
            // 执行加密操作
            String encryptedText = JasyptUtil.encrypt(plainText, password, algorithm, iteration);
            
            // 验证加密结果不为空且不等于原文
            assertNotNull(encryptedText);
            assertNotEquals(plainText, encryptedText);
            
            // 执行解密操作
            String decryptedText = JasyptUtil.decrypt(encryptedText, password, algorithm, iteration);
            
            // 验证解密结果与原文一致
            assertEquals(plainText, decryptedText);
        }
    }

    /**
     * 测试使用不支持的加密算法
     *
     * <AUTHOR>
     * @date 2025-05-14
     * @since 1.6.0
     */
    @Test
    @DisplayName("测试使用不支持的加密算法")
    @Tags({
            @Tag("@id:127"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2025-05-14")
    })
    void testUnsupportedAlgorithm() {
        // 准备测试数据
        String plainText = "测试明文123456!@#$%^";
        String password = "testPassword";
        String unsupportedAlgorithm = "UnsupportedAlgorithm";
        int iterations = 1000;

        // 使用不支持的算法加密，预期会抛出异常
        Exception exception = assertThrows(Exception.class, () -> {
            JasyptUtil.encrypt(plainText, password, unsupportedAlgorithm, iterations);
        });
        
        // 验证异常信息不为空
        assertNotNull(exception);
        
        // 验证异常信息包含算法相关的错误提示
        assertTrue(exception.getMessage().contains("algorithm") || 
                   exception.getMessage().contains("Algorithm") || 
                   exception.getMessage().contains("加密") || 
                   exception.getCause().toString().contains("algorithm"),
                   "异常信息应包含与算法相关的错误提示");
    }
} 