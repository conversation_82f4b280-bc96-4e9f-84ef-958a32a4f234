/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.jsons;

import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Option;
import com.jayway.jsonpath.PathNotFoundException;
import com.snbc.bbpf.commons.strings.StringEmptyCheck;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * 主要功能： 通过 JSONPath 查询 JSON 中符合条件的数据。
 * 通过JSONPath 将json中的数据，以map
 * jsonpath的具体的语法
 * 参照：https://blog.csdn.net/xiaodeng2017/article/details/124905029
 *
 * <AUTHOR>
 * @date 2023/8/23
 * @since 1.2.0
 * 创建时间:  [2023/9/1 15:02]
 */
public class JSONPathUtils {
    /**
     * 通过 JSONPath 查询 JSON 中符合条件的数据。
     * <p>
     * String jsonData = "{ " +
     * "\"employees\": [" +
     * "    {\"id\": 1, \"name\": \"John\"}," +
     * "    {\"id\": 2, \"name\": \"Jane\"}" +
     * "  ]" +
     * "}";
     * String i = "$.employees[?(@.id == 1)]";
     * List<LinkedHashMap<String, Object>> re = JSONPathUtils.findDataByJSONPath(jsonData, i);
     *
     * @param json     JSON 字符串
     * @param jsonPath JSONPath 表达式
     * @return 符合条件的数据，如果未找到则返回 null
     * <AUTHOR>
     * @date 2023/8/23
     * @since 1.2.0
     */
    public static List<LinkedHashMap<String, Object>> findDataByJSONPath(String json, String jsonPath) {
        if (StringEmptyCheck.isEmpty(jsonPath)) {
            throw new IllegalArgumentException("Input parameter is empty");
        }
        try {
            Configuration conf = Configuration.builder()
                    .options(Option.AS_PATH_LIST).build();

            return JsonPath
                    .using(conf)
                    .parse(json)
                    .read(jsonPath, List.class);
        } catch (PathNotFoundException e) {
            throw new IllegalArgumentException("PathNotFoundException", e);
        }
    }
}
