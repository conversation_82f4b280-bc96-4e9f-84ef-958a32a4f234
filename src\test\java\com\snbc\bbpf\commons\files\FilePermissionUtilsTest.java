package com.snbc.bbpf.commons.files;

import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledOnOs;
import org.junit.jupiter.api.condition.OS;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * FilePermissionUtils单元测试类
 *
 * <AUTHOR>
 * @module 文件处理
 * @date 2024/8/9 15:12
 * @copyright 2024 山东新北洋信息技术股份有限公司. All rights reserved
 */
@ExtendWith(MockitoExtension.class)
class FilePermissionUtilsTest {

    private static final String PATH = "files";
    private static final String TEST_FILE_PATH = "testFile.txt";
    private static final String TEST_LINUX_PERMISSIONS = "rw-r--r--";

    /**
     * 获取文件路径
     *
     * @param filePath
     * @return
     */
    public static String findFile(String filePath) {
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        File file = new File(classLoader.getResource(filePath).getFile());
        if(!file.exists()){
            throw new IllegalArgumentException("Resource not found: " + filePath);
        }
        return file.getAbsolutePath();
    }

    @BeforeAll
    static void setup() throws IOException {
        // Create a test file before tests
        String filePath = findFile(PATH)+File.separator+TEST_FILE_PATH;
        Files.createFile(Paths.get(filePath));
    }

    @AfterAll
    static void teardown() throws IOException {
        // Delete the test file after tests
        String filePath = findFile(PATH)+File.separator+TEST_FILE_PATH;
        Files.delete(Paths.get(filePath));
    }

    @Test
    @DisplayName("获取文件权限列表-正常文件有读写执行权限")
    @Tags({
            @Tag("@id:100"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2024/8/9")
    })
    void testGetFilePermissions_normal() throws IOException {
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        File file = new File(classLoader.getResource("files/sample.jar").getFile());

        List<FilePermissionType> result = FilePermissionUtils.getFilePermissions(file.getAbsolutePath());
        assertTrue(result.contains(FilePermissionType.READ));
        assertTrue(result.contains(FilePermissionType.WRITE));
    }

    @Test
    @DisplayName("获取文件权限列表-不存在文件抛出参数异常错误")
    @Tags({
            @Tag("@id:100"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2024/8/9")
    })
    void testGetFilePermissions_notFile() {
        assertThrows(IllegalArgumentException.class,() -> {
            List<FilePermissionType> result = FilePermissionUtils.getFilePermissions("filePath");
        });
    }

    @EnabledOnOs(OS.LINUX)
    @Test
    @DisplayName("设置文件权限列表-linux系统-设置只有读写权限")
    @Tags({
            @Tag("@id:100"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2024/8/9")
    })
    void testSetFilePermissions_Linux() throws IOException {
        // Given: A valid Linux permission string
        String linuxPermissions = TEST_LINUX_PERMISSIONS;
        String filePath = findFile(PATH)+File.separator+TEST_FILE_PATH;
        // When: Setting file permissions
        FilePermissionUtils.setFilePermissions(filePath, Collections.emptyMap(), linuxPermissions);

        // Then: The file should have the specified permissions
        List<FilePermissionType> posixPermissions = FilePermissionUtils.getFilePermissions(filePath);
        assertTrue(posixPermissions.contains(FilePermissionType.READ));
        assertTrue(posixPermissions.contains(FilePermissionType.WRITE));
        assertFalse(posixPermissions.contains(FilePermissionType.EXECUTE));
    }

    @EnabledOnOs(OS.LINUX)
    @Test
    @DisplayName("设置文件权限列表-linux系统-设置无效权限抛出异常")
    @Tags({
            @Tag("@id:100"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2024/8/9")
    })
    void testSetFilePermissions_InvalidLinuxPermissions() {
        // Given: An invalid Linux permission string
        String invalidLinuxPermissions = "invalid";
        String filePath = findFile(PATH)+File.separator+TEST_FILE_PATH;
        // When: Attempting to set invalid permissions
        Exception exception = assertThrows(RuntimeException.class, () -> {
            FilePermissionUtils.setFilePermissions(filePath, Collections.emptyMap(), invalidLinuxPermissions);
        });
    }

    @EnabledOnOs(OS.WINDOWS)
    @Test
    @DisplayName("设置文件权限列表-windows系统-设置读写权限")
    @Tags({
            @Tag("@id:100"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2024/8/9")
    })
    void testSetFilePermissions_Windows() throws IOException {
        // Given: A map of Windows permissions
        Map<String, List<FilePermissionType>> windowsPermissions = new HashMap<>();
        windowsPermissions.put(System.getProperty("user.name"), Arrays.asList(FilePermissionType.READ, FilePermissionType.WRITE));
        String filePath = findFile(PATH)+File.separator+TEST_FILE_PATH;
        // When: Setting file permissions
        assertDoesNotThrow(() -> {
            FilePermissionUtils.setFilePermissions(filePath, windowsPermissions, null);
        });

        List<FilePermissionType> posixPermissions = FilePermissionUtils.getFilePermissions(filePath);
        assertTrue(posixPermissions.contains(FilePermissionType.READ));
        assertTrue(posixPermissions.contains(FilePermissionType.WRITE));
        assertTrue(posixPermissions.contains(FilePermissionType.EXECUTE));
    }

    @EnabledOnOs(OS.WINDOWS)
    @Test
    @DisplayName("设置文件权限列表-windows系统-清除已有权限设置读写权限")
    @Tags({
            @Tag("@id:100"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2024/8/9")
    })
    void testSetFilePermissions_WindowsClear() throws IOException {
        // Given: A map of Windows permissions
        Map<String, List<FilePermissionType>> windowsPermissions = new HashMap<>();
        windowsPermissions.put(System.getProperty("user.name"), Arrays.asList(FilePermissionType.READ, FilePermissionType.WRITE));
        String filePath = findFile(PATH)+File.separator+TEST_FILE_PATH;
        // When: Setting file permissions
        assertDoesNotThrow(() -> {
            FilePermissionUtils.setFilePermissions(filePath, windowsPermissions, null, true);
        });

        List<FilePermissionType> posixPermissions = FilePermissionUtils.getFilePermissions(filePath);
        assertTrue(posixPermissions.contains(FilePermissionType.READ));
        assertTrue(posixPermissions.contains(FilePermissionType.WRITE));
        assertFalse(posixPermissions.contains(FilePermissionType.EXECUTE));
    }

    @Test
    @DisplayName("设置文件权限列表-null文件路径-抛出参数异常")
    @Tags({
            @Tag("@id:100"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2024/8/9")
    })
    void testSetFilePermissions_NullFilePath() {
        // Given: A null file path
        String nullFilePath = null;

        // When: Attempting to set permissions with a null path
        Exception exception = assertThrows(IllegalArgumentException.class, () -> {
            FilePermissionUtils.setFilePermissions(nullFilePath, Collections.emptyMap(), null);
        });

        // Then: An exception should be thrown
        assertEquals("File path must not be empty", exception.getMessage());
    }

    @Test
    @DisplayName("设置文件权限列表-无效文件路径-抛出参数异常")
    @Tags({
            @Tag("@id:100"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2024/8/9")
    })
    void testSetFilePermissions_notFoundFilePath() {
        // Given: A null file path
        String notFoundFilePath = "test";

        // When: Attempting to set permissions with a null path
        Exception exception = assertThrows(IllegalArgumentException.class, () -> {
            FilePermissionUtils.setFilePermissions(notFoundFilePath, Collections.emptyMap(), null);
        });
    }

}
