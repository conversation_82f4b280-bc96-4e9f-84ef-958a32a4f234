/*
 * 版权所有 2009-2023 山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.xmls;

import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.Node;
import org.dom4j.QName;
import org.dom4j.io.SAXReader;
import org.dom4j.tree.DefaultAttribute;
import org.dom4j.tree.DefaultElement;
import org.xml.sax.InputSource;

import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathFactory;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * #利用XPATH查找XML内容工具类
 *
 * <p>功能列表</p>
 * <p>1、支持直接定义xpath表达式进行内存查找</p>
 * <p>2、支持根据节点名称、属性名称查找对应的节点取值</p>
 * <p>3、支持根据节点名称、属性名称查找对应的节点xml字符串</p>
 * <p>4、支持根据节点名称、属性名称查找对应的属性取值</p>
 * <p>5、对xml中符合条件的节点和属性数据进行修改</p>
 * <p>6、对xml中符合条件的父节点中插入子节点（包括子节点属性和text），如果又多个匹配则需要考虑插入策略，是只对第一个父节点中插入，还是所有父节点都插入</p>
 * <p>7、对xml中符合条件的节点前或后插入节点（包括节点属性和text），如果又多个匹配则需要考虑插入策略，是只对第一个节点中插入，还是所有节点都插入</p>
 * <p>8、删除xml中符合条件的节点/属性/text值</p>
 *
 * <AUTHOR>
 * @date 2023/9/25 13:26
 * @since 1.2.0
 */
public final class XPathUtils {

    private XPathUtils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 校验xml字符串格式是否正确
     *  如果待校验字符串为空，则抛出RuntimeException
     *  如果待校验字符串格式错误，抛出XmlFormateErrorException
     *
     * @param xmlStr 待校验xml字符串
     * @return void
     * @throws IllegalArgumentException 待校验字符串为空时抛出
     * <AUTHOR>
     * @date 2023/9/25 13:59
     * @since 1.2.0
     **/
    public static void verfyXmlFormat(String xmlStr) {
        //1、xml字符串如果为空，则抛出异常
        if (StringUtils.isBlank(xmlStr)) {
            throw new IllegalArgumentException("未指定待校验的xml字符串");
        }
        //2、校验xml字符串格式，如果能正常获取Document对象，则为正确格式；反之，抛出异常
        getDocumentForXmlStr(xmlStr);
    }

    /**
     * 从指定字符串中查找满足条件的节点对应的字符串
     *
     * @param xmlStr 带解析的xml字符串
     * @param xpath xpath 表达式
     * 实例
     * 第一种形式：
     *  /AAA/BBB/CCC:表示层级结构，表示AAA下面BBB下面的所有CCC
     * 第二种形式：
     *  //BBB：选择文档中所有的BBB元素
     * 第三种形式：
     * /AAA/BBB/*：选择目录下的所有元素
     * //*：选择所有的元素
     * 第四种形式：
     * //AAA[1]/BBB：选择第一个AAA下的BBB元素
     * //AAA/BBB[1]：选择所有AAA的第一个BBB元素
     * //AAA/BBB[last()]：选择所有AAA的最后一个BBB元素
     * 第五种形式：
     * //@id：选择所有的id属性
     * //BBB[@id]：选择具有id属性的BBB元素
     * 第六种形式：
     * //BBB[@id='b1'] ：选择含有属性id并且其值为b1的BBB元素
     *
     * @return java.util.List<java.lang.String> 满足条件的节点xml字符串
     * @throws IllegalArgumentException xmlStr或xpath为空时抛出
     * @throws RuntimeException xpath校验或xml解析异常时抛出
     * <AUTHOR>
     * @date 2023/9/25 14:13
     * @since 1.2.0
     **/
    public static List<String> getChildStringByXpath(String xmlStr, String xpath) {
        if (StringUtils.isBlank(xmlStr)) {
            throw new IllegalArgumentException("未指定待查询的xml字符串");
        }
        if (StringUtils.isBlank(xpath)) {
            throw new IllegalArgumentException("未指定待查询的xpath脚本");
        }
        try {
            //校验xpath表达式是否正确 如果不正确 这接抛出异常
            XPathFactory factory = XPathFactory.newInstance();
            XPath xPath = factory.newXPath();
            xPath.compile(xpath);
            //根据待校验的xml字符串生成Document对象
            Document document = getDocumentForXmlStr(xmlStr);
            //获取满足xpath规则的节点集合  如果不存在 则返回空集合
            List<Node> nodes = Optional.ofNullable(document.selectNodes(xpath)).orElse(Collections.emptyList());
            //获取满足条件的节点对应的xml字符串并赋给返回值
            List<String> result = new ArrayList<>();
            nodes.forEach(node -> result.add(node.asXML()));
            return result;
        }catch (Exception e) {
            //xpath校验异常及xml字符串异常统一按照RuntimeException抛出
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取第一个指定名称且包含指定属性的节点的取值
     *
     * @param xmlStr xml字符串 如果为空，抛出异常
     * @param nodeName 节点名称 如果为空，抛出异常
     * @param attributeName 属性名称 如果为空，抛出异常
     *
     * @return java.lang.String 节点值
     * @throws IllegalArgumentException 参数为空时抛出
     * @throws IndexOutOfBoundsException 无匹配节点时抛出
     * <AUTHOR>
     * @date 2023/10/27 13:30
     * @since  1.3.0
     **/
    public static String getFirstNodeValue(String xmlStr, String nodeName, String attributeName) {
        validateArgs(xmlStr,nodeName,attributeName);
        return getNodes(xmlStr,nodeName,attributeName).get(0).getStringValue();
    }


    /**
     * 获取所有指定名称且包含指定属性的节点的取值
     *
     * @param xmlStr xml字符串 如果为空，抛出异常
     * @param nodeName 节点名称 如果为空，抛出异常
     * @param attributeName 属性名称 如果为空，抛出异常
     *
     * @return java.lang.String 节点值
     * <AUTHOR>
     * @Date 2023/10/27 13:30
     * @since  1.3.0
     **/
    public static List<String> getAllNodeValue(String xmlStr, String nodeName, String attributeName) {
        validateArgs(xmlStr,nodeName,attributeName);
        return getNodes(xmlStr,nodeName,attributeName).stream().map(Node::getStringValue).collect(Collectors.toList());
    }

    /**
     * 获取第一个指定名称且包含指定属性的节点的xml字符串
     *
     * @param xmlStr xml字符串 如果为空，抛出异常
     * @param nodeName 节点名称 如果为空，抛出异常
     * @param attributeName 属性名称 如果为空，抛出异常
     *
     * @return java.lang.String 节点xml字符串
     * <AUTHOR>
     * @Date 2023/10/27 13:30
     * @since  1.3.0
     **/
    public static String getFirstNodeXmlStr(String xmlStr, String nodeName, String attributeName) {
        validateArgs(xmlStr,nodeName,attributeName);
        return getNodes(xmlStr,nodeName,attributeName).get(0).asXML();
    }


    /**
     * 获取所有指定名称且包含指定属性的节点的xml字符串
     *
     * @param xmlStr xml字符串 如果为空，抛出异常
     * @param nodeName 节点名称 如果为空，抛出异常
     * @param attributeName 属性名称 如果为空，抛出异常
     *
     * @return java.lang.String 节点xml字符串
     * <AUTHOR>
     * @Date 2023/10/27 13:30
     * @since  1.3.0
     **/
    public static List<String> getAllNodeXmlStr(String xmlStr, String nodeName, String attributeName) {
        validateArgs(xmlStr,nodeName,attributeName);
        return getNodes(xmlStr,nodeName,attributeName).stream().map(Node::asXML).collect(Collectors.toList());
    }

    /**
     * <p>
     *     获取第一个指定名称且包含指定属性的节点的该属性取值
     *     如果根据条件在字符串中查找不到对应的属性，则抛出异常
     * </p>
     *
     * @param xmlStr xml字符串  如果为空，抛出异常
     * @param nodeName 节点名称  如果为空，抛出异常
     * @param attributeName 属性名称  如果为空，抛出异常
     * @return java.lang.String 属性取值
     * <AUTHOR>
     * @date 2023/10/31 09:23
     * @since 1.3.0
     */
    public static String getFirstNodeAttributeValue(String xmlStr, String nodeName, String attributeName) {
        validateArgs(xmlStr,nodeName, attributeName);
        List<Node> nodes = getNodes(xmlStr, nodeName, attributeName);
        return ((Element)nodes.get(0)).attribute(attributeName).getValue();
    }

    /**
     * <p>
     *     获取所有指定名称且包含指定属性的节点的该属性取值
     *     如果根据条件在字符串中查找不到对应的属性，则抛出异常
     * </p>
     *
     * @param xmlStr xml字符串  如果为空，抛出异常
     * @param nodeName 节点名称  如果为空，抛出异常
     * @param attributeName 属性名称  如果为空，抛出异常
     * @return java.lang.String 属性取值
     * <AUTHOR>
     * @date 2023/10/31 09:23
     * @since  1.3.0
     */
    public static List<String> getAllNodeAttributeValue(String xmlStr, String nodeName, String attributeName) {
        validateArgs(xmlStr,nodeName, attributeName);
        List<Node> nodes = getNodes(xmlStr, nodeName, attributeName);
        return nodes.stream().map(node -> ((Element)node).attribute(attributeName).getValue()).collect(Collectors.toList());
    }

    /**
     * <p>删除满足条件的节点</p>
     *
     * @param xml 待操作的xml数据
     * @param nodeXpathExpr xpath表达式
     * @return java.lang.String 删除成功后的xml字符串
     * <AUTHOR>
     * @date 2024/06/07 10:21
     * @since 1.5.0
     */
    public static String removeNode(String xml, String nodeXpathExpr) {
        Document document = getDocumentForXmlStr(xml);
        List<Node> nodes = Optional.ofNullable(document.selectNodes(nodeXpathExpr)).orElse(Collections.emptyList());
        for (Node node : nodes) {
            Element parent = node.getParent();
            if (null == parent) {
                throw new RuntimeException("Cannot delete root node");
            } else {
                parent.remove(node);
            }
        }
        return document.asXML();
    }

    /**
     * <p>删除满足条件的属性</p>
     *
     * @param xml 待操作的xml数据
     * @param nodeXpathExpr xpath表达式
     * @return java.lang.String 删除成功后的xml字符串
     * <AUTHOR>
     * @date 2024/06/07 10:21
     * @since 1.5.0
     */
    public static String removeAttribute(String xml, String nodeXpathExpr,String attributeName) {
        Document document = getDocumentForXmlStr(xml);
        List<Node> nodes = Optional.ofNullable(document.selectNodes(nodeXpathExpr)).orElse(Collections.emptyList());
        nodes.forEach(node -> ((Element)node).remove(new DefaultAttribute(new QName(attributeName))));
        return document.asXML();
    }

    /**
     * <p>删除满足条件的text</p>
     *
     * @param xml 待操作的xml数据
     * @param nodeXpathExpr xpath表达式
     * @return java.lang.String 删除成功后的xml字符串
     * <AUTHOR>
     * @date 2024/06/07 10:21
     * @since 1.5.0
     */
    public static String removeNodeText(String xml, String nodeXpathExpr) {
        Document document = getDocumentForXmlStr(xml);
        List<Node> nodes = Optional.ofNullable(document.selectNodes(nodeXpathExpr)).orElse(Collections.emptyList());
        nodes.forEach(node -> ((Element)node).setText(""));
        return document.asXML();
    }

    /**
     * <p>获取满足xpath规则的节点text</p>
     *
     * @param xml 待操作xml数据
     * @param nodeXpathExpr xpath表达式
     * @param onlyFirstFlag 是否仅获取第一个节点
     * @return java.util.List<java.lang.String> 满足条件的text取值集合
     * <AUTHOR>
     * @date 2024/08/10 09:26
     * @since 1.5.0
     */
    public static  List<String> getNodeText(String xml, String nodeXpathExpr, Boolean onlyFirstFlag) {
        Document document = getDocumentForXmlStr(xml);
        List<Node> nodes = Optional.ofNullable(document.selectNodes(nodeXpathExpr)).orElse(Collections.emptyList());
        if(nodes.isEmpty()) {
            return Collections.emptyList();
        }
        if (Boolean.TRUE.equals(onlyFirstFlag)) {
            return Arrays.asList(((Element)nodes.get(0)).getTextTrim());
        }
        List<String> list = new ArrayList<>(nodes.size());
        nodes.forEach(node -> list.add(((Element)node).getTextTrim()));
        return list;
    }

    /**
     * <p>修改满足条件的节点的属性属性取值</p>
     *
     * @param xml 待操作的xml数据
     * @param nodeXpathExpr xpath表达式
     * @return java.lang.String 更新成功后的xml字符串
     * <AUTHOR>
     * @date 2024/06/07 10:21
     * @since 1.5.0
     */
    public static String updateAttribute(String xml, String nodeXpathExpr,String attributeName, String attributeValueNew, boolean onlyFirstFlag) {
        Document document = getDocumentForXmlStr(xml);
        List<Node> nodes = Optional.ofNullable(document.selectNodes(nodeXpathExpr)).orElse(Collections.emptyList());
        if (nodes.isEmpty()) {
            return xml;
        }
        if (Boolean.TRUE.equals(onlyFirstFlag)) {
            ((Element)nodes.get(0)).attribute(attributeName).setValue(attributeValueNew);
        }else {
            nodes.forEach(node -> ((Element) node).attribute(attributeName).setValue(attributeValueNew));
        }
        return document.asXML();
    }

    /**
     * <p>修改满足条件的节点的text</p>
     *
     * @param xml 待操作的xml数据
     * @param nodeXpathExpr xpath表达式
     * @return java.lang.String 更新成功后的xml字符串
     * <AUTHOR>
     * @date 2024/06/07 10:21
     * @since 1.5.0
     */
    public static String updateNodeText(String xml, String nodeXpathExpr, String textValueNew, boolean onlyFirstFlag) {
        Document document = getDocumentForXmlStr(xml);
        List<Node> nodes = Optional.ofNullable(document.selectNodes(nodeXpathExpr)).orElse(Collections.emptyList());
        if (nodes.isEmpty()) {
            return xml;
        }
        if (Boolean.TRUE.equals(onlyFirstFlag)) {
            nodes.get(0).setText(textValueNew);
        }
        nodes.forEach(node -> node.setText(textValueNew));
        return document.asXML();
    }




    /**
     * <p>向满足条件条件的节点新增子节点</p>
     *
     * @param xml 待操作xml字符串
     * @param xpathExpr xpath表达式  示例  //p1  查找名称为 p1 的节点
     * @param newNodeName 待新增节点名称
     * @param newNodeValue 待新增节点取值
     * @param newNodeAttributeMap 待新增节点属性集合
     * @param addForFirst 是否仅新增第一个满足条件的节点 true：是  false：否
     * @return java.lang.String 处理成功后的xml字符串
     * <AUTHOR>
     * @date 2024/06/07 10:21
     * @since 1.5.0
     */
    public static String addChildNode(String xml, String xpathExpr, String newNodeName, String newNodeValue, Map<String,String> newNodeAttributeMap,
                                      boolean addForFirst) {
        Document document = getDocumentForXmlStr(xml);
        List<Node> nodes = Optional.ofNullable(document.selectNodes(xpathExpr)).orElse(Collections.emptyList());

        for (Node node : nodes) {
            DefaultElement domElement = (DefaultElement) node;
            DefaultElement newElement = new DefaultElement(newNodeName);
            newElement.setText(newNodeValue);
            newNodeAttributeMap = Optional.ofNullable(newNodeAttributeMap).orElse(new HashMap<>());
            newNodeAttributeMap.forEach((keys, vals) -> newElement.addAttribute(new QName(keys),vals));
            domElement.add(newElement);
            if (!addForFirst) {
                break;
            }
        }
        return document.asXML();
    }

    /**
     * <p>向满足条件的节点前面或者后面插入节点</p>
     *
     * @param xml 待处理xml字符串
     * @param xpathExpr xpath表达式  例如：//p1  标识名称 p1 的节点
     * @param newNnodeName 新增节点名称
     * @param newNodeValue 新增节点取值
     * @param newNodeAttributeMap 新增节点属性集合
     * @param beforeOrAfter 前面插入还是后附插入  true：前方插入 false：后方插入
     * @param insertForFirst 是否仅对第一个满足条件的节点操作  true：仅对满足条件的第一个进行操作  false：对所有满足条件的节点进行操作
     * @return java.lang.String 处理成功后的xml字符串
     * <AUTHOR>
     * @date 2024/06/08 13:15
     * @since 1.5.0
     */
    public static String insertNode(String xml, String xpathExpr, String newNnodeName, String newNodeValue, Map<String,String> newNodeAttributeMap,
                             boolean beforeOrAfter, boolean insertForFirst) {
        Document document = getDocumentForXmlStr(xml);
        List<Node> nodes = Optional.ofNullable(document.selectNodes(xpathExpr)).orElse(Collections.emptyList());
        for (Node node : nodes) {
            Element parent = node.getParent();
            Element currentElement = (Element) node;
            DefaultElement newElement = new DefaultElement(newNnodeName);
            newElement.setText(newNodeValue);
            newNodeAttributeMap = Optional.ofNullable(newNodeAttributeMap).orElse(new HashMap<>()) ;
            newNodeAttributeMap.forEach(newElement::addAttribute);
            if (beforeOrAfter) {
                parent.remove(currentElement);
                parent.add(newElement);
                parent.add(currentElement);
            }else {
                parent.add(newElement);
            }
            if (insertForFirst) {
                break;
            }

        }
        return document.asXML();
    }



    /**
     * 获取Document对象
     *
     * @param xmlStr 待处理的xml字符串
     * @return org.dom4j.Document 结果对象
     * <AUTHOR>
     * @Date 2023/9/27 10:38
     * @since 1.2.0
     **/
    private static Document getDocumentForXmlStr(String xmlStr) {
        try {
            // 创建解析器
            SAXReader xmlReader = new SAXReader();
            xmlReader.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            // 解析xml得到Document
            InputSource resourceAsStream = new InputSource(new StringReader(xmlStr));
            return xmlReader.read(resourceAsStream);
        }catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 参数校验
     *
     * @param xmlStr xml字符串
     * @param nodeName 节点名称
     * @param attributeName 属性名称
     *
     * <AUTHOR>
     * @Date 2023/10/27 14:02
     * @since  1.3.0
     **/
    private static void validateArgs(String xmlStr, String nodeName, String attributeName) {
        if (StringUtils.isBlank(xmlStr)) {
            throw new IllegalArgumentException("No XML string specified for query");
        }
        if (StringUtils.isBlank(nodeName)) {
            throw new IllegalArgumentException("Node name cannot be empty");
        }
        if (StringUtils.isBlank(attributeName)) {
            throw new IllegalArgumentException("Property name cannot be empty");
        }
    }

    /*
     * 获取所有指定名称且包含指定属性的节点
     *  如果不存在，则抛出异常
     *
     * @param xmlStr xml字符串
     * @param nodeName 节点名称
     * @param attributeName  属性名称
     *
     * @return java.util.List<org.dom4j.Node> 节点集合
     * <AUTHOR>
     * @Date 2023/10/27 14:20
     * @since  1.3.0
     **/
    private static List<Node> getNodes(String xmlStr, String nodeName, String attributeName) {
        try {
            Document document = getDocumentForXmlStr(xmlStr);
            StringBuilder sb = new StringBuilder("");
            sb.append("//").append(nodeName).append("[@").append(attributeName).append("]");
            List<Node> nodes = Optional.ofNullable(document.selectNodes(sb.toString())).orElse(Collections.emptyList());
            if (nodes.isEmpty()) {
                throw new RuntimeException("No nodes found that meet the criteria");
            }
            return nodes;
        }catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
