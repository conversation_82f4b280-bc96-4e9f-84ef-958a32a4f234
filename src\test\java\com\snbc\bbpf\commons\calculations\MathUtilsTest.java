/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.calculations;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.math.RoundingMode;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * 加减乘除法精度运算测试类
 *
 * <AUTHOR>
 * @module 精度计算
 * @date 2023/6/13 19:09
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class MathUtilsTest {

    @Test
    @DisplayName("加法小数点后默认保留10位")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testAdd_10Scale() {
        assertEquals(2.5555555556, MathUtils.add(1.00000000000000, 1.55555555555555555));
    }

    @Test
    @DisplayName("两个正数的加法")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testAdd_positive() {
        assertEquals(2.1, MathUtils.add(1, 1.1));
    }

    @Test
    @DisplayName("两个0的加法")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testAdd_zero() {
        assertEquals(0, MathUtils.add(0, 0));
    }

    @Test
    @DisplayName("两个负数相加")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testAdd_negative() {
        assertEquals(-2.246, MathUtils.add(-1.111, -1.135));
    }

    @Test
    @DisplayName("正负整数相加")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testAdd_Integer() {
        assertEquals(1999900000, MathUtils.add(-99999, 1.999999999E9));
    }

    @Test
    @DisplayName("0与整数相加")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testAdd_ZeroAndInteger() {
        assertEquals(1999999999, MathUtils.add(0, 1999999999));
    }

    @Test
    @DisplayName("正数与0相加")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testAdd_DoubleAndZero() {
        assertEquals(321654789.321654789, MathUtils.add(321654789.321654789, 0));
    }

    @Test
    @DisplayName("两个正数的加法，保留两位小数四舍五入")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testAddSetScale_positive() {
        assertEquals(2.25, MathUtils.addSetScale(1.15, 1.1, 2, RoundingMode.HALF_UP));
    }

    @Test
    @DisplayName("两个0的加法，保留两位小数四舍五入")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testAddSetScale_zero() {
        assertEquals(0, MathUtils.addSetScale(0, 0, 0, RoundingMode.HALF_UP));
    }

    @Test
    @DisplayName("两个负数相加，保留四位小数四舍五入")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testAddSetScale_negative() {
        assertEquals(-2.2466, MathUtils.addSetScale(-1.11111, -1.13544, 4, RoundingMode.HALF_UP));
    }

    @Test
    @DisplayName("正负整数相加，保留零位小数向上进位")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testAddSetScale_Integer() {
        assertEquals(1999900000, MathUtils.addSetScale(-99999, 1999999999, 0, RoundingMode.UP));
    }

    @Test
    @DisplayName("0与整数相加，保留一位小数向上进位")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testAddSetScale_ZeroAndInteger() {
        assertEquals(1999999999, MathUtils.addSetScale(0, 1999999999, 1, RoundingMode.UP));
    }

    @Test
    @DisplayName("正数与0相加，保留六位小数向上进位")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testAddSetScale_DoubleAndZero() {
        assertEquals(321654789.321655, MathUtils.addSetScale(321654789.321654789, 0, 6, RoundingMode.UP));
    }

    @Test
    @DisplayName("加法，非法的精度参数")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testAddSetScale_InvalidScale() {
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> MathUtils.addSetScale(321654789.321654789, 0, -6, RoundingMode.UP));
        assertEquals("The scale must be a positive integer or zero", thrown.getMessage());
    }

    @Test
    @DisplayName("加法，非法的计算模式")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testAddSetScale_InvalidRoundingMode() {
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> MathUtils.addSetScale(321654789.321654789, 0, 0, RoundingMode.DOWN));
        assertEquals("Invalid rounding mode", thrown.getMessage());
    }

    @Test
    @DisplayName("加法，计算模式为空")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/6")
    })
    void testAddSetScale_NullRoundingMode() {
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> MathUtils.addSetScale(321654789.321654789, 0, 0, null));
        assertEquals("null rounding mode", thrown.getMessage());
    }

    @Test
    @DisplayName("两个正数的减法")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testSub_positive() {
        assertEquals(-0.1, MathUtils.sub(1, 1.1));
    }

    @Test
    @DisplayName("两个0的减法")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testSub_zero() {
        assertEquals(0, MathUtils.sub(0, 0));
    }

    @Test
    @DisplayName("减法，小数点后默认保留10位")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testSub_10Scale() {
        assertEquals(100, MathUtils.sub(100, 0.00000000001));
    }

    @Test
    @DisplayName("两个负数相减")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testSub_negative() {
        assertEquals(0.024, MathUtils.sub(-1.111, -1.135));
    }

    @Test
    @DisplayName("正负整数相减")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testSub_Integer() {
        assertEquals(-200099998, MathUtils.sub(-99999, 199999999));
    }

    @Test
    @DisplayName("0与整数相减")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testSub_ZeroAndInteger() {
        assertEquals(-1999999999, MathUtils.sub(0, 1999999999));
    }

    @Test
    @DisplayName("正数与0相减")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testSub_DoubleAndZero() {
        assertEquals(321654789.321654789, MathUtils.sub(321654789.321654789, 0));
    }

    @Test
    @DisplayName("两个正数的减法，保留两位小数四舍五入")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testsubSetScale_positive() {
        assertEquals(0.05, MathUtils.subSetScale(1.15, 1.1, 2, RoundingMode.HALF_UP));
    }

    @Test
    @DisplayName("两个0的减法，保留两位小数四舍五入")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testsubSetScale_zero() {
        assertEquals(0, MathUtils.subSetScale(0, 0, 0, RoundingMode.HALF_UP));
    }

    @Test
    @DisplayName("两个负数相减，保留四位小数四舍五入")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testsubSetScale_negative() {
        assertEquals(0.0243, MathUtils.subSetScale(-1.11111, -1.13544, 4, RoundingMode.HALF_UP));
    }

    @Test
    @DisplayName("正负整数相减，保留零位小数向上进位")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testsubSetScale_Integer() {
        assertEquals(-2000099998, MathUtils.subSetScale(-99999, 1999999999, 0, RoundingMode.UP));
    }

    @Test
    @DisplayName("0与整数相减，保留一位小数向上进位")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testsubSetScale_ZeroAndInteger() {
        assertEquals(-1999999999, MathUtils.subSetScale(0, 1999999999, 1, RoundingMode.UP));
    }

    @Test
    @DisplayName("正数与0相减，保留六位小数向上进位")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testsubSetScale_DoubleAndZero() {
        assertEquals(321654789.321655, MathUtils.subSetScale(321654789.321654789, 0, 6, RoundingMode.UP));
    }

    @Test
    @DisplayName("减法，非法的精度参数")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testsubSetScale_InvalidScale() {
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> MathUtils.subSetScale(321654789.321654789, 0, -6, RoundingMode.UP));
        assertEquals("The scale must be a positive integer or zero", thrown.getMessage());
    }

    @Test
    @DisplayName("减法，非法的计算模式")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testsubSetScale_InvalidRoundingMode() {
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> MathUtils.subSetScale(321654789.321654789, 0, 0, RoundingMode.DOWN));
        assertEquals("Invalid rounding mode", thrown.getMessage());
    }

    @Test
    @DisplayName("减法，计算模式为空")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/6")
    })
    void testsubSetScale_NullRoundingMode() {
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> MathUtils.subSetScale(321654789.321654789, 0, 0, null));
        assertEquals("null rounding mode", thrown.getMessage());
    }

    @Test
    @DisplayName("两个正数的乘法")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testmul_positive() {
        assertEquals(1.1, MathUtils.mul(1, 1.1));
    }

    @Test
    @DisplayName("两个0的乘法")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testmul_zero() {
        assertEquals(0, MathUtils.mul(0, 0));
    }

    @Test
    @DisplayName("两个负数相乘")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testmul_negative() {
        assertEquals(1.260985, MathUtils.mul(-1.111, -1.135));
    }

    @Test
    @DisplayName("正负整数相乘")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testmul_Integer() {
        assertEquals(-19899801, MathUtils.mul(-99999, 199));
    }

    @Test
    @DisplayName("0与整数相乘")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testmul_ZeroAndInteger() {
        assertEquals(0, MathUtils.mul(0, 1999999999));
    }

    @Test
    @DisplayName("正数与0相乘")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testmul_DoubleAndZero() {
        assertEquals(0, MathUtils.mul(321654789.321654789, 0));
    }

    @Test
    @DisplayName("乘法，小数点后默认保留10位")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testmul_10Scale() {
        assertEquals(222222.2222222222, MathUtils.mul(222222.2222222222222222222222, 1));
    }

    @Test
    @DisplayName("两个正数的乘法，保留两位小数四舍五入")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testmulSetScale_positive() {
        assertEquals(1.27, MathUtils.mulSetScale(1.15, 1.1, 2, RoundingMode.HALF_UP));
    }

    @Test
    @DisplayName("两个0的乘法，保留两位小数四舍五入")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testmulSetScale_zero() {
        assertEquals(0, MathUtils.mulSetScale(0, 0, 0, RoundingMode.HALF_UP));
    }

    @Test
    @DisplayName("两个负数相乘，保留四位小数四舍五入")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testmulSetScale_negative() {
        assertEquals(1.2616, MathUtils.mulSetScale(-1.11111, -1.13544, 4, RoundingMode.HALF_UP));
    }

    @Test
    @DisplayName("正负整数相乘，保留零位小数向上进位")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testmulSetScale_Integer() {
        assertEquals(-9.9999E9, MathUtils.mulSetScale(-99999, 100000, 0, RoundingMode.UP));
    }

    @Test
    @DisplayName("0与整数相乘，保留一位小数向上进位")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testmulSetScale_ZeroAndInteger() {
        assertEquals(0, MathUtils.mulSetScale(0, 1999999999, 1, RoundingMode.UP));
    }

    @Test
    @DisplayName("正数与0相乘，保留六位小数向上进位")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testmulSetScale_DoubleAndZero() {
        assertEquals(0, MathUtils.mulSetScale(321654789.321654789, -0, 6, RoundingMode.UP));
    }

    @Test
    @DisplayName("乘法，非法的精度参数")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testmulSetScale_InvalidScale() {
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> MathUtils.mulSetScale(321654789.321654789, 0, -6, RoundingMode.UP));
        assertEquals("The scale must be a positive integer or zero", thrown.getMessage());
    }

    @Test
    @DisplayName("乘法，非法的计算模式")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testmulSetScale_InvalidRoundingMode() {
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> MathUtils.mulSetScale(321654789.321654789, 0, 0, RoundingMode.DOWN));
        assertEquals("Invalid rounding mode", thrown.getMessage());
    }

    @Test
    @DisplayName("乘法，计算模式为空")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/6")
    })
    void testmulSetScale_NullRoundingMode() {
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> MathUtils.mulSetScale(321654789.321654789, 0, 0, null));
        assertEquals("null rounding mode", thrown.getMessage());
    }

    @Test
    @DisplayName("两个正数的除法")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testdiv_positive() {
        assertEquals(0.9090909091, MathUtils.div(1, 1.1));
    }

    @Test
    @DisplayName("两个0的除法")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testdiv_zero() {
        ArithmeticException thrown = assertThrows(ArithmeticException.class,
                () -> MathUtils.div(0, 0));
        assertEquals("/ by zero", thrown.getMessage());
    }

    @Test
    @DisplayName("两个负数相除")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testdiv_negative() {
        assertEquals(0.9788546256, MathUtils.div(-1.111, -1.135));
    }

    @Test
    @DisplayName("正负整数相除")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testdiv_Integer() {
        assertEquals(-1, MathUtils.div(-199, 199));
    }

    @Test
    @DisplayName("0与整数相除")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testdiv_ZeroAndInteger() {
        assertEquals(0, MathUtils.div(0, 1999999999));
    }

    @Test
    @DisplayName("正数与1相除")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testdiv_DoubleAndZero() {
        assertEquals(321654789.32, MathUtils.div(321654789.32, 1));
    }

    @Test
    @DisplayName("两个正数的除法，保留两位小数四舍五入")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testdivSetScale_positive() {
        assertEquals(1.05, MathUtils.divSetScale(1.15, 1.1, 2, RoundingMode.HALF_UP));
    }

    @Test
    @DisplayName("两个0的除法，保留两位小数四舍五入")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testdivSetScale_zero() {
        ArithmeticException thrown = assertThrows(ArithmeticException.class,
                () -> MathUtils.divSetScale(0, 0, 0, RoundingMode.HALF_UP));
        assertEquals("/ by zero", thrown.getMessage());
    }

    @Test
    @DisplayName("两个负数相除，保留四位小数四舍五入")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testdivSetScale_negative() {
        assertEquals(4.4444, MathUtils.divSetScale(-8.888888, -2, 4, RoundingMode.HALF_UP));
    }

    @Test
    @DisplayName("正负整数相除，保留零位小数向上进位")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testdivSetScale_Integer() {
        assertEquals(-1, MathUtils.divSetScale(-99999, 100000, 0, RoundingMode.UP));
    }

    @Test
    @DisplayName("0与整数相除，保留一位小数向上进位")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testdivSetScale_ZeroAndInteger() {
        assertEquals(0, MathUtils.divSetScale(0, 1999999999, 1, RoundingMode.UP));
    }

    @Test
    @DisplayName("正数与1相除，保留六位小数向上进位")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testdivSetScale_DoubleAndZero() {
        assertEquals(321654789.321655, MathUtils.divSetScale(321654789.321654789, 1, 6, RoundingMode.UP));
    }

    @Test
    @DisplayName("除法，非法的精度参数")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testdivSetScale_InvalidScale() {
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> MathUtils.divSetScale(321654789.321654789, 0, -6, RoundingMode.UP));
        assertEquals("The scale must be a positive integer or zero", thrown.getMessage());
    }

    @Test
    @DisplayName("除法，非法的计算模式")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testdivSetScale_InvalidRoundingMode() {
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> MathUtils.divSetScale(321654789.321654789, 0, 0, RoundingMode.DOWN));
        assertEquals("Invalid rounding mode", thrown.getMessage());
    }

    @Test
    @DisplayName("除法，计算模式为空")
    @Tags({
            @Tag("@id:40"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/5")
    })
    void testdivSetScale_NullRoundingMode() {
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> MathUtils.divSetScale(321654789.321654789, 0, 0, null));
        assertEquals("null rounding mode", thrown.getMessage());
    }
}
    