/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.dates;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 对LocalDateTime进行时间计算  单元测试类
 * <p>计算当前时间一定时间（天，小时，分钟，秒，毫秒）之前或之后的时间</p>
 * <p>计算两个时间之间间隔时差（天，小时，分钟，秒，毫秒）<p>
 * <p>计算当前时间一定天数之前或之后的00点00分00秒或23点59分59秒的时间</p>
 * <p>计算当前时间所在当前年/季/月/周第一天的起始时间和最后一天的起始时间，可区分00点00分00秒或23点59分59秒</p>
 * <p>计算当前时间的（天，小时，分钟，秒，毫秒）数</p>
 * <p>根据（天，小时，分钟，秒，毫秒）数转换成时间</p>
 *
 * <AUTHOR>
 * @module 日期处理
 * @date 2023-05-16 13:04
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class LocalDateTimeUtilsTest {
    LocalDateTime localDateTime;
    LocalDateTime targetDateTime;

    @BeforeEach
    void setUp() {
        localDateTime = LocalDateTime.of(2023, 5, 16, 17, 18, 19, 20000000);
        targetDateTime = LocalDateTime.of(2024, 5, 16, 17, 18, 19, 20000000);
    }

    @Test
    @DisplayName("测试LocalDateTime的加任意年月日时分秒毫秒")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testPlus() {
        final LocalDateTime plusReslut = LocalDateTimeUtils.plus(localDateTime, 1, 1, 1, 1, 1, 1, 1);
        assertEquals(2024, LocalDateTimeUtils.year(plusReslut));
        assertEquals(6, LocalDateTimeUtils.month(plusReslut));
        assertEquals(17, LocalDateTimeUtils.day(plusReslut));
        assertEquals(18, LocalDateTimeUtils.hour(plusReslut));
        assertEquals(19, LocalDateTimeUtils.minute(plusReslut));
        assertEquals(20, LocalDateTimeUtils.second(plusReslut));
        assertEquals(21, LocalDateTimeUtils.millisecond(plusReslut));
    }

    @Test
    @DisplayName("测试LocalDateTime的diff")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testDiff() {
        assertEquals(366, LocalDateTimeUtils.diff(localDateTime, targetDateTime, TimeUnit.DAYS));
        assertEquals(8784, LocalDateTimeUtils.diff(localDateTime, targetDateTime, TimeUnit.HOURS));
        assertEquals(527040, LocalDateTimeUtils.diff(localDateTime, targetDateTime, TimeUnit.MINUTES));
    }


    @Test
    @DisplayName("测试LocalDateTime的加任意年")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void plusYear() {
        assertEquals(2024, LocalDateTimeUtils.year(LocalDateTimeUtils.plusYears(localDateTime, 1)));
    }

    @Test
    @DisplayName("测试LocalDateTime的加任意月")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void plusMonth() {
        assertEquals(6, LocalDateTimeUtils.month(LocalDateTimeUtils.plusMonths(localDateTime, 1)));
    }

    @Test
    @DisplayName("测试LocalDateTime的加任意日")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void plusDays() {
        assertEquals(17, LocalDateTimeUtils.day(LocalDateTimeUtils.plusDays(localDateTime, 1)));
    }


    @Test
    @DisplayName("测试LocalDateTime的加任意小时")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testPlusHours() {
        assertEquals(18, LocalDateTimeUtils.hour(LocalDateTimeUtils.plusHours(localDateTime, 1)));
    }

    @Test
    @DisplayName("测试LocalDateTime的加任意分钟")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testPlusMinutes() {
        assertEquals(19, LocalDateTimeUtils.minute(LocalDateTimeUtils.plusMinutes(localDateTime, 1)));
    }

    @Test
    @DisplayName("测试LocalDateTime的加任意秒")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testPlusSeconds() {
        assertEquals(20, LocalDateTimeUtils.second(LocalDateTimeUtils.plusSeconds(localDateTime, 1)));
    }

    @Test
    @DisplayName("测试LocalDateTime的加任意毫秒")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testPlusMilliseconds() {
        assertEquals(21, LocalDateTimeUtils.millisecond(LocalDateTimeUtils.plusMilliseconds(localDateTime, 1)));
    }


    @Test
    @DisplayName("获取LocaleDateTime的年份")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testYear() {
        assertEquals(2023, LocalDateTimeUtils.year(localDateTime));
    }

    @Test
    @DisplayName("获取LocaleDateTime的月份")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testMonth() {
        assertEquals(5, LocalDateTimeUtils.month(localDateTime));
    }

    @Test
    @DisplayName("获取LocaleDateTime的天数")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testDay() {
        assertEquals(16, LocalDateTimeUtils.day(localDateTime));
    }

    @Test
    @DisplayName("获取LocaleDateTime的小时")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testHour() {
        assertEquals(17, LocalDateTimeUtils.hour(localDateTime));
    }

    @Test
    @DisplayName("获取LocaleDateTime的分钟")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testMinute() {
        assertEquals(18, LocalDateTimeUtils.minute(localDateTime));
    }

    @Test
    @DisplayName("获取LocaleDateTime的秒")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testSecond() {
        assertEquals(19, LocalDateTimeUtils.second(localDateTime));
    }

    @Test
    @DisplayName("获取LocaleDateTime的毫秒")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testMillisecond() {
        assertEquals(20, LocalDateTimeUtils.millisecond(localDateTime));
    }

    @Test
    @DisplayName("获取LocaleDateTime的starOfDay")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void starOfDay() {
        final LocalDateTime reslut = LocalDateTimeUtils.starOfDay(localDateTime);
        assertEquals(0, LocalDateTimeUtils.hour(reslut));
        assertEquals(0, LocalDateTimeUtils.minute(reslut));
        assertEquals(0, LocalDateTimeUtils.second(reslut));
        assertEquals(0, LocalDateTimeUtils.millisecond(reslut));
    }

    @Test
    @DisplayName("获取LocaleDateTime的endOfDay")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void endOfDay() {
        final LocalDateTime reslut = LocalDateTimeUtils.endOfDay(localDateTime);
        assertEquals(23, LocalDateTimeUtils.hour(reslut));
        assertEquals(59, LocalDateTimeUtils.minute(reslut));
        assertEquals(59, LocalDateTimeUtils.second(reslut));
    }

    @Test
    @DisplayName("获取LocaleDateTime的firstDayOfYear")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void firstDayOfYear() {
        final LocalDateTime reslut = LocalDateTimeUtils.firstDayOfYear(localDateTime);
        assertEquals(2023, LocalDateTimeUtils.year(reslut));
        assertEquals(1, LocalDateTimeUtils.month(reslut));
        assertEquals(1, LocalDateTimeUtils.day(reslut));
        assertEquals(0, LocalDateTimeUtils.hour(reslut));
        assertEquals(0, LocalDateTimeUtils.minute(reslut));
        assertEquals(0, LocalDateTimeUtils.second(reslut));
    }

    @Test
    @DisplayName("获取LocaleDateTime的firstDayOfYearWithOption")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void firstDayOfYear_with_option() {
        final LocalDateTime reslut = LocalDateTimeUtils.firstDayOfYear(localDateTime, false);
        assertEquals(2023, LocalDateTimeUtils.year(reslut));
        assertEquals(1, LocalDateTimeUtils.month(reslut));
        assertEquals(1, LocalDateTimeUtils.day(reslut));
        assertEquals(23, LocalDateTimeUtils.hour(reslut));
        assertEquals(59, LocalDateTimeUtils.minute(reslut));
        assertEquals(59, LocalDateTimeUtils.second(reslut));
    }

    @Test
    @DisplayName("获取LocaleDateTime的lastDayOfYear")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void lastDayOfYear() {
        final LocalDateTime reslut = LocalDateTimeUtils.lastDayOfYear(localDateTime);
        assertEquals(2023, LocalDateTimeUtils.year(reslut));
        assertEquals(12, LocalDateTimeUtils.month(reslut));
        assertEquals(31, LocalDateTimeUtils.day(reslut));
        assertEquals(0, LocalDateTimeUtils.hour(reslut));
        assertEquals(0, LocalDateTimeUtils.minute(reslut));
        assertEquals(0, LocalDateTimeUtils.second(reslut));
    }

    @Test
    @DisplayName("获取LocaleDateTime的lastDayOfYearWithOption")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void lastDayOfYear_with_option() {
        final LocalDateTime reslut = LocalDateTimeUtils.lastDayOfYear(localDateTime, false);
        assertEquals(2023, LocalDateTimeUtils.year(reslut));
        assertEquals(12, LocalDateTimeUtils.month(reslut));
        assertEquals(31, LocalDateTimeUtils.day(reslut));
        assertEquals(23, LocalDateTimeUtils.hour(reslut));
        assertEquals(59, LocalDateTimeUtils.minute(reslut));
        assertEquals(59, LocalDateTimeUtils.second(reslut));
    }

    @Test
    @DisplayName("获取LocaleDateTime的firstDayOfMonth")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void firstDayOfMonth() {
        final LocalDateTime reslut = LocalDateTimeUtils.firstDayOfMonth(localDateTime);
        assertEquals(2023, LocalDateTimeUtils.year(reslut));
        assertEquals(5, LocalDateTimeUtils.month(reslut));
        assertEquals(1, LocalDateTimeUtils.day(reslut));
        assertEquals(0, LocalDateTimeUtils.hour(reslut));
        assertEquals(0, LocalDateTimeUtils.minute(reslut));
        assertEquals(0, LocalDateTimeUtils.second(reslut));
    }

    @Test
    @DisplayName("获取LocaleDateTime的firstDayOfMonthWithOption")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void firstDayOfMonth_with_option() {
        final LocalDateTime reslut = LocalDateTimeUtils.firstDayOfMonth(localDateTime, false);
        assertEquals(2023, LocalDateTimeUtils.year(reslut));
        assertEquals(5, LocalDateTimeUtils.month(reslut));
        assertEquals(1, LocalDateTimeUtils.day(reslut));
        assertEquals(23, LocalDateTimeUtils.hour(reslut));
        assertEquals(59, LocalDateTimeUtils.minute(reslut));
        assertEquals(59, LocalDateTimeUtils.second(reslut));
    }

    @Test
    @DisplayName("获取LocaleDateTime的lastDayOfMonth")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void lastDayOfMonth() {
        final LocalDateTime reslut = LocalDateTimeUtils.lastDayOfMonth(localDateTime);
        assertEquals(2023, LocalDateTimeUtils.year(reslut));
        assertEquals(5, LocalDateTimeUtils.month(reslut));
        assertEquals(31, LocalDateTimeUtils.day(reslut));
        assertEquals(0, LocalDateTimeUtils.hour(reslut));
        assertEquals(0, LocalDateTimeUtils.minute(reslut));
        assertEquals(0, LocalDateTimeUtils.second(reslut));

    }

    @Test
    @DisplayName("获取LocaleDateTime的lastDayOfMonthWithOption")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void lastDayOfMonth_with_option() {
        final LocalDateTime reslut = LocalDateTimeUtils.lastDayOfMonth(localDateTime, false);
        assertEquals(2023, LocalDateTimeUtils.year(reslut));
        assertEquals(5, LocalDateTimeUtils.month(reslut));
        assertEquals(31, LocalDateTimeUtils.day(reslut));
        assertEquals(23, LocalDateTimeUtils.hour(reslut));
        assertEquals(59, LocalDateTimeUtils.minute(reslut));
        assertEquals(59, LocalDateTimeUtils.second(reslut));
    }

    @Test
    @DisplayName("获取LocaleDateTime的ofLocalDateTime")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void ofLocalDateTime() {
        LocalDateTime result = LocalDateTimeUtils.ofLocalDateTime(2023, 5, 16, 17, 18, 19, 20000000);
        assertEquals(2023, LocalDateTimeUtils.year(result));
        assertEquals(5, LocalDateTimeUtils.month(result));
        assertEquals(16, LocalDateTimeUtils.day(result));
        assertEquals(17, LocalDateTimeUtils.hour(result));
        assertEquals(18, LocalDateTimeUtils.minute(result));
        assertEquals(19, LocalDateTimeUtils.second(result));
        assertEquals(20, LocalDateTimeUtils.millisecond(result));
    }

    @Test
    @DisplayName("判断时间是否处于start和end时间之间")
    @Tags({
            @Tag("@id:23"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/4/21")
    })
    void test_isEffectiveDate(){
        LocalDateTime nowDate = LocalDateTimeUtils.ofLocalDateTime(2023, 5, 16, 17, 18, 19, 20000000);
        LocalDateTime startDate = LocalDateTimeUtils.ofLocalDateTime(2023, 5, 16, 16, 18, 19, 20000000);
        LocalDateTime endDate = LocalDateTimeUtils.ofLocalDateTime(2023, 5, 16, 18, 18, 19, 20000000);
        assertTrue(LocalDateTimeUtils.isEffectiveDate(nowDate, startDate, endDate));
        startDate = LocalDateTimeUtils.ofLocalDateTime(2023, 5, 16, 19, 19, 19, 20000000);
        endDate = LocalDateTimeUtils.ofLocalDateTime(2023, 5, 16, 20, 18, 19, 20000000);
        assertFalse(LocalDateTimeUtils.isEffectiveDate(nowDate, startDate, endDate));
    }

    @Test
    @DisplayName("LocalDateTime转换为LocalDate")
    @Tags({
            @Tag("@id:20"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/5/24")
    })
    void test_toLocalDate(){
        LocalDateTime nowDate = LocalDateTimeUtils.ofLocalDateTime(2023, 5, 16, 17, 18, 19, 20000000);
        LocalDate loacalDate = LocalDateTimeUtils.toLocalDate(nowDate);
        int year = loacalDate.getYear();
        int monthValue = loacalDate.getMonthValue();
        int dayOfMonth = loacalDate.getDayOfMonth();
        assertEquals(year,2023);
        assertEquals(monthValue,5);
        assertEquals(dayOfMonth,16);
    }

    @Test
    @DisplayName("LocalDateTime转换为Date")
    @Tags({
            @Tag("@id:20"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/5/24")
    })
    void test_toDate(){
        LocalDateTime nowDate = LocalDateTimeUtils.ofLocalDateTime(2023, 5, 16, 17, 18, 19, 20000000);
        Date date = LocalDateTimeUtils.toDate(nowDate);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int year = calendar.get(Calendar.YEAR);
        int monthValue = calendar.get(Calendar.MONTH)+1;
        int dayOfMonth = calendar.get(Calendar.DAY_OF_MONTH);
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int minute = calendar.get(Calendar.MINUTE);
        int second = calendar.get(Calendar.SECOND);
        assertEquals(year,2023);
        assertEquals(monthValue,5);
        assertEquals(dayOfMonth,16);
        assertEquals(hour,17);
        assertEquals(minute,18);
        assertEquals(second,19);
    }

    @Test
    @DisplayName("LocalDateTime转换为ZonedDateTime")
    @Tags({
            @Tag("@id:20"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/5/24")
    })
    void test_toZonedDateTime(){
        LocalDateTime nowDate = LocalDateTimeUtils.ofLocalDateTime(2023, 5, 16, 17, 18, 19, 20000000);
        ZonedDateTime zonedDateTime = LocalDateTimeUtils.toZonedDateTime(nowDate);
        int year = zonedDateTime.getYear();
        int monthValue = zonedDateTime.getMonthValue();
        int dayOfMonth = zonedDateTime.getDayOfMonth();
        int hour = zonedDateTime.getHour();
        int minute = zonedDateTime.getMinute();
        int second = zonedDateTime.getSecond();
        assertEquals(year,2023);
        assertEquals(monthValue,5);
        assertEquals(dayOfMonth,16);
        assertEquals(hour,17);
        assertEquals(minute,18);
        assertEquals(second,19);
    }

    @Test
    @DisplayName("获取两个时间之间的日期")
    @Tags({
            @Tag("@id:104"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/6/20")
    })
    void testGetBetweenDate_tenDay(){
        LocalDateTime start = LocalDateTimeUtils.ofLocalDateTime(2024,6,21,0,0,0,0);
        LocalDateTime end = LocalDateTimeUtils.ofLocalDateTime(2024,7,1,0,0,0,0);
        List<LocalDateTime> betweenDate = LocalDateTimeUtils.getBetweenDate(start, end, ChronoUnit.DAYS, 1);
        assertEquals(betweenDate.size(),11);
    }

    @Test
    @DisplayName("获取两个时间之间的日期,不满足条件测试")
    @Tags({
            @Tag("@id:104"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/6/20")
    })
    void testGetBetweenDate_parmError(){
        LocalDateTime start = LocalDateTimeUtils.ofLocalDateTime(2024,6,21,0,0,0,0);
        LocalDateTime end = LocalDateTimeUtils.ofLocalDateTime(2024,7,1,0,0,0,0);

        assertThrows(IllegalArgumentException.class,
                () -> LocalDateTimeUtils.getBetweenDate(null, end, ChronoUnit.DAYS, 1));
        assertThrows(IllegalArgumentException.class,
                () -> LocalDateTimeUtils.getBetweenDate(start, null, ChronoUnit.DAYS, 1));
        assertThrows(IllegalArgumentException.class,
                () -> LocalDateTimeUtils.getBetweenDate(start, end, ChronoUnit.DAYS, 0));
    }

    @Test
    @DisplayName("获取某个时间之前或之后的时间")
    @Tags({
            @Tag("@id:104"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/6/20")
    })
    void testGetNextDates_tenDay(){
        LocalDateTime start = LocalDateTimeUtils.ofLocalDateTime(2024,6,21,0,0,0,0);
        List<LocalDateTime> nextAfterDates = LocalDateTimeUtils.getNextDates(start, ChronoUnit.DAYS, 10, false);
        assertEquals(nextAfterDates.size(),10);
        List<LocalDateTime> nextBeforDates = LocalDateTimeUtils.getNextDates(start, ChronoUnit.DAYS, -10, false);
        assertEquals(nextBeforDates.size(),10);
    }

    @Test
    @DisplayName("获取某个时间之前或之后的时间,错误参数")
    @Tags({
            @Tag("@id:104"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/6/20")
    })
    void testGetNextDates_parmError(){
        LocalDateTime start = LocalDateTimeUtils.ofLocalDateTime(2024,6,21,0,0,0,0);
        assertThrows(IllegalArgumentException.class,
                () -> LocalDateTimeUtils.getNextDates(null, ChronoUnit.DAYS, 10, false));
        assertThrows(IllegalArgumentException.class,
                () -> LocalDateTimeUtils.getNextDates(start, null, 10, false));
    }

    @Test
    @DisplayName("获取两个时间之间的日期,不包括开始和结束时间")
    @Tags({
            @Tag("@id:104"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/6/20")
    })
    void testGetBetweenDate_noIncludeStartAndEnd(){
        LocalDateTime start = LocalDateTimeUtils.ofLocalDateTime(2024,6,21,0,0,0,0);
        LocalDateTime end = LocalDateTimeUtils.ofLocalDateTime(2024,7,1,0,0,0,0);
        List<LocalDateTime> betweenDate = LocalDateTimeUtils.getBetweenDate(start, end, ChronoUnit.DAYS, 1,false,false);
        assertEquals(betweenDate.size(),9);
        assertNotEquals(betweenDate.get(0),start);
        assertNotEquals(betweenDate.get(betweenDate.size()-1),end);
    }

    @Test
    @DisplayName("获取两个时间之间的日期,倒序列表")
    @Tags({
            @Tag("@id:104"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/6/20")
    })
    void testGetBetweenDate_reverse(){
        LocalDateTime start = LocalDateTimeUtils.ofLocalDateTime(2024,6,21,0,0,0,0);
        LocalDateTime end = LocalDateTimeUtils.ofLocalDateTime(2024,7,1,0,0,0,0);
        List<LocalDateTime> betweenDate = LocalDateTimeUtils.getBetweenDate(start, end, ChronoUnit.DAYS, -1);
        assertEquals(betweenDate.size(),11);
        assertEquals(betweenDate.get(0),end);
        assertEquals(betweenDate.get(betweenDate.size()-1),start);
    }
}