/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.strings;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class StringEscapeUtilsTest {

    @ParameterizedTest
    @DisplayName("转义Java字符串")
    @Tags({
            @Tag("@id:16"),
            @Tag("@author:liulimin"),
            @Tag("@date:2023/5/16")
    })
    @ValueSource(strings = {"He didn't say, \"Stop!\""})
    void testEscapeJava(String input) {
        assertEquals("He didn't say, \\\"Stop!\\\"",StringEscapeUtils.escapeJava(input));
    }

    @ParameterizedTest
    @DisplayName("反转义Java字符串")
    @Tags({
            @Tag("@id:16"),
            @Tag("@author:liulimin"),
            @Tag("@date:2023/5/16")
    })
    @ValueSource(strings = {"He didn't say, \\\"Stop!\\\""})
    void testUnescapeJava(String input) {
        assertEquals("He didn't say, \"Stop!\"",StringEscapeUtils.unescapeJava(input));
    }

    @ParameterizedTest
    @DisplayName("转义JSON字符串")
    @Tags({
            @Tag("@id:16"),
            @Tag("@author:liulimin"),
            @Tag("@date:2023/5/16")
    })
    @ValueSource(strings = {"He didn't say, \"Stop!\""})
    void testEscapeJson(String input) {
        assertEquals("He didn't say, \\\"Stop!\\\"",StringEscapeUtils.escapeJson(input));
    }

    @ParameterizedTest
    @DisplayName("反转义JSON字符串")
    @Tags({
            @Tag("@id:16"),
            @Tag("@author:liulimin"),
            @Tag("@date:2023/5/16")
    })
    @ValueSource(strings = {"He didn't say, \\\"Stop!\\\""})
    void testUnescapeJson(String input) {
        assertEquals("He didn't say, \"Stop!\"",StringEscapeUtils.unescapeJson(input));
    }

    @ParameterizedTest
    @DisplayName("转义XML1.0字符串")
    @Tags({
            @Tag("@id:16"),
            @Tag("@author:liulimin"),
            @Tag("@date:2023/5/16")
    })
    @ValueSource(strings = {"\"bread\" & \"butter\""})
    void testEscapeXML10(String input) {
        assertEquals("&quot;bread&quot; &amp; &quot;butter&quot;",StringEscapeUtils.escapeXml10(input));
    }

    @ParameterizedTest
    @DisplayName("转义XML1.1字符串")
    @Tags({
            @Tag("@id:16"),
            @Tag("@author:liulimin"),
            @Tag("@date:2023/5/16")
    })
    @ValueSource(strings = {"\"bread\" & \"butter\""})
    void testEscapeXML11(String input) {
        assertEquals("&quot;bread&quot; &amp; &quot;butter&quot;",StringEscapeUtils.escapeXml11(input));
    }

    @ParameterizedTest
    @DisplayName("反转义XML字符串")
    @Tags({
            @Tag("@id:16"),
            @Tag("@author:liulimin"),
            @Tag("@date:2023/5/16")
    })
    @ValueSource(strings = {"&quot;bread&quot; &amp; &quot;butter&quot;"})
    void testunEscapeXML(String input) {
        assertEquals("\"bread\" & \"butter\"",StringEscapeUtils.unescapeXml(input));
    }

    @ParameterizedTest
    @DisplayName("转义CSV字符串")
    @Tags({
            @Tag("@id:16"),
            @Tag("@author:liulimin"),
            @Tag("@date:2023/5/16")
    })
    @ValueSource(strings = {"Venture \"Extended Edition\""})
    void testEscapeCsv(String input) {
        assertEquals("\"Venture \"\"Extended Edition\"\"\"",StringEscapeUtils.escapeCsv(input));
    }

    @ParameterizedTest
    @DisplayName("反转义CSV字符串")
    @Tags({
            @Tag("@id:16"),
            @Tag("@author:liulimin"),
            @Tag("@date:2023/5/16")
    })
    @ValueSource(strings = {"\"Venture \"\"Extended Edition\"\"\""})
    void testunEscapeCsv(String input) {
        assertEquals("Venture \"Extended Edition\"",StringEscapeUtils.unescapeCsv(input));
    }

    @ParameterizedTest
    @DisplayName("转义HTML3.0字符串")
    @Tags({
            @Tag("@id:16"),
            @Tag("@author:liulimin"),
            @Tag("@date:2023/5/16")
    })
    @ValueSource(strings = {"2 > 1"})
    void testEscapeHtml3(String input) {
        assertEquals("2 &gt; 1",StringEscapeUtils.escapeHtml3(input));
    }

    @ParameterizedTest
    @DisplayName("反转义HTML3.0字符串")
    @Tags({
            @Tag("@id:16"),
            @Tag("@author:liulimin"),
            @Tag("@date:2023/5/16")
    })
    @ValueSource(strings = {"2 &gt; 1"})
    void testUnEscapeHtml3(String input) {
        assertEquals("2 > 1",StringEscapeUtils.unescapeHtml3(input));
    }

    @ParameterizedTest
    @DisplayName("转义HTML4.0字符串")
    @Tags({
            @Tag("@id:16"),
            @Tag("@author:liulimin"),
            @Tag("@date:2023/5/16")
    })
    @ValueSource(strings = {"2 > 1"})
    void testEscapeHtml4(String input) {
        assertEquals("2 &gt; 1",StringEscapeUtils.escapeHtml4(input));
    }


    @ParameterizedTest
    @DisplayName("反转义HTML4.0字符串")
    @Tags({
            @Tag("@id:16"),
            @Tag("@author:liulimin"),
            @Tag("@date:2023/5/16")
    })
    @ValueSource(strings = {"2 &gt; 1"})
    void testUnEscapeHtml4(String input) {
        assertEquals("2 > 1",StringEscapeUtils.unescapeHtml4(input));
    }

    @ParameterizedTest
    @DisplayName("转义XSI字符串")
    @Tags({
            @Tag("@id:16"),
            @Tag("@author:liulimin"),
            @Tag("@date:2023/5/16")
    })
    @ValueSource(strings = {"He didn't say, \"Stop!\""})
    void testEscapeXSI(String input) {
        assertEquals("He\\ didn\\'t\\ say,\\ \\\"Stop!\\\"",StringEscapeUtils.escapeXSI(input));
    }

    @ParameterizedTest
    @DisplayName("反转义XSI字符串")
    @Tags({
            @Tag("@id:16"),
            @Tag("@author:liulimin"),
            @Tag("@date:2023/5/16")
    })
    @ValueSource(strings = {"He\\ didn\\'t\\ say,\\ \\\"Stop!\\\""})
    void testUnescapeXSI(String input) {
        assertEquals("He didn't say, \"Stop!\"",StringEscapeUtils.unescapeXSI(input));
    }


    @ParameterizedTest
    @DisplayName("转义JS字符串")
    @Tags({
            @Tag("@id:16"),
            @Tag("@author:liulimin"),
            @Tag("@date:2023/5/16")
    })
    @ValueSource(strings = {"He didn't say, \"Stop!\""})
    void testEcmaScript(String input) {
        assertEquals("He didn\\'t say, \\\"Stop!\\\"",StringEscapeUtils.escapeEcmaScript(input));
    }

    @ParameterizedTest
    @DisplayName("反转义JS字符串")
    @Tags({
            @Tag("@id:16"),
            @Tag("@author:liulimin"),
            @Tag("@date:2023/5/16")
    })
    @ValueSource(strings = {"He didn\\'t say, \\\"Stop!\\\""})
    void testUnescapeEcmaScript(String input) {
        assertEquals("He didn't say, \"Stop!\"",StringEscapeUtils.unescapeEcmaScript(input));
    }

}
