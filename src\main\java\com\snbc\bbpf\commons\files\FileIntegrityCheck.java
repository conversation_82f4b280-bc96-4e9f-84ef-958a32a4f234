/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.files;

import org.apache.commons.codec.digest.DigestUtils;
import java.io.File;
import java.io.FileInputStream;

import com.snbc.bbpf.commons.strings.StringEmptyCheck;


/**
 * 对文件完整性检查处理类
 * <p>功能列表</p>
 * <p>1.生成文件完整性的校验码</p>
 * <p>2.根据校验码检查文件完整性</p>
 *
 * <p>使用apache的codec组件，对指定文件生成不同算法的校验码，参数可以传入文件路径或文件对象或文件流，指定算法（MD5 sha1 sha256 sha384 sha512）</p>
 * <AUTHOR>
 * @module 文件处理模块
 * @date 2023/8/25 20:10
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class FileIntegrityCheck {

	public enum CheckAlgorithm {
		MD5, SHA1, SHA256, SHA384, SHA512
	}
	
    public static final String PARAMETER_NULL = "Input parameter is null";
    public static final String PARAMETER_EMPTY = "Input parameter is empty";
    public static final String PARAMETER_UNSUPP = "Input parameter is not supported";

    private FileIntegrityCheck() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 传入文件流生成文件完整性的校验码
     * @param fis 文件流
     * @param algorithm 生成校验码的算法，支持MD5, SHA1, SHA256, SHA384, SHA512
     * @return 校验码
     * @throws IllegalArgumentException 如果输入文件异常，算法不支持
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/8/25
     */
	public static String generateCheckCode(FileInputStream fis, CheckAlgorithm algorithm) {
		if (fis == null) {
			throw new IllegalArgumentException(PARAMETER_NULL);
		}
	
		String hex = null;
		try {
			switch (algorithm){
				case MD5:
					hex = DigestUtils.md5Hex(fis);
					break;
				case SHA1:
					hex = DigestUtils.sha1Hex(fis);
					break;
				case SHA256:
					hex = DigestUtils.sha256Hex(fis);
					break;
				case SHA384:
					hex = DigestUtils.sha384Hex(fis);
					break;
				case SHA512:
					hex = DigestUtils.sha512Hex(fis);
					break;			
				default:
					throw new IllegalArgumentException(PARAMETER_UNSUPP);
			}
        } catch (Exception e) {
			throw new RuntimeException(e);
        }
		
		return hex;
	}  

    /**
     * 传入文件对象生成文件完整性的校验码
     * @param file 文件对象
     * @param algorithm 生成校验码的算法，支持MD5, SHA1, SHA256, SHA384, SHA512
     * @return 校验码
     * @throws IllegalArgumentException 如果输入文件异常，算法不支持
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/8/25
     */
	public static String generateCheckCode(File file, CheckAlgorithm algorithm) throws Exception {
		if (file == null) {
			throw new IllegalArgumentException(PARAMETER_NULL);
		}
	
        try (FileInputStream fis = new FileInputStream(file)) {
			return generateCheckCode(fis, algorithm);
        } catch (Exception e) {
			throw new RuntimeException(e);
        }
	}  	
    /**
     * 传入文件路径生成文件完整性的校验码
     * @param fileName 文件完整路径
     * @param algorithm 生成校验码的算法，支持MD5, SHA1, SHA256, SHA384, SHA512
     * @return 校验码
     * @throws IllegalArgumentException 如果输入文件异常，算法不支持
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/8/25
     */
	public static String generateCheckCode(String fileName, CheckAlgorithm algorithm) throws Exception {
		if (StringEmptyCheck.isEmpty(fileName)) {
			throw new IllegalArgumentException(PARAMETER_EMPTY);
		}

		return generateCheckCode(new File(fileName), algorithm);
	}  

    /**
     * 根据校验码与传入文件流检查文件完整性
     * @param fis 文件流
     * @param algorithm 检查文件完整性的算法，支持MD5, SHA1, SHA256, SHA384, SHA512
     * @param checkCode 校验码
     * @return 校验码
     * @throws IllegalArgumentException 如果输入文件异常，算法不支持
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/8/25
     */
	public static boolean check(FileInputStream fis, CheckAlgorithm algorithm, String checkCode) {
		if (fis == null) {
			throw new IllegalArgumentException(PARAMETER_NULL);
		}
		return checkCode.equalsIgnoreCase(generateCheckCode(fis, algorithm));
	}

    /**
     * 根据校验码与传入文件对象检查文件完整性
     * @param file 文件对象
     * @param algorithm 检查文件完整性的算法，支持MD5, SHA1, SHA256, SHA384, SHA512
     * @param checkCode 校验码
     * @return 校验码
     * @throws IllegalArgumentException 如果输入文件异常，算法不支持
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/8/25
     */
	public static boolean check(File file, CheckAlgorithm algorithm, String checkCode) throws Exception {
		if (file == null) {
			throw new IllegalArgumentException(PARAMETER_NULL);
		}

		return checkCode.equalsIgnoreCase(generateCheckCode(file, algorithm));
	}

    /**
     * 根据校验码与传入文件路径检查文件完整性
     * @param fileName 文件完整路径
     * @param algorithm 检查文件完整性的算法，支持MD5, SHA1, SHA256, SHA384, SHA512
     * @param checkCode 校验码
     * @return 校验码
     * @throws IllegalArgumentException 如果输入文件异常，算法不支持
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/8/25
     */
	public static boolean check(String fileName, CheckAlgorithm algorithm, String checkCode) throws Exception {
		if (StringEmptyCheck.isEmpty(fileName)) {
			throw new IllegalArgumentException(PARAMETER_EMPTY);
		}

		return checkCode.equalsIgnoreCase(generateCheckCode(fileName, algorithm));
	}


}
