/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.crypt;

/**
 * 对称加密算法枚举类
 *
 * <AUTHOR>
 * @module
 * @date 2023/11/26 21:19
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public enum SymmetricAlgorithm {
    AES("AES"), // AES算法
    DES_3DES("DESEDE"), // 3DES算法
    SM4("SM4"), //SM4算法
    RSA("RSA"); //RSA算法

    private String value;

    /**
     * @param value
     */
    SymmetricAlgorithm(String value) {
        this.value = value;
    }

    /**
     * 根据值取对应枚举
     *
     * @param type
     * @return Algorithm
     * @throws
     * <AUTHOR>
     * @date 2023/11/27
     * @since 1.4.0
     */
    public static SymmetricAlgorithm getByValue(String type) {

        for (SymmetricAlgorithm val : values()) {

            if (val.getValue().equals(type)) {
                return val;
            }
        }
        return null;
    }

    public String getValue() {
        return value;
    }
}
