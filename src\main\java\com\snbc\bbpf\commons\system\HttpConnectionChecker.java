/*
 * 版权所有 2009-2024山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.system;

import org.apache.http.Header;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpHead;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicHeader;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * #1.网络连接检查器，用于检测多个URL的连通性。
 * #2.使用HEAD请求替代GET，仅获取响应头信息。
 * #3.需要注意中间页或挟持问题，只代表本机有网络，不代表真的能连互联网。
 * #4.例如WIFI有可能会需要授权访问的中间页，实际并未连通互联网。
 *
 * 使用示例:
 * <pre>
 * try (HttpConnectionChecker checker = new HttpConnectionChecker()) {
 *     List<String> urls = Arrays.asList(
 *         "https://www.baidu.com",
 *         "https://www.qq.com"
 *     );
 *     boolean isConnected = checker.checkConnections(urls);
 *     System.out.println("网络连接状态: " + (isConnected ? "可连接" : "不可连接"));
 * }
 * </pre>
 *
 * <AUTHOR>
 * @module 系统资源与诊断
 * @date 2025/05/12 10:00 上午
 * @copyright 2024 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class HttpConnectionChecker implements AutoCloseable {

    private static final int HTTP_STATUS_400 = 400;
    private static final String USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) ";
    private static final int CONNECTION_TIMEOUT = 5000; // 连接超时时间，单位毫秒
    private static final int SOCKET_TIMEOUT = 5000; // 读取超时时间，单位毫秒
    private static final int CONNECTION_REQUEST_TIMEOUT = 5000; // 连接请求超时时间，单位毫秒
    private static final int MAX_TOTAL_CONNECTIONS = 100; // 最大总连接数
    private static final int SHUTDOWN_TIMEOUT = 5; // 关闭超时时间，单位秒

    // 压缩和优化相关的常量
    private static final String ACCEPT_ENCODING_VALUE = "gzip";

    private final ExecutorService executorService;
    private final CloseableHttpClient httpClient;
    private volatile boolean closed;

    /**
     * #创建一个默认的HttpConnectionChecker实例
     * #使用固定线程池和配置好的HttpClient
     *
     * <AUTHOR>
     * @date 2025/5/12
     * @since 1.6.0
     */
    public HttpConnectionChecker() {
        this(Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors()),
                createHttpClient());
    }

    /**
     * #创建一个HttpConnectionChecker实例，使用自定义的ExecutorService和HttpClient
     * 
     * @param executorService     用于异步执行请求的ExecutorService
     * @param httpClient          用于发送HTTP请求的HttpClient
     * <AUTHOR>
     * @date 2025/5/12
     * @since 1.6.0
     */
    public HttpConnectionChecker(ExecutorService executorService, CloseableHttpClient httpClient) {
        if (executorService == null) {
            throw new IllegalArgumentException("executorService不能为null");
        }
        if (httpClient == null) {
            throw new IllegalArgumentException("httpClient不能为null");
        }
        this.executorService = executorService;
        this.httpClient = httpClient;
    }

    /**
     * #创建一个配置好的HttpClient实例
     * #包含压缩和优化的请求头配置，以减少流量开销
     * 
     * @return 配置好的CloseableHttpClient实例
     * <AUTHOR>
     * @date 2025/5/12
     * @since 1.6.0
     */
    private static CloseableHttpClient createHttpClient() {
        // 创建连接池管理器
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(MAX_TOTAL_CONNECTIONS);

        // 创建请求配置
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(CONNECTION_TIMEOUT)
                .setSocketTimeout(SOCKET_TIMEOUT)
                .setConnectionRequestTimeout(CONNECTION_REQUEST_TIMEOUT)
                .build();

        // 创建默认请求头列表，减少每次请求时重复创建头的开销
        List<Header> defaultHeaders = new ArrayList<>();
        defaultHeaders.add(new BasicHeader(HttpHeaders.USER_AGENT, USER_AGENT));
        defaultHeaders.add(new BasicHeader(HttpHeaders.ACCEPT_ENCODING, ACCEPT_ENCODING_VALUE));

        // 创建并配置HttpClient
        return HttpClientBuilder.create()
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(requestConfig)
                .setDefaultHeaders(defaultHeaders)
                .build();
    }

    /**
     * #检查单个URL的连通性
     * # 使用条件：这种方式可能会存在中间页或挟持问题，只代表本机有网络，不代表真的能连互联网
     * # 使用条件：WIFI有可能会需要授权访问的中间页，实际并未连通互联网
     * 
     * @param url     要检查的URL
     * @return        如果URL可访问则返回true，否则返回false
     * @throws IllegalStateException 如果检查器已关闭
     * <AUTHOR>
     * @date 2025/5/12
     * @since 1.6.0
     */
    public boolean checkConnection(String url) {
        checkNotClosed();
        if (url == null || url.trim().isEmpty()) {
            return false;
        }

        HttpHead request = new HttpHead(url);
        try {
            HttpResponse response = httpClient.execute(request);
            int statusCode = response.getStatusLine().getStatusCode();

            // 状态码小于400表示可访问，407/504表示客户端与代理/CDN 连通，但目标服务器可能不可达
            return statusCode < HTTP_STATUS_400;
        } catch (IOException e) {
            // 连接失败
            return false;
        } finally {
            request.releaseConnection();
        }
    }

    /**
     * #依次检查URL的连通性，找到第一个可访问的URL后停止检查
     * # 使用条件：这种方式可能会存在中间页或挟持问题，只代表本机有网络，不代表真的能连互联网
     * # 使用条件：WIFI有可能会需要授权访问的中间页，实际并未连通互联网
     * 
     * @param urls    要检查的URL列表
     * @return        如果有任何一个URL可访问，则返回true；否则返回false
     * @throws IllegalStateException 如果检查器已关闭
     * <AUTHOR>
     * @date 2025/5/12
     * @since 1.6.0
     */
    public boolean checkConnections(List<String> urls) {
        checkNotClosed();
        if (urls == null || urls.isEmpty()) {
            return false;
        }

        // 依次检查每个URL
        for (String url : urls) {
            if (url != null && !url.trim().isEmpty()) {
                boolean isAccessible = checkConnection(url);

                // 如果找到可访问的URL，立即返回true
                if (isAccessible) {
                    return true;
                }
            }
        }

        // 所有URL都不可访问
        return false;
    }

    /**
     * #检查检查器是否已关闭
     *
     * @throws IllegalStateException 如果检查器已关闭
     * <AUTHOR>
     * @date 2025/5/12
     * @since 1.6.0
     */
    private void checkNotClosed() {
        if (closed) {
            throw new IllegalStateException("HttpConnectionChecker已关闭");
        }
    }

    /**
     * #关闭HttpConnectionChecker，释放资源
     * #实现AutoCloseable接口
     *
     * <AUTHOR>
     * @date 2025/5/12
     * @since 1.6.0
     */
    @Override
    public void close() {
        if (closed) {
            return;
        }
        closed = true;

        // 关闭ExecutorService
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(SHUTDOWN_TIMEOUT, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            // 恢复中断状态
            Thread.currentThread().interrupt();
            executorService.shutdownNow();
        }

        // 关闭HttpClient
        try {
            httpClient.close();
        } catch (IOException e) {
            // 关闭HttpClient失败，但已尽力尝试
        }
    }
}