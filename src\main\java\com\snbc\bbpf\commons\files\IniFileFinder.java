package com.snbc.bbpf.commons.files;

import com.snbc.bbpf.commons.strings.StringEmptyCheck;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * ini查找工具类
 * 1. 可以根据key（正则）返回值，如果key在多个分组中，则放回对应分组和对应的值
 * 2. 可以根据key（正则）和分组（正则）进行查找返回对应的值
 * 3. 可查找分组（正则），返回对应分组下的key和value列表。
 * 4. 根据ini文件路径返回所有ini的文件中的内容，作为集合返回。
 * <p>
 * List<IniSectionDomain> result = iniFileFinder.getValuesByKeyAndGroupRegex("Key1", "Section1");
 * List<IniSectionDomain> result = iniFileFinder.getValuesByKeyRegexWithMultipleSections("Key1");
 * List<IniSectionDomain> result = iniFileFinder.getValuesByKeyAndGroupRegex("Section001", "Key1");
 * <p>
 * INI 文件操作工具类
 * 实现对 INI 文件的增删改查操作
 */
public class IniFileFinder {
    private Map<String, Map<String, String>> data;


    /**
     * 构造函数，初始化数据存储结构
     *
     * <AUTHOR>
     * @date 2023/11/24
     * @since 1.4.0
     */


    public IniFileFinder(String filePath) {
        if (StringEmptyCheck.isBlank(filePath)) {
            throw new IllegalArgumentException("filePath must not be empty");
        }
        data = new HashMap<>();
        load(filePath);
    }


    /**
     * 加载 INI 文件
     *
     * @param filePath 文件路径
     * <AUTHOR>
     * @date 2023/11/24
     * @since 1.4.0
     */
    public final void load(String filePath) {
        try (BufferedReader reader = Files.newBufferedReader(Paths.get(filePath))) {
            String line;
            String currentSection = null;

            while ((line = reader.readLine()) != null) {
                line = line.trim();

                if (!(line.isEmpty() || line.startsWith(";"))) {
                    if (line.startsWith("[")) {
                        currentSection = line.substring(1, line.indexOf("]"));
                        data.put(currentSection, new HashMap<>());
                    } else if (currentSection != null) {
                        int equalsIndex = line.indexOf("=");
                        if (equalsIndex > 0) {
                            String key = line.substring(0, equalsIndex).trim();
                            String value = line.substring(equalsIndex + 1).trim();
                            data.get(currentSection).put(key, value);
                        }
                    }
                }
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 获取指定分组和键的值
     *
     * @param section 分组名称
     * @param key     键名称
     * @return 键对应的值，如果不存在则返回 null
     * <AUTHOR>
     * @date 2023/11/24
     * @since 1.4.0
     */
    public String getValue(String section, String key) {
        Map<String, String> sectionData = data.get(section);
        if (sectionData != null) {
            return sectionData.get(key);
        }
        return null;
    }


    /**
     * 根据正则表达式匹配的键返回对应的值（包括多个分组内的匹配项）
     *
     * @param keyRegex 键名称的正则表达式
     * @return 匹配的键值对列表，如果没有匹配项则返回空列表
     * <AUTHOR>
     * @date 2023/11/24
     * @since 1.4.0
     */
    public List<IniSectionDomain> getValuesByKeyRegexWithMultipleSections(String keyRegex) {
        if (keyRegex == null) {
            return Collections.emptyList();
        }
        List<IniSectionDomain> matchedEntries = new ArrayList<>();
        Pattern pattern = Pattern.compile(keyRegex);
        //遍历所有的section
        //遍历每组section下的key
        data.entrySet().forEach(section -> {
            for (Map.Entry<String, String> entry : section.getValue().entrySet()) {
                //匹配所有的key
                if (pattern.matcher(entry.getKey()).matches()) {
                    IniSectionDomain iniSectionDomain = new IniSectionDomain(section.getKey(), entry.getKey(),
                            entry.getValue());
                    matchedEntries.add(iniSectionDomain);
                }
            }
        });

        return matchedEntries;
    }

    /**
     * 根据正则表达式匹配的键和分组查找对应的值
     *
     * @param keyRegex   键的正则表达式
     * @param groupRegex 分组的正则表达式
     * @return 匹配的键值对列表
     * <AUTHOR>
     * @date 2023/11/24
     * @since 1.4.0
     */
    public List<IniSectionDomain> getValuesByKeyAndGroupRegex(String keyRegex, String groupRegex) {
        List<IniSectionDomain> re = getValuesByKeyRegexWithMultipleSections(keyRegex);
        List<IniSectionDomain> re1 = new ArrayList<>();
        if (re != null) {
            Optional<IniSectionDomain> optional = Optional.empty();
            for (IniSectionDomain it : re) {
                if (groupRegex.equals(it.getSection())) {
                    optional = Optional.of(it);
                    break;
                }
            }
            if (optional.isPresent()) {
                re1.add(optional.get());
            }
        }
        return re1;
    }

    /**
     * 根据正则表达式匹配的分组，返回对应分组下的键和值列表
     *
     * @param groupRegex 分组的正则表达式
     * @return 分组下的键值对列表
     * <AUTHOR>
     * @date 2023/11/24
     * @since 1.4.0
     */
    public List<IniSectionDomain> getKeyValuePairsByGroupRegex(String groupRegex) {
        if (groupRegex == null) {
            return Collections.emptyList();
        }
        List<IniSectionDomain> re = new ArrayList<>();
        Pattern groupPattern = Pattern.compile(groupRegex);
        Matcher groupMatcher;
        for (Map.Entry<String, Map<String, String>> entry : data.entrySet()) {
            groupMatcher = groupPattern.matcher(entry.getKey());
            if (groupMatcher.matches()) {
                Map<String, String> sectionData = data.get(entry.getKey());
                sectionData.forEach((k, v) -> re.add(new IniSectionDomain(entry.getKey(), k, v)));
            }
        }
        return re;
    }

    /**
     * 根据ini文件路径返回所有ini的文件中的内容，作为集合返回。
     *
     * @return
     * <AUTHOR>
     * @date 2023/11/24
     * @since 1.4.0
     */
    public List<IniSectionDomain> findAll() {
        List<IniSectionDomain> re = new ArrayList<>();
        for (Map.Entry<String, Map<String, String>> entry : data.entrySet()) {
            Map<String, String> sectionData = data.get(entry.getKey());
            sectionData.forEach((k, v) -> re.add(new IniSectionDomain(entry.getKey(), k, v)));
        }
        return re;
    }
}
