package com.snbc.bbpf.commons.ftps;

import com.snbc.bbpf.commons.ftps.config.ConnectConfig;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 抽象的Ftp客户端，将通用的方法放在这里
 *
 * <AUTHOR>
 * @module 数据传输
 * @date 2023/9/26 14:22
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class BaseFtpClient {
    public static final String LINUX_SEPARATOR = "/";
    public static final String WINDOWS_SEPARATOR = "\\";
    ConnectConfig connectConfig;
    AtomicBoolean isConnect = new AtomicBoolean(false);

    /**
     * 数据传输初始化
     * @param connectConfig 链接参数对象
     * @return boolean 是否初始化成功
     * @throws
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/9/27
     */
    public boolean init(ConnectConfig connectConfig) {
        if (isConnected()){
            return false;
        }
        this.connectConfig = connectConfig;
        return true;
    }
    /**
     * 判断客户端是否还连接
     *
     * @return boolean 返回是否连接
     * @throws
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/9/27
     */
    public boolean isConnected() {
        return isConnect.get();
    }
    /**
     * 根据文件路径获取文件名
     *
     * @param filePath    文件路径
     * @return java.lang.String 文件名
     * @throws
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/9/27
     */
    public static String getFileNameByFilePath(String filePath) {
        int index = filePath.lastIndexOf(WINDOWS_SEPARATOR);
        if(index<0){
            index = filePath.lastIndexOf(LINUX_SEPARATOR);
        }
        return filePath.substring(index);
    }
    
    /**
     * 将目录最后的分隔符去掉
     * @param dirPath
     * @return java.lang.String
     * @throws 
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/9/27       
     */
    public static String getDirPathWithoutLastSeparator(String dirPath) {
        StringBuilder dirStringBuilder = new StringBuilder(dirPath);
        while (dirStringBuilder.toString().endsWith(WINDOWS_SEPARATOR)||
                dirStringBuilder.toString().endsWith(LINUX_SEPARATOR)){
            dirStringBuilder.deleteCharAt(dirStringBuilder.length()-1);
        }
        return dirStringBuilder.toString();
    }

}
