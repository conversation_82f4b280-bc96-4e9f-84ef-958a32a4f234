/*
 * 版权所有 2009-2024山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.system;

import com.snbc.bbpf.commons.objs.ObjectEmptyCheck;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * #1.对指定的地址的连通性进行测试，指定超时时间。
 * #2.对指定地址和端口的连通性进行测试，指定超时时间。使用完后及时关闭连接。
 * #3.需要避免多次同时创建多个连接调用。
 *
 * <AUTHOR>
 * @module 系统资源与诊断
 * @date 2024/05/27 11:38 下午
 * @copyright 2024 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class ConnectCheckUtils {

    private static final String LINUX_IPV4 = "ping -c 1 ";
    private static final String WIN_IPV4 = "ping -n 1 ";
    private static final String UNIX_IPV6 = "ping6 -c 1 ";
    private static final String WIN_IPV6 = "ping -6 -n 1 ";

    /**
     * #对指定的地址的连通性进行测试 默认支持ipv4
     * # 使用条件： 如果主机或防火墙禁用了tcmp，则会测试通信失败
     * # 使用条件： Linux中依赖系统的ipUtils包，BSD中依赖PING utility要求大约FreeBSD 4.0
     *
     * @param addr     主机、ip地址、net等
     * @param time     超时时间
     * @param timeUnit 超时单位
     * @return 主机是否能够达到
     * <AUTHOR>
     * @date 2024/5/27
     * @since 1.5.0
     */
    public static boolean hostIsAlive(String addr, Integer time, TimeUnit timeUnit) {
        if (ObjectEmptyCheck.isEmpty(addr) ||
                ObjectEmptyCheck.isEmpty(time) ||
                ObjectEmptyCheck.isEmpty(timeUnit)) {
            throw new IllegalArgumentException("addr,time,timeUnit must not be null");
        }
        String os = System.getProperty("os.name");
        if (StringUtils.containsIgnoreCase(os, "windows")) {
            return hostIsAlive(addr, time, timeUnit, WIN_IPV4);
        } else {
            return hostIsAlive(addr, time, timeUnit, LINUX_IPV4);
        }
    }

    /**
     * #对指定的地址的连通性进行测试 支持ipv6
     * # 使用条件： 如果主机或防火墙禁用了tcmp，则会测试通信失败
     * # 使用条件： Linux中依赖系统的ipUtils包，BSD中依赖PING utility要求大约FreeBSD 4.0
     *
     * @param addr     主机、ip地址、net等
     * @param time     超时时间
     * @param timeUnit 超时单位
     * @return 主机是否能够达到
     * <AUTHOR>
     * @date 2024/5/27
     * @since 1.5.0
     */
    public static boolean hostIsAliveIpv6(String addr, Integer time, TimeUnit timeUnit) {
        if (ObjectEmptyCheck.isEmpty(addr) ||
                ObjectEmptyCheck.isEmpty(time) ||
                ObjectEmptyCheck.isEmpty(timeUnit)) {
            throw new IllegalArgumentException("addr,time,timeUnit must not be null");
        }
        String os = System.getProperty("os.name");
        if (StringUtils.containsIgnoreCase(os, "windows")) {
            return hostIsAlive(addr, time, timeUnit, WIN_IPV6);
        } else if (StringUtils.containsIgnoreCase(os, "linux")) {
            return hostIsAlive(addr, time, timeUnit, UNIX_IPV6);
        } else {
            return hostIsAlive(addr, time, timeUnit, UNIX_IPV6);
        }
    }


    /**
     * #对指定的地址的连通性进行测试
     * # 使用条件： 如果主机或防火墙禁用了tcmp，则会测试通信失败
     *
     * @param addr     主机、ip地址、net等
     * @param time     超时时间
     * @param timeUnit 超时单位
     * @param command  命令
     * @return 主机是否能够达到
     * <AUTHOR>
     * @date 2024/5/27
     * @since 1.5.0
     */
    private static boolean hostIsAlive(String addr, Integer time, TimeUnit timeUnit, String command) {
        ExecutorService executorService = Executors.newFixedThreadPool(1);
        Future<Boolean> future = executorService.submit(() -> {
            Process p1 = null;
            try {
                final InetAddress[] hosts = InetAddress.getAllByName(addr);
                for (InetAddress host : hosts) {
                    if (host == null) {
                        return false;
                    }
                }
                p1 = Runtime.getRuntime().exec(command + hosts[0].getHostAddress());
                return p1.waitFor() == 0;
            } catch (IOException e) {
                e.printStackTrace();
                return false;
            } catch (InterruptedException e) {
                e.printStackTrace();
                Thread.currentThread().interrupt();
                return false;
            } finally {
                if (p1 != null) {
                    p1.destroy();
                }
            }
        });
        try {
            return future.get(time, timeUnit);
        } catch (InterruptedException e) {
            e.printStackTrace();
            Thread.currentThread().interrupt();
            return false;
        } catch (ExecutionException | TimeoutException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * #对指定地址和端口的连通性进行测试，指定超时时间 支持 ipv4 ipv6
     *
     * @param addr     主机、ip地址
     * @param port     端口号
     * @param time     超时时间
     * @param timeUnit 超时单位
     * @return
     * <AUTHOR>
     * @date 2024/5/27
     * @since 1.5.0
     */
    public static boolean hostPortIsAlive(String addr, Integer port, Integer time, TimeUnit timeUnit) {
        if (ObjectEmptyCheck.isEmpty(addr) ||
                ObjectEmptyCheck.isEmpty(port) ||
                ObjectEmptyCheck.isEmpty(time) ||
                ObjectEmptyCheck.isEmpty(timeUnit)
        ) {
            throw new IllegalArgumentException("addr，port,time，timeunit must not be null");
        }
        try (Socket soc = new Socket()) {
            soc.connect(new InetSocketAddress(addr, port), (int) timeUnit.convert(time, TimeUnit.MILLISECONDS));
            return true;
        } catch (IOException ex) {
            return false;
        }
    }

}
