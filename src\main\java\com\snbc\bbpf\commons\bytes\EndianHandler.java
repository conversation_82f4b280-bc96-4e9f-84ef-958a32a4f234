/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.bytes;

/**
 * byte数组大小端处理类
 * <p>将byte数组转换大或小端方式</p>
 * <p>功能列表</p>
 * <p>1.将byte数组根据字节数、开始/结束位置转换大或小端 </p>
 * <p>2.将byte数组根据字节数、开始位置转换大或小端 </p>
 * <p>3.将byte数组根据字节数转换大或小端 </p>
 * <p>4.将byte数组转换大或小端 </p>
 *
 * <AUTHOR>
 * @module 字节处理
 * @date 2023/5/17 19:18
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class EndianHandler {

    private static final int DEFAULT_BYTESIZE = 4;
	
    //最小字节数
    private static final int MINIMUM_BYTESIZE = 2;
	//取一半长度
    private static final int HALF_LENGTH = 2;

	private EndianHandler() {
		throw new IllegalStateException("Utility class");
	}
    /**
     * 检查参数
     *
     * @param bytes 待处理的byte数组
     * @param byteSize 字节数
     * @param beginIndex 开始位置
     * @param endIndex 结束位置(不包含该位置元素)
     * @throws IllegalArgumentException 如果输入byte数组为空、字节数=0、开始/结束位置无效
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/17
     */
    private static void checkParameter(byte[] bytes, int byteSize, int beginIndex, int endIndex){
		if (bytes == null || bytes.length < MINIMUM_BYTESIZE){
			throw new IllegalArgumentException("bytes array parameter invalid");		
		}

		if (byteSize < MINIMUM_BYTESIZE){
			//字节数大于等于2才有意义
			throw new IllegalArgumentException("byteSize parameter invalid");			
		}
		
		if (beginIndex < 0 || endIndex < 0) {
			throw new IllegalArgumentException("index parameter < 0 invalid");			
		}
		
		if (beginIndex >= bytes.length || endIndex > bytes.length) {
			throw new IllegalArgumentException("index parameter > length invalid"); 		
		}
		
		if (beginIndex >= endIndex || (beginIndex + endIndex) > bytes.length) {
			throw new IllegalArgumentException("index parameter invalid");			
		} 
		
		if (0 != (endIndex - beginIndex) % byteSize){
			throw new IllegalArgumentException("bytes.length parameter invalid");			
		}
	}


    /**
     * 将byte数组变成大或小端方式
     *
     * <p>例如：输入{0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x10,0x11}, 字节数4，开始位置0，结束位置11 </p>  
     * <p>返回{0x03,0x02,0x01,0x00,0x07,0x06,0x05,0x04,0x11,0x10,0x09,0x08} </p>
     *
     * @param bytes 待处理的byte数组
     * @param byteSize 字节数
     * @param beginIndex 开始位置
     * @param endIndex 结束位置(不包含该位置元素)
     * @return 处理后的byte数组
     * @throws IllegalArgumentException 如果输入byte数组为空、字节数=0、开始/结束位置无效
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/17
     */
    public static byte[] endian(byte[] bytes, int byteSize, int beginIndex, int endIndex){
        checkParameter(bytes, byteSize, beginIndex, endIndex);

		//+1表示包含结束位置
		byte[] byteEndian = new byte[endIndex - beginIndex];
		int iCount = byteEndian.length / byteSize;
		int offset = 0;
		int half = byteSize / HALF_LENGTH;
		boolean bEven = 0 == byteSize % HALF_LENGTH;
		for (int i = 0; i < iCount; ++i) {
			
			offset = i * byteSize;
			for (int m = 0; m < half; ++m) {
				//一个字节数内，大小端转换
				byteEndian[offset + m] = bytes[beginIndex + offset + byteSize - 1 - m];
				byteEndian[offset + byteSize - 1 - m] = bytes[beginIndex + offset + m];
			}

			//字节数非偶数时，中间位置不变
			if (!bEven) {
				byteEndian[offset + half] = bytes[beginIndex + offset + half];
			}
		}
		return byteEndian;
    }

    /**
     * 将byte数组变成大或小端方式
     *
     * <p>例如：输入{0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x10,0x11}, 字节数4，开始位置0，结束位置11 </p>  
     * <p>返回{0x03,0x02,0x01,0x00,0x07,0x06,0x05,0x04,0x11,0x10,0x09,0x08} </p>
     *
     * @param bytes 待处理的byte数组
     * @param byteSize 字节数
     * @param beginIndex 开始位置
     * @return 处理后的byte数组
     * @throws IllegalArgumentException 如果输入byte数组为空、字节数=0、开始位置无效
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/17
     */
    public static byte[] endian(byte[] bytes, int byteSize, int beginIndex){
        return endian(bytes, byteSize, beginIndex, bytes.length);
    }


    /**
     * 将byte数组变成大或小端方式
     *
     * <p>例如：输入{0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x10,0x11}, 字节数4，开始位置0，结束位置11 </p>  
     * <p>返回{0x03,0x02,0x01,0x00,0x07,0x06,0x05,0x04,0x11,0x10,0x09,0x08} </p>
     *
     * @param bytes 待处理的byte数组
     * @param byteSize 字节数
     * @return 处理后的byte数组
     * @throws IllegalArgumentException 如果输入byte数组为空、字节数=0
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/17
     */
    public static byte[] endian(byte[] bytes, int byteSize){
        return endian(bytes, byteSize, 0, bytes.length);
    }

    /**
     * 将byte数组变成大或小端方式
     *
     * <p>例如：输入{0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x10,0x11}, 字节数4，开始位置0，结束位置11 </p>  
     * <p>返回{0x03,0x02,0x01,0x00,0x07,0x06,0x05,0x04,0x11,0x10,0x09,0x08} </p>
     *
     * @param bytes 待处理的byte数组
     * @return 处理后的byte数组
     * @throws IllegalArgumentException 如果输入byte数组为空
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/17
     */
    public static byte[] endian(byte[] bytes){
        return endian(bytes, DEFAULT_BYTESIZE, 0, bytes.length);
    }
}

