/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.calculations;

import com.snbc.bbpf.commons.calculations.PrecisionCalculationUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.math.RoundingMode;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * 类描述:    .
 *
 * <AUTHOR>
 * 创建时间:  [2023/6/8 19:38]
 */
class PrecisionCalculationUtilsTest {

    @DisplayName("计算一个数字占另外一个数字的百分比字符串")
    @Tags({
            @Tag("@id:41"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/6/8")
    })
    @Test
    void calculatePercentage() {
        double number = 75.0;
        double total = 200.0;
        int decimalPlaces = 2;
        RoundingMode roundingMode = RoundingMode.HALF_UP;

        String percentage = PrecisionCalculationUtils.calculatePercentage(number, total, decimalPlaces, roundingMode);
        assertEquals(percentage, "37.50%");  // 输出：37.50%

    }

    @DisplayName("计算一个数字占另外一个数字的百分比字符串")
    @Tags({
            @Tag("@id:41"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/6/8")
    })
    @Test
    void calculatePercentage_int() {
        int number = 75;
        int total = 200;
        int decimalPlaces = 2;
        RoundingMode roundingMode = RoundingMode.HALF_UP;

        String percentage = PrecisionCalculationUtils.calculatePercentage(number, total, decimalPlaces, roundingMode);
        assertEquals(percentage, "37.50%");  // 输出：37.50%

    }

    @DisplayName("计算一个数字占另外一个数字的百分比字符串")
    @Tags({
            @Tag("@id:41"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/6/8")
    })
    @Test
    void calculatePercentage_long() {
        long number = 75L;
        long total = 200L;
        int decimalPlaces = 2;
        RoundingMode roundingMode = RoundingMode.UNNECESSARY;
        String percentage = PrecisionCalculationUtils.calculatePercentage(number, total, decimalPlaces, roundingMode);
        assertEquals(percentage, "37.50%");  // 输出：37.50%

    }


    @DisplayName("计算一个数字占另外一个数字的百分比字符串")
    @Tags({
            @Tag("@id:41"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/6/8")
    })
    @Test
    void calculatePercentage_110() {
        long number = 1L;
        long total = 3L;
        int decimalPlaces = 2;
        RoundingMode roundingMode = RoundingMode.HALF_UP;
        String percentage = PrecisionCalculationUtils.calculatePercentage(number, total, decimalPlaces, roundingMode);
        assertEquals("33.33%", percentage);  // 输出：37.50%

    }

    @DisplayName("计算一个数字占另外一个数字的百分比字符串--null")
    @Tags({
            @Tag("@id:41"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/6/8")
    })
    @Test
    void calculatePercentage_null() {
        long number = 3L;
        long total = 10L;
        int decimalPlaces = 2;
        String percentage = PrecisionCalculationUtils.calculatePercentage(number, total, decimalPlaces, null);
        assertEquals("30.00%", percentage);  // 输出：37.50%

    }


}
