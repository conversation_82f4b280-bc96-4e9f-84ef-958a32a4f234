/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.dates;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Locale;
import java.util.Objects;


/**
 * <AUTHOR>
 * @module Date日期处理
 * 日期处理
 * <p>
 * 1.将Java8以前Date，LocalDate、LocalDateTime、ZonedDateTime转化为指定格式字符串
 * 2.和指定格式字符串解析为Java8以前Date，LocalDate、LocalDateTime、ZonedDateTime
 * 制定格式：
 * HH:mm:ss
 * HHmmss
 * yyyy-MM-dd
 * yyyyMMdd
 * yyyy-MM-dd HH:mm
 * yyyyMMddhhmm
 * yyyy-MM-dd HH:mm:ss
 * yyyyMMddhhmmss
 * yyyy-MM-dd HH:mm:ss.SSS
 * yyyyMMddhhmmssSSS
 * yyyy-MM-dd'T'HH:mm:ss.SSS'Z'
 * yyyy/M/d HH:mm
 * X年X月X日
 * @date 2023/4/23 9:58
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */

public final class DateFormatUtil {


    private static final String DATE = "yyyy-MM-dd";
    public static final DateTimeFormatter DATE_FORMATTER = createFormatter(DATE);
    private static final String TIME = "HH:mm:ss";
    public static final DateTimeFormatter TIME_FORMATTER = createFormatter(TIME);
    private static final String DATETIME_MINUTE = "yyyy-MM-dd HH:mm";
    public static final DateTimeFormatter DATETIME_MINUTE_FORMATTER = createFormatter(DATETIME_MINUTE);
    private static final String DATETIME = "yyyy-MM-dd HH:mm:ss";
    public static final DateTimeFormatter DATETIME_FORMATTER = createFormatter(DATETIME);
    private static final String DATETIME_MS = "yyyy-MM-dd HH:mm:ss.SSS";
    public static final DateTimeFormatter DATETIME_MS_FORMATTER = createFormatter(DATETIME_MS);

    private static final String CHINESE_DATE = "yyyy年MM月dd日";
    public static final DateTimeFormatter CHINESE_DATE_FORMATTER = createFormatter(CHINESE_DATE);
    private static final String CHINESE_DATE_TIME = "yyyy年MM月dd日HH时mm分ss秒";
    public static final DateTimeFormatter CHINESE_DATE_TIME_FORMATTER = createFormatter(CHINESE_DATE_TIME);
    private static final String PURE_DATE = "yyyyMMdd";
    public static final DateTimeFormatter PURE_DATE_FORMATTER = createFormatter(PURE_DATE);
    private static final String PURE_TIME = "HHmmss";
    public static final DateTimeFormatter PURE_TIME_FORMATTER = createFormatter(PURE_TIME);
    private static final String ACCOUNTANT_TIME = "yyyy/M/d HH:mm";
    public static final DateTimeFormatter ACCOUNTANT_TIME_FORMATTER = createFormatter(ACCOUNTANT_TIME);
    private static final String PURE_DATETIME_NO_SECOND = "yyyyMMddHHmm";
    public static final DateTimeFormatter PURE_DATETIME_NO_SECOND_FORMATTER = createFormatter(PURE_DATETIME_NO_SECOND);
    private static final String PURE_DATETIME = "yyyyMMddHHmmss";
    public static final DateTimeFormatter PURE_DATETIME_FORMATTER = createFormatter(PURE_DATETIME);
    private static final String PURE_DATETIME_MS = "yyyyMMddHHmmssSSS";
    public static final DateTimeFormatter PURE_DATETIME_MS_FORMATTER = createFormatter(PURE_DATETIME_MS);
    private static final String UTC_MS = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
    public static final DateTimeFormatter UTC_MS_FORMATTER = createFormatter(UTC_MS);
    private static final String DATE_TIME_FORMATTER_IS_NULL = "dateTimeFormatter is null";
    private static final String DATE_STR_IS_NULL = "dateStr is null";

    private DateFormatUtil() {
        throw new IllegalStateException("Utility class");
    }

    public static DateTimeFormatter createFormatter(String pattern) {
        return DateTimeFormatter.ofPattern(pattern, Locale.getDefault()).withZone(ZoneId.systemDefault());
    }


    /**
     * # 日期转换为目标格式
     *
     * @param date              日期
     * @param dateTimeFormatter 日期格式
     * @return 目标格式
     * @throws NullPointerException 参数为空
     * <AUTHOR>
     * @date 2023/4/27
     * @since 1.0.0
     */
    public static String format(Date date, DateTimeFormatter dateTimeFormatter) {
        Objects.requireNonNull(date, "date");
        Objects.requireNonNull(dateTimeFormatter, DATE_TIME_FORMATTER_IS_NULL);
        return dateTimeFormatter.format(date.toInstant());

    }

    /**
     * # 将localDate转化为目标格式
     *
     * @param date              日期
     * @param dateTimeFormatter 日期格式
     * @return 目标格式
     * @throws NullPointerException 参数为空
     * <AUTHOR>
     * @date 2023/4/27
     * @since 1.0.0
     */
    public static String format(LocalDate localDate, DateTimeFormatter dateTimeFormatter) {
        Objects.requireNonNull(localDate, "localDate");
        Objects.requireNonNull(dateTimeFormatter, DATE_TIME_FORMATTER_IS_NULL);
        return dateTimeFormatter.format(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());

    }

    /**
     * # 将localTime转化为目标格式
     *
     * @param localTime         日期
     * @param dateTimeFormatter 日期格式
     * @return 目标格式
     * @throws NullPointerException 参数为空
     * <AUTHOR>
     * @date 2023/4/27
     * @since 1.0.0
     */
    public static String format(LocalTime localTime, DateTimeFormatter dateTimeFormatter) {
        Objects.requireNonNull(localTime, "localTime");
        Objects.requireNonNull(dateTimeFormatter, DATE_TIME_FORMATTER_IS_NULL);
        return localTime.format(dateTimeFormatter);

    }

    /**
     * # 将localDateTime转化为目标格式
     *
     * @param localDateTime     日期
     * @param dateTimeFormatter 日期格式
     * @return
     * @throws
     * <AUTHOR>
     * @date 2023/4/27
     * @since 1.0.0
     */
    public static String format(LocalDateTime localDateTime, DateTimeFormatter dateTimeFormatter) {
        Objects.requireNonNull(localDateTime, "localDateTime");
        Objects.requireNonNull(dateTimeFormatter, DATE_TIME_FORMATTER_IS_NULL);
        return dateTimeFormatter.format(localDateTime.atZone(ZoneId.systemDefault()).toInstant());

    }


    /**
     * # 将zonedDateTime转化为目标格式
     *
     * @param zonedDateTime     时区日期
     * @param dateTimeFormatter 日期格式
     * @return
     * @throws
     * <AUTHOR>
     * @date 2023/4/27
     * @since 1.0.0
     */
    public static String format(ZonedDateTime zonedDateTime, DateTimeFormatter dateTimeFormatter) {
        Objects.requireNonNull(zonedDateTime, "zonedDateTime");
        Objects.requireNonNull(dateTimeFormatter, DATE_TIME_FORMATTER_IS_NULL);
        return dateTimeFormatter.format(zonedDateTime);

    }


    /**
     * # 解析字符串转化为LocalDateTime
     *
     * @param dateStr           需要转换的日期字符串
     * @param dateTimeFormatter 字符串格式
     * @return LocalDateTime 本地日期时间
     * @throws DateTimeParseException 解析日期异常
     * @throws DateTimeException      转换日期异常
     * <AUTHOR>
     * @date 2023/4/27
     * @since 1.0.0
     */
    public static LocalDateTime parseToLocalDateTime(String dateStr, DateTimeFormatter dateTimeFormatter) {
        Objects.requireNonNull(dateStr, DATE_STR_IS_NULL);
        Objects.requireNonNull(dateTimeFormatter, DATE_TIME_FORMATTER_IS_NULL);
        return LocalDateTime.from(dateTimeFormatter.parse(dateStr));
    }

    /**
     * # 解析字符串转化为LocalDate
     *
     * @param dateStr           需要转换的日期字符串
     * @param dateTimeFormatter 字符串格式
     * @return LocalDate 本地日期
     * @throws DateTimeParseException 解析日期异常
     * @throws DateTimeException      转换日期异常
     * <AUTHOR>
     * @date 2023/4/27
     * @since 1.0.0
     */
    public static LocalDate parseToLocalDate(String dateStr, DateTimeFormatter dateTimeFormatter) {
        Objects.requireNonNull(dateStr, DATE_STR_IS_NULL);
        Objects.requireNonNull(dateTimeFormatter, DATE_TIME_FORMATTER_IS_NULL);
        return LocalDate.from(dateTimeFormatter.parse(dateStr));
    }

    /**
     * # 解析字符串转化为ZonedDateTime
     *
     * @param dateStr           需要转换的日期字符串
     * @param dateTimeFormatter 字符串格式
     * @return ZonedDateTime 时区日期时间
     * @throws DateTimeParseException 解析日期异常
     * @throws DateTimeException      转换日期异常
     * <AUTHOR>
     * @date 2023/4/27
     * @since 1.0.0
     */
    public static ZonedDateTime parseToZonedDateTime(String dateStr, DateTimeFormatter dateTimeFormatter) {
        Objects.requireNonNull(dateStr, DATE_STR_IS_NULL);
        Objects.requireNonNull(dateTimeFormatter, DATE_TIME_FORMATTER_IS_NULL);
        return ZonedDateTime.from(dateTimeFormatter.parse(dateStr));
    }

    /**
     * # 解析字符串转化为Date
     *
     * @param dateStr           需要转换的日期字符串
     * @param dateTimeFormatter 字符串格式
     * @return Date lang.util Date
     * @throws DateTimeParseException 解析日期异常
     * @throws DateTimeException      转换日期异常
     * <AUTHOR>
     * @date 2023/4/27
     * @since 1.0.0
     */
    public static Date parseToDate(String dateStr, DateTimeFormatter dateTimeFormatter) {
        Objects.requireNonNull(dateStr, DATE_STR_IS_NULL);
        Objects.requireNonNull(dateTimeFormatter, DATE_TIME_FORMATTER_IS_NULL);
        Date date = null;
        if (dateTimeFormatter.equals(PURE_DATE_FORMATTER)
                || dateTimeFormatter.equals(DATE_FORMATTER)
                || dateTimeFormatter.equals(CHINESE_DATE_FORMATTER)
        ) {
            date = LocalDateUtils.toDate(LocalDate.parse(dateStr, dateTimeFormatter));
        } else if (equalsDateTimes(dateTimeFormatter)
                || dateTimeFormatter.equals(CHINESE_DATE_TIME_FORMATTER)
                || equalsPureDateTimes(dateTimeFormatter)

        ) {
            date = LocalDateTimeUtils.toDate(LocalDateTime.parse(dateStr, dateTimeFormatter));
        } else if (dateTimeFormatter.equals(UTC_MS_FORMATTER)) {
            date = ZoneDateTimeUtils.toDate(ZonedDateTime.parse(dateStr, dateTimeFormatter));
        } else {
            throw new RuntimeException("no format adappter");
        }
        return date;
    }

    /**
     * 检测时间格式-带“-”格式年月日
     * @param dateTimeFormatter
     * @return boolean true/false
     * @throws
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/11/9
     */
    private static boolean equalsDateTimes(DateTimeFormatter dateTimeFormatter){
        return dateTimeFormatter.equals(DATETIME_MINUTE_FORMATTER)
                || dateTimeFormatter.equals(DATETIME_FORMATTER)
                || dateTimeFormatter.equals(DATETIME_MS_FORMATTER);
    }

    /**
     * 检测时间格式-不带“-”年月日格式
     * @param dateTimeFormatter
     * @return boolean true/false
     * @throws
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/11/9
     */
    private static boolean equalsPureDateTimes(DateTimeFormatter dateTimeFormatter){
        return dateTimeFormatter.equals(PURE_DATETIME_NO_SECOND_FORMATTER)
                || dateTimeFormatter.equals(PURE_DATETIME_FORMATTER)
                || dateTimeFormatter.equals(PURE_DATETIME_MS_FORMATTER);
    }
}