/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.calculations;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.Collections;
import java.util.StringJoiner;

/**
 * 类描述:   计算一个数字占另外一个数字的百分比字符串 .
 * <p>
 * <p>
 * 数字类型可以是int，long，double等基础类型
 * <p>
 * <p>
 * 需要指定保留小数点后位数，和四舍五入方式
 * RoundingMode（舍入模式）是一个枚举类型，定义了进行舍入操作时的不同模式。以下是常见的RoundingMode模式：
 * <p>
 * UP：向远离零的方向舍入，即进一法。正数舍入时表现为向上，负数舍入时表现为向下。
 * DOWN：向接近零的方向舍入，即舍弃小数部分。
 * CEILING：向正无穷方向舍入，即向上舍入。
 * FLOOR：向负无穷方向舍入，即向下舍入。
 * HALF_UP：最近数字舍入（5进位），如果舍弃部分大于等于0.5，则进位；否则，舍弃。
 * HALF_DOWN：最近数字舍入（5舍弃），如果舍弃部分大于0.5，则进位；否则，舍弃。
 * HALF_EVEN：最近数字舍入（银行家舍入法），如果舍弃部分左边的数字为奇数，则舍入行为同HALF_UP；如果为偶数，则舍入行为同HALF_DOWN。
 * UNNECESSARY：断言请求的操作具有精确结果，因此不需要舍入。如果对结果进行舍入，则抛出ArithmeticException异常。
 * 这些舍入模式提供了不同的舍入规则，可根据需求选择适当的模式进行舍入操作。
 * <p>
 * 在Java中，可以通过RoundingMode枚举类型的常量来表示相应的舍入模式，例如RoundingMode.UP、RoundingMode.DOWN等。
 *
 * <AUTHOR>
 * 创建时间:  [2023/6/8 15:36]
 */
public class PrecisionCalculationUtils {

    public static final String STRING = "";
    public static final int ONE_HUNDRED = 100;

    /**
     * 数字类型 double  指定保留小数点后位数，和四舍五入方式。
     * <p>
     * PrecisionCalculationUtils.calculatePercentage(分子, 分母, 保留小数位数,  RoundingMode.HALF_UP);
     * RoundingMode（舍入模式）是一个枚举类型 查看本类说明
     *
     * @param number        分子
     * @param total         分母
     * @param decimalPlaces 保留小数位数
     * @param roundingMode  四舍五入的模式
     * @return
     * <AUTHOR>
     * @date 2023/6/6
     * @since 1.1.0
     */
    public static String calculatePercentage(double number, double total, int decimalPlaces,
                                             RoundingMode roundingMode) {

        BigDecimal numerator = BigDecimal.valueOf(number);
        BigDecimal denominator = BigDecimal.valueOf(total);
        return calculatePercentageBase(numerator, denominator, decimalPlaces, roundingMode);
    }

    /**
     * 字符类型的数字进行处理
     * PrecisionCalculationUtils.calculatePercentageBase(分子, 分母, 保留小数位数,  RoundingMode.HALF_UP);
     * RoundingMode（舍入模式）是一个枚举类型 查看本类说明
     *
     * @param number        分子
     * @param total         分母
     * @param decimalPlaces 保留小数位数
     * @param roundingMode  四舍五入的模式
     * @return String 格式化后的百分比字符串（带%符号）
     * @throws ArithmeticException 当total为0或舍入模式不匹配时抛出
     * <AUTHOR>
     * @date 2023/6/6
     * @since 1.1.0
     */
    private static String calculatePercentageBase(BigDecimal number, BigDecimal total, int decimalPlaces,
                                                  RoundingMode roundingMode) {
        if (roundingMode == null) {
            roundingMode = RoundingMode.UNNECESSARY;
        }
        number = number.multiply(BigDecimal.valueOf(ONE_HUNDRED));
        BigDecimal percentage =
                number.divide(total, decimalPlaces, roundingMode);
        DecimalFormat df = new DecimalFormat("0." + repeat("0", decimalPlaces));
        df.setRoundingMode(roundingMode);

        return df.format(percentage) + "%";
    }

    /**
     * 数字类型 int  指定保留小数点后位数，和四舍五入方式。
     * PrecisionCalculationUtils.calculatePercentage(分子, 分母, 保留小数位数,  RoundingMode.HALF_UP);
     * RoundingMode（舍入模式）是一个枚举类型 查看本类说明
     *
     * @param number        分子
     * @param total         分母
     * @param decimalPlaces 保留小数位数
     * @param roundingMode  四舍五入的模式
     * @return String 格式化后的百分比字符串（带%符号）
     * @throws ArithmeticException 当total为0时抛出
     * <AUTHOR>
     * @date 2023/6/6
     * @since 1.1.0
     */
    public static String calculatePercentage(int number, int total, int decimalPlaces, RoundingMode roundingMode) {
        BigDecimal numerator = new BigDecimal(number);
        BigDecimal denominator = new BigDecimal(total);
        return calculatePercentageBase(numerator, denominator, decimalPlaces, roundingMode);
    }

    /**
     * 数字类型 long  指定保留小数点后位数，和四舍五入方式。
     * PrecisionCalculationUtils.calculatePercentage(分子, 分母, 保留小数位数,  RoundingMode.HALF_UP);
     * RoundingMode（舍入模式）是一个枚举类型 查看本类说明
     *
     * @param number        分子
     * @param total         分母
     * @param decimalPlaces 保留小数位数
     * @param roundingMode  四舍五入的模式
     * @return String 格式化后的百分比字符串（带%符号）
     * @throws ArithmeticException 当total为0时抛出
     * <AUTHOR>
     * @date 2023/6/6
     * @since 1.1.0
     */
    public static String calculatePercentage(long number, long total, int decimalPlaces, RoundingMode roundingMode) {
        BigDecimal numerator = new BigDecimal(number);
        BigDecimal denominator = new BigDecimal(total);
        return calculatePercentageBase(numerator, denominator, decimalPlaces, roundingMode);
    }

    /**
     * 生成指定次数的重复字符串
     * <p>示例：repeat("0", 3) 返回"000"</p>
     * @param src   要重复的基础字符串（非空）
     * @param times 重复次数（非负）
     * @return String 重复后的字符串（times为0时返回空字符串）
     * <AUTHOR>
     * @date 2023/6/8
     * @since 1.1.0
     */
    private static String repeat(String src, int times) {
        StringJoiner sj = new StringJoiner(STRING);
        Collections.nCopies(times, src).forEach(sj::add);
        return sj.toString();
    }


}
