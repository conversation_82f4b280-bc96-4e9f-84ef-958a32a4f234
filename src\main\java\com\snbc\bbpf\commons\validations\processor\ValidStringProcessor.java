/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.validations.processor;

import com.google.auto.service.AutoService;
import com.snbc.bbpf.commons.validations.annotation.ValidString;

import javax.annotation.processing.AbstractProcessor;
import javax.annotation.processing.Messager;
import javax.annotation.processing.ProcessingEnvironment;
import javax.annotation.processing.Processor;
import javax.annotation.processing.RoundEnvironment;
import javax.lang.model.SourceVersion;
import javax.lang.model.element.Element;
import javax.lang.model.element.ElementKind;
import javax.lang.model.element.TypeElement;
import javax.lang.model.type.TypeMirror;
import javax.tools.Diagnostic;
import java.util.LinkedHashSet;
import java.util.Set;

/**
 * 字符串是否包含指定字符串注解处理器
 * 编译时检查注解使用是否正确
 *
 * <AUTHOR>
 * @module 注解
 * @date 2023/8/30
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 * @since 1.2.0
 */
@AutoService(Processor.class)
public class ValidStringProcessor extends AbstractProcessor {
    private Messager messager;

    @Override
    public synchronized void init(ProcessingEnvironment processingEnv) {
        super.init(processingEnv);
        messager = processingEnv.getMessager();
    }

    @Override
    public boolean process(Set<? extends TypeElement> annotations, RoundEnvironment roundEnv) {
        Set<? extends Element> elementsAnnotatedWith = roundEnv.getElementsAnnotatedWith(ValidString.class);
        for (Element element : elementsAnnotatedWith) {

            if (element.getKind() == ElementKind.METHOD) {
                continue;
            }
            if (element.getKind() != ElementKind.FIELD && element.getKind() != ElementKind.PARAMETER) {
                messager.printMessage(
                        Diagnostic.Kind.ERROR,
                        String.format("Only FIELD or PARAMETER can be annotated with @%s", ValidString.class.getSimpleName()),
                        element);

            } else {
                TypeMirror stringType = processingEnv.getElementUtils()
                    .getTypeElement(String.class.getCanonicalName()).asType();
                if (!processingEnv.getTypeUtils().isSameType(element.asType(), stringType)) {
                    messager.printMessage(
                        Diagnostic.Kind.ERROR,
                        String.format("Only String type can be annotated with @%s", ValidString.class.getSimpleName()),
                        element);
                }
            }
        }
        return true;
    }

    @Override
    public Set<String> getSupportedAnnotationTypes() {
        Set<String> annotataions = new LinkedHashSet<>();
        annotataions.add(ValidString.class.getCanonicalName());
        return annotataions;
    }

    @Override
    public SourceVersion getSupportedSourceVersion() {
        return SourceVersion.latestSupported();
    }
}
