/*
 * 版权所有 2009-2025山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.strings;

import com.snbc.bbpf.commons.validations.annotation.ValidStringType;
import com.snbc.bbpf.commons.validations.utils.ValidStringUtil;
import org.apache.commons.lang3.StringUtils;

import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 【字符串处理】对通用字符串进行格式化
 * <p>银行账号/卡号格式化，每四位一个占位符。支持占位符自定义，默认占位符为空格 </p>
 * <p>证件号（身份证）格式化，每四位一个占位符。支持占位符自定义，默认占位符为空格 </p>
 * <p>手机号/固话格式化，手机号前3位一个空格，其他4位一个占位符。</p>
 * <p>固话带区号带横线，固话号带占位符。占位符可自定义 </p>
 * <p>金额格式化保留指定位小数（位数可以自定义，默认2位），参数支持是否四舍五入（默认直接舍去）</p>
 * <p>金额格式化加千分位分隔符（分隔符可自定义，默认是逗号） </p>
 * <p>将数字金额显示带符号、单位的金额，如￥ 1.00，$ 1.00，1.00元，1.02 万元</p>
 *
 * <AUTHOR>
 * @module
 * @date 2025/5/27 20:56
 * @copyright 2025 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class StringFormatUtils {
    private StringFormatUtils() {
        throw new IllegalStateException("Utility class");
    }

    private static final Pattern CARDNO_PATTERN = Pattern.compile("(.{4})(?=.)");// 账号长度不固定每4位匹配的正则
    private static final Pattern IDNO_PATTERN = Pattern.compile("(.{4})(?=.)");// 证件号长度不固定每4位插入占位符（最后不足4位保留）
    private static final Pattern PHONE_PATTERN = Pattern.compile("(\\d{3})(\\d{4})(\\d{4})");// 手机号3-4-4结构
    private static final Pattern THREE_DIGIT_AREA_CODE_PATTERN = Pattern.compile("^0(1[0-9]|2[0-9])\\d{7,8}$");// 三位区号
    private static final Pattern FOUR_DIGIT_AREA_CODE_PATTERN = Pattern.compile("^0(3|4|5|7|8|9)\\d{8,9}$");// 四位区号

    // 默认常量
    private static final String DEFAULT_PLACEHOLDER = " ";
    private static final String DEFAULT_AMOUNT_SEPARATOR = ",";
    private static final String DEFAULT_LANDLINE_SEPARATOR = "-";
    private static final int DEFAULT_DECIMAL_PLACES = 2;
    private static final int THREE_DIGIT_HEAD = 3;// 三位区号头
    private static final int FOUR_DIGIT_HEAD = 4;// 四位区号头

    /**
     * 格式化银行卡号（使用默认空格作为占位符）
     * <p>
     * StringFormatUtils.formatBankCard("****************") = "6222 5210 515 3430"
     * </p>
     *
     * @param input 输入的银行卡号
     * @return 格式化后的银行卡号
     * @throws IllegalArgumentException 格式校验失败
     * <AUTHOR>
     * @date 2024/5/31
     * @since 1.6.0
     */
    public static String formatBankCard(String input) {
        return formatBankCard(input, DEFAULT_PLACEHOLDER);
    }

    /**
     * 使用正则表达式格式化银行卡号（每4位插入一个占位符）
     * <p>
     * StringFormatUtils.formatBankCard("****************", "-") = "6222-5210-3515-3430"
     * StringFormatUtils.formatBankCard("****************", null) = "6222 5210 515 3430"
     * </p>
     *
     * @param input       输入的银行卡号
     * @param placeholder 占位符，若为空则使用默认空格
     * @return 格式化后的银行卡号
     * @throws IllegalArgumentException 格式校验失败
     * <AUTHOR>
     * @date 2024/5/31
     * @since 1.6.0
     */
    public static String formatBankCard(String input, String placeholder) {
        //卡号验证
        ValidStringUtil.validateString(input, "The bank card format is incorrect", "", ValidStringType.BANK_CARD);
        placeholder = StringEmptyCheck.isEmpty(placeholder) ? DEFAULT_PLACEHOLDER : placeholder;

        // 每四位一组，用占位符连接
        return CARDNO_PATTERN.matcher(input).replaceAll("$1" + placeholder);


    }

    /**
     * 格式化身份证号（使用默认空格作为占位符）
     * <p>
     * StringFormatUtils.formatIDCard("37108119930301966X") = "3710 8119 9303 0196 6X"
     * </p>
     *
     * @param input 输入的身份证号
     * @return 格式化后的身份证号
     * @throws IllegalArgumentException 格式校验失败
     * <AUTHOR>
     * @date 2024/5/31
     * @since 1.6.0
     */
    public static String formatIDCard(String input) {
        return formatIDCard(input, DEFAULT_PLACEHOLDER);
    }

    /**
     * 使用正则表达式格式化身份证号（每4位插入一个占位符）
     * <p>
     * StringFormatUtils.formatIDCard("37108119930301966X", "-") = "3710-8119-9303-0196-6X"
     * StringFormatUtils.formatIDCard("37108119930301966X", null) = "3710 8119 9303 0196 6X"
     * </p>
     *
     * @param input       输入的身份证号
     * @param placeholder 占位符，若为空则使用默认空格
     * @return 格式化后的身份证号
     * @throws IllegalArgumentException 格式校验失败
     * <AUTHOR>
     * @date 2024/5/31
     * @since 1.6.0
     */
    public static String formatIDCard(String input, String placeholder) {
        //身份证号验证
        ValidStringUtil.validateString(input, "", "", ValidStringType.IDENTITY_CARD);
        placeholder = StringEmptyCheck.isEmpty(placeholder) ? DEFAULT_PLACEHOLDER : placeholder;

        // 每四位一组，用占位符连接
        return IDNO_PATTERN.matcher(input).replaceAll("$1" + placeholder);
    }

    /**
     * 格式化手机号（使用默认空格作为占位符）
     * <p>
     * StringFormatUtils.formatPhoneNumber("17862724771") = "178 6272 4771"
     * </p>
     *
     * @param input 输入的手机号
     * @return 格式化后的手机号
     * @throws IllegalArgumentException 格式校验失败
     * <AUTHOR>
     * @date 2024/5/31
     * @since 1.6.0
     */
    public static String formatPhoneNumber(String input) {
        return formatPhoneNumber(input, DEFAULT_PLACEHOLDER);
    }

    /**
     * 使用正则表达式格式化手机号（3-4-4结构）
     * <p>
     * StringFormatUtils.formatPhoneNumber("17862724771", "-") = "178-6272-4771"
     * StringFormatUtils.formatPhoneNumber("17862724771", null) = "178 6272 4771"
     * </p>
     *
     * @param input       输入的手机号
     * @param placeholder 占位符，若为空则使用默认空格
     * @return 格式化后的手机号
     * @throws IllegalArgumentException 格式校验失败
     * <AUTHOR>
     * @date 2024/5/31
     * @since 1.6.0
     */
    public static String formatPhoneNumber(String input, String placeholder) {
        //手机号验证
        ValidStringUtil.validateString(input, "", "", ValidStringType.PHONE_NUMBER);
        placeholder = StringEmptyCheck.isEmpty(placeholder) ? DEFAULT_PLACEHOLDER : placeholder;
        Matcher matcher = PHONE_PATTERN.matcher(input);
        return matcher.matches() ? matcher.replaceAll("$1" + placeholder + "$2" + placeholder + "$3") : input;
    }

    /**
     * 格式化金额（使用默认locale格式，保留2位小数，不四舍五入）
     * <p>
     * StringFormatUtils.formatAmount(1234567.89) = "1,234,567.89"
     * </p>
     *
     * @param amount 输入的金额
     * @return 格式化后的金额
     * @throws IllegalArgumentException 格式校验失败
     * <AUTHOR>
     * @date 2024/5/31
     * @since 1.6.0
     */
    public static String formatAmount(double amount) {
        return formatAmount(Locale.getDefault(), amount, false, DEFAULT_DECIMAL_PLACES,
                false, DEFAULT_AMOUNT_SEPARATOR, null);
    }

    /**
     * 格式化金额
     * <p>
     * StringFormatUtils.formatAmount(Locale.US, 1234567.89, true, 0, false, ",", "美元") = "$1,234,567美元"  // 显示货币符号+自定义单位
     * StringFormatUtils.formatAmount(Locale.CHINA, -1234.567, false, 2, true, ",", "") = "-1,234.57"  // 负数+四舍五入（保留2位）
     * StringFormatUtils.formatAmount(Locale.CHINA, 123.456, false, 1, true, ",", "") = "123.5"  // 保留1位小数
     * StringFormatUtils.formatAmount(Locale.CHINA, 123.4567, true, 3, true, ",", "") = "￥123.457"  // 保留3位小数
     * StringFormatUtils.formatAmount(Locale.CHINA, -1234.567, false, 2, false, ",", "") = "-1,234.56"  // 负数+不四舍五入
     * </p>
     *
     * @param locale        本地化设置
     * @param amount        金额
     * @param showSymbol    是否显示货币符号
     * @param decimalPlaces 保留小数位数
     * @param roundingMode  是否四舍五入（true四舍五入 false直接舍弃）
     * @param separator     千位分隔符
     * @param unit          单位
     * @return 格式化后的金额字符串
     * @throws IllegalArgumentException 格式校验失败
     * <AUTHOR>
     * @date 2024/5/31
     * @since 1.6.0
     */
    public static String formatAmount(Locale locale, double amount, boolean showSymbol,
                                      int decimalPlaces, boolean roundingMode,
                                      String separator, String unit) {
        separator = StringEmptyCheck.isEmpty(separator) ? DEFAULT_LANDLINE_SEPARATOR : separator;
        // 参数校验增强
        if (decimalPlaces < 0) {
            decimalPlaces = DEFAULT_DECIMAL_PLACES;
        }

        // 创建自定义模式的DecimalFormat
        String pattern = (showSymbol ? "¤" : "") +
                "#" + separator + "##0" +
                (decimalPlaces > 0 ? ("." + StringUtils.repeat("0", decimalPlaces)) : "")
                + (StringEmptyCheck.isEmpty(unit) ? "" : unit);
        DecimalFormat decimalFormat = new DecimalFormat(pattern);
        decimalFormat.setDecimalFormatSymbols(DecimalFormatSymbols.getInstance(locale));
        // 设置舍入模式
        decimalFormat.setRoundingMode(roundingMode ? RoundingMode.HALF_UP : RoundingMode.DOWN);

        return decimalFormat.format(amount);
    }

    /**
     * 判断是否为三位区号（仅限 01x 和 02x）
     *
     * @param input 输入的区号
     * @return true符合 false不符合
     * @throws
     * <AUTHOR>
     * @date 2024/5/31
     * @since 1.6.0
     */
    private static boolean isThreeDigitAreaCode(String input) {
        return !StringEmptyCheck.isEmpty(input) && THREE_DIGIT_AREA_CODE_PATTERN.matcher(input).matches();
    }

    /**
     * 判断是否为四位区号（除 01x 和 02x 以外的区号）
     *
     * @param input 输入的区号
     * @return true符合 false不符合
     * @throws
     * <AUTHOR>
     * @date 2024/5/31
     * @since 1.6.0
     */
    private static boolean isFourDigitAreaCode(String input) {
        return !StringEmptyCheck.isEmpty(input) && FOUR_DIGIT_AREA_CODE_PATTERN.matcher(input).matches();
    }

    /**
     * 获取区号长度（3或4），否则返回0表示非固话
     *
     * @param input 输入的区号
     * @return 区号长度
     * @throws
     * <AUTHOR>
     * @date 2024/5/31
     * @since 1.6.0
     */
    public static int getAreaCodeLength(String input) {
        if (isThreeDigitAreaCode(input)) {
            return THREE_DIGIT_HEAD;
        } else if (isFourDigitAreaCode(input)) {
            return FOUR_DIGIT_HEAD;
        } else {
            return 0; // 不是标准固话格式
        }
    }

    /**
     * 格式化固话号码（使用默认横线作为占位符）
     * <p>
     * StringFormatUtils.formatFixedLine("01012345678") = "010-12345678"
     * </p>
     *
     * @param input 输入的固话号码
     * @return 格式化后的固话号码
     * @throws IllegalArgumentException 格式校验失败
     * <AUTHOR>
     * @date 2024/5/31
     * @since 1.6.0
     */
    public static String formatFixedLine(String input) {
        return formatFixedLineWithPlaceholder(input, DEFAULT_LANDLINE_SEPARATOR);
    }

    /**
     * 格式化固话号码（增加号码部分分组逻辑）
     * <p>
     * StringFormatUtils.formatFixedLineWithPlaceholder("01012345678", "-") = "010-12345678"  // 三位区号正常值
     * StringFormatUtils.formatFixedLineWithPlaceholder("05318765", "-") = "05318765"  // 四位区号但号码过短
     * StringFormatUtils.formatFixedLineWithPlaceholder("02012345", "-") = "02012345"  // 非标准区号格式保留原输入
     * StringFormatUtils.formatFixedLineWithPlaceholder("05318765432", "*") = "0531*8765432"  // 四位区号自定义占位符
     * </p>
     *
     * @param input       输入的固话号码
     * @param placeholder 占位符，若为空则使用默认横线
     * @return 格式化后的固话号码
     * @throws IllegalArgumentException 格式校验失败
     * <AUTHOR>
     * @date 2024/5/31
     * @since 1.6.0
     */
    public static String formatFixedLineWithPlaceholder(String input, String placeholder) {
        if (StringEmptyCheck.isEmpty(input)) {
            throw new IllegalArgumentException("Fixed line number is empty");
        }

        int areaCodeLength = getAreaCodeLength(input);
        if (areaCodeLength > 0 && input.length() > areaCodeLength) {
            String areaCode = input.substring(0, areaCodeLength);
            String localNumber = input.substring(areaCodeLength);
            placeholder = StringEmptyCheck.isEmpty(placeholder) ? DEFAULT_LANDLINE_SEPARATOR : placeholder;
            return areaCode + placeholder + localNumber;
        }

        return input; // 不符合规范直接返回原值
    }


}
