/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.validations.aspect;

import com.snbc.bbpf.commons.objs.ObjectEmptyCheck;
import com.snbc.bbpf.commons.strings.StringEmptyCheck;
import com.snbc.bbpf.commons.validations.annotation.ValidNotEmpty;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;

/**
 * 非空验证注解方法参数的切面类
 *
 * <AUTHOR>
 * @module
 * @date 2023/9/12 16:57
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
@Aspect
public class ValidParameterNotEmptyAspect {
    //扫描所有配置@ValidNotEmpty注解的方法
    @Pointcut("execution(@com.snbc.bbpf.commons.validations.annotation.ValidNotEmpty *  *(..))")
    public void myPointcut() {
    }

    /*
     * 方法的环绕切面（对配置非空验证注解的方法参数进行非空校验）
     *
     * @param joinPoint
     * @return Object
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/9/12
     */
    @Around("myPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {

        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Parameter[] parameters = method.getParameters();
        // 所有的参数
        Object[] args = joinPoint.getArgs();
        // 校验每个参数
        for (int i = 0; i < parameters.length; i++) {
            Parameter parameter = parameters[i];
            // 获取参数注解
            ValidNotEmpty validNotEmpty = parameter.getAnnotation(ValidNotEmpty.class);
            // 存在@ValidNotEmpty，忽略
            if (null == validNotEmpty) {
                continue;
            }
            if (ObjectEmptyCheck.isEmpty(args[i])) {
                throw new IllegalArgumentException(StringEmptyCheck.isEmpty(validNotEmpty.message()) ?
                        ("method[" + joinPoint.getSignature().getName() + "]parameter[" + parameter.getName() + "]can't be empty") : validNotEmpty.message());
            }
        }
        return joinPoint.proceed();
    }

}
