/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.validations.utils;

import com.snbc.bbpf.commons.validations.annotation.ValidStringNumber;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * StringNumber注解验证工具类
 *
 * <AUTHOR>
 * @module 注解
 * @date 2023/8/30
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class ValidStringNumberUtilTest {

    //测试类
    private StringNumebrTestSuccessObj testObjSuccess;
    private StringNumebrTestFailObj testObjFailure;

    @BeforeEach
    public void init() {
        testObjSuccess = new StringNumebrTestSuccessObj();
        testObjSuccess.setField1("123");
        testObjSuccess.setField2("0");
        testObjSuccess.setField3("-123");
        testObjSuccess.setField4("1.2");
        testObjFailure = new StringNumebrTestFailObj();
        testObjFailure.setField1("abc");
        testObjFailure.setField2("1de");
        testObjFailure.setField3("!@");
        testObjFailure.setField4("-");
    }

    @Test
    @DisplayName("验证成功")
    @Tags({
            @Tag("@id:48"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/8/30")
    })
    void testStringFieldsValidate_contains() {
        assertDoesNotThrow(() -> {
            ValidStringNumberUtil.stringFieldsValidate(testObjSuccess);
        });
    }

    @Test
    @DisplayName("验证失败")
    @Tags({
            @Tag("@id:48"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/8/30")
    })
    void testStringFieldsValidate_nocontains() {
        assertThrows(IllegalArgumentException.class,() -> {
            ValidStringNumberUtil.stringFieldsValidate(testObjFailure);
        });
    }


    public class StringNumebrTestSuccessObj {
        @ValidStringNumber
        private String field1;

        @ValidStringNumber
        private String field2;

        private String field3;

        private String field4;

        public String getField1() {
            return field1;
        }

        public void setField1(String field1) {
            this.field1 = field1;
        }

        public String getField2() {
            return field2;
        }

        public void setField2(String field2) {
            this.field2 = field2;
        }

        public String getField3() {
            return field3;
        }

        public void setField3(String field3) {
            this.field3 = field3;
        }

        public String getField4() {
            return field4;
        }

        public void setField4(String field4) {
            this.field4 = field4;
        }
    }

    public class StringNumebrTestFailObj {
        @ValidStringNumber
        private String field1;

        @ValidStringNumber
        private String field2;

        private String field3;

        private String field4;

        public String getField1() {
            return field1;
        }

        public void setField1(String field1) {
            this.field1 = field1;
        }

        public String getField2() {
            return field2;
        }

        public void setField2(String field2) {
            this.field2 = field2;
        }

        public String getField3() {
            return field3;
        }

        public void setField3(String field3) {
            this.field3 = field3;
        }

        public String getField4() {
            return field4;
        }

        public void setField4(String field4) {
            this.field4 = field4;
        }
    }

}