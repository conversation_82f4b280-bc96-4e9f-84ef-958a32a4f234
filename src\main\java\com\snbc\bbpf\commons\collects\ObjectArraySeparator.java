/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.collects;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 【集合处理】将对象集合和带有指定分隔符的字符串进行互相转换
 * <p>
 * 将对象集合（array，list，map）和带有指定分隔符的字符串进行互相转换。
 * 需要完成如下几种场景：
 * </p>
 * <p>1.将字符串集合（array，list，map）和指定分隔符的字符串进行互相转换</p>
 * <p>2.集合对象中对象如果是集合需要递归处理。</p>
 * <p>3.带有分隔符的字符串可指定转换成成不同集合类型（array，list，map）</p>
 * <p>4.如果是map类型，可以指定是key还是value进行分割，有可以指定包含key和value，同时包括key和value之间的分隔符。</p>
 * <p>注意处理效率。</p>
 *
 * <AUTHOR>
 * @module 集合处理
 * @date 2023/4/23 21:46
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class ObjectArraySeparator {

    private ObjectArraySeparator() {
        throw new IllegalStateException("Utility class");
    }
    public static final String CANNOT_BE_NULL = "Array or Children of Array cannot be NULL.";
    public static final String DEFAULT_KV_SEPARATOR = "=";

    /**
     * 将字符串数组转换为带分隔符的字符串。
     *
     * <p>例如：</p>
     * <p>   objs={"a","b"} separator="," => "a,b" </p>
     * <p>   objs={"a"} separator="," => "a" </p>
     * <p>   objs={""} separator="," => "" </p>
     *
     * @param objs 字符串数组
     * @param separator 分隔符
     * @return java.lang.String  拼接后的字符串
     * @throws IllegalArgumentException 参数不能为空，数组中也不能有NULL字符串
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/24
     */
    public static String join(String[] objs, String separator){
        if(ObjectArrayCheck.isNull(objs) ||
                ObjectArrayCheck.hasNullItem(objs) ||
                ObjectArrayCheck.isNull(separator)){
            throw new IllegalArgumentException(CANNOT_BE_NULL);
        }
        return String.join(separator, objs);
    }

    /**
     * 将带分隔符的字符串转换为字符串数组
     * <p>例如：</p>
     * <p>   str="a,b" separator="," => {“a”,“b”} </p>
     * <p>   str="a" separator="," => {"a"} </p>
     * <p>   str="" separator="," => {""} </p>
     * <p>   str="," separator="," => {} </p>
     * <p>   str="a," separator="," => {"a"} </p>
     *
     * @param str 带分隔符的字符串
     * @param separator 分隔符
     * @return java.lang.String[] 字符串数组
     * @throws IllegalArgumentException 参数不能为空
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/24
     */
    public static String[] splitToString(String str, String separator){
        if(ObjectArrayCheck.isNull(str)||ObjectArrayCheck.isNull(separator)){
            throw new IllegalArgumentException(CANNOT_BE_NULL);
        }
        return Arrays.stream(str.split(separator)).toArray(String[]::new);
    }

    /**
     * 将字符串列表转换为带分隔符的字符串。
     * <p>例如：</p>
     * <p>   lists={"a","b"} separator="," => "a,b" </p>
     * <p>   lists={"a"} separator="," => "a" </p>
     * <p>   lists={"a",""} separator="," => "a," </p>
     * <p>   lists={"a","b"} separator="" => "ab" </p>
     *
     * @param lists  字符串列表
     * @param separator 分隔符
     * @return java.lang.String
     * @throws IllegalArgumentException 参数不能为空
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/24
     */
    public static String join(List<String> lists, String separator){
        if(ObjectArrayCheck.isNull(lists) ||
                ObjectArrayCheck.isNull(separator)){
            throw new IllegalArgumentException(CANNOT_BE_NULL);
        }
        return String.join(separator, lists);
    }

    /**
     * 将带分隔符的字符串转换为字符串列表。
     * <p>例如：</p>
     * <p>   str="a,b" separator="," => {"a","b"} </p>
     * <p>   str="a" separator="," => {"a"} </p>
     * <p>   str="a," separator="," => {"a"} </p>
     * <p>   str="a,b," separator="," => {"a","b"} </p>
     * <p>   str="ab" separator="" => {"a","b"} </p>
     * <p>   str="a,b" separator="" => {"a",",","b"} </p>
     *
     * @param str 带分隔符的字符串
     * @param separator 分隔符
     * @return java.util.List<java.lang.String> 字符串列表
     * @throws IllegalArgumentException 参数不能为空
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/24
     */
    public static List<String> splitToList(String str, String separator){
        if(ObjectArrayCheck.isNull(str)||ObjectArrayCheck.isNull(separator)){
            throw new IllegalArgumentException(CANNOT_BE_NULL);
        }
        return Arrays.stream(str.split(separator)).collect(Collectors.toList());
    }

    /**
     * 将字符串Map转换为带分隔符的字符串。
     * <p>例如：</p>
     * <p>   maps={k="a" v="1",k="b" v="2"} separator="," => "a=1,b=2" </p>
     * <p>   maps={k="a" v="1"} separator="," => "a=1" </p>
     * <p>   maps={k="a" v="1",k="b" v=""} separator="," => "a=1,b=" </p>
     * <p>   maps={k="a" v="1",k="" v="2"} separator="," => "=2,a=1" </p>
     * <p>   maps={k="a" v="1",k="b" v="2"} separator="" => "a=1b=2" </p>
     * <p>   maps={k="a" v="1",k="b" v="2"} separator="|" => "a=1|b=2" </p>
     *
     * @param maps 字符串Map
     * @param separator 分隔符
     * @return java.lang.String
     * @throws IllegalArgumentException 参数不能为空
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/24
     */
    public static String join(Map<String,String> maps, String separator){
        return join(maps,separator, DEFAULT_KV_SEPARATOR);
    }

    /**
     * 将字符串Map转换为kv分隔符,条目分隔符的字符串。
     * <p>例如：</p>
     * <p>   maps={k="a" v="1",k="b" v="2"} itemSeparator="," kvSeparator="=" => "a=1,b=2" </p>
     * <p>   maps={k="a" v="1",k="b" v="2"} itemSeparator="," kvSeparator=":" => "a:1,b:2" </p>
     * <p>   maps={k="a" v="1"} separator="," kvSeparator="=" => "a=1" </p>
     * <p>   maps={k="a" v="1",k="b" v="2"} itemSeparator="" kvSeparator=":" => "a:1b:2" </p>
     * <p>   maps={k="a" v="1",k="b" v="2"} itemSeparator="," kvSeparator="" => "a1,b2" </p>
     * <p>   maps={k="" v="1",k="b" v="2"} separator="," kvSeparator=":" => ":1,b:2" </p>
     * <p>   maps={k="a" v="",k="b" v="2"} separator="," kvSeparator=":" => "a:,b:2" </p>
     *
     * @param maps 字符串Map
     * @param itemSeparator 条目分隔符
     * @param kvSeparator  kv分隔符
     * @return java.lang.String
     * @throws IllegalArgumentException 参数不能为空
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/24
     */
    public static String join(Map<String,String> maps, String itemSeparator, String kvSeparator){
        if(ObjectArrayCheck.isNull(maps)||
                ObjectArrayCheck.isNull(itemSeparator)||
                ObjectArrayCheck.isNull(kvSeparator)){
            throw new IllegalArgumentException(CANNOT_BE_NULL);
        }
        return maps.entrySet().stream()
                .map(e -> e.getKey() + kvSeparator + e.getValue())
                .collect(Collectors.joining(itemSeparator));
    }

    /**
     * 将带分隔符的字符串转换成字符串Map
     * <p>默认kv分隔符为"="</p>
     * <p>例如：</p>
     * <p>   str="a=1,b=2" itemSeparator="," => {k="a" v="1",k="b" v="2"} </p>
     * <p>   str="a=1" itemSeparator=","=> {k="a" v="1"} </p>
     *
     *
     * @param str 带分隔符的字符串
     * @param separator 分隔符
     * @return java.util.Map<java.lang.String,java.lang.String> 字符串Map
     * @throws IllegalArgumentException 参数不能为空
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/24
     */
    public static Map<String,String> splitToMap(String str, String separator){
        return splitToMap(str,separator, DEFAULT_KV_SEPARATOR);
    }

    /**
     * 将带item分隔符和kv分隔符的字符串转换成字符串Map
     * <p>例如：</p>
     * <p>   str="a:1,b:2" itemSeparator="," kvSeparator=":" => {k="a" v="1",k="b" v="2"} </p>
     * <p>   str="a:1" itemSeparator="," kvSeparator=":" => {k="a" v="1"} </p>
     * <p>   str=":1,b:2" itemSeparator="," kvSeparator=":" => {k="" v="1",k="b" v="2"} </p>
     *
     * @param str 将带item分隔符和kv分隔符的字符串
     * @param itemSeparator  item分隔符
     * @param kvSeparator kv分隔符
     * @return java.util.Map<java.lang.String,java.lang.String> 字符串Map
     * @throws IllegalArgumentException 参数不能为空
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/24
     */
    public static Map<String,String> splitToMap(String str, String itemSeparator, String kvSeparator){
        if(ObjectArrayCheck.isNull(str)||
                ObjectArrayCheck.isNull(itemSeparator)||
                ObjectArrayCheck.isNull(kvSeparator)){
            throw new IllegalArgumentException(CANNOT_BE_NULL);
        }
        return Arrays.stream(str.split(itemSeparator)).map(m -> m.split(kvSeparator))
                .collect(Collectors.toMap(n -> n[0],n -> n[1]));
    }

}
