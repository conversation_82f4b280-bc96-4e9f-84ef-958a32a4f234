/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.jsons;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.snbc.bbpf.commons.strings.StringEmptyCheck;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * #JSON字符串和对象之间的转换处理
 *
 * <p>兼融各种日期格式、空属性</p>
 * <p>使用注意：默认的配置已经可以完成95%以上的序列化需求
 * 使用@JsonFormat可以完成98%以上的需求
 * 更多需求请参考Jackson下的其他annatation
 * </p>
 *
 * <AUTHOR>
 * @module JSON模块
 * @date 2023-09-07 16:04
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class JsonObjectMapper {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    static {
        OBJECT_MAPPER.registerModule(new JavaTimeModule());
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    private JsonObjectMapper() {

    }

    /**
     * # 将对象转化为JSON
     *
     * @param object 要转化为JSON的对象
     * @return String json字符串
     * @throws RuntimeException
     * <AUTHOR>
     * @date 2023/9/7
     * @since 1.2.0
     */
    public static String marshall(Object object) {
        Objects.requireNonNull(object, "object is not allowed null");
        try {
            return OBJECT_MAPPER.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * # 将对象转化为JSON
     * <p>
     * SerializationFeature默认特性，根据需要做启用和禁用
     * USE_BIG_DECIMAL_FOR_FLOATS(false),
     * USE_BIG_INTEGER_FOR_INTS(false),
     * USE_LONG_FOR_INTS(false),
     * USE_JAVA_ARRAY_FOR_JSON_ARRAY(false),
     * FAIL_ON_UNKNOWN_PROPERTIES(true),
     * FAIL_ON_NULL_FOR_PRIMITIVES(false),
     * FAIL_ON_NUMBERS_FOR_ENUMS(false),
     * FAIL_ON_INVALID_SUBTYPE(true),
     * FAIL_ON_READING_DUP_TREE_KEY(false),
     * FAIL_ON_IGNORED_PROPERTIES(false),
     * FAIL_ON_UNRESOLVED_OBJECT_IDS(true),
     * FAIL_ON_MISSING_CREATOR_PROPERTIES(false),
     * FAIL_ON_NULL_CREATOR_PROPERTIES(false),
     * FAIL_ON_MISSING_EXTERNAL_TYPE_ID_PROPERTY(true),
     * FAIL_ON_TRAILING_TOKENS(false),
     * WRAP_EXCEPTIONS(true),
     * ACCEPT_SINGLE_VALUE_AS_ARRAY(false),
     * UNWRAP_SINGLE_VALUE_ARRAYS(false),
     * UNWRAP_ROOT_VALUE(false),
     * ACCEPT_EMPTY_STRING_AS_NULL_OBJECT(false),
     * ACCEPT_EMPTY_ARRAY_AS_NULL_OBJECT(false),
     * ACCEPT_FLOAT_AS_INT(true),
     * READ_ENUMS_USING_TO_STRING(false),
     * READ_UNKNOWN_ENUM_VALUES_AS_NULL(false),
     * READ_UNKNOWN_ENUM_VALUES_USING_DEFAULT_VALUE(false),
     * READ_DATE_TIMESTAMPS_AS_NANOSECONDS(true),
     * ADJUST_DATES_TO_CONTEXT_TIME_ZONE(true),
     * EAGER_DESERIALIZER_FETCH(true);
     *
     * @param object          要转化为JSON的对象
     * @param enableFeatures  要启用的特性
     * @param disableFeatures 要禁用的特性
     * @return json字符串
     * @throws RuntimeException
     * <AUTHOR>
     * @date 2023/9/7
     * @since 1.2.0
     */
    public static String marshall(Object object, List<SerializationFeature> enableFeatures,
                                  List<SerializationFeature> disableFeatures) {
        try {
            ObjectMapper newObjectMapper = new ObjectMapper();
            newObjectMapper.registerModule(new JavaTimeModule());
            Optional.ofNullable(enableFeatures).orElseGet(ArrayList::new).stream()
                    .forEach(s -> newObjectMapper.configure(s, true));
            Optional.ofNullable(disableFeatures).orElseGet(ArrayList::new)
                    .stream().forEach(s -> newObjectMapper.configure(s, false));
            return newObjectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * # 将jsonStr转化在对象
     *
     * @param jsonStrStr 要转化的jsonStr文本
     * @param clazz      目标类
     * @return T 范型目标对象
     * @throws RuntimeException
     * <AUTHOR>
     * @date 2023/9/7
     * @since 1.2.0
     */
    public static <T> T unmarshall(String jsonStrStr, Class<T> clazz) {
        try {
            return OBJECT_MAPPER.readValue(jsonStrStr, clazz);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * # 将jsonStr转化在对象
     * <p>
     * DeserializationFeature默认特性，根据需要启用、禁用特性
     * <p>
     * USE_BIG_DECIMAL_FOR_FLOATS(false),
     * USE_BIG_INTEGER_FOR_INTS(false),
     * USE_LONG_FOR_INTS(false),
     * USE_JAVA_ARRAY_FOR_JSON_ARRAY(false),
     * FAIL_ON_UNKNOWN_PROPERTIES(true),
     * FAIL_ON_NULL_FOR_PRIMITIVES(false),
     * FAIL_ON_NUMBERS_FOR_ENUMS(false),
     * FAIL_ON_INVALID_SUBTYPE(true),
     * FAIL_ON_READING_DUP_TREE_KEY(false),
     * FAIL_ON_IGNORED_PROPERTIES(false),
     * FAIL_ON_UNRESOLVED_OBJECT_IDS(true),
     * FAIL_ON_MISSING_CREATOR_PROPERTIES(false),
     * FAIL_ON_NULL_CREATOR_PROPERTIES(false),
     * FAIL_ON_MISSING_EXTERNAL_TYPE_ID_PROPERTY(true),
     * FAIL_ON_TRAILING_TOKENS(false),
     * WRAP_EXCEPTIONS(true),
     * ACCEPT_SINGLE_VALUE_AS_ARRAY(false),
     * UNWRAP_SINGLE_VALUE_ARRAYS(false),
     * UNWRAP_ROOT_VALUE(false),
     * ACCEPT_EMPTY_STRING_AS_NULL_OBJECT(false),
     * ACCEPT_EMPTY_ARRAY_AS_NULL_OBJECT(false),
     * ACCEPT_FLOAT_AS_INT(true),
     * READ_ENUMS_USING_TO_STRING(false),
     * READ_UNKNOWN_ENUM_VALUES_AS_NULL(false),
     * READ_UNKNOWN_ENUM_VALUES_USING_DEFAULT_VALUE(false),
     * READ_DATE_TIMESTAMPS_AS_NANOSECONDS(true),
     * ADJUST_DATES_TO_CONTEXT_TIME_ZONE(true),
     * EAGER_DESERIALIZER_FETCH(true);
     *
     * @param jsonStrStr 要转化的jsonStr文本
     * @param clazz      目标类
     * @return T  范型目标对象
     * @throws RuntimeException
     * <AUTHOR>
     * @date 2023/9/7
     * @since 1.2.0
     */
    public static <T> T unmarshallWithFeatures(String jsonStrStr, Class<T> clazz
            , List<DeserializationFeature> enableFeatures, List<DeserializationFeature> disableFeatures) {
        try {
            return getUnmarshallMapper(enableFeatures, disableFeatures).readValue(jsonStrStr, clazz);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 将jsonStr转化在对象，适用于集合等复杂对象的转化
     * <p>
     * <p>如果是List<List<HasChildMapObject>>, unmarshall(json,new TypeReference<List<List<HasChildMapObject>>>() {})</p>
     * <p>HashMap<String,MyBean>, unmarshall(json,new TypeReference<Map<String,MyBean>>() {})</p>
     *
     * @param jsonStr       待转换的json串
     * @param typeReference 定义的目标对象泛型
     * @return T 泛型对象
     * <AUTHOR>
     * @date 2023/9/25
     * @since 1.2.0
     */
    public static <T> T unmarshall(String jsonStr, TypeReference<T> typeReference) {
        try {
            if (null == typeReference) {
                throw new IllegalArgumentException("typeReference is null");
            }
            if (StringEmptyCheck.isEmpty(jsonStr)) {
                return null;
            }
            return OBJECT_MAPPER.readValue(jsonStr, typeReference);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 将jsonStr转化在对象，适用于集合等复杂对象的转化(按自定义特性转换)
     *
     * <p>如果是List<List<HasChildMapObject>>, unmarshallWithFeatures(json,new TypeReference<List<List<HasChildMapObject>>>() {},null,null)</p>
     * <p>HashMap<String,MyBean>, unmarshallWithFeatures(json,new TypeReference<Map<String,MyBean>>() {},null,null)</p>
     * <p>DeserializationFeature反序列化特性，根据需要启用、禁用特性</p>
     *
     * @param jsonStr         待转换的json串
     * @param typeReference   定义的目标对象泛型
     * @param enableFeatures  允许的特性集合，若不需设置则传null
     * @param disableFeatures 禁止的特性集合，若不需设置则传null
     * @return T 泛型对象
     * <AUTHOR>
     * @date 2023/9/25
     * @since 1.2.0
     */
    public static <T> T unmarshallWithFeatures(String jsonStr, TypeReference<T> typeReference
            , List<DeserializationFeature> enableFeatures, List<DeserializationFeature> disableFeatures) {
        try {
            if (null == typeReference) {
                throw new IllegalArgumentException("typeReference is null");
            }
            if (StringEmptyCheck.isEmpty(jsonStr)) {
                return null;
            }
            return getUnmarshallMapper(enableFeatures, disableFeatures).readValue(jsonStr, typeReference);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 取反序列化ObjectMapper
     *
     * @param enableFeatures  允许的特性集合，若不需设置则传null
     * @param disableFeatures 禁止的特性集合，若不需设置则传null
     * @return ObjectMapper
     * <AUTHOR>
     * @date 2023/9/25
     * @since 1.2.0
     */
    private static ObjectMapper getUnmarshallMapper(List<DeserializationFeature> enableFeatures, List<DeserializationFeature> disableFeatures) {
        ObjectMapper newObjectMapper = new ObjectMapper();
        newObjectMapper.registerModule(new JavaTimeModule());
        newObjectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        Optional.ofNullable(enableFeatures).orElseGet(ArrayList::new).stream()
                .forEach(s -> newObjectMapper.configure(s, true));
        Optional.ofNullable(disableFeatures).orElseGet(ArrayList::new)
                .stream().forEach(s -> newObjectMapper.configure(s, false));
        return newObjectMapper;
    }
}
