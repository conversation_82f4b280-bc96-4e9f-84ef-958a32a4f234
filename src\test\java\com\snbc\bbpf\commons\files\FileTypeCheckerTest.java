/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.files;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.net.URL;

/**
 * 类描述:    .
 *
 * <AUTHOR>
 * 创建时间:  [2023/9/1 13:54]
 */
class FileTypeCheckerTest {


    /**
     * 尝试从 resource 中读取文件信息
     *
     * @param filePath .
     * @return .
     */

    public static String findFileInputStream(String filePath) {
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        URL resourceUrl = classLoader.getResource(filePath);
        if (resourceUrl != null) {
            return resourceUrl.getPath();
        } else {
            throw new IllegalArgumentException("Resource not found: " + filePath);
        }
    }

    @Test
    @DisplayName("test  File Type For Png")
    @Tags({
            @Tag("@id:56"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/6/4")
    })
    void testIsFileType_testFileForPng() {
        // 测试示例
        String filePath = "files/filetype/1.png"; // 替换为要检查的文件路径


        boolean isMatching = FileTypeChecker.isFileType(findFileInputStream(filePath), FileType.PNG);
        Assertions.assertTrue(isMatching);
    }

    @Test
    @DisplayName("test  FileType for docx")
    @Tags({
            @Tag("@id:56"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/6/4")
    })
    void testIsFileType_testFileForDocx() {
        // 测试示例
        String filePath = "files/filetype/1.docx"; // 替换为要检查的文件路径
        boolean isMatching = FileTypeChecker.isFileType(findFileInputStream(filePath), FileType.DOCX);
        Assertions.assertTrue(isMatching);
    }

    @Test
    @DisplayName("test  File Type  doc ")
    @Tags({
            @Tag("@id:56"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/6/4")
    })
    void testIsFileType_testFileForDoc() {
        // 测试示例
        String filePath = "files/filetype/1.doc"; // 替换为要检查的文件路径
        boolean isMatching = FileTypeChecker.isFileType(findFileInputStream(filePath), FileType.DOC);
        Assertions.assertTrue(isMatching);
    }

    @Test
    @DisplayName("test  File Type   pdf")
    @Tags({
            @Tag("@id:56"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/6/4")
    })
    void testIsFileType_testFileForPdf() {
        // 测试示例
        String filePath = "files/filetype/1.pdf"; // 替换为要检查的文件路径
        boolean isMatching = FileTypeChecker.isFileType(findFileInputStream(filePath), FileType.PDF);
        Assertions.assertTrue(isMatching);
    }

    @Test
    @DisplayName("test  File Type txt ")
    @Tags({
            @Tag("@id:56"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/6/4")
    })
    void testIsFileType_testFileForTxt() {
        // 测试示例
        String filePath = "files/filetype/1.txt"; // 替换为要检查的文件路径
        boolean isMatching = FileTypeChecker.isFileType(findFileInputStream(filePath), FileType.TXT);
        Assertions.assertTrue(isMatching);
    }

    @Test
    @DisplayName("test  File Type  zip ")
    @Tags({
            @Tag("@id:56"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/6/4")
    })
    void testIsFileType_testFileForZip() {
        // 测试示例
        String filePath = "files/filetype/1.zip"; // 替换为要检查的文件路径
        boolean isMatching = FileTypeChecker.isFileType(findFileInputStream(filePath), FileType.ZIP);
        Assertions.assertTrue(isMatching);
    }

    @Test
    @DisplayName("test  File Type  tar.gz ")
    @Tags({
            @Tag("@id:56"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/6/4")
    })
    void testIsFileType_testFileForGz() {
        // 测试示例
        String filePath = "files/filetype/1.tar.gz"; // 替换为要检查的文件路径
        boolean isMatching = FileTypeChecker.isFileType(findFileInputStream(filePath), FileType.GZ);
        Assertions.assertTrue(isMatching);
    }

    @Test
    @DisplayName("test  File Type  rar ")
    @Tags({
            @Tag("@id:56"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/6/4")
    })
    void testIsFileType_testFileForRar() {
        // 测试示例
        String filePath = "files/filetype/1.rar"; // 替换为要检查的文件路径
        boolean isMatching = FileTypeChecker.isFileType(findFileInputStream(filePath), FileType.RAR);
        Assertions.assertTrue(isMatching);
    }

    @Test
    @DisplayName("test  File Type   xls")
    @Tags({
            @Tag("@id:56"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/6/4")
    })
    void testIsFileType_testFileForXls() {
        // 测试示例
        String filePath = "files/filetype/1.xls"; // 替换为要检查的文件路径
        boolean isMatching = FileTypeChecker.isFileType(findFileInputStream(filePath), FileType.XLS);
        Assertions.assertTrue(isMatching);
    }

    @Test
    @DisplayName("test  File Type  xlsx ")
    @Tags({
            @Tag("@id:56"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/6/4")
    })
    void testIsFileType_testFileForXlsx() {
        // 测试示例
        String filePath = "files/filetype/1.xlsx"; // 替换为要检查的文件路径
        boolean isMatching = FileTypeChecker.isFileType(findFileInputStream(filePath), FileType.XLSX);
        Assertions.assertTrue(isMatching);
    }

    @Test
    @DisplayName("test  File Type  ppt ")
    @Tags({
            @Tag("@id:56"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/6/4")
    })
    void testIsFileType_testFileForPpt() {
        // 测试示例
        String filePath = "files/filetype/1.ppt"; // 替换为要检查的文件路径
        boolean isMatching = FileTypeChecker.isFileType(findFileInputStream(filePath), FileType.PPT);
        Assertions.assertTrue(isMatching);
    }

    @Test
    @DisplayName("test  File Type   pptx")
    @Tags({
            @Tag("@id:56"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/6/4")
    })
    void testIsFileType_testFileForPptx() {
        // 测试示例
        String filePath = "files/filetype/1.pptx"; // 替换为要检查的文件路径
        boolean isMatching = FileTypeChecker.isFileType(findFileInputStream(filePath), FileType.PPTX);
        Assertions.assertTrue(isMatching);
    }

    @Test
    @DisplayName("test  File Type   cmd")
    @Tags({
            @Tag("@id:56"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/6/4")
    })
    void testIsFileType_testFileForCmd() {
        // 测试示例
        String filePath = "files/filetype/1.cmd"; // 替换为要检查的文件路径
        boolean isMatching = FileTypeChecker.isFileType(findFileInputStream(filePath), FileType.CMD);
        Assertions.assertTrue(isMatching);
    }

    @Test
    @DisplayName("test  File Type   exe")
    @Tags({
            @Tag("@id:56"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/6/4")
    })
    void testIsFileType_testFileForExe() {
        // 测试示例
        String filePath = "files/filetype/1.exe"; // 替换为要检查的文件路径
        boolean isMatching = FileTypeChecker.isFileType(findFileInputStream(filePath), FileType.EXE);
        Assertions.assertTrue(isMatching);
    }

    @Test
    @DisplayName("test  File Type  apk ")
    @Tags({
            @Tag("@id:56"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/6/4")
    })
    void testIsFileType_testFileForAPK() {
        // 测试示例
        String filePath = "files/filetype/1.apk"; // 替换为要检查的文件路径
        boolean isMatching = FileTypeChecker.isFileType(findFileInputStream(filePath), FileType.APK);
        Assertions.assertTrue(isMatching);
    }

    @Test
    @DisplayName("test  File Type  iso ")
    @Tags({
            @Tag("@id:56"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/6/4")
    })
    void testIsFileType_testFileForISO() {
        // 测试示例
        String filePath = "files/filetype/1.iso"; // 替换为要检查的文件路径
        boolean isMatching = FileTypeChecker.isFileType(findFileInputStream(filePath), FileType.ISO);
        Assertions.assertTrue(isMatching);
    }

    @Test
    @DisplayName("test  File Type  gif ")
    @Tags({
            @Tag("@id:56"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/6/4")
    })
    void testIsFileType_testFileForGIF() {
        // 测试示例
        String filePath = "files/filetype/1.gif"; // 替换为要检查的文件路径
        boolean isMatching = FileTypeChecker.isFileType(findFileInputStream(filePath), FileType.GIF);
        Assertions.assertTrue(isMatching);
    }

    @Test
    @DisplayName("test  File Type   wav")
    @Tags({
            @Tag("@id:56"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/6/4")
    })
    void testIsFileType_testFileForWAV() {
        // 测试示例
        String filePath = "files/filetype/1.wav"; // 替换为要检查的文件路径
        boolean isMatching = FileTypeChecker.isFileType(findFileInputStream(filePath), FileType.WAV);
        Assertions.assertTrue(isMatching);
    }

    @Test
    @DisplayName("test  File Type  mp3 ")
    @Tags({
            @Tag("@id:56"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/6/4")
    })
    void testIsFileType_testFileForMP3() {
        // 测试示例
        String filePath = "files/filetype/1.mp3"; // 替换为要检查的文件路径
        boolean isMatching = FileTypeChecker.isFileType(findFileInputStream(filePath), FileType.MP3);
        Assertions.assertTrue(isMatching);
    }

    @Test
    @DisplayName("test  File Type  mp4 ")
    @Tags({
            @Tag("@id:56"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/6/4")
    })
    void testIsFileType_testFileForMP4() {
        // 测试示例
        String filePath = "files/filetype/1.mp4"; // 替换为要检查的文件路径
        boolean isMatching = FileTypeChecker.isFileType(findFileInputStream(filePath), FileType.MP4);
        Assertions.assertTrue(isMatching);
    }

    @Test
    @DisplayName("test  File Type  amr ")
    @Tags({
            @Tag("@id:56"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/6/4")
    })
    void testIsFileType_testFileForAMR() {
        // 测试示例
        String filePath = "files/filetype/1.amr"; // 替换为要检查的文件路径
        boolean isMatching = FileTypeChecker.isFileType(findFileInputStream(filePath), FileType.AMR);
        Assertions.assertTrue(isMatching);
    }

    @Test
    @DisplayName("test  File Type  avi ")
    @Tags({
            @Tag("@id:56"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/6/4")
    })
    void testIsFileType_testFileForAVI() {
        // 测试示例
        String filePath = "files/filetype/1.avi"; // 替换为要检查的文件路径
        boolean isMatching = FileTypeChecker.isFileType(findFileInputStream(filePath), FileType.AVI);
        Assertions.assertTrue(isMatching);
    }
}
