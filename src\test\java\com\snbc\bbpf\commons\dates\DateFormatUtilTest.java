/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.dates;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.assertEquals;


/**
 * 将Date、LocalDate、LocalDateTime、ZonedDateTime和指定格式字符串进行互相转换 单元测试类
 *
 * <AUTHOR>
 * @module 日期处理
 * @date 2023/4/27 2:34 下午
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */

class DateFormatUtilTest {

    @DisplayName("将Date以指定格式格式化为字符串")
    @Tags({
            @Tag("@id:21"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/4/27"),
    })
    @Test
    void testFormat_date() throws ParseException {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateStr = "2023-04-27 14:45:26";
        Date date = df.parse(dateStr);
        final String expect = DateFormatUtil.format(date, DateFormatUtil.DATETIME_FORMATTER);
        assertEquals(dateStr, expect);
    }

    @DisplayName("将LocalDate以指定格式格式化为字符串")
    @Tags({
            @Tag("@id:21"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/4/27"),
    })
    @Test
    void testFormat_localDate() {
        String dateStrWithMinus = "2023-04-27";
        String dateStrWithZh = "2023年04月27日";
        String dateStrSimple = "20230427";
        final LocalDate localDate = LocalDate.of(2023, 4, 27);
        final String expectWithMinus = DateFormatUtil.format(localDate, DateFormatUtil.DATE_FORMATTER);
        final String expectWithZh = DateFormatUtil.format(localDate, DateFormatUtil.CHINESE_DATE_FORMATTER);
        final String expectSimple = DateFormatUtil.format(localDate, DateFormatUtil.PURE_DATE_FORMATTER);

        assertEquals(dateStrWithMinus, expectWithMinus);
        assertEquals(dateStrWithZh, expectWithZh);
        assertEquals(dateStrSimple, expectSimple);
    }

    @DisplayName("将LocalTime以指定格式格式化为字符串")
    @Tags({
            @Tag("@id:21"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/4/27"),
    })
    @Test
    void testFormat_localTime() {
        String dateStr = "14:45:26";
        String dateStrSimple = "144526";
        final LocalTime localTime = LocalTime.of(14, 45, 26);
        final String expect = DateFormatUtil.format(localTime, DateFormatUtil.TIME_FORMATTER);
        final String expectSimple = DateFormatUtil.format(localTime, DateFormatUtil.PURE_TIME_FORMATTER);
        assertEquals(dateStr, expect);
        assertEquals(dateStrSimple, expectSimple);
    }

    @DisplayName("将LocalDateTime以指定格式格式化为字符串")
    @Tags({
            @Tag("@id:21"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/4/27"),
    })
    @Test
    void testFormat_localDateTime() {
        String dateStr = "2023年04月27日14时45分26秒";
        String dateStrWithoutSecond = "2023-04-27 14:45";
        String dateStrSimpleWithoutSecond = "202304271445";
        String dateStrUTC = "2023-04-27T14:45:26.027Z";
        String dateStrAcount = "2023/4/27 14:45";
        String dateSimpleFull = "**************027";

        final LocalDateTime localDateTime = LocalDateTime.of(2023, 4, 27, 14, 45, 26, 27000000);
        final String expect = DateFormatUtil.format(localDateTime, DateFormatUtil.CHINESE_DATE_TIME_FORMATTER);
        final String expectWithoutSecond = DateFormatUtil.format(localDateTime, DateFormatUtil.DATETIME_MINUTE_FORMATTER);
        final String expectSimpleWithoutSecond = DateFormatUtil.format(localDateTime, DateFormatUtil.PURE_DATETIME_NO_SECOND_FORMATTER);
        final String expectUTC = DateFormatUtil.format(localDateTime, DateFormatUtil.UTC_MS_FORMATTER);
        final String expectAcount = DateFormatUtil.format(localDateTime, DateFormatUtil.ACCOUNTANT_TIME_FORMATTER);
        final String expectSimpleFull = DateFormatUtil.format(localDateTime, DateFormatUtil.PURE_DATETIME_MS_FORMATTER);
        assertEquals(dateStr, expect);
        assertEquals(dateStrWithoutSecond, expectWithoutSecond);
        assertEquals(dateStrSimpleWithoutSecond, expectSimpleWithoutSecond);
        assertEquals(dateStrUTC, expectUTC);
        assertEquals(dateStrAcount, expectAcount);
        assertEquals(dateSimpleFull, expectSimpleFull);
    }

    @DisplayName("将ZonedDateTime以指定格式格式化为字符串")
    @Tags({
            @Tag("@id:21"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/4/27"),
    })
    @Test
    void testFormat_zonedDateTime() {
        String dateStr = "**************";
        final LocalDateTime localDateTime = LocalDateTime.of(2023, 4, 27, 14, 45, 26);
        final ZonedDateTime zonedDateTime = ZonedDateTime.of(localDateTime, ZoneId.systemDefault());
        final String expect = DateFormatUtil.format(zonedDateTime, DateFormatUtil.PURE_DATETIME_FORMATTER);
        assertEquals(dateStr, expect);
    }

    @DisplayName("将字符串以指定格式格式化为LocalDateTime")
    @Tags({
            @Tag("@id:21"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/4/27"),
    })
    @Test
    void testParseToLocalDateTime() {
        String dateStr = "2023-04-27 14:45:26.666";

        final LocalDateTime toLocalDateTime = DateFormatUtil.parseToLocalDateTime(dateStr, DateFormatUtil.DATETIME_MS_FORMATTER);
        final LocalDateTime localDateTime = LocalDateTime.of(2023, 4, 27, 14, 45, 26, 666000000);
        assertEquals(localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli(), toLocalDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
    }

    @DisplayName("将字符串以指定格式格式化为LocalDate")
    @Tags({
            @Tag("@id:21"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/4/27"),
    })
    @Test
    void testParseToLocalDate() {
        String dateStr = "2023-04-27 14:45:26";
        final LocalDate localDate = DateFormatUtil.parseToLocalDate(dateStr, DateFormatUtil.DATETIME_FORMATTER);
        final LocalDateTime localDateTime = LocalDateTime.of(2023, 4, 27, 0, 0, 0);

        assertEquals(localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli(), localDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli());
    }


    @DisplayName("将字符串以指定格式格式化为ZonedDateTime")
    @Tags({
            @Tag("@id:21"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/4/27"),
    }
    )
    @Test
    void testParseToZonedDateTime() {
        String dateStr = "2023-04-27 14:45:26";
        final ZonedDateTime zonedDateTime = DateFormatUtil.parseToZonedDateTime(dateStr, DateFormatUtil.DATETIME_FORMATTER);
        final LocalDateTime localDateTime = LocalDateTime.of(2023, 4, 27, 14, 45, 26);
        assertEquals(localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli(), zonedDateTime.toInstant().toEpochMilli());
    }

    @Test
    @DisplayName("将字符串（年月日时分秒）以指定格式格式化为java.util下Date")
    @Tags({
            @Tag("@id:21"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/4/27"),
    }
    )
    void testParseToDate_with_time() {
        String dateStr = "2023-04-27 14:45:26";
        Date exppectDate = DateFormatUtil.parseToDate(dateStr, DateFormatUtil.DATETIME_FORMATTER);
        LocalDateTime localDateTime = LocalDateTime.of(2023, 4, 27, 14, 45, 26);
        assertEquals(localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli(), exppectDate.toInstant().toEpochMilli());

        dateStr = "2023-04-27 14:45";
        localDateTime = LocalDateTime.of(2023, 4, 27, 14, 45, 0);
        exppectDate = DateFormatUtil.parseToDate(dateStr, DateFormatUtil.DATETIME_MINUTE_FORMATTER);
        assertEquals(localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli(), exppectDate.toInstant().toEpochMilli());
        dateStr = "2023年04月27日14时45分26秒";
        localDateTime = LocalDateTime.of(2023, 4, 27, 14, 45, 26);
        exppectDate = DateFormatUtil.parseToDate(dateStr, DateFormatUtil.CHINESE_DATE_TIME_FORMATTER);
        assertEquals(localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli(), exppectDate.toInstant().toEpochMilli());

    }

    @Test
    @DisplayName("将字符串(年月日）以指定格式格式化为java.util下Date")
    @Tags({
            @Tag("@id:21"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/4/27"),
    }
    )
    void testParseToDate() {
        String dateStr = "20230427";
        Date exppectDate = DateFormatUtil.parseToDate(dateStr, DateFormatUtil.PURE_DATE_FORMATTER);
        LocalDateTime localDateTime = LocalDateTime.of(2023, 4, 27, 0, 0, 0);
        assertEquals(localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli(), exppectDate.toInstant().toEpochMilli());
        dateStr = "2023-04-27";
        exppectDate = DateFormatUtil.parseToDate(dateStr, DateFormatUtil.DATE_FORMATTER);
        assertEquals(localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli(), exppectDate.toInstant().toEpochMilli());

        dateStr = "2023年04月27日";
        exppectDate = DateFormatUtil.parseToDate(dateStr, DateFormatUtil.CHINESE_DATE_FORMATTER);
        assertEquals(localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli(), exppectDate.toInstant().toEpochMilli());
    }


    @DisplayName("将字符串以指定格式格式化为java.util下Date,入参为null")
    @Tags({
            @Tag("@id:21"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/4/27"),
    }
    )
    @Test()
    void testParseToDate_when_date_null() {
        NullPointerException thrown = Assertions.assertThrows(NullPointerException.class, () -> {
            DateFormatUtil.parseToDate(null, DateFormatUtil.DATETIME_FORMATTER);
        });
        Assertions.assertEquals("dateStr is null", thrown.getMessage());
    }
}