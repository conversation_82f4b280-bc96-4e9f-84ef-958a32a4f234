package com.snbc.bbpf.commons.files;

import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 文件工具类单元测试类
 *
 * <AUTHOR>
 * @module 文件处理模块
 * @date 2023/9/25 20:30
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class FileUtilsTest {

    /**
     * 文件内容
     */
    private static String fileContext = "file test context";
    private static String fileContext2 = "file test context2";

    /**
     * 文件名
     */
    private static String fileName = "./test_fileUtils.txt";
    private static String path1 = "./path1";
    private static String path2 = "./path1/path2";
    private static String filea = "./path1/a.txt";
    private static String fileb = "./path1/path2/b.txt";
    private static String filec = "./path1/path2/c.txt";
    private static String path3 = "./path1/path3";
    private static String movePath = "./path1/path4/";
    private static String movePathTarget = "./path1/path5/";
    private static String movePathFileSource = "./path1/path4/a.txt";
    private static String movePathA = "./path1/path6";
    private static String movePathB = "./path1/path7";
    private static String movePathBFileA = "./path1/path7/movea.txt";
    private static String movePathBFileB = "./path1/path7/moveb.txt";
    private static String pathRenameFile = "./path1/reanme.txt";
    private static String pathRenameDistory = "./path1/path8";

    @BeforeAll
    @DisplayName("创建文件")
    @Tags({
            @Tag("@id:96"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/6/3")
    })
    static void creatFile() throws IOException {
        new File(path2).mkdirs();
        new File(filea).createNewFile();
        new File(fileb).createNewFile();
        new File(filec).createNewFile();
        new File(pathRenameFile).createNewFile();
        new File(pathRenameDistory).mkdirs();
        new File(path3).mkdirs();
        new File(movePath).mkdirs();
        new File(movePathTarget).mkdirs();
        new File(movePathFileSource).createNewFile();
        new File(movePathA).mkdirs();
        new File(movePathB).mkdirs();
        new File(movePathBFileA).createNewFile();
        new File(movePathBFileB).createNewFile();
    }

    @AfterAll
    @DisplayName("删除测试文件")
    @Tags({
            @Tag("@id:96"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/6/3")
    })
    static void afterAll_deleteFile() {
        FileUtils.delete(path1);
    }

    @BeforeEach
    @DisplayName("创建并写入内容，方便后续测试")
    @Tags({
            @Tag("@id:60"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/9/25")
    })
    void init() {
        FileUtils.write(fileName, fileContext);
        assertTrue(new File(fileName).exists());
    }

    @Test
    @DisplayName("读取文件内容")
    @Tags({
            @Tag("@id:60"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/9/25")
    })
    void testReadString_readFile() {
        String s = FileUtils.readString(fileName);
        assertEquals(fileContext, s);
    }

    @Test
    @DisplayName("追加文件内容")
    @Tags({
            @Tag("@id:60"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/9/25")
    })
    void testWriteAppend_writeFile() {
        FileUtils.writeAppend(fileName, fileContext2);
        String s = FileUtils.readString(fileName);
        assertEquals(fileContext.concat(fileContext2), s);
    }

    @Test
    @DisplayName("查找文件")
    @Tags({
            @Tag("@id:60"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/9/25")
    })
    void testFilter_findFile() {
        List<Path> filter = FileUtils.filter("./", path -> path.getFileName().toString().equals("test_fileUtils.txt"));
        assertEquals(1, filter.size());
    }

    @Test
    @DisplayName("文件路径不对，查找不到文件")
    @Tags({
            @Tag("@id:60"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/9/25")
    })
    void testFilter_pathErrNotFindFile() {
        assertThrows(RuntimeException.class, () -> {
            FileUtils.filter(fileName.concat("1"), path -> true);
        });
    }

    @Test
    @DisplayName("文件路径对，查找不到文件")
    @Tags({
            @Tag("@id:60"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/9/25")
    })
    void testFilter_notFindFile() {
        List<Path> filter = FileUtils.filter("./", path -> path.getFileName().toString().equals(fileName + 1));
        assertEquals(0, filter.size());
    }

    @Test
    @DisplayName("文件路径对，无条件")
    @Tags({
            @Tag("@id:60"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/9/25")
    })
    void testFilter_emptyFilter() {
        List<Path> filter = FileUtils.filter("./", null);
        assertTrue(filter.size()>=0);
    }

    @Test
    @DisplayName("创建文件失败，向不存在的文件中写入内容")
    @Tags({
            @Tag("@id:60"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/9/25")
    })
    void testWrite_deleteFileAfterWrite() {
        FileUtils.delete("./", path -> path.getFileName().toString().equals("test_fileUtils.txt"));
        assertThrows(RuntimeException.class, () -> {
            FileUtils.write(fileName, fileContext, false, false);
        });
    }


    @Test
    @DisplayName("测试读写文件字符串集合")
    @Tags({
            @Tag("@id:75"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/11/01")
    })
    void testWirteFileAndReadFileLines() {
        List<String> lines = new ArrayList<>();
        lines.add("abc");
        lines.add("123");
        lines.add("A1c");
        FileUtils.writeLines(new File(fileName), lines);
        List<String> readLines = FileUtils.readLines(new File(fileName));
        assertEquals(lines, readLines);
    }

    @AfterEach
    @DisplayName("删除文件")
    @Tags({
            @Tag("@id:60"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/9/25")
    })
    void teardown() {
        if (new File(fileName).exists()) {
            FileUtils.delete(fileName);
            assertFalse(new File(fileName).exists());
        }
    }

    @Test
    @DisplayName("目录下没有文件")
    @Tags({
            @Tag("@id:96"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/6/3")
    })
    void testCountFiles_onlyPath() {
        assertEquals(0, FileUtils.countFiles(path3));
    }

    @Test
    @DisplayName("目录下有文件，不统计子目录")
    @Tags({
            @Tag("@id:96"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/6/3")
    })
    void testCountFiles_haveFiles_zeroDepth() {
        assertEquals(1, FileUtils.countFiles(path1, 0));
    }

    @Test
    @DisplayName("目录下有文件，深度为负数")
    @Tags({
            @Tag("@id:96"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/6/3")
    })
    void testCountFiles_haveFiles_negativeDepth() {

        Exception e = assertThrows(IllegalArgumentException.class,
                () -> FileUtils.countFiles(path1, -1));
        assertEquals("'maxDepth' is negative", e.getMessage());
    }


    @Test
    @DisplayName("目录下有文件并统计子目录")
    @Tags({
            @Tag("@id:96"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/6/3")
    })
    void testCountFiles_haveFiles_maxDepth() {
        assertEquals(7, FileUtils.countFiles(path1));
    }

    @Test
    @DisplayName("路径为文件，文件个数为1")
    @Tags({
            @Tag("@id:96"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/6/3")
    })
    void testCountFiles_onlyFile() {
        assertEquals(1, FileUtils.countFiles(filea));
    }

    @Test
    @DisplayName("错误的路径，抛出异常")
    @Tags({
            @Tag("@id:96"),
            @Tag("@author:taoxin"),
            @Tag("@date:2024/6/3")
    })
    void testCountFiles_errorPath() {
        assertThrows(RuntimeException.class,
                () -> FileUtils.countFiles("11/22"));
    }

    @Test
    @DisplayName("移动文件")
    @Tags({
            @Tag("@id:97"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/6/11")
    })
    void move_file() {
        try {
            FileUtils.move(movePathFileSource,movePathTarget);
            File file = new File(movePathTarget+"a.txt");
            assertTrue(file.exists());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    @DisplayName("移动文件,移动文件不存在")
    @Tags({
            @Tag("@id:97"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/6/11")
    })
    void move_fileNotExists() {
       assertThrows(IOException.class, ()->FileUtils.move(movePathFileSource+"1111",movePathTarget));
    }

    @Test
    @DisplayName("移动目录")
    @Tags({
            @Tag("@id:97"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/6/11")
    })
    void move_mkdir() {
        try {
            FileUtils.move(movePathA,movePath);
            File file = new File(movePath+"path6");
            assertTrue(file.exists());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    @DisplayName("移动目录的同时将目录展开")
    @Tags({
            @Tag("@id:97"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/6/11")
    })
    void move_mkdirUnwind() {
        try {
            FileUtils.move(movePathB,movePath,false,true);
            File file = new File(movePath+"path7");
            assertFalse(file.exists());
            File movea = new File(movePath+"movea.txt");
            File moveb = new File(movePath+"moveb.txt");
            assertTrue(movea.exists());
            assertTrue(moveb.exists());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    @DisplayName("重命名，不存在的文件")
    @Tags({
            @Tag("@id:98"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/6/17")
    })
    void testRename_noexitFile() {
        assertThrows(IllegalArgumentException.class,
                () -> FileUtils.rename("nofile.txt",""));
    }

    @Test
    @DisplayName("重命名，不存在的目录")
    @Tags({
            @Tag("@id:98"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/6/17")
    })
    void testRename_noexitDistory() {
        assertThrows(IllegalArgumentException.class,
                () -> FileUtils.rename("nodistory",""));
    }

    @Test
    @DisplayName("重命名，重命名为空")
    @Tags({
            @Tag("@id:98"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/6/17")
    })
    void testRename_nameNull() {
        assertThrows(IllegalArgumentException.class,
                () -> FileUtils.rename(pathRenameFile,""));
    }

    @Test
    @DisplayName("重命名文件或目录，成功")
    @Tags({
            @Tag("@id:98"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/6/17")
    })
    void testRename_success() {
        assertTrue(FileUtils.rename(pathRenameFile, "rename.txt"));
        assertTrue(FileUtils.rename(pathRenameDistory, "renamePath"));
    }
}
