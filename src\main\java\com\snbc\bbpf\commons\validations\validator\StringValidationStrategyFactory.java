/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.validations.validator;

import com.snbc.bbpf.commons.validations.annotation.ValidStringType;

import java.util.EnumMap;
import java.util.Map;

/**
 * 字符串验证策略工厂
 *
 * <AUTHOR>
 * @module 字符串校验模块
 * @date 2023/7/16
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class StringValidationStrategyFactory {
    private static final Map<ValidStringType, IStringValidator> validators = new EnumMap<>(ValidStringType.class);

    static {
        validators.put(ValidStringType.IDENTITY_CARD, new IdentityCardValidator());
        validators.put(ValidStringType.EMAIL, new EmailValidator());
        validators.put(ValidStringType.CHINESE_CHARACTER, new ChineseCharacterValidator());
        validators.put(ValidStringType.BANK_CARD, new BankCardValidator());
        validators.put(ValidStringType.IP_ADDRESS, new IpAddressValidator());
        validators.put(ValidStringType.PHONE_NUMBER, new PhoneNumberValidator());
        validators.put(ValidStringType.URL, new UrlValidator());
    }

    /**
     * 根据策略名称获取对应的验证器
     *
     * @param validStringType 策略名称
     * @return IStringValidator 验证器
     * <AUTHOR>
     * @date 2023/7/16
     * @since 1.1.0
     */
    public static IStringValidator getValidator(ValidStringType validStringType) {
        return validators.get(validStringType);
    }
}
