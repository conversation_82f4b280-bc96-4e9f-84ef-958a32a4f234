package com.snbc.bbpf.commons.objs;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

/**
 * TODO
 * TODO
 *
 * <AUTHOR>
 * @module TODO
 * @date 2023/7/28 13:33
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class IConvertTest {


    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("user对象转换为userVO对象")
    @Tags({
            @Tag("@id:31"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/7/28")
    })
    void testTo(){
        User user = new User();
        user.setId("111");
        user.setName("222");

        UserVO vo = (UserVO)UserCover.INSTANCE.to(user);
        assertEquals(user.getId(),vo.getId());
        assertEquals(user.getName(),vo.getName());
    }

    @Test
    @DisplayName("userVO转换成user")
    @Tags({
            @Tag("@id:31"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/7/28")
    })
    void testFrom(){
        UserVO userVO = new UserVO();
        userVO.setId("111");
        userVO.setName("222");

        User user = (User)UserCover.INSTANCE.from(userVO);
        assertEquals(userVO.getId(),user.getId());
        assertEquals(userVO.getName(),user.getName());
    }

    @Test
    @DisplayName("map转换为user对象")
    @Tags({
            @Tag("@id:29"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2023/7/28")
    })
    void testFromMap(){
        Map map = new HashMap<String ,String>();
        map.put("id","111");
        map.put("name","222");
        User user = (User)UserCoverMap.INSTANCE.from(map);
        assertEquals("111",user.getId());
        assertEquals("222",user.getName());
    }

}
@Mapper
interface UserCover extends IConvert<User, UserVO> {
    IConvert INSTANCE = Mappers.getMapper(UserCover.class);
}
@Mapper
interface UserCoverMap extends IConvert<User, HashMap<String,String>> {
    IConvert INSTANCE = Mappers.getMapper(UserCoverMap.class);
}
class User{
    String id;
    String name;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}

class UserVO{
    String id;
    String name;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}