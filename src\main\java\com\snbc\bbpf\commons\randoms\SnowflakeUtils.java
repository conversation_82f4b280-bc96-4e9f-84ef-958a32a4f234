/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.randoms;


import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.util.Date;

/**
 * # 雪花算法
 * # 在集群环境中，分布环境中，生成唯一有序全局id，
 * # 可以跨机房使用
 * # 需求：为所有的数据库中的数据表生成唯一id，
 * 因为uuid作为数据库索引存在，性能，可用性，存储空间，无序等问题
 * 所以项目需要一个有序的，全局唯一的id生产算法，
 * 再通过自定义mybaties的插件在插入的时候自动生成主键id。
 * 19位雪花算法的更能保证唯一性，在使用低于19位的雪花id时需要注意唯一性。
 * <p>
 * 使用方法： 工具类提供快速创建雪花id的方法
 * 获取1-18位雪花id的方法
 * SnowflakeUtils.genId(final int count)
 * 获取雪花id（19位）
 * SnowflakeUtils.snowflakeId();
 * 在跨机房等情况下可以主动构造雪花算法工具类。
 * 针对19位雪花id可以有一些扩展的用法，比如获取创建时间，数据中心id，workid等
 * 用法：需要先构造雪花算法工具类
 * SnowflakeUtils snowflakeUtils = new SnowflakeUtils(1, 2);
 * 获取雪花id（19位）的方法如下
 * snowflakeUtils.nextId()
 * 获取数据中心id
 * SnowflakeUtils.getDataCenterId(SnowflakeUtils.snowflakeId());
 * 获取workid
 * SnowflakeUtils.getWorkerId(SnowflakeUtils.snowflakeId());
 * 获取创建时间
 * snowflakeUtils.getGenerateDateTime(SnowflakeUtils.snowflakeId());
 *
 * <AUTHOR>
 * @module 随机模块
 * @date 2023/6/13 15:02 下午
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */

public final class SnowflakeUtils {

    /**
     * 默认的起始时间，为Thu, 04 Nov 2010 01:42:54 GMT
     */
    public static final long DEFAULT_TWEPOCH = 1_288_834_974_657L;
    /**
     * 默认回拨时间，2S
     */
    public static final long DEFAULT_TIME_OFFSET = 2000L;
    public static final int DATA_CENTER_ID = 2;
    /**
     * 时间部分所占长度
     */
    private static final int TIME_LEN = 41;
    /**
     * 数据中心id所占长度
     */
    private static final int DATA_LEN = 5;
    /**
     * 机器id所占长度
     */
    private static final int WORK_LEN = 5;
    /**
     * 毫秒内序列所占长度
     */
    private static final int SEQ_LEN = 12;
    /**
     * 定义起始时间 2019-11-26 15:49:00
     */
    private static final long START_TIME = 1_574_754_591_041L;
    /**
     * 时间部分向左移动的位数 22
     */
    private static final int TIME_LEFT_BIT = 64 - 1 - TIME_LEN;
    /**
     * 数据中心id左移位数 17
     */
    private static final int DATA_LEFT_BIT = TIME_LEFT_BIT - DATA_LEN;
    /**
     * 机器id左移位数 12
     */
    private static final int WORK_LEFT_BIT = DATA_LEFT_BIT - WORK_LEN;
    /**
     * 数据中心id最大值 31
     */
    private static final int DATA_MAX_NUM = (~(-1 << DATA_LEN));
    /**
     * 自动获取数据中心id（可以手动定义 0-31之间的数）
     */
    private static final long DATA_ID = getDataId();
    /**
     * 机器id最大值 31
     */
    private static final int WORK_MAX_NUM = (~(-1 << WORK_LEN));
    /**
     * 自动机器id（可以手动定义 0-31之间的数）
     */
    private static final long WORK_ID = getWorkId();
    private static final int ZERO = 0;
    private static final int ONE = 1;
    private static final int MAX_LENGTH_18 = 18;
    /**
     * 毫秒内序列的最大值 4095
     */
    private static final long SEQ_MAX_NUM = ~(-1 << SEQ_LEN);
    private static final long serialVersionUID = 1L;
    private static final long WORKER_ID_BITS = 5L;
    // 最大支持机器节点数0~31，一共32个
    @SuppressWarnings({"PointlessBitwiseExpression", "FieldCanBeLocal"})
    private static final long MAX_WORKER_ID = -1L ^ (-1L << WORKER_ID_BITS);
    private static final long DATA_CENTER_ID_BITS = 5L;
    // 最大支持数据中心节点数0~31，一共32个
    @SuppressWarnings({"PointlessBitwiseExpression", "FieldCanBeLocal"})
    private static final long MAX_DATA_CENTER_ID = -1L ^ (-1L << DATA_CENTER_ID_BITS);
    // 序列号12位（表示只允许workId的范围为：0-4095）
    private static final long SEQUENCE_BITS = 12L;
    // 机器节点左移12位
    private static final long WORKER_ID_SHIFT = SEQUENCE_BITS;
    // 数据中心节点左移17位
    private static final long DATA_CENTER_ID_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS;
    // 时间毫秒数左移22位
    private static final long TIMESTAMP_LEFT_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS + DATA_CENTER_ID_BITS;
    // 序列掩码，用于限定序列最大值不能超过4095
    @SuppressWarnings("FieldCanBeLocal")
    private static final long SEQUENCE_MASK = ~(-1L << SEQUENCE_BITS);// 4095
    /**
     * 上次生成ID的时间截
     */
    private static long lastTimeStamp = -1L;
    /**
     * 上一次的毫秒内序列值
     */
    private static long lastSeq = ZERO;
    private final long twepoch;
    private final long workerId;
    private final long dataCenterId;
    private final boolean useSystemClock;
    // 允许的时钟回拨数
    private final long timeOffset;

    private long sequence;
    private long aLong = -1L;


    public SnowflakeUtils() {
        this(1, 1);
    }

    /**
     * 构造
     *
     * @param workerId 终端ID
     */
    public SnowflakeUtils(long workerId) {
        this(workerId, DATA_CENTER_ID);
    }


    /**
     * 构造
     *
     * @param workerId     终端ID
     * @param dataCenterId 数据中心ID
     * <AUTHOR>
     * @date 2024-08-06
     * @since 1.5.0

     */
    public SnowflakeUtils(long workerId, long dataCenterId) {
        this(workerId, dataCenterId, false);
    }

    /**
     * 构造
     *
     * @param workerId         终端ID
     * @param dataCenterId     数据中心ID
     * @param isUseSystemClock 是否使用{@link SystemClock} 获取当前时间戳
     * <AUTHOR>
     * @date 2024-08-06
     * @since 1.5.0
     */
    public SnowflakeUtils(long workerId, long dataCenterId, boolean isUseSystemClock) {
        this(null, workerId, dataCenterId, isUseSystemClock);
    }

    /**
     * @param epochDate        初始化时间起点（null表示默认起始日期）,后期修改会导致id重复,如果要修改连workerId dataCenterId，慎用
     * @param workerId         工作机器节点id
     * @param dataCenterId     数据中心id
     * @param isUseSystemClock 是否使用{@link SystemClock} 获取当前时间戳
     * <AUTHOR>
     * @date 2024-08-06
     * @since 1.5.0
     */
    public SnowflakeUtils(Date epochDate, long workerId, long dataCenterId, boolean isUseSystemClock) {
        this(epochDate, workerId, dataCenterId, isUseSystemClock, DEFAULT_TIME_OFFSET);
    }


    /**
     * @param epochDate        初始化时间起点（null表示默认起始日期）,后期修改会导致id重复,如果要修改连workerId dataCenterId，慎用
     * @param workerId         工作机器节点id
     * @param dataCenterId     数据中心id
     * @param isUseSystemClock 是否使用{@link SystemClock} 获取当前时间戳
     * @param timeOffset       允许时间回拨的毫秒数
     * <AUTHOR>
     * @date 2024-08-06
     * @since 1.5.0
     */
    public SnowflakeUtils(Date epochDate, long workerId, long dataCenterId, boolean isUseSystemClock, long timeOffset) {
        if (null != epochDate) {
            this.twepoch = epochDate.getTime();
        } else {
            this.twepoch = DEFAULT_TWEPOCH;
        }
        if (workerId > MAX_WORKER_ID || workerId < 0) {
            throw new IllegalArgumentException(String.format("worker Id can't be greater than  %s or less than 0",
                    MAX_WORKER_ID));
        }
        if (dataCenterId > MAX_DATA_CENTER_ID || dataCenterId < 0) {
            throw new IllegalArgumentException(String.format("datacenter Id can't be greater than  %s or less than 0",
                    MAX_DATA_CENTER_ID));
        }
        this.workerId = workerId;
        this.dataCenterId = dataCenterId;
        this.useSystemClock = isUseSystemClock;
        this.timeOffset = timeOffset;
    }

    /**
     * 获得下一个ID (该方法是线程安全的)
     * <AUTHOR>
     * @date 2024-08-06
     * @since 1.5.0
     * @return 雪花算法ID
     */
    public static Long genId() {
        int dataId = getDataId();
        int workId = getWorkId();
        return genId(MAX_LENGTH_18, dataId, workId);
    }

    /**
     * # 指定长度的雪花算法
     *
     * <p>
     * 使用注意
     * 默认使用数据中心 采用主机名称/ip地址，最大31
     * 默认wordId ，采用的ip地址计算
     * # 使用注意，如果长度制定的小于18位，不能保证生成的数值不重复
     * # 最大位数为18位
     * <p>例如</p>
     * <p>input 0 result: </p>
     * <p>input 12 result: 469296384862</p>
     * <p>input 18 result: 469296384862</p>
     * <p>input 20 IllegalArgumentException: Requested random string length 20 is more than 18.</p>
     * <p>input -1 IllegalArgumentException: Requested random string length -1  is less than 0.</p>
     * </p>
     *
     * @param count 指定的位数
     * @return String 雪花算法随机字符串
     * @throws
     * <AUTHOR>
     * @date 2023/6/13
     * @since 1.1.0
     */
    public static Long genId(final int count) {
        int dataId = getDataId();
        int workId = getWorkId();
        return genId(count, dataId, workId);
    }

    /**
     * # 指定长度，指定wordId雪花算法
     *
     * @param count  指定的位数
     * @param workId 指定workdId 最大31
     * @return String 雪花算法随机字符串
     * @throws
     * <AUTHOR>
     * @date 2023/6/13
     * @since 1.1.0
     */
    public static Long genIdByCustomerWorkId(final int count, int workId) {
        int dataId = getDataId();
        return genId(count, dataId, workId);
    }

    /**
     * # 指定长度，制定wordId雪花算法
     *
     * @param count  指定的位数
     * @param dataId 指定dataId 最大31
     * @return String 雪花算法随机字符串
     * @throws
     * <AUTHOR>
     * @date 2023/6/13
     * @since 1.1.0
     */
    public static Long genIdByCustomerDataId(final int count, int dataId) {
        int workId = getWorkId();
        return genId(count, dataId, workId);
    }

    /**
     * 指定长度的雪花算法
     * 自定义 dataId
     *
     * @param count  指定位数
     * @param dataId 数据中心id
     * @param workId workId
     * @return 雪花字符串
     * @throws
     * <AUTHOR>
     * @date 2023/6/13
     * @since 1.1.0
     */
    public static Long genId(final int count, int dataId, int workId) {
        Long x = checkLength(count);
        if (x != null) {
            return x;
        }
        if (dataId > DATA_MAX_NUM || dataId < 1) {
            throw new IllegalArgumentException("dataId区间在1～31位");
        }
        if (workId > WORK_MAX_NUM || workId < 1) {
            throw new IllegalArgumentException("workId区间在1～31位");
        }
        synchronized (SnowflakeUtils.class) {
            long now = System.currentTimeMillis();

            // 如果当前时间小于上一次ID生成的时间戳，说明系统时钟回退过这个时候应当抛出异常
            if (now < lastTimeStamp) {
                throw new RuntimeException(String.format("System Time Error！ %d ms injet preduce ID！",
                        START_TIME - now));
            }

            if (now == lastTimeStamp) {
                lastSeq = ((lastSeq + ONE) & SEQ_MAX_NUM);
                if (lastSeq == ZERO) {
                    now = nextMillis(lastTimeStamp);
                }
            } else {
                lastSeq = ZERO;
            }

            // 上次生成ID的时间截
            lastTimeStamp = now;

            long resultLong =
                    ((now - START_TIME) << TIME_LEFT_BIT) | (DATA_ID << DATA_LEFT_BIT) | (WORK_ID << WORK_LEFT_BIT) | lastSeq;
            String result = Long.toString(resultLong);
            if (result.length() < count) {
                result = result.concat(RandomNumberUtils.randomNumeric(count - result.length()));
            }
            if (result.length() > count) {
                result = result.substring(0, count);
            }
            return Long.parseLong(result);
        }
    }

    /**
     * 检查位数是否在0-18之间
     *
     * @param count 指定位数
     * @return java.lang.Long
     * @throws IllegalArgumentException
     * <AUTHOR>
     * @date 2023/6/13
     * @since 1.1.0
     */
    private static Long checkLength(int count) {
        if (count == 0) {
            return 0L;
        }
        if (count < 0) {
            throw new IllegalArgumentException("Requested random string length " + count + " is less than 0.");
        }
        if (count > MAX_LENGTH_18) {
            throw new IllegalArgumentException("Requested random string length " + count + " is more than 18.");
        }
        return null;
    }

    /**
     * 获取下一不同毫秒的时间戳，不能与最后的时间戳一样
     * <AUTHOR>
     * @date 2024-08-06
     * @since 1.5.0
     */
    private static long nextMillis(long lastMillis) {
        long now = System.currentTimeMillis();
        while (now <= lastMillis) {
            now = System.currentTimeMillis();
        }
        return now;
    }


    /**
     * 根据 host address 取余，发生异常就获取 0到31之间的随机数
     * <AUTHOR>
     * @date 2024-08-06
     * @since 1.5.0
     */
    private static int getWorkId() {
        int workId;
        try {
            workId = getHostId(InetAddress.getLocalHost().getHostAddress(), WORK_MAX_NUM);
        } catch (UnknownHostException e) {
            // 无法获取本地主机地址时，使用默认值
            workId = ONE;
        }
        return workId;
    }

    /**
     * 根据 host name 取余，发生异常就获取 0到31之间的随机数
     * <AUTHOR>
     * @date 2024-08-06
     * @since 1.5.0
     */
    private static int getDataId() {
        int dataId;
        try {
            dataId = getHostId(InetAddress.getLocalHost().getHostName(), DATA_MAX_NUM);
        } catch (UnknownHostException e) {
            // 无法获取主机名时，使用默认值
            dataId = DATA_CENTER_ID;
        }
        return dataId;
    }

    /**
     * 获取下一个ID
     *
     * @param maxDatacenterId 最大数据中心id
     * @return 雪花算法生成的ID
     * <AUTHOR>
     * @date 2024-06-17
     * @since 1.5.0
     */
    public static long getDataCenterId(int maxDatacenterId) {
        int workId;
        try {
            workId = getHostId(InetAddress.getLocalHost().getHostAddress(), maxDatacenterId);
        } catch (UnknownHostException e) {
            // 无法获取本地主机地址时，使用默认值
            workId = DATA_CENTER_ID;
        }
        return workId;
    }

    /**
     * 获取字符串s的字节数组，然后将数组的元素相加，对（max+1）取余
     *
     * <AUTHOR>
     * @date 2024-06-17
     * @since 1.5.0
     */
    public static int getHostId(String s, int max) {
        byte[] bytes = s.getBytes(StandardCharsets.UTF_8);
        int sums = 0;
        for (int b : bytes) {
            sums += b;
        }
        int mod = Math.abs(sums % (max + 1));
        return mod == 0 ? 1 : mod;
    }

    public static SnowflakeUtils getInstance() {
        return InstanceHolder.INSTANCE;
    }

    /**
     * 通过静态方法创建不同的id
     * <AUTHOR>
     * @date 2024-08-06
     * @since 1.5.0
     * @return 雪花id
     */
    public static Long snowflakeId() {
        return getInstance().nextId();
    }

    /**
     * 根据Snowflake的ID，获取机器id
     *
     * @param id snowflake算法生成的id
     * @return 所属机器的id
     * <AUTHOR>
     * @date 2024-06-17
     * @since 1.5.0
     */
    public static long getWorkerId(long id) {
        return ((id >> WORKER_ID_SHIFT) & (~(-1L << WORKER_ID_BITS)));
    }

    /**
     * 根据Snowflake的ID，获取数据中心id
     *
     * @param id snowflake算法生成的id
     * @return 所属数据中心
     * <AUTHOR>
     * @date 2024-06-17
     * @since 1.5.0
     */
    public static long getDataCenterId(long id) {
        return ((id >> DATA_CENTER_ID_SHIFT) & (~(-1L << DATA_CENTER_ID_BITS)));
    }

    /**
     * 根据Snowflake的ID，获取生成时间
     *
     * @param id snowflake算法生成的id
     * @return 生成的时间
     * <AUTHOR>
     * @date 2024-06-17
     * @since 1.5.0
     */
    public long getGenerateDateTime(long id) {
        return (((id >> TIMESTAMP_LEFT_SHIFT) & (~(-1L << TIME_LEN))) + twepoch);
    }

    /**
     * 下一个ID 这里是19位的整数
     *
     * @return ID
     * <AUTHOR>
     * @date 2024-06-17
     * @since 1.5.0
     */
    public synchronized long nextId() {
        long timestamp = genTime();
        if (timestamp < this.aLong) {
            if (this.aLong - timestamp < timeOffset) {
                timestamp = aLong;
            } else {
                throw new IllegalStateException(String.format("Clock moved backwards. Refusing to generate id for " +
                        " %s ms", aLong - timestamp));
            }
        }

        if (timestamp == this.aLong) {
            long sequence1 = (this.sequence + 1) & SEQUENCE_MASK;
            if (sequence1 == 0) {
                timestamp = tilNextMillis(aLong);
            }
            this.sequence = sequence1;
        } else {
            sequence = 0L;
        }

        aLong = timestamp;

        return ((timestamp - twepoch) << TIMESTAMP_LEFT_SHIFT)
                | (dataCenterId << DATA_CENTER_ID_SHIFT)
                | (workerId << WORKER_ID_SHIFT)
                | sequence;
    }

    /**
     * 下一个ID（字符串形式）
     *
     * @return ID 字符串形式
     * <AUTHOR>
     * @date 2024-06-17
     * @since 1.5.0
     */
    public String nextIdStr() {
        return Long.toString(nextId());
    }

    /**
     * 循环等待下一个时间
     *
     * @param lastTimestamp 上次记录的时间
     * @return 下一个时间
     * <AUTHOR>
     * @date 2024-06-17
     * @since 1.5.0
     */
    private long tilNextMillis(long lastTimestamp) {
        // 生成当前时间戳
        long timestamp = genTime();
        // 循环直到获取到与上次时间戳不同的时间戳
        while (timestamp == lastTimestamp) {
            timestamp = genTime();
        }
        // 如果获取到的时间戳小于上次时间戳，则抛出异常
        if (timestamp < lastTimestamp) {
            throw new IllegalStateException(
                    String.format("Clock moved backwards. Refusing to generate id for  %s ms",
                            // 格式化异常信息，显示时钟回退的时间长度
                            lastTimestamp - timestamp));
        }
        // 返回获取到的时间戳
        return timestamp;
    }

    /**
     * 生成时间戳
     *
     * @return 时间戳
     * <AUTHOR>
     * @date 2024-06-17
     * @since 1.5.0
     */
    private long genTime() {
        return this.useSystemClock ? SystemClock.now() : System.currentTimeMillis();
    }

    /**
     * 获取单例
     * <AUTHOR>
     * @date 2024-06-17
     * @since 1.5.0
     */
    private static class InstanceHolder {
        public static final SnowflakeUtils INSTANCE = new SnowflakeUtils(1, 1);
    }


}
