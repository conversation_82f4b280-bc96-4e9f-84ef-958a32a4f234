/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.validations.utils;

import com.snbc.bbpf.commons.validations.annotation.ValidNotEmpty;
import org.junit.jupiter.api.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

/**
 * 非空验证测试类
 *
 * <AUTHOR>
 * @module
 * @date 2023/7/20 16:30
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class ValidNotEmptyUtilTest {

    //测试类
    private Person person;

    @BeforeEach
    public void init() {
        person = new Person();
        person.setName("Name");
        person.setSex("Sex");
        person.setAge(1);
        HashMap<String, Long> gradeMap = new HashMap<>();
        gradeMap.put("英语", 100L);
        gradeMap.put("数学", 1L);
        person.setGradeMap(gradeMap);
        ArrayList<String> hobbiesList = new ArrayList<>();
        hobbiesList.add("enlis");
        hobbiesList.add("ssss");
        person.setHobbiesList(hobbiesList);
        person.setIncome(new BigDecimal(1));
        person.setOtherArray(new Double[]{1.1, 2.2});
        Friend friend = new Friend();
        person.setFriend(friend);
    }

    @Test
    @DisplayName("对象中的属性值全部非空的验证，未抛出非空验证异常")
    @Tags({
            @Tag("@id:37"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/20")
    })
    void testValidNotEmpty_notEmpty() {

        assertDoesNotThrow(() -> {
            ValidNotEmptyUtil.emptyFieldValidate(person);
        });
    }

    @Test
    @DisplayName("值为空且未配置ValidNotEmpty注解，未抛出非空验证异常")
    @Tags({
            @Tag("@id:37"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/20")
    })
    void testValidNotEmpty_noAnnocation() {
        person.setSex("");
        assertDoesNotThrow(() -> {
            ValidNotEmptyUtil.emptyFieldValidate(person);
        });
    }

    @Test
    @DisplayName("未配置ValidNotEmpty注解，未抛出非空验证异常")
    @Tags({
            @Tag("@id:37"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/20")
    })
    void testValidNotEmpty_objNoAnnocation() {
        assertDoesNotThrow(() -> {
            ValidNotEmptyUtil.emptyFieldValidate(new Friend());
        });
    }

    @Test
    @DisplayName("String类型的对象存在空值的验证，抛出ValidException异常")
    @Tags({
            @Tag("@id:37"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/20")
    })
    void testValidNotEmpty_emptyString() {
        person.setName("");

        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> ValidNotEmptyUtil.emptyFieldValidate(person));
        assertEquals("客户姓名不能为空", thrown.getMessage());
    }

    @Test
    @DisplayName("对象为null的验证，抛出ValidException异常")
    @Tags({
            @Tag("@id:37"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/20")
    })
    void testValidNotEmpty_null() {
        Person customer = new Person();
        customer.setName(null);
//        customer.setEducation(null);
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> ValidNotEmptyUtil.emptyFieldValidate(customer));
        assertEquals("客户姓名不能为空", thrown.getMessage());
    }

    @Test
    @DisplayName("传入参数对象属性均为空，抛出ValidException异常")
    @Tags({
            @Tag("@id:37"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/20")
    })
    void testValidNotEmpty_emptyObj() {
        Person person1 = new Person();
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> ValidNotEmptyUtil.emptyFieldValidate(person1));
        assertEquals("客户姓名不能为空", thrown.getMessage());
    }

    @Test
    @DisplayName("传入参数为null，抛出ValidException异常")
    @Tags({
            @Tag("@id:37"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/20")
    })
    void testValidNotEmpty_emptyOtherObj() {
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> ValidNotEmptyUtil.emptyFieldValidate(null));
        assertEquals("object can't be null", thrown.getMessage());
    }

    @Test
    @DisplayName("对象非javabean，未抛出异常")
    @Tags({
            @Tag("@id:37"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/20")
    })
    void testValidNotEmpty_notJavaBean() {
        assertDoesNotThrow(() -> {
            ValidNotEmptyUtil.emptyFieldValidate(1);
        });
    }

    @Test
    @DisplayName("Integer类属性为空的验证，抛出ValidException异常")
    @Tags({
            @Tag("@id:37"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/20")
    })
    void testValidNotEmpty_emptyInteger() {
        person.setAge(null);
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> ValidNotEmptyUtil.emptyFieldValidate(person));
        assertEquals("age can't be empty", thrown.getMessage());
    }

    @Test
    @DisplayName("BigDecimal类属性为空的验证，抛出ValidException异常")
    @Tags({
            @Tag("@id:37"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/20")
    })
    void testValidNotEmpty_emptyBigDecimal() {
        person.setIncome(null);
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> ValidNotEmptyUtil.emptyFieldValidate(person));
        assertEquals("income can't be empty", thrown.getMessage());
    }

    @Test
    @DisplayName("List类属性为空的验证，抛出ValidException异常")
    @Tags({
            @Tag("@id:37"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/20")
    })
    void testValidNotEmpty_emptyList() {
        person.setHobbiesList(new ArrayList());
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> ValidNotEmptyUtil.emptyFieldValidate(person));
        assertEquals("hobbiesList can't be empty", thrown.getMessage());
    }

    @Test
    @DisplayName("List类属性存在空值的验证，抛出ValidException异常")
    @Tags({
            @Tag("@id:37"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/20")
    })
    void testValidNotEmpty_emptyListValue() {
        ArrayList<String> hobbiesList = new ArrayList<>();
        hobbiesList.add("");
        hobbiesList.add("1");
        person.setHobbiesList(hobbiesList);
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> ValidNotEmptyUtil.emptyFieldValidate(person));
        assertEquals("hobbiesList can't be empty", thrown.getMessage());
    }

    @Test
    @DisplayName("Map类属性为空的验证，抛出ValidException异常")
    @Tags({
            @Tag("@id:37"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/20")
    })
    void testValidNotEmpty_emptyMap() {
        person.setGradeMap(new HashMap());
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> ValidNotEmptyUtil.emptyFieldValidate(person));
        assertEquals("gradeMap can't be empty", thrown.getMessage());
    }

    @Test
    @DisplayName("Map类属性存在空的value，抛出ValidException异常")
    @Tags({
            @Tag("@id:37"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/20")
    })
    void testValidNotEmpty_emptyMapValue() {
        HashMap<String, Long> gradeMap = new HashMap<>();
        gradeMap.put("英语", null);
        person.setGradeMap(gradeMap);
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> ValidNotEmptyUtil.emptyFieldValidate(person));
        assertEquals("gradeMap can't be empty", thrown.getMessage());
    }

    @Test
    @DisplayName("数组属性为空的验证，抛出ValidException异常")
    @Tags({
            @Tag("@id:37"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/20")
    })
    void testValidNotEmpty_emptyArray() {
        person.setOtherArray(new Double[]{1.1, null});
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> ValidNotEmptyUtil.emptyFieldValidate(person));
        assertEquals("otherArray can't be empty", thrown.getMessage());
    }

    @Test
    @DisplayName("对象属性为空的验证，抛出ValidException异常")
    @Tags({
            @Tag("@id:37"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/20")
    })
    void testValidNotEmpty_emptyObjField() {
        person.setFriend(null);
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> ValidNotEmptyUtil.emptyFieldValidate(person));
        assertEquals("friend can't be empty", thrown.getMessage());
    }

    @Test
    @DisplayName("值不为空但未定义get方法，抛出ValidException异常")
    @Tags({
            @Tag("@id:37"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/7/20")
    })
    void testValidNotEmpty_noGetMethod() {
        Family family = new Family();
        family.setFamily("aha");
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class,
                () -> ValidNotEmptyUtil.emptyFieldValidate(new Family()));
        assertEquals("java.beans.IntrospectionException: Method not found: isFamily", thrown.getMessage());
    }

    // 测试类
    class Person {

        /**
         * 性别
         */
        private String sex;
        /**
         * 姓名
         */
        @ValidNotEmpty(message = "客户姓名不能为空")
        private String name;
        /**
         * 年龄
         */
        @ValidNotEmpty
        private Integer age;
        /**
         * 收入
         */
        @ValidNotEmpty
        private BigDecimal income;
        /**
         * 爱好
         */
        @ValidNotEmpty
        private List hobbiesList;
        /**
         * 成绩
         */
        @ValidNotEmpty
        private Map gradeMap;
        /**
         * 其他
         */
        @ValidNotEmpty
        private Double[] otherArray;
        /**
         * 朋友
         */
        @ValidNotEmpty
        private Friend friend;

        public void setName(String name) {
            this.name = name;
        }

        public void setAge(Integer age) {
            this.age = age;
        }

        public void setSex(String sex) {
            this.sex = sex;
        }

        public void setIncome(BigDecimal income) {
            this.income = income;
        }

        public void setHobbiesList(List hobbiesList) {
            this.hobbiesList = hobbiesList;
        }

        public void setGradeMap(Map gradeMap) {
            this.gradeMap = gradeMap;
        }

        public void setOtherArray(Double[] otherArray) {
            this.otherArray = otherArray;
        }

        public String getName() {
            return name;
        }

        public Integer getAge() {
            return age;
        }

        public String getSex() {
            return sex;
        }

        public BigDecimal getIncome() {
            return income;
        }

        public List getHobbiesList() {
            return hobbiesList;
        }

        public Map getGradeMap() {
            return gradeMap;
        }

        public Double[] getOtherArray() {
            return otherArray;
        }

        public Friend getFriend() {
            return friend;
        }

        public void setFriend(Friend friend) {
            this.friend = friend;
        }
    }

    class Friend {
        private String friend;

        public String getFriend() {
            return friend;
        }

        public void setFriend(String friend) {
            this.friend = friend;
        }
    }

    class Family {
        @ValidNotEmpty
        private String family;

        public void setFamily(String family) {
            this.family = family;
        }
    }
}