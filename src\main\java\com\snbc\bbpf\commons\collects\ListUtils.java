/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.collects;

import com.fasterxml.jackson.databind.SerializationFeature;
import com.snbc.bbpf.commons.jsons.JsonObjectMapper;
import com.snbc.bbpf.commons.objs.ObjectEmptyCheck;

import java.util.List;

/**
 * 【集合模块】集合转换成JSON字符串处理。
 * <p>List<T>  转换成 JSON String 可新建ListUtils实现</p>
 * <p>需要考虑日期格式 ，空属性的转换场景。</p>
 *
 * <AUTHOR>
 * @module
 * @date 2023/9/14 10:24
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class ListUtils {

    private ListUtils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * list转为json
     * <p>在此方法基础上封装{@link com.snbc.bbpf.commons.jsons.JsonObjectMapper#marshall(Object)}</p>
     *
     * @param list 要转换的list
     * @return String 转换后的json串
     * @throws RuntimeException
     * <AUTHOR>
     * @date 2023/9/19
     * @since 1.2.0
     */
    public static String listToJson(List<?> list) {
        if (ObjectEmptyCheck.isEmpty(list)) {
            return null;
        }
        return JsonObjectMapper.marshall(list);
    }

    /**
     * list转为json（按自定义特性转换）
     * <p>在此方法基础上封装{@link com.snbc.bbpf.commons.jsons.JsonObjectMapper#marshall(Object, List, List)}</p>
     *
     * @param list            要转换的list
     * @param enableFeatures  允许的特性集合，若不需设置则传null
     * @param disableFeatures 禁止的特性集合，若不需设置则传null
     * @return String 转换后的json串
     * @throws RuntimeException
     * <AUTHOR>
     * @date 2023/9/19
     * @since 1.2.0
     */
    public static String listToJson(List<?> list, List<SerializationFeature> enableFeatures,
                                    List<SerializationFeature> disableFeatures) {
        if (ObjectEmptyCheck.isEmpty(list)) {
            return null;
        }
        return JsonObjectMapper.marshall(list, enableFeatures, disableFeatures);
    }
}
