package com.snbc.bbpf.commons.validations.validator;

import com.snbc.bbpf.commons.validations.ValidException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.assertThrows;
class UrlValidatorTest {
    private final UrlValidator urlValidator = new UrlValidator();
    @Test
    @DisplayName("输入非空")
    @Tags({
            @Tag("@id:36"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/6/28")
    })
    void testValidateWithEmptyUrl() {
        String url = "";
        String message = "URL地址为空或格式错误";
        assertThrows(IllegalArgumentException.class, () -> {
            urlValidator.validate(url, message);
        });
    }
    @Test
    @DisplayName("输入非空")
    @Tags({
            @Tag("@id:36"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/6/28")
    })
    void testValidateWithInvalidUrlFormat() {
        String url = "www.example.com";
        String message = "URL地址格式不正确";
        assertThrows(IllegalArgumentException.class, () -> {
            urlValidator.validate(url, message);
        });
    }
    @Test
    @DisplayName("输入非空")
    @Tags({
            @Tag("@id:36"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/6/28")
    })
    void testValidateWithValidUrl() {
        String url = "http://www.example.com";
        String message = null;
        try {
            urlValidator.validate(url, message);
        } catch (IllegalArgumentException e) {
            throw new RuntimeException(e);
        }
    }
}