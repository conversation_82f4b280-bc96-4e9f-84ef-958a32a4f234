/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.dates;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.Period;
import java.time.temporal.ChronoField;
import java.time.temporal.Temporal;
import java.time.temporal.TemporalAdjuster;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @module Date日期通用调节处理
 * 对Date、LocalDate、LocalDateTime、ZonedDateTime进行时间计算。
 * <p>计算当前时间一定时间（天，小时，分钟，秒，毫秒）之前或之后的时间</p>
 * <p>计算两个时间之间间隔时差（天，小时，分钟，秒，毫秒）<p>
 * @date 2023/4/23 9:58
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class BaseDateDiffUtil {

    protected static final int INT_23 = 23;
    protected static final int INT_59 = 59;

    protected static final int QUARTER_MONTH = 3;
    /**
     * #Temporal 加任意年月日时分秒毫秒
     *
     * @param temporal     制定日期
     * @param years        年
     * @param months       月
     * @param days         日
     * @param hours        时
     * @param minutes      分
     * @param seconds      秒
     * @param milliseconds 毫秒
     * @return Temporal
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    protected static Temporal commonPlus(Temporal temporal, int years, int months, int days, int hours, long minutes, long seconds, long milliseconds) {
        TemporalAdjuster adjustYears = t -> t.plus(Period.ofYears(years));
        TemporalAdjuster adjustMonths = t -> t.plus(Period.ofMonths(months));
        TemporalAdjuster adjustDays = t -> t.plus(Period.ofDays(days));
        TemporalAdjuster adjustHours = t -> t.plus(Duration.ofHours(hours));
        TemporalAdjuster adjustMiutes = t -> t.plus(Duration.ofMinutes(minutes));
        TemporalAdjuster adjustSecond = t -> t.plus(Duration.ofSeconds(seconds));
        TemporalAdjuster adjustNilliseconds = t -> t.plus(Duration.ofMillis(milliseconds));
        return temporal.with(adjustYears).with(adjustMonths).with(adjustDays).with(adjustHours).with(adjustMiutes).with(adjustSecond).with(adjustNilliseconds);
    }

    /**
     * 计算时间差（年月日，时分秒来决定单位）
     *
     * @param start    开始日期
     * @param end      结束日期
     * @param timeUnit
     * @return long
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    protected static long commondDiff(Date start, Date end, TimeUnit timeUnit) {
        long diffInMillies = Math.abs(start.getTime() - end.getTime());
        return timeUnit.convert(diffInMillies, TimeUnit.MILLISECONDS);
    }


    /**
     * #获取年
     *
     * @param temporal 通用Temporal
     * @return int 年
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    protected static int yearTemporal(Temporal temporal) {
        return temporal.get(ChronoField.YEAR);
    }

    /**
     * #获取月
     *
     * @param temporal 通用Temporal
     * @return int 月
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    protected static int monthTemporal(Temporal temporal) {
        return temporal.get(ChronoField.MONTH_OF_YEAR);
    }

    /**
     * #获取天
     *
     * @param temporal 通用Temporal
     * @return int 天
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    protected static int dayTemporal(Temporal temporal) {
        return temporal.get(ChronoField.DAY_OF_MONTH);
    }

    /**
     * #获取小时
     *
     * @param temporal 通用Temporal
     * @return int 小时
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    protected static int hourTemporal(Temporal temporal) {
        return temporal.get(ChronoField.HOUR_OF_DAY);
    }

    /**
     * #获取分钟
     *
     * @param temporal 通用Temporal
     * @return int 分钟
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    protected static int minuteTemporal(Temporal temporal) {
        return temporal.get(ChronoField.MINUTE_OF_HOUR);
    }

    /**
     * #获取秒数
     *
     * @param temporal 通用Temporal
     * @return int 秒数目
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    protected static int secondTemporal(Temporal temporal) {
        return temporal.get(ChronoField.SECOND_OF_MINUTE);
    }

    /**
     * #获取毫秒
     *
     * @param temporal 通用Temporal
     * @return int 毫秒数目
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    protected static int millisecondTemporal(Temporal temporal) {
        return temporal.get(ChronoField.MILLI_OF_SECOND);
    }


    /**
     * # 根据localdate返回早晨时间00点00分00秒
     *
     * @param localDate 日期
     * @return LocalDateTime
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    protected static LocalDateTime morning(LocalDate localDate) {
        return LocalDateTime.of(localDate, LocalTime.of(0, 0, 0, 0));
    }

    /**
     * # 根据localdate返回晚上时间23点59分59秒
     *
     * @param localDate 日期
     * @return LocalDateTime
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    protected static LocalDateTime evening(LocalDate localDate) {
        return LocalDateTime.of(localDate, LocalTime.of(INT_23, INT_59, INT_59, 0));
    }

}
