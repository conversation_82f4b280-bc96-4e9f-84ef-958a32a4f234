/*
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.commons.strings.security;

import com.snbc.bbpf.commons.strings.security.annotation.SensitiveType;
import com.snbc.bbpf.commons.strings.security.annotation.Desensitized;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

/**
 * 脱敏工具类测试类
 *
 * <AUTHOR>
 * @module 脱敏
 * @date 2023/5/06 18:18
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class DesensitizedUtilsTest {

    ///模拟测试的数据单个转换的数据
    private BlackDto blackDto;

    ///模拟测试的数据单个转换的数据
    private List<BlackDto> blackDtoLst = new ArrayList();

    @BeforeEach
    void setUp() throws Exception {
        blackDto = new BlackDto();
        blackDto.setName("测试名字");
        blackDto.setIdCard("12345678901234567890");
        blackDto.setUserPhone("12345671231");
        blackDto.setUserPwd("123456654321");
        blackDto.setEmail("<EMAIL>");
        blackDto.setAddress("中国北京市朝阳区第三街道第五大道宇宙中心");
        blackDtoLst.add(blackDto);
        BlackDto blackDto1 = new BlackDto();
        blackDto.setName("测试名字2");
        blackDto.setIdCard("22345678901234567822");
        blackDto.setUserPhone("223456712322");
        blackDto.setUserPwd("123456654321");
        blackDto.setEmail("<EMAIL>");
        blackDto.setAddress("中国北京市朝阳区闪金镇第五大道宇宙中心");
        blackDtoLst.add(blackDto1);
        BlackDto blackDto2 = new BlackDto();
        blackDto.setName("测试名字3");
        blackDto.setIdCard("22345678901234567822");
        blackDto.setUserPhone("223456712322");
        blackDto.setUserPwd("123456654321");
        blackDto.setEmail("<EMAIL>");
        blackDto.setAddress("中国北京市朝阳区看忙镇第五大道宇宙中心");
        blackDtoLst.add(blackDto2);
    }

    @Test
    @DisplayName("输入非空")
    @Tags({
            @Tag("@id:1"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/5/6")
    })
    void testGetJson_WithNonNullInput() throws Exception {
        Object result = DesensitizedUtils.getJson(blackDto);
        assertNotNull(result); // Ensure that the output is not null
        assertEquals(blackDto, result); // Ensure that the output is the same as the input
    }

    @Test
    @DisplayName("输入为空")
    @Tags({
            @Tag("@id:1"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/5/6")
    })
    void testGetJson_WithNullInput() throws Exception {
        Object javaBean = null; // Create a null object
        Object result = DesensitizedUtils.getJson(javaBean);
        assertNull(result); // Ensure that the output is null
    }

    @Test
    @DisplayName("密码脱敏")
    @Tags({
            @Tag("@id:1"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/5/6")
    })
    void testDesensitize_Password() {
        String oldOut = "wdcf12!";
        String out = DesensitizedUtils.desensitize(oldOut, SensitiveType.PASSWORD);
        assertEquals("*******", out);
    }

    @Test
    @DisplayName("中文名字脱敏")
    @Tags({
            @Tag("@id:1"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/5/6")
    })
    void testDesensitize_ChinaName() {
        String oldOut = "阳伟鹏";
        String out = DesensitizedUtils.desensitize(oldOut, SensitiveType.CHINESE_NAME);
        assertEquals("阳**", out);
    }

    @Test
    @DisplayName("身份证号脱敏")
    @Tags({
            @Tag("@id:1"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/5/6")
    })
    void testDesensitize_Card() {
        String oldOut = "******************";
        String out = DesensitizedUtils.desensitize(oldOut, SensitiveType.ID_CARD);
        assertEquals("**************3297", out);
    }

    @Test
    @DisplayName("银行卡脱敏-银行卡为null")
    @Tags({
            @Tag("@id:1"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/5/6")
    })
    void testDesensitize_BankCardIsNull() {
        String oldOut = null;
        String out = DesensitizedUtils.desensitize(oldOut, SensitiveType.ADDRESS);
        assertEquals(null, out);
        oldOut = "";
        out = DesensitizedUtils.desensitize(oldOut, SensitiveType.ADDRESS);
        assertEquals("", out);
    }

    @Test
    @DisplayName("银行卡脱敏")
    @Tags({
            @Tag("@id:1"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/5/6")
    })
    void testDesensitize_BankCard() {
        String oldOut = "622260043938429381234";
        String out = DesensitizedUtils.desensitize(oldOut, SensitiveType.BANK_CARD);
        assertEquals("622260***********1234", out);
    }

    @Test
    @DisplayName("非银行银行卡脱敏")
    @Tags({
            @Tag("@id:1"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/5/6")
    })
    void testDesensitize_NotBankCard() {
        String oldOut = "6222600439384292dddd";
        String out = DesensitizedUtils.desensitize(oldOut, SensitiveType.BANK_CARD);
        assertEquals("622260**********dddd", out);
    }

    @Test
    @DisplayName("地址脱敏-大于6个字符")
    @Tags({
            @Tag("@id:1"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/5/6")
    })
    void testDesensitize_Address() {
        String oldOut = "北京市丰台区玉林东里";
        String out = DesensitizedUtils.desensitize(oldOut, SensitiveType.ADDRESS);
        assertEquals("北京市丰台区****", out);
    }

    @Test
    @DisplayName("地址脱敏-小于6个字符")
    @Tags({
            @Tag("@id:1"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/5/6")
    })
    void testDesensitize_AddresslessSixChar() {
        String oldOut = "北京市丰台";
        String out = DesensitizedUtils.desensitize(oldOut, SensitiveType.ADDRESS);
        assertEquals("北****", out);
    }

    @Test
    @DisplayName("地址脱敏-地址为null")
    @Tags({
            @Tag("@id:1"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/5/6")
    })
    void testDesensitize_AddressIsNull() {
        String oldOut = null;
        String out = DesensitizedUtils.desensitize(oldOut, SensitiveType.ADDRESS);
        assertEquals(null, out);
    }

    @Test
    @DisplayName("地址脱敏-地址为空")
    @Tags({
            @Tag("@id:1"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/5/6")
    })
    void testDesensitize_AddressIsBlank() {
        String oldOut = "";
        String out = DesensitizedUtils.desensitize(oldOut, SensitiveType.ADDRESS);
        assertEquals("", out);
    }

    @Test
    @DisplayName("email脱敏")
    @Tags({
            @Tag("@id:1"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/5/6")
    })
    void testDesensitize_Email() {
        String oldOut = "<EMAIL>";
        String out = DesensitizedUtils.desensitize(oldOut, SensitiveType.EMAIL);
        assertEquals("o*******@163.com", out);
    }

    @Test
    @DisplayName("email脱敏-null")
    @Tags({
            @Tag("@id:1"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/5/6")
    })
    void testDesensitize_EmailIsNULL() {
        String oldOut = null;
        String out = DesensitizedUtils.desensitize(oldOut, SensitiveType.EMAIL);
        assertEquals(null, out);
    }

    @Test
    @DisplayName("email脱敏-空字符")
    @Tags({
            @Tag("@id:1"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/5/6")
    })
    void testDesensitize_EmailIsEmpty() {
        String oldOut = "";
        String out = DesensitizedUtils.desensitize(oldOut, SensitiveType.EMAIL);
        assertEquals("", out);
    }

    @Test
    @DisplayName("固定电话脱敏")
    @Tags({
            @Tag("@id:1"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/5/6")
    })
    void testDesensitize_FixedPhone() {
        String oldOut = "01083123943";
        String out = DesensitizedUtils.desensitize(oldOut, SensitiveType.FIXED_PHONE);
        assertEquals("*******3943", out);
    }

    @Test
    @DisplayName("list对象脱敏")
    @Tags({
            @Tag("@id:1"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/5/6")
    })
    void testDesensitize_List() throws Exception {
        String oldOut = "[{\"address\":\"中国北京市朝阳区看忙镇第五大道宇宙中心\",\"email\":\"<EMAIL>\",\"idCard\":\"22345678901234567822\",\"name\":\"测试名字3\",\"userPhone\":\"223456712322\",\"userPwd\":\"123456654321\"},{},{}]\n";
        String out = DesensitizedUtils.desensitizedList(blackDtoLst).toString();
        assertNotEquals(oldOut, out);
    }

    @Test
    @DisplayName("对象脱敏")
    @Tags({
            @Tag("@id:1"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/5/6")
    })
    void testDesensitize_Object() throws Exception {
        String oldOut = "[{\"address\":\"中国北京市朝阳区看忙镇第五大道宇宙中心\",\"email\":\"<EMAIL>\",\"idCard\":\"22345678901234567822\",\"name\":\"测试名字3\",\"userPhone\":\"223456712322\",\"userPwd\":\"123456654321\"},{},{}]\n";
        String out = DesensitizedUtils.desensitizedObject(blackDto).toString();
        assertNotEquals(oldOut, out);
    }

    @Test
    @DisplayName("手机号码脱敏11位")
    @Tags({
            @Tag("@id:1"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/5/6")
    })
    void testDesensitize_PhoneWith11() {
        String oldOut = "13412345678";
        String out = DesensitizedUtils.desensitize(oldOut, SensitiveType.MOBILE_PHONE);
        assertEquals("134****5678", out);
    }

    @Test
    @DisplayName("手机号码脱敏13位")
    @Tags({
            @Tag("@id:1"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/5/6")
    })
    void testDesensitize_PhoneWith13() {
        String oldOut = "1341234567823";
        String out = DesensitizedUtils.desensitize(oldOut, SensitiveType.MOBILE_PHONE);
        assertEquals("13412****7823", out);
    }

    @Test
    @DisplayName("手机号码脱敏-其他位数不脱敏14位")
    @Tags({
            @Tag("@id:1"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/5/6")
    })
    void testDesensitize_PhoneOther() {
        String oldOut = "13412345678231";
        String out = DesensitizedUtils.desensitize(oldOut, SensitiveType.MOBILE_PHONE);
        assertEquals("13412345678231", out);
    }


    @Test
    @DisplayName("范围脱敏-指定开始和结束位置")
    @Tags({
            @Tag("@id:1"),
            @Tag("@author:xuping"),
            @Tag("@date:2025/06/09")
    })
    void testDesensitizeByPosition() {
        String oldOut = "1234567890";
        String out = DesensitizedUtils.desensitizeByPosition(oldOut, 3, 7);
        assertEquals("123****890", out);
    }

    @Test
    @DisplayName("范围脱敏-无效范围")
    @Tags({
            @Tag("@id:1"),
            @Tag("@author:xuping"),
            @Tag("@date:2025/06/09")
    })
    void testDesensitizeByPosition_InvalidRange() {
        String oldOut = "1234567890";
        String out = DesensitizedUtils.desensitizeByPosition(oldOut, -1, 5);
        assertEquals("*****67890", out);

        out = DesensitizedUtils.desensitizeByPosition(oldOut, 10, 5);
        assertEquals(oldOut, out);

        out = DesensitizedUtils.desensitizeByPosition(oldOut, 5, 15);
        assertEquals("12345*****", out);
    }

    @Test
    @DisplayName("范围脱敏-输入为null")
    @Tags({
            @Tag("@id:1"),
            @Tag("@author:xuping"),
            @Tag("@date:2025/06/09")
    })
    void testDesensitizeByPosition_NullInput() {
        String out = DesensitizedUtils.desensitizeByPosition(null, 0, 5);
        assertNull(out);
    }

    @Test
    @DisplayName("长度脱敏-正常情况")
    @Tags({
            @Tag("@id:1"),
            @Tag("@author:xuping"),
            @Tag("@date:2025/06/09")
    })
    void testDesensitizeByLength_Normal() {
        String oldOut = "1234567890";
        String out = DesensitizedUtils.desensitizeByLength(oldOut, 3, 4);
        assertEquals("123****890", out);
    }

    @Test
    @DisplayName("长度脱敏-无效范围")
    @Tags({
            @Tag("@id:1"),
            @Tag("@author:xuping"),
            @Tag("@date:2025/06/09")
    })
    void testDesensitizeByLength_InvalidRange() {
        String oldOut = "1234567890";
        String out = DesensitizedUtils.desensitizeByLength(oldOut, -1, 5);
        assertEquals("*****567890", out);

        out = DesensitizedUtils.desensitizeByLength(oldOut, 10, 5);
        assertEquals("1234567890", out);

        out = DesensitizedUtils.desensitizeByLength(oldOut, 5, 15);
        assertEquals("12345***************", out);
    }

    @Test
    @DisplayName("长度脱敏-输入为null")
    @Tags({
            @Tag("@id:1"),
            @Tag("@author:xuping"),
            @Tag("@date:2025/06/09")
    })
    void testDesensitizeByLength_NullInput() {
        String out = DesensitizedUtils.desensitizeByLength(null, 3, 4);
        assertNull(out);
    }

    @Test
    @DisplayName("范围脱敏-指定符号-正常情况")
    @Tags({
            @Tag("@id:1"),
            @Tag("@author:xuping"),
            @Tag("@date:2025/06/09")
    })
    void testDesensitizeByRangeWithSymbol_Normal() {
        String oldOut = "1234567890";
        String out = DesensitizedUtils.desensitizeByRange(oldOut, 3, 7, "#");
        assertEquals("123####890", out);
    }

    @Test
    @DisplayName("范围脱敏-指定符号-无效范围")
    @Tags({
            @Tag("@id:1"),
            @Tag("@author:xuping"),
            @Tag("@date:2025/06/09")
    })
    void testDesensitizeByRangeWithSymbol_InvalidRange() {
        String oldOut = "1234567890";
        String out = DesensitizedUtils.desensitizeByRange(oldOut, -1, 5, "#");
        assertEquals("#####67890", out);

        out = DesensitizedUtils.desensitizeByRange(oldOut, 10, 5, "#");
        assertEquals(oldOut, out);

        out = DesensitizedUtils.desensitizeByRange(oldOut, 5, 15, "#");
        assertEquals("12345#####", out);
    }

    @Test
    @DisplayName("范围脱敏-指定符号-输入为null")
    @Tags({
            @Tag("@id:1"),
            @Tag("@author:xuping"),
            @Tag("@date:2025/06/09")
    })
    void testDesensitizeByRangeWithSymbol_NullInput() {
        String out = DesensitizedUtils.desensitizeByRange(null, 3, 7, "#");
        assertNull(out);
    }

    @Test
    @DisplayName("自定义脱敏-默认参数（前3后4位）")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/28")
    })
    void testDesensitizeCustom_Default() {
        String oldOut = "1234567890123";
        String out = DesensitizedUtils.desensitizeCustom(oldOut, 3, 4);
        assertEquals("123******0123", out);
    }

    @Test
    @DisplayName("自定义脱敏-指定脱敏符号")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/28")
    })
    void testDesensitizeCustom_WithSymbol() {
        String oldOut = "1234567890123";
        String out = DesensitizedUtils.desensitizeCustom(oldOut, 3, 4, "#");
        assertEquals("123######0123", out);
    }

    @Test
    @DisplayName("自定义脱敏-前2后3位")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/28")
    })
    void testDesensitizeCustom_Prefix2Suffix3() {
        String oldOut = "abcdefghijk";
        String out = DesensitizedUtils.desensitizeCustom(oldOut, 2, 3, "@");
        assertEquals("ab@@@@@@ijk", out);
    }

    @Test
    @DisplayName("自定义脱敏-字符串长度不足")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/28")
    })
    void testDesensitizeCustom_ShortString() {
        String oldOut = "123456";
        String out = DesensitizedUtils.desensitizeCustom(oldOut, 3, 4);
        assertEquals("123456", out); // 长度不足，不脱敏
    }

    @Test
    @DisplayName("自定义脱敏-只保留前缀")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/28")
    })
    void testDesensitizeCustom_PrefixOnly() {
        String oldOut = "1234567890";
        String out = DesensitizedUtils.desensitizeCustom(oldOut, 3, 0, "*");
        assertEquals("123*******", out);
    }

    @Test
    @DisplayName("自定义脱敏-只保留后缀")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/28")
    })
    void testDesensitizeCustom_SuffixOnly() {
        String oldOut = "1234567890";
        String out = DesensitizedUtils.desensitizeCustom(oldOut, 0, 3, "*");
        assertEquals("*******890", out);
    }

    @Test
    @DisplayName("自定义脱敏-空字符串")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/28")
    })
    void testDesensitizeCustom_EmptyString() {
        String oldOut = "";
        String out = DesensitizedUtils.desensitizeCustom(oldOut, 3, 4);
        assertEquals("", out);
    }

    @Test
    @DisplayName("自定义脱敏-null字符串")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/28")
    })
    void testDesensitizeCustom_NullString() {
        String oldOut = null;
        String out = DesensitizedUtils.desensitizeCustom(oldOut, 3, 4);
        assertNull(out);
    }

    @Test
    @DisplayName("自定义脱敏-中文字符串")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/28")
    })
    void testDesensitizeCustom_ChineseString() {
        String oldOut = "张三李四王五赵六";
        String out = DesensitizedUtils.desensitizeCustom(oldOut, 2, 2, "●");
        assertEquals("张三●●●●赵六", out);
    }

    @Test
    @DisplayName("自定义脱敏注解-对象脱敏")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/28")
    })
    void testDesensitizeCustom_Annotation() throws Exception {
        BlackDto testDto = new BlackDto();
        testDto.setCustomField("1234567890123");

        BlackDto result = (BlackDto) DesensitizedUtils.desensitizedObject(testDto);

        // 验证自定义脱敏：保留前2位后3位，用#替换
        assertEquals("12########123", result.getCustomField());
    }

    @Test
    @DisplayName("自定义脱敏注解-字符串长度不足")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/28")
    })
    void testDesensitizeCustom_Annotation_ShortString() throws Exception {
        BlackDto testDto = new BlackDto();
        testDto.setCustomField("12345"); // 长度为5，小于前2+后3=5

        BlackDto result = (BlackDto) DesensitizedUtils.desensitizedObject(testDto);

        // 长度不足，不进行脱敏
        assertEquals("12345", result.getCustomField());
    }

    @Test
    @DisplayName("自定义脱敏注解-空字符串")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/28")
    })
    void testDesensitizeCustom_Annotation_EmptyString() throws Exception {
        BlackDto testDto = new BlackDto();
        testDto.setCustomField("");

        BlackDto result = (BlackDto) DesensitizedUtils.desensitizedObject(testDto);

        assertEquals("", result.getCustomField());
    }

    @Test
    @DisplayName("自定义脱敏注解-null字符串")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/28")
    })
    void testDesensitizeCustom_Annotation_NullString() throws Exception {
        BlackDto testDto = new BlackDto();
        testDto.setCustomField(null);

        BlackDto result = (BlackDto) DesensitizedUtils.desensitizedObject(testDto);

        assertNull(result.getCustomField());
    }
}
class BlackDto {
    /**
     * 姓名
     */
    private String name;
    /**
     * 身份证
     */
    @Desensitized(type = SensitiveType.ID_CARD)
    private String idCard;
    /**
     * 手机号
     */
    @Desensitized(type = SensitiveType.MOBILE_PHONE)
    private String userPhone;
    /**
     * 密码
     */
    @Desensitized(type = SensitiveType.PASSWORD)
    private String userPwd;
    /**
     * 邮件
     */
    @Desensitized(type = SensitiveType.EMAIL)
    private String email;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getUserPhone() {
        return userPhone;
    }

    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }

    public String getUserPwd() {
        return userPwd;
    }

    public void setUserPwd(String userPwd) {
        this.userPwd = userPwd;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getCustomField() {
        return customField;
    }

    public void setCustomField(String customField) {
        this.customField = customField;
    }

    /**
     * 地址
     */
    @Desensitized(type = SensitiveType.ADDRESS)
    private String address;

    /**
     * 自定义脱敏字段（保留前2位后3位，用#替换）
     */
    @Desensitized(type = SensitiveType.CUSTOM, prefixKeep = 2, suffixKeep = 3, maskSymbol = "#")
    private String customField;

    public BlackDto() {

    }

    //跟上面定义的顺序一致
    public BlackDto(String name, String ic, String tel, String pw, String em, String ad) {
        super();
        this.address = ad;
        this.email = em;
        this.idCard = ic;
        this.name = name;
        this.userPhone = tel;
        this.userPwd = pw;

    }

}
