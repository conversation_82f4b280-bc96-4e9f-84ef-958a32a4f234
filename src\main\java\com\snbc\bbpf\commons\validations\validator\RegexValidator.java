/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.validations.validator;

import com.snbc.bbpf.commons.strings.StringEmptyCheck;
import com.snbc.bbpf.commons.validations.ValidException;
import org.apache.commons.lang3.StringUtils;

/**
 *
 * <AUTHOR>
 * @module 字符串校验模块
 * @date 2023/7/16
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class RegexValidator implements IStringValidator {
    private String regex;

    public RegexValidator(String regex) {
        this.regex = regex;
    }

    /**
     * 正则表达式校验
     *
     * @param str 待验证的字符串
     * @param message 验证不通过时的提示信息
     * @return void
     * @throws ValidException
     * <AUTHOR>
     * @date 2023/7/16
     * @since 1.1.0
     */
    @Override
    public void validate(String str, String message) throws IllegalArgumentException {
        if (StringUtils.isEmpty(str)) {
            throw new IllegalArgumentException("String cannot be empty");
        }
        // 验证手机号码地址格式
        if (!str.matches(regex)) {
            throw new IllegalArgumentException(!StringEmptyCheck.isEmpty(message) ? message : "String does not meet regular expression");
        }
    }
}
