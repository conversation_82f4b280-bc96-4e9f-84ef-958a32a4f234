package com.snbc.bbpf.commons.bytes.tlv;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TLV数据类型枚举单元测试类
 *
 * <AUTHOR>
 * @module 字节处理模块
 * @date 2025/07/25 11:30
 * @copyright 2024 山东新北洋信息技术股份有限公司. All rights reserved
 */
class TlvDataTypeTest {

    @BeforeEach
    void setUp() {
        // 清除自定义类型映射
        TlvDataType.clearCustomTypes();
    }

    @AfterEach
    void tearDown() {
        // 清除自定义类型映射
        TlvDataType.clearCustomTypes();
    }

    @Test
    @DisplayName("测试基本数据类型映射")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testBasicDataTypeMapping() {
        // 测试整数类型
        assertEquals(TlvDataType.BYTE, TlvDataType.fromJavaType(Byte.class));
        assertEquals(TlvDataType.BYTE, TlvDataType.fromJavaType(byte.class));
        assertEquals(TlvDataType.SHORT, TlvDataType.fromJavaType(Short.class));
        assertEquals(TlvDataType.SHORT, TlvDataType.fromJavaType(short.class));
        assertEquals(TlvDataType.INTEGER, TlvDataType.fromJavaType(Integer.class));
        assertEquals(TlvDataType.INTEGER, TlvDataType.fromJavaType(int.class));
        assertEquals(TlvDataType.LONG, TlvDataType.fromJavaType(Long.class));
        assertEquals(TlvDataType.LONG, TlvDataType.fromJavaType(long.class));
        assertEquals(TlvDataType.BIG_INTEGER, TlvDataType.fromJavaType(BigInteger.class));

        // 测试浮点数类型
        assertEquals(TlvDataType.FLOAT, TlvDataType.fromJavaType(Float.class));
        assertEquals(TlvDataType.FLOAT, TlvDataType.fromJavaType(float.class));
        assertEquals(TlvDataType.DOUBLE, TlvDataType.fromJavaType(Double.class));
        assertEquals(TlvDataType.DOUBLE, TlvDataType.fromJavaType(double.class));
        assertEquals(TlvDataType.BIG_DECIMAL, TlvDataType.fromJavaType(BigDecimal.class));

        // 测试布尔类型
        assertEquals(TlvDataType.BOOLEAN, TlvDataType.fromJavaType(Boolean.class));
        assertEquals(TlvDataType.BOOLEAN, TlvDataType.fromJavaType(boolean.class));

        // 测试字符串类型
        assertEquals(TlvDataType.STRING_UTF8, TlvDataType.fromJavaType(String.class));
    }

    @Test
    @DisplayName("测试复杂数据类型映射")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testComplexDataTypeMapping() {
        // 测试字节数组
        assertEquals(TlvDataType.BYTE_ARRAY, TlvDataType.fromJavaType(byte[].class));

        // 测试Map类型
        assertEquals(TlvDataType.MAP, TlvDataType.fromJavaType(Map.class));
        assertEquals(TlvDataType.MAP, TlvDataType.fromJavaType(HashMap.class));

        // 测试Iterable类型
        assertEquals(TlvDataType.ARRAY, TlvDataType.fromJavaType(Iterable.class));
        assertEquals(TlvDataType.ARRAY, TlvDataType.fromJavaType(ArrayList.class));
    }

    @Test
    @DisplayName("测试Type值映射")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testTypeValueMapping() {
        assertEquals(TlvDataType.BYTE, TlvDataType.fromTypeValue(0x01));
        assertEquals(TlvDataType.SHORT, TlvDataType.fromTypeValue(0x02));
        assertEquals(TlvDataType.INTEGER, TlvDataType.fromTypeValue(0x03));
        assertEquals(TlvDataType.LONG, TlvDataType.fromTypeValue(0x04));
        assertEquals(TlvDataType.BIG_INTEGER, TlvDataType.fromTypeValue(0x05));

        assertEquals(TlvDataType.FLOAT, TlvDataType.fromTypeValue(0x10));
        assertEquals(TlvDataType.DOUBLE, TlvDataType.fromTypeValue(0x11));
        assertEquals(TlvDataType.BIG_DECIMAL, TlvDataType.fromTypeValue(0x12));

        assertEquals(TlvDataType.BOOLEAN, TlvDataType.fromTypeValue(0x20));

        assertEquals(TlvDataType.STRING_UTF8, TlvDataType.fromTypeValue(0x30));
        assertEquals(TlvDataType.STRING_ASCII, TlvDataType.fromTypeValue(0x31));

        assertEquals(TlvDataType.MAP, TlvDataType.fromTypeValue(0x40));
        assertEquals(TlvDataType.ARRAY, TlvDataType.fromTypeValue(0x41));

        assertEquals(TlvDataType.BYTE_ARRAY, TlvDataType.fromTypeValue(0x50));

        assertEquals(TlvDataType.NULL, TlvDataType.fromTypeValue(0x00));
    }

    @Test
    @DisplayName("测试不支持的类型")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testUnsupportedTypes() {
        assertNull(TlvDataType.fromJavaType(Object.class));
        assertNull(TlvDataType.fromJavaType(Thread.class));
        assertNull(TlvDataType.fromTypeValue(0xFF));
        assertNull(TlvDataType.fromTypeValue(-1));
    }

    @Test
    @DisplayName("测试自定义类型注册")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testCustomTypeRegistration() {
        // 注册自定义类型
        TlvDataType.registerCustomType(StringBuilder.class, TlvDataType.STRING_UTF8);

        // 验证自定义类型映射
        assertEquals(TlvDataType.STRING_UTF8, TlvDataType.fromJavaType(StringBuilder.class));

        // 移除自定义类型
        TlvDataType.unregisterCustomType(StringBuilder.class);
        assertNull(TlvDataType.fromJavaType(StringBuilder.class));
    }

    @Test
    @DisplayName("测试自定义类型注册异常情况")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testCustomTypeRegistrationExceptions() {
        // 测试null参数
        assertThrows(IllegalArgumentException.class, () -> 
            TlvDataType.registerCustomType(null, TlvDataType.STRING_UTF8));
        
        assertThrows(IllegalArgumentException.class, () -> 
            TlvDataType.registerCustomType(String.class, null));
    }

    @Test
    @DisplayName("测试类型支持检查")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testTypeSupportCheck() {
        // 测试支持的类型
        assertTrue(TlvDataType.isSupported(String.class));
        assertTrue(TlvDataType.isSupported(Integer.class));
        assertTrue(TlvDataType.isSupported(byte[].class));
        assertTrue(TlvDataType.isSupported(Map.class));

        // 测试不支持的类型
        assertFalse(TlvDataType.isSupported(Object.class));
        assertFalse(TlvDataType.isSupported(Thread.class));
    }

    @Test
    @DisplayName("测试获取支持的Type值")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testGetSupportedTypeValues() {
        int[] supportedValues = TlvDataType.getSupportedTypeValues();
        
        assertNotNull(supportedValues);
        assertTrue(supportedValues.length > 0);
        
        // 验证包含已知的Type值
        boolean containsByte = false;
        boolean containsString = false;
        for (int value : supportedValues) {
            if (value == 0x01) containsByte = true;
            if (value == 0x30) containsString = true;
        }
        assertTrue(containsByte);
        assertTrue(containsString);
    }

    @Test
    @DisplayName("测试获取Java类型")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testGetJavaTypes() {
        Class<?>[] integerTypes = TlvDataType.INTEGER.getJavaTypes();
        assertEquals(2, integerTypes.length);
        assertTrue(containsClass(integerTypes, Integer.class));
        assertTrue(containsClass(integerTypes, int.class));

        Class<?>[] stringTypes = TlvDataType.STRING_UTF8.getJavaTypes();
        assertEquals(1, stringTypes.length);
        assertTrue(containsClass(stringTypes, String.class));
    }

    @Test
    @DisplayName("测试Type值获取")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testGetTypeValue() {
        assertEquals(0x01, TlvDataType.BYTE.getTypeValue());
        assertEquals(0x03, TlvDataType.INTEGER.getTypeValue());
        assertEquals(0x30, TlvDataType.STRING_UTF8.getTypeValue());
        assertEquals(0x40, TlvDataType.MAP.getTypeValue());
        assertEquals(0x00, TlvDataType.NULL.getTypeValue());
    }

    /**
     * 辅助方法：检查类型数组是否包含指定类型
     */
    private boolean containsClass(Class<?>[] types, Class<?> target) {
        for (Class<?> type : types) {
            if (type.equals(target)) {
                return true;
            }
        }
        return false;
    }
}
