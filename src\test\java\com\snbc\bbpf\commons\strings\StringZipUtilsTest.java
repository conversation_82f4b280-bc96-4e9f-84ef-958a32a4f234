/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.strings;

import com.snbc.bbpf.commons.randoms.RandomStringUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeAll;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTimeoutPreemptively;
import static java.time.Duration.ofMillis;

/**
 * 对字符串进行压缩和解压单元测试类
 *
 * <AUTHOR>
 * @module 字符串处理
 * @date 2023/8/16 20:30
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class StringZipUtilsTest {
	public static String strBigString;
	public static String strBigCompressString;
	public static byte[] byBigCompressByteArray;
	@BeforeAll
	@DisplayName("生成1M大字符串，以便性能测试")
	@Tags({
			@Tag("@id:43"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/8/16")
	})
	static void beforeAll_generateBigString () {
		int iCount = 1024 * 1024;
		strBigString = RandomStringUtils.randomAlphabetic(iCount, false);
		strBigCompressString = StringZipUtils.compressToString(strBigString);
		byBigCompressByteArray = StringZipUtils.compressToByteArray(strBigString);
	}

    @Test
	@DisplayName("对字符串进行压缩成字节数组")
	@Tags({
			@Tag("@id:43"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/16")
	})
    void testCompressToByteArray() {
		assertArrayEquals(new byte[] {120,-38,37,-115,-47,14,-63,64,16,69,127,93,35,81,-94,59,72,43,36,-35,-121,-94,
						90,66,105,37,4,89,-11,51,59,-77,-77,79,126,-63,110,-10,-7,-100,115,-81,-55,58,-3,6,90,
						-74,36,46,90,21,-36,-89,118,-69,48,16,-37,-45,-6,-89,18,-68,-66,-24,30,-39,120,70,114,-62,
						-3,16,-53,26,63,29,-90,-62,-93,-68,-96,-90,-76,114,-64,85,-28,52,-113,-102,-107,57,87,-6,
						121,-29,111,-50,-101,4,97,106,-44,17,-25,16,102,125,34,15,4,123,-54,30,56,110,-61,-93,-93,
						56,18,-63,-25,122,-25,-110,63,101,118,103,61},
			StringZipUtils.compressToByteArray("由于数据传输频率高，对流量有较大影响，因此需要对大字符串" +
					"进行压缩后传输，在接收到数据后再进行解压"));
    }

    @Test
	@DisplayName("对字符串进行压缩成字节数组，压缩级别为0")
	@Tags({
			@Tag("@id:43"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/16")
	})
    void testCompressToByteArray_level0() {
		assertArrayEquals(new byte[] {120,1,1,-112,0,111,-1,-25,-108,-79,-28,-70,-114,-26,-107,-80,-26,-115,-82,
						-28,-68,-96,-24,-66,-109,-23,-94,-111,-25,-114,-121,-23,-85,-104,-17,-68,-116,-27,-81,
						-71,-26,-75,-127,-23,-121,-113,-26,-100,-119,-24,-66,-125,-27,-92,-89,-27,-67,-79,-27,
						-109,-115,-17,-68,-116,-27,-101,-96,-26,-83,-92,-23,-100,-128,-24,-90,-127,-27,-81,-71,
						-27,-92,-89,-27,-83,-105,-25,-84,-90,-28,-72,-78,-24,-65,-101,-24,-95,-116,-27,-114,-117,
						-25,-68,-87,-27,-112,-114,-28,-68,-96,-24,-66,-109,-17,-68,-116,-27,-100,-88,-26,-114,-91,
						-26,-108,-74,-27,-120,-80,-26,-107,-80,-26,-115,-82,-27,-112,-114,-27,-122,-115,-24,-65,-101,
						-24,-95,-116,-24,-89,-93,-27,-114,-117,101,118,103,61},
			StringZipUtils.compressToByteArray("由于数据传输频率高，对流量有较大影响，因此需要对大字符串" +
					"进行压缩后传输，在接收到数据后再进行解压", 0));
    }

    @Test
	@DisplayName("对空字符串进行压缩成字节数组")
	@Tags({
			@Tag("@id:43"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/16")
	})
    void testCompressToByteArray_strEmpty() {
		assertArrayEquals(new byte[] {120,-38,3,0,0,0,0,1,},
			StringZipUtils.compressToByteArray(""));
    }

    @Test
	@DisplayName("对空格字符串进行压缩成字节数组")
	@Tags({
			@Tag("@id:43"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/16")
	})
    void testCompressToByteArray_strBlank() {
		assertArrayEquals(new byte[] {120,-38,83,0,0,0,33,0,33,},
			StringZipUtils.compressToByteArray(" "));
    }


    @Test
	@DisplayName("输入null字符串应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:43"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/16")
	})
    void testCompressToByteArray_strNullShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> StringZipUtils.compressToByteArray(null),
				"Input parameter is null");
    }

    @Test
	@DisplayName("输入小于0压缩级别应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:43"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/16")
	})
    void testCompressToByteArray_lessLevelShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> StringZipUtils.compressToByteArray("123", -1),
				"Input parameter is invalid");
    }

    @Test
	@DisplayName("输入大于9压缩级别应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:43"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/16")
	})
    void testCompressToByteArray_moreLevelShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> StringZipUtils.compressToByteArray("123", 10),
				"Input parameter is invalid");
    }



    @Test
	@DisplayName("对字符串进行压缩成字符串")
	@Tags({
			@Tag("@id:43"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/16")
	})
    void testCompressToString() {
        assertEquals("eNoljdEOwUAQRX9dI1GiO0grJN2HolpCaSUEWfUzO7OzT37Bbvb5nHOvyTr9Blq2JC" +
				"5aFdyndrswENvT+qcSvL7oHtl4RnLC/RDLGj8dpsKjvKCmtHLAVeQ0j5qVOVf6eeNvzpsEYWrUEecQ" +
				"Zn0iDwR7yh44bsOjozgSwed655I/ZXZnPQ==",
			StringZipUtils.compressToString("由于数据传输频率高，对流量有较大影响，因此需要对大字符串" +
					"进行压缩后传输，在接收到数据后再进行解压"));
    }

    @Test
	@DisplayName("对字符串进行压缩成字符串，压缩级别为0")
	@Tags({
			@Tag("@id:43"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/16")
	})
    void testCompressToString_level0() {
        assertEquals("eAEBkABv/+eUseS6juaVsOaNruS8oOi+k+mikeeOh+mrmO+8jOWvuea1gemHj+aciei+" +
						"g+Wkp+W9seWTje+8jOWboOatpOmcgOimgeWvueWkp+Wtl+espuS4sui/m+ihjOWOi+e8qeWQjuS8o" +
						"Oi+k++8jOWcqOaOpeaUtuWIsOaVsOaNruWQjuWGjei/m+ihjOino+WOi2V2Zz0=",
			StringZipUtils.compressToString("由于数据传输频率高，对流量有较大影响，因此需要对大字符串" +
					"进行压缩后传输，在接收到数据后再进行解压", 0));
    }

    @Test
	@DisplayName("对空字符串进行压缩成字节数组")
	@Tags({
			@Tag("@id:43"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/16")
	})
    void testCompressToString_strEmpty() {
        assertEquals("eNoDAAAAAAE=",
			StringZipUtils.compressToString(""));
    }

    @Test
	@DisplayName("对空格字符串进行压缩成字节数组")
	@Tags({
			@Tag("@id:43"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/16")
	})
    void testCompressToString_strBlank() {
        assertEquals("eNpTAAAAIQAh",
			StringZipUtils.compressToString(" "));
    }


    @Test
	@DisplayName("输入null字符串应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:43"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/16")
	})
    void testCompressToString_strNullShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> StringZipUtils.compressToString(null),
				"Input parameter is null");
    }

    @Test
	@DisplayName("对字节数组进行解压")
	@Tags({
			@Tag("@id:43"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/16")
	})
    void testUncompress_byteArray() throws Exception {
        assertEquals("由于数据传输频率高，对流量有较大影响，因此需要对大字符串" +
				"进行压缩后传输，在接收到数据后再进行解压",
			StringZipUtils.uncompress(new byte[] {120,-38,37,-115,-47,14,-63,64,16,69,127,93,35,81,-94,59,72,43,36,-35,-121,-94,
					90,66,105,37,4,89,-11,51,59,-77,-77,79,126,-63,110,-10,-7,-100,115,-81,-55,58,-3,6,90,
					-74,36,46,90,21,-36,-89,118,-69,48,16,-37,-45,-6,-89,18,-68,-66,-24,30,-39,120,70,114,-62,
					-3,16,-53,26,63,29,-90,-62,-93,-68,-96,-90,-76,114,-64,85,-28,52,-113,-102,-107,57,87,-6,
					121,-29,111,-50,-101,4,97,106,-44,17,-25,16,102,125,34,15,4,123,-54,30,56,110,-61,-93,-93,
					56,18,-63,-25,122,-25,-110,63,101,118,103,61}));
    }

    @Test
	@DisplayName("对null字节数组进行解压应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:43"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/16")
	})
    void testUncompress_nullByteArrayShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> StringZipUtils.uncompress((byte[])null),
				"Input parameter is null");
    }

    @Test
	@DisplayName("对无效字节数组进行解压应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:43"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/16")
	})
    void testUncompress_invalidByteArrayShouldThrowException() {
		assertThrows(RuntimeException.class,
				() -> StringZipUtils.uncompress(new byte[] {0x12,0x45,0x64,0x34,0x78,0x23,0x37,0x37,0x23}),
				"Input parameter is invalid");
    }


    @Test
	@DisplayName("对字符串进行解压")
	@Tags({
			@Tag("@id:43"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/16")
	})
    void testUncompress_string() throws Exception {
		assertEquals("由于数据传输频率高，对流量有较大影响，因此需要对大字符串" +
				"进行压缩后传输，在接收到数据后再进行解压",
			StringZipUtils.uncompress("eNoljdEOwUAQRX9dI1GiO0grJN2HolpCaSUEWfUzO7OzT37Bbvb5nHOvyTr9Blq2JC" +
					"5aFdyndrswENvT+qcSvL7oHtl4RnLC/RDLGj8dpsKjvKCmtHLAVeQ0j5qVOVf6eeNvzpsEYWrUEecQ" +
					"Zn0iDwR7yh44bsOjozgSwed655I/ZXZnPQ=="));
    }

    @Test
	@DisplayName("对null字符串进行解压应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:43"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/16")
	})
    void testUncompress_nullStringShouldThrowException() {
		assertThrows(IllegalArgumentException.class,
				() -> StringZipUtils.uncompress((String)null),
				"Input parameter is null");
    }

    @Test
	@DisplayName("对无效字符串进行解压应该抛出提示参数是空的IllegalArgumentException异常")
	@Tags({
			@Tag("@id:43"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/16")
	})
    void testUncompress_invalidStringShouldThrowException() {
		assertThrows(RuntimeException.class,
				() -> StringZipUtils.uncompress("12344567435254567535"),
				"Input parameter is invalid");
    }

    @Test
	@DisplayName("对大字符串进行压缩成字节数组性能测试(与CPU性能有关)")
	@Tags({
			@Tag("@id:43"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/16")
	})
    void testCompressToByteArray_bigString() {
		assertTimeoutPreemptively(ofMillis(200), ()->StringZipUtils.compressToByteArray(strBigString));
    }

    @Test
	@DisplayName("对大字符串进行压缩成字符串性能测试(与CPU性能有关)")
	@Tags({
			@Tag("@id:43"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/16")
	})
    void testCompressToString_bigString() {
		assertTimeoutPreemptively(ofMillis(1000), ()->StringZipUtils.compressToString(strBigString));
    }
	
    @Test
	@DisplayName("对大字节数组进行解压成字符串性能测试(与CPU性能有关)")
	@Tags({
			@Tag("@id:43"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/16")
	})
    void testUncompress_bigByteArray() {
		assertTimeoutPreemptively(ofMillis(200), ()->StringZipUtils.uncompress(byBigCompressByteArray));
    }

    @Test
	@DisplayName("对大字符串进行解压成字符串性能测试(与CPU性能有关)")
	@Tags({
			@Tag("@id:43"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/8/16")
	})
    void testUncompress_bigString() {
		assertTimeoutPreemptively(ofMillis(200), ()->StringZipUtils.uncompress(strBigCompressString));
    }
	
}
