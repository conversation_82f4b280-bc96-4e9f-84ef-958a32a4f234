package com.snbc.bbpf.commons.ftps.config;

/**
 * SFTP 认证方式枚举
 * 1. password 使用用户密码的方式 默认交互方式
 * 2. publickKey 使用公钥的方式
 * 3. keyboardInteractive 使用键盘交互的方式
 *
 * <AUTHOR>
 * @module FTP/SFTP客户端
 * @date 2024/8/26 16:52
 * @copyright 2024 山东新北洋信息技术股份有限公司. All rights reserved
 */
public enum SFTPAuthType {
    SFTP_AUTH_TYPE_PASSWORD("password"),
    SFTP_AUTH_TYPE_PRIVATE_KEY("publickKey"),
    SFTP_AUTH_TYPE_KEYBOARD_INTERACTIVE("keyboardInteractive");

    private final String type;

    SFTPAuthType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }
}
