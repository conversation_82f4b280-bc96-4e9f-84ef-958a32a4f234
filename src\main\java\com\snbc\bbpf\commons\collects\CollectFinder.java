package com.snbc.bbpf.commons.collects;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.function.Predicate;
import java.util.regex.Pattern;

/**
 * 类描述:查找集合中元素
 * 1. 查找集合中是否存在完全匹配的元素（包括对象和基本数据类型）
 * 2. 对于集合中有String的情况需要支持正则匹配。
 * 3. 查找集合中存在部分匹配的对象（可只对对象部分属性设置值），并返回匹配的集合。
 * 4. 需要考虑集合中包含集合的情况。
 * // 调用方法进行查找
 * List<Person> matches = CollectFinder.findPartialMatches(people, person -> person.getName().equals("Bob"));
 *
 * <AUTHOR>
 * 创建时间:  [2023/11/7 09:00]
 */
public class CollectFinder {

    /**
     * 查找集合中是否存在完全匹配的元素
     * <p>
     *     CollectFinder.existsExactMatch(collection, "test") = true
     * </p>
     * @param collection 待查找的集合
     * @param match 要匹配的元素
     * @return boolean 如果集合中存在匹配元素则返回true
     * <AUTHOR>
     * @date 2023/11/7
     * @since 1.3.0
     */
    public static <T> boolean existsExactMatch(Collection<T> collection, T match) {
        if (collection == null) {
            return false;
        }
        for (T item : collection) {
            if (item.equals(match)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 对于集合中有String的情况需要支持正则匹配
     * <p>
     *     CollectFinder.existsRegexMatch(collection, "\\d+") = true
     * </p>
     * @param collection 待查找的字符串集合
     * @param regex 要匹配的正则表达式
     * @return boolean 如果集合中存在匹配正则的元素则返回true
     * @throws PatternSyntaxException 如果正则表达式语法无效
     * <AUTHOR>
     * @date 2023/11/7
     * @since 1.3.0
     */

    public static boolean existsRegexMatch(Collection<String> collection, String regex) {
        if (collection == null) {
            return false;
        }
        Pattern pattern = Pattern.compile(regex);
        for (String item : collection) {
            if (pattern.matcher(item).matches()) {
                return true;
            }
        }
        return false;

    }


    /**
     * 查找集合中存在部分匹配的对象（可只对对象部分属性设置值），
     * 并返回匹配的集合。
     * <p>
     *     CollectFinder.findPartialMatches(people, p -> p.getAge() > 30)
     * </p>
     * @param collection 待查找的集合
     * @param predicate 部分匹配的条件
     * @return List<T> 匹配的对象集合
     * @throws IllegalArgumentException 如果predicate为null
     * <AUTHOR>
     * @date 2023/11/7
     * @since 1.3.0
     */
    public static <T> List<T> findPartialMatches(Collection<T> collection, Predicate<T> predicate) {
        if (collection == null || collection.isEmpty()) {
            return Collections.emptyList();
        }
        List<T> matches = new ArrayList<>();

        for (T obj : collection) {
            if (predicate.test(obj)) {
                matches.add(obj);
            }
        }

        return matches;
    }


}
