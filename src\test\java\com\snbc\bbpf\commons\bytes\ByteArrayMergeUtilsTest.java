/*
 * 版权所有 2009-2024山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.bytes;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.AfterAll;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
/**
 * byte数组合并处理单元测试类
 *
 * <AUTHOR>
 * @module 字节处理模块
 * @date 2024/6/7 20:30
 * @copyright 2024 山东新北洋信息技术股份有限公司. All rights reserved
 */
class ByteArrayMergeUtilsTest {

    @Test
	@DisplayName("合并两个字节数组到一个字节数组，第一个数据为null")
	@Tags({
			@Tag("@id:95"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2024/6/7")
	})
    void testMergeBytes_data1Null() {
		assertArrayEquals(new byte[] {0x02, 0x03 }, 
			ByteArrayMergeUtils.mergeBytes(null, new byte[] { 0x02, 0x03 }));
    }

    @Test
	@DisplayName("合并两个字节数组到一个字节数组，第二个数据为null")
	@Tags({
			@Tag("@id:95"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2024/6/7")
	})
    void testMergeBytes_data2Null() {
		assertArrayEquals(new byte[] {0x00, 0x01 }, 
			ByteArrayMergeUtils.mergeBytes(new byte[] {0x00, 0x01 }, null));
    }

    @Test
	@DisplayName("合并两个字节数组到一个字节数组，第二个数据为null")
	@Tags({
			@Tag("@id:95"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2024/6/7")
	})
    void testMergeBytes_data12Null() {
		assertThrows(IllegalArgumentException.class,() ->  ByteArrayMergeUtils.mergeBytes(null, null),
				"All bytes are null");
    }

    @Test
	@DisplayName("合并两个字节数组到一个字节数组，第一个数据为Empty")
	@Tags({
			@Tag("@id:95"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2024/6/7")
	})
    void testMergeBytes_data1Empty() {
		assertArrayEquals(new byte[] {0x02, 0x03 }, 
			ByteArrayMergeUtils.mergeBytes(new byte[] {}, new byte[] { 0x02, 0x03 }));
    }
	
    @Test
	@DisplayName("合并两个字节数组到一个字节数组，第二个数据为Empty")
	@Tags({
			@Tag("@id:95"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2024/6/7")
	})
    void testMergeBytes_data2Empty() {
		assertArrayEquals(new byte[] {0x02, 0x03 }, 
			ByteArrayMergeUtils.mergeBytes(new byte[] { 0x02, 0x03 }, new byte[] {}));
    }
	
    @Test
	@DisplayName("合并两个字节数组到一个字节数组，两个数据为Empty")
	@Tags({
			@Tag("@id:95"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2024/6/7")
	})
    void testMergeBytes_data12Empty() {
		assertArrayEquals(new byte[] {}, 
			ByteArrayMergeUtils.mergeBytes(new byte[] {}, new byte[] {}));
    }

    @Test
	@DisplayName("合并两个字节数组到一个字节数组")
	@Tags({
			@Tag("@id:95"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2024/6/7")
	})
    void testMergeBytes() {
		assertArrayEquals(new byte[] { 0x00, 0x01, 0x02, 0x03 }, 
			ByteArrayMergeUtils.mergeBytes(new byte[] { 0x00, 0x01 }, new byte[] { 0x02, 0x03 }));
    }

    @Test
	@DisplayName("合并0个字节数组到一个字节数组")
	@Tags({
			@Tag("@id:95"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2024/6/7")
	})
    void testMergeBytes_0Bytes() {
		assertThrows(IllegalArgumentException.class,() ->  ByteArrayMergeUtils.mergeBytes(),
				"values.length is 0");
    }

    @Test
	@DisplayName("合并3个null字节数组到一个字节数组")
	@Tags({
			@Tag("@id:95"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2024/6/7")
	})
    void testMergeBytes_allNull() {
		assertThrows(IllegalArgumentException.class,() ->  ByteArrayMergeUtils.mergeBytes(null,null,null),
				"All bytes are null");
    }

    @Test
	@DisplayName("合并1个null，2个正常字节数组到一个字节数组")
	@Tags({
			@Tag("@id:95"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2024/6/7")
	})
    void testMergeBytes_1Null() {
		assertArrayEquals(new byte[] { 0x02, 0x03, 0x04, 0x05 }, 
			ByteArrayMergeUtils.mergeBytes(null, new byte[] { 0x02, 0x03 }, new byte[] { 0x04, 0x05 }));
    }


    @Test
	@DisplayName("合并3个字节数组到一个字节数组")
	@Tags({
			@Tag("@id:95"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2024/6/7")
	})
    void testMergeBytes_3Bytes() {
		assertArrayEquals(new byte[] { 0x00, 0x01, 0x02, 0x03, 0x04, 0x05 }, 
			ByteArrayMergeUtils.mergeBytes(new byte[] { 0x00, 0x01 }, new byte[] { 0x02, 0x03 }, new byte[] { 0x04, 0x05 }));
    }

    @Test
	@DisplayName("合并5个字节数组到一个字节数组")
	@Tags({
			@Tag("@id:95"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2024/6/7")
	})
    void testMergeBytes_ManyBytes() {
		assertArrayEquals(new byte[] { 0x00, 0x03, 0x01, 0x04, 0x02, 0x03, 0x04, 0x05, 0x05, 0x11, 0x04, 0x05 }, 
			ByteArrayMergeUtils.mergeBytes(new byte[] { 0x00 }, new byte[] { 0x03, 0x01, 0x04 }, new byte[] { 0x02, 0x03 }, new byte[] { 0x04, 0x05, 0x05, 0x11 }, new byte[] { 0x04, 0x05 }));
    }
	
}

