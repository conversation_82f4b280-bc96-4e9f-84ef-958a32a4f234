/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.validations.utils;

import com.snbc.bbpf.commons.validations.annotation.ValidStringContains;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * StringContains注解验证工具类
 *
 * <AUTHOR>
 * @module 注解
 * @date 2023/8/30
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class ValidStringContainsUtilTest {

    //测试类
    private StringContainsTestObj testObjSuccess;
    private StringContainsTestObj testObjFailure;

    private StringContainsTestObj testObjValNull;

    private StringContainsTestNullObj testObjNullFailure;
    @BeforeEach
    public void init() {
        testObjSuccess = new StringContainsTestObj();
        testObjSuccess.setField1("11abc11");
        testObjSuccess.setField2("1112343");
        testObjFailure = new StringContainsTestObj();
        testObjFailure.setField1("1234");
        testObjFailure.setField2("12");

        testObjFailure = new StringContainsTestObj();
        testObjFailure.setField1("1234");
        testObjFailure.setField2("12");

        testObjValNull = new StringContainsTestObj();

        testObjNullFailure = new StringContainsTestNullObj();
        testObjNullFailure.setField1("11222");
        testObjNullFailure.setField2("3234");
    }

    @Test
    @DisplayName("验证成功")
    @Tags({
            @Tag("@id:47"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/8/30")
    })
    void testStringFieldsValidate_contains() {
        assertDoesNotThrow(() -> {
            ValidStringContainsUtil.stringFieldsValidate(testObjSuccess);
        });
    }

    @Test
    @DisplayName("验证失败")
    @Tags({
            @Tag("@id:47"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/8/30")
    })
    void testStringFieldsValidate_nocontains() {
        assertThrows(IllegalArgumentException.class,() -> {
            ValidStringContainsUtil.stringFieldsValidate(testObjFailure);
        });
    }

    @Test
    @DisplayName("验证值为null失败")
    @Tags({
            @Tag("@id:47"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/8/30")
    })
    void testStringFieldsValidate_valNull() {
        assertThrows(IllegalArgumentException.class,() -> {
            ValidStringContainsUtil.stringFieldsValidate(testObjValNull);
        });
    }

    @Test
    @DisplayName("验证值包含''成功")
    @Tags({
            @Tag("@id:47"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/8/30")
    })
    void testStringFieldsValidate_contailsNull() {
        assertDoesNotThrow(() -> {
            ValidStringContainsUtil.stringFieldsValidate(testObjNullFailure);
        });
    }

    public class StringContainsTestObj {
        @ValidStringContains(value = "abc", message = "field1不包含abc")
        private String field1;

        private String field2;

        public String getField1() {
            return field1;
        }

        public void setField1(String field1) {
            this.field1 = field1;
        }

        public String getField2() {
            return field2;
        }

        public void setField2(String field2) {
            this.field2 = field2;
        }
    }

    public class StringContainsTestNullObj {
        @ValidStringContains(value = "", message = "")
        private String field1;

        private String field2;

        public String getField1() {
            return field1;
        }

        public void setField1(String field1) {
            this.field1 = field1;
        }

        public String getField2() {
            return field2;
        }

        public void setField2(String field2) {
            this.field2 = field2;
        }
    }

}