/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.validations.validator;

import com.snbc.bbpf.commons.strings.StringEmptyCheck;
import com.snbc.bbpf.commons.validations.ValidException;

import java.util.regex.Pattern;

/**
 * 中文字符校验
 *
 * <AUTHOR>
 * @module 字符串校验模块
 * @date 2023/7/16
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class ChineseCharacterValidator implements IStringValidator {
    public static final Pattern CHINESE_PATTERN = Pattern.compile("^[\u4e00-\u9fa5]{0,}$");

    /**
     * 中文字符校验
     *
     * @param chinese 待验证的字符串
     * @param message 验证不通过时的提示信息
     * @throws ValidException
     * <AUTHOR>
     * @date 2023/7/16
     * @since 1.1.0
     */
    @Override
    public void validate(String chinese, String message) throws IllegalArgumentException {
        if (chinese == null) {
            throw new IllegalArgumentException("String is empty or malformed");
        }
        // 验证汉字格式
        if (!CHINESE_PATTERN.matcher(chinese).matches()) {
            throw new IllegalArgumentException(!StringEmptyCheck.isEmpty(message) ? message : "The string is not in Chinese character format");
        }
    }
}