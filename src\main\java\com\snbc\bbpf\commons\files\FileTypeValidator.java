/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.files;

import java.util.List;

/**
 * 类描述:    要求对文件扩展名进行校验，根据传入文件名和文件类型（我们常用的文件格式类型）.
 *
 * <AUTHOR>
 * @module 文件
 * @date 2023/4/29 9:58
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 * @since 1.2.0
 */
public class FileTypeValidator {

    /**
     * 要求对文件扩展名进行校验，根据传入文件名和文件类型（我们常用的文件格式类型）
     * System.out.println(isValidFileType("example.jpg", validFileTypes)); // true
     * System.out.println(isValidFileType("example.txt", validFileTypes)); // false
     *
     * @param fileName  文件名称
     * @param fileTypes 文件类型
     * @return 返回是否
     * <AUTHOR>
     * @date 2023/8/26
     * @since 1.2.0
     */
    public static boolean isValidFileType(String fileName, List<String> fileTypes) {
        if (fileName == null) {
            return false;
        }
        String fileExtension = getFileExtension(fileName);
        return fileTypes.contains(fileExtension);
    }


    /**
     * 获取文件后缀名的方法
     *
     * @param fileName 文件名称
     * @return 。
     * <AUTHOR>
     * @date 2023/8/26
     * @since 1.2.0
     */
    private static String getFileExtension(String fileName) {
        if (fileName == null) {
            throw new IllegalArgumentException("fileName is empty");
        }
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex >= 0) {
            return fileName.substring(dotIndex + 1);
        } else {
            return "";
        }
    }
}
