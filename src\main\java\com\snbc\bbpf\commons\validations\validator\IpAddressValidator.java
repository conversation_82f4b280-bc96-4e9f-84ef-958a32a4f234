/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.validations.validator;

import com.snbc.bbpf.commons.strings.StringEmptyCheck;
import com.snbc.bbpf.commons.validations.ValidException;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

/**
 * 验证IP地址格式
 *
 * <AUTHOR>
 * @module 字符串校验模块
 * @date 2023/7/16
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class IpAddressValidator implements IStringValidator {
    public static final Pattern IP_PATTERN = Pattern.compile("^((25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]?\\d)\\.){3}(25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]?\\d)$");

    /**
     * 验证IP地址格式
     *
     * @param ipAddress IP地址字符串
     * @param message   错误消息
     * @throws ValidException 如果IP地址为空或格式错误
     * <AUTHOR>
     * @date 2023/7/16
     * @since 1.1.0
     */
    @Override
    public void validate(String ipAddress, String message) throws IllegalArgumentException {
        if (StringUtils.isEmpty(ipAddress)) {
            throw new IllegalArgumentException("IP address is empty or null");
        }
        // 验证IP地址格式
        if (!IP_PATTERN.matcher(ipAddress).matches()) {
            throw new IllegalArgumentException(!StringEmptyCheck.isEmpty(message) ? message : "IP address format error");
        }
    }
}
