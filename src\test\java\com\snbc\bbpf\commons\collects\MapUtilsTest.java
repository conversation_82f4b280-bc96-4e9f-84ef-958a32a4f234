/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.collects;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.google.common.collect.Lists;
import com.snbc.bbpf.commons.dates.LocalDateTimeUtils;
import com.snbc.bbpf.commons.validations.annotation.ValidStringType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * 将Map和对象进行互相转换单元测试类
 *
 * <AUTHOR>
 * @module 对象处理
 * @date 2023/7/25 20:30
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class MapUtilsTest {
    ContainerInfo containerInfo;
    Map<String, Object> map = new HashMap();
    final LocalDateTime now = LocalDateTime.of(2023, 9, 5, 15, 30, 45, 0);
    HasChildMapObject testHasChildMapObject1 = new HasChildMapObject("1", 1);
    HasChildMapObject testHasChildMapObject2 = new HasChildMapObject("2", 2);

    @BeforeEach
    void setup() {

        containerInfo = new ContainerInfo(12,
                "新北洋1号售货机",
                LocalDateTimeUtils.toDate(now)
                ,
                now, null, null, LocalDateTimeUtils.toDate(now));
    }

    @Test
    @DisplayName("正常将对象转成Map")
    @Tags({
            @Tag("@id:29"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/7/25")
    })
    void objectToMap() {
        TestMapObject obj = new TestMapObject("123", 123);
        Map<String, Object> result = MapUtils.objectToMap(obj);

        Map<String, String> map = new HashMap<>();
        map.put("data1", "1");
        map.put("data2", "2");

        assertEquals(result.get("str"), "123");
        assertEquals(result.get("itNumber"), 123);
        assertEquals(result.get("map"), map);
    }

    @Test
    @DisplayName("空对象转成Map")
    @Tags({
            @Tag("@id:29"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/7/25")
    })
    void objectToMap_isNull() {
        Map<String, Object> result = MapUtils.objectToMap(null);
        assertEquals(result, Collections.emptyMap());
    }

    @Test
    @DisplayName("将对象转成Map 含有父级对象")
    @Tags({
            @Tag("@id:29"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/7/25")
    })
    void objectToMap_hasparent() {
        TestHasChildMapObject obj = new TestHasChildMapObject("123", 123);
        Map<String, Object> result = MapUtils.objectToMap(obj);

        TestMapObject cobj = new TestMapObject("456", 789);
        Map<String, Object> cresult = MapUtils.objectToMap(cobj);


        assertEquals(result.get("str"), "123");
        assertEquals(result.get("itNumber"), 123);
        assertEquals(result.get("testMapObject"), cresult);
    }

    @Test
    @DisplayName("正常将Map转成对象")
    @Tags({
            @Tag("@id:29"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/7/25")
    })
    void mapToClass() {
        Map<String, Object> testMap = new HashMap<>();
        Map<String, String> map = new HashMap<>();
        map.put("data2", "2");
        map.put("data1", "1");
        testMap.put("str", "123");
        testMap.put("itNumber", 123);
        testMap.put("map", map);
        TestMapObject result = MapUtils.mapToClass(testMap, TestMapObject.class);
        TestMapObject obj = new TestMapObject("123", 123);
        assertEquals(result.getStr(), obj.getStr());
        assertEquals(result.getItNumber(), obj.getItNumber());
        assertEquals(result.getMap(), obj.getMap());
    }

    @Test
    @DisplayName("将Map转成对象 MAP是空对象")
    @Tags({
            @Tag("@id:29"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/7/25")
    })
    void mapToClass_mapisnull() {
        TestMapObject result = MapUtils.mapToClass(null, TestMapObject.class);
        assertEquals(result, null);
    }

    @Test
    @DisplayName("将Map转成对象 obj是空对象")
    @Tags({
            @Tag("@id:29"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/7/25")
    })
    void mapToClass_objisnull() {
        Map<String, Object> obj = new HashMap<>();
        obj.put("data2", "2");
        obj.put("data1", "1");
        TestMapObject result = MapUtils.mapToClass(obj, null);
        assertEquals(result, null);
    }

    @Test
    @DisplayName("将Map转成对象 所有均是空对象")
    @Tags({
            @Tag("@id:29"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/7/25")
    })
    void mapToClass_allisnull() {
        TestMapObject result = MapUtils.mapToClass(null, null);
        assertEquals(result, null);
    }

    @Test
    @DisplayName("将Map转成对象  缺少部分属性")
    @Tags({
            @Tag("@id:29"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/7/25")
    })
    void mapToClass_litpro() {
        Map<String, Object> testMap = new HashMap<>();
        Map<String, String> map = new HashMap<>();
        map.put("data2", "2");
        map.put("data1", "1");
        testMap.put("str", "123");
        testMap.put("map", map);
        TestMapObject result = MapUtils.mapToClass(testMap, TestMapObject.class);
        TestMapObject obj = new TestMapObject("123", 123);
        assertEquals(result.getStr(), obj.getStr());
        assertEquals(result.getItNumber(), 0);
        assertEquals(result.getMap(), obj.getMap());
    }

    @Test
    @DisplayName("将Map转成对象  增加部分属性")
    @Tags({
            @Tag("@id:29"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/7/25")
    })
    void mapToClass_morepro() {
        Map<String, Object> testMap = new HashMap<>();
        Map<String, String> map = new HashMap<>();
        map.put("data2", "2");
        map.put("data1", "1");
        testMap.put("str", "123");
        testMap.put("itNumber", 123);
        testMap.put("i22tNumber", 123);
        testMap.put("map", map);
        TestMapObject result = MapUtils.mapToClass(testMap, TestMapObject.class);
        TestMapObject obj = new TestMapObject("123", 123);
        assertEquals(result.getStr(), obj.getStr());
        assertEquals(result.getItNumber(), obj.getItNumber());
        assertEquals(result.getMap(), obj.getMap());
    }

    @Test
    @DisplayName("将Map转成对象  含有父级对象")
    @Tags({
            @Tag("@id:29"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/7/25")
    })
    void mapToClass_hasParent() {
        Map<String, Object> testMap = new HashMap<>();
        Map<String, String> map = new HashMap<>();
        map.put("data2", "2");
        map.put("data1", "1");
        testMap.put("str", "456666");
        testMap.put("itNumber", 789666);
        testMap.put("map", map);
        Map<String, Object> ptestMap = new HashMap<>();
        ptestMap.put("str", "12333");
        ptestMap.put("itNumber", 12333);
        ptestMap.put("testMapObject", testMap);

        TestHasChildMapObject result = MapUtils.mapToClass(ptestMap, TestHasChildMapObject.class);

        TestHasChildMapObject destObj = new TestHasChildMapObject("12333", 12333);
        destObj.setTestMapObject(new TestMapObject("456666", 789666));
        assertEquals(result.getStr(), destObj.getStr());
        assertEquals(result.getItNumber(), destObj.getItNumber());
        assertEquals(result.getTestMapObject().getMap(), destObj.getTestMapObject().getMap());
        assertEquals(result.getTestMapObject().getStr(), destObj.getTestMapObject().getStr());
        assertEquals(result.getTestMapObject().getItNumber(), destObj.getTestMapObject().getItNumber());
    }


    @Test
    @DisplayName("正常将Map转成对象 严格模式")
    @Tags({
            @Tag("@id:29"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/7/25")
    })
    void mapToClassStrict() {
        Map<String, Object> testMap = new HashMap<>();
        Map<String, String> map = new HashMap<>();
        map.put("data2", "2");
        map.put("data1", "1");
        testMap.put("str", "123");
        testMap.put("itNumber", 123);
        testMap.put("map", map);
        TestMapObject result = MapUtils.mapToClassStrict(testMap, TestMapObject.class);
        TestMapObject obj = new TestMapObject("123", 123);
        assertEquals(result.getStr(), obj.getStr());
        assertEquals(result.getItNumber(), obj.getItNumber());
        assertEquals(result.getMap(), obj.getMap());
    }

    @Test
    @DisplayName("将Map转成对象 严格模式 MAP是空对象")
    @Tags({
            @Tag("@id:29"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/7/25")
    })
    void mapToClassStrict_mapisnull() {
        TestMapObject result = MapUtils.mapToClassStrict(null, TestMapObject.class);
        assertEquals(result, null);
    }

    @Test
    @DisplayName("将Map转成对象 严格模式 obj是空对象")
    @Tags({
            @Tag("@id:29"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/7/25")
    })
    void mapToClassStrict_objisnull() {
        Map<String, Object> obj = new HashMap<>();
        obj.put("data2", "2");
        obj.put("data1", "1");
        TestMapObject result = MapUtils.mapToClassStrict(obj, null);
        assertEquals(result, null);
    }

    @Test
    @DisplayName("将Map转成对象 严格模式 所有均是空对象")
    @Tags({
            @Tag("@id:29"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/7/25")
    })
    void mapToClassStrict_allisnull() {
        TestMapObject result = MapUtils.mapToClassStrict(null, null);
        assertEquals(result, null);
    }

    @Test
    @DisplayName("将Map转成对象  严格模式 缺少部分属性")
    @Tags({
            @Tag("@id:29"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/7/25")
    })
    void mapToClassStrict_litpro() {
        Map<String, Object> testMap = new HashMap<>();
        Map<String, String> map = new HashMap<>();
        map.put("data2", "2");
        map.put("data1", "1");
        testMap.put("str", "123");
        testMap.put("map", map);
        Exception e = assertThrows(RuntimeException.class,
                () -> MapUtils.mapToClassStrict(testMap, TestMapObject.class));
        assertEquals(e.getMessage(), "Inconsistent properties of the conversion object");

    }

    @Test
    @DisplayName("将Map转成对象  严格模式 增加部分属性")
    @Tags({
            @Tag("@id:29"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/7/25")
    })
    void mapToClassStrict_morepro() {
        Map<String, Object> testMap = new HashMap<>();
        Map<String, String> map = new HashMap<>();
        map.put("data2", "2");
        map.put("data1", "1");
        testMap.put("str", "123");
        testMap.put("itNumber", 123);
        testMap.put("i22tNumber", 123);
        testMap.put("map", map);
        Exception e = assertThrows(RuntimeException.class,
                () -> MapUtils.mapToClassStrict(testMap, TestMapObject.class));
        assertEquals(e.getMessage(), "Inconsistent properties of the conversion object");

    }

    @Test
    @DisplayName("将Map转成对象  严格模式 含有父级对象")
    @Tags({
            @Tag("@id:29"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/7/25")
    })
    void mapToClassStrict_hasParent() {
        Map<String, Object> testMap = new HashMap<>();
        Map<String, String> map = new HashMap<>();
        map.put("data2", "2");
        map.put("data1", "1");
        testMap.put("str", "456666");
        testMap.put("itNumber", 789666);
        testMap.put("map", map);
        Map<String, Object> ptestMap = new HashMap<>();
        ptestMap.put("str", "12333");
        ptestMap.put("itNumber", 12333);
        ptestMap.put("testMapObject", testMap);

        TestHasChildMapObject result = MapUtils.mapToClassStrict(ptestMap, TestHasChildMapObject.class);

        TestHasChildMapObject destObj = new TestHasChildMapObject("12333", 12333);
        destObj.setTestMapObject(new TestMapObject("456666", 789666));
        assertEquals(result.getStr(), destObj.getStr());
        assertEquals(result.getItNumber(), destObj.getItNumber());
        assertEquals(result.getTestMapObject().getMap(), destObj.getTestMapObject().getMap());
        assertEquals(result.getTestMapObject().getStr(), destObj.getTestMapObject().getStr());
        assertEquals(result.getTestMapObject().getItNumber(), destObj.getTestMapObject().getItNumber());
    }


    @Test
    @DisplayName("空map转为json")
    @Tags({
            @Tag("@id:53"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/19")
    })
    void testMapToJson_emptyMapToJson() {
        assertEquals(null, MapUtils.mapToJson(new HashMap<>()));
    }

    @Test
    @DisplayName("value值为空转为json")
    @Tags({
            @Tag("@id:53"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/19")
    })
    void testMapToJson_nullValueMapToJson() {
        map.put("", null);
        assertEquals(null, MapUtils.mapToJson(map));
    }

    @Test
    @DisplayName("嵌套map转为json")
    @Tags({
            @Tag("@id:53"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/19")
    })
    void testMapToJson_nestedMapToJson() {

        Map<String, String> map1 = new HashMap<>();
        map1.put("map1_str2", "2");
        map1.put("map1_str1", "1");
        Map<String, Object> map2 = new HashMap<>();
        map2.put("map2_str", "22str");
        map2.put("map2_itNumber", 2222);
        map2.put("map2_map", map1);
        Map<String, Object> map3 = new HashMap<>();
        map3.put("map3_str", "33str");
        map3.put("map3_itNumber", 333);
        map3.put("map3_map", map2);
        assertEquals("{\"map3_map\":{\"map2_str\":\"22str\",\"map2_map\":{\"map1_str2\":\"2\",\"map1_str1\":\"1\"},\"map2_itNumber\":2222},\"map3_itNumber\":333,\"map3_str\":\"33str\"}", MapUtils.mapToJson(map3));

    }

    @Test
    @DisplayName("value为list的map转为json")
    @Tags({
            @Tag("@id:53"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/19")
    })
    void testMapToJson_listMapToJson() {
        map.put("list1", Lists.newArrayList("1", "2", "3"));
        assertEquals("{\"list1\":[\"1\",\"2\",\"3\"]}", MapUtils.mapToJson(map));

    }

    @Test
    @DisplayName("value为JavaBean的map转为json")
    @Tags({
            @Tag("@id:53"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/19")
    })
    void testMapToJson_objMapToJson() {

        map.put("data1", testHasChildMapObject1);
        map.put("data2", testHasChildMapObject2);
        String str = "{\"data2\":{\"str\":\"2\",\"itNumber\":2,\"testMapObject\":{\"str\":\"456\",\"itNumber\":789,\"map\":{\"data2\":\"2\",\"data1\":\"1\"}}},\"data1\":{\"str\":\"1\",\"itNumber\":1,\"testMapObject\":{\"str\":\"456\",\"itNumber\":789,\"map\":{\"data2\":\"2\",\"data1\":\"1\"}}}}";
        assertEquals(str, MapUtils.mapToJson(map));
    }

    @Test
    @DisplayName("value为enum的map转为json")
    @Tags({
            @Tag("@id:53"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/19")
    })
    void testMapToJson_enumMapToJson() {
        map.put("REGEX", ValidStringType.REGEX);
        map.put("PHONE_NUMBER", ValidStringType.PHONE_NUMBER);
        assertEquals("{\"REGEX\":\"REGEX\",\"PHONE_NUMBER\":\"PHONE_NUMBER\"}", MapUtils.mapToJson(map));

    }

    @Test
    @DisplayName("value为各种类型的map转为json")
    @Tags({
            @Tag("@id:53"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/19")
    })
    void testMapToJson_fixTypeToJson() {
        map.put("date", LocalDateTimeUtils.toDate(now));
        map.put("obj", testHasChildMapObject1);
        map.put("list", Lists.newArrayList(testHasChildMapObject1));
        map.put("enum", ValidStringType.REGEX);
        Map<String, String> map1 = new HashMap<>();
        map1.put("key", "value");
        map.put("map", map1);
        map.put("string", "str");
        map.put("int", 20);
        assertEquals("{\"date\":1693899045000,\"string\":\"str\",\"obj\":{\"str\":\"1\",\"itNumber\":1,\"testMapObject\":{\"str\":\"456\",\"itNumber\":789,\"map\":{\"data2\":\"2\",\"data1\":\"1\"}}},\"list\":[{\"str\":\"1\",\"itNumber\":1,\"testMapObject\":{\"str\":\"456\",\"itNumber\":789,\"map\":{\"data2\":\"2\",\"data1\":\"1\"}}}],\"map\":{\"key\":\"value\"},\"enum\":\"REGEX\",\"int\":20}", MapUtils.mapToJson(map));

    }

    @Test
    @DisplayName("value为属性未初始化的Bean转为json")
    @Tags({
            @Tag("@id:53"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/19")
    })
    void testMapToJson_emptyValueBeanMapToJson() {
        map.put("bean", new HasChildMapObject());
        assertEquals("{\"bean\":{\"str\":null,\"itNumber\":0,\"testMapObject\":{\"str\":null,\"itNumber\":0,\"map\":null}}}", MapUtils.mapToJson(map));

    }

    @Test
    @DisplayName("含有注解的方式转为json")
    @Tags({
            @Tag("@id:53"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/19")
    })
    void testMapToJson_jaksonAnnotion() {
        map.put("annotion", containerInfo);
        assertEquals("{\"annotion\":{\"id\":12,\"name\":\"新北洋1号售货机\",\"createTime\":\"2023-09-05 15:30:45\",\"updateTime\":\"2023-09-05 15:30:45\",\"mark\":null,\"tags\":null,\"markDate\":1693899045000}}", MapUtils.mapToJson(map));

    }

    @Test
    @DisplayName("使用自定义特性，空map转为json")
    @Tags({
            @Tag("@id:53"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/19")
    })
    void testMapToJson_emptyMapToJsonSelfFeature() {
        assertEquals(null, MapUtils.mapToJson(new HashMap<>(), null, null));
    }

    @Test
    @DisplayName("使用自定义特性，value值为空转为json")
    @Tags({
            @Tag("@id:53"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/19")
    })
    void testMapToJson_nullValueMapToJsonSelfFeature() {
        map.put("", null);
        assertEquals(null, MapUtils.mapToJson(map, null, null));
    }

    @Test
    @DisplayName("使用自定义特性，嵌套map转为json")
    @Tags({
            @Tag("@id:53"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/19")
    })
    void testMapToJson_nestedMapToJsonSelfFeature() {

        Map<String, String> map1 = new HashMap<>();
        map1.put("map1_str2", "2");
        map1.put("map1_str1", "1");
        Map<String, Object> map2 = new HashMap<>();
        map2.put("map2_str", "22str");
        map2.put("map2_itNumber", 2222);
        map2.put("map2_map", map1);
        Map<String, Object> map3 = new HashMap<>();
        map3.put("map3_str", "33str");
        map3.put("map3_itNumber", 333);
        map3.put("map3_map", map2);
        assertEquals("{\"map3_map\":{\"map2_str\":\"22str\",\"map2_map\":{\"map1_str2\":\"2\",\"map1_str1\":\"1\"},\"map2_itNumber\":2222},\"map3_itNumber\":333,\"map3_str\":\"33str\"}", MapUtils.mapToJson(map3, null, null));

    }

    @Test
    @DisplayName("使用自定义特性，value为list的map转为json")
    @Tags({
            @Tag("@id:53"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/19")
    })
    void testMapToJson_listMapToJsonSelfFeature() {
        map.put("list1", Lists.newArrayList("1", "2", "3"));
        assertEquals("{\"list1\":[\"1\",\"2\",\"3\"]}", MapUtils.mapToJson(map, null, null));

    }

    @Test
    @DisplayName("使用自定义特性，value为JavaBean的map转为json")
    @Tags({
            @Tag("@id:53"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/19")
    })
    void testMapToJson_objMapToJsonSelfFeature() {

        map.put("data1", testHasChildMapObject1);
        map.put("data2", testHasChildMapObject2);
        String str = "{\"data2\":{\"str\":\"2\",\"itNumber\":2,\"testMapObject\":{\"str\":\"456\",\"itNumber\":789,\"map\":{\"data2\":\"2\",\"data1\":\"1\"}}},\"data1\":{\"str\":\"1\",\"itNumber\":1,\"testMapObject\":{\"str\":\"456\",\"itNumber\":789,\"map\":{\"data2\":\"2\",\"data1\":\"1\"}}}}";
        assertEquals(str, MapUtils.mapToJson(map, null, null));
    }

    @Test
    @DisplayName("使用自定义特性，value为enum的map转为json，输出索引")
    @Tags({
            @Tag("@id:53"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/19")
    })
    void testMapToJson_enumMapToJsonSelfFeature() {
        final ArrayList<SerializationFeature> enablesFeatures = Lists.newArrayList(SerializationFeature.WRITE_ENUMS_USING_INDEX);

        map.put("REGEX", ValidStringType.REGEX);
        map.put("PHONE_NUMBER", ValidStringType.PHONE_NUMBER);
        assertEquals("{\"REGEX\":7,\"PHONE_NUMBER\":5}", MapUtils.mapToJson(map, enablesFeatures, null));

    }

    @Test
    @DisplayName("使用自定义特性，value为各种类型的map转为json")
    @Tags({
            @Tag("@id:53"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/19")
    })
    void testMapToJson_fixTypeToJsonSelfFeature() {
        map.put("date", LocalDateTimeUtils.toDate(now));
        map.put("obj", testHasChildMapObject1);
        map.put("list", Lists.newArrayList(testHasChildMapObject1));
        map.put("enum", ValidStringType.REGEX);
        Map<String, String> map1 = new HashMap<>();
        map1.put("key", "value");
        map.put("map", map1);
        map.put("string", "str");
        map.put("int", 20);
        assertEquals("{\"date\":1693899045000,\"string\":\"str\",\"obj\":{\"str\":\"1\",\"itNumber\":1,\"testMapObject\":{\"str\":\"456\",\"itNumber\":789,\"map\":{\"data2\":\"2\",\"data1\":\"1\"}}},\"list\":[{\"str\":\"1\",\"itNumber\":1,\"testMapObject\":{\"str\":\"456\",\"itNumber\":789,\"map\":{\"data2\":\"2\",\"data1\":\"1\"}}}],\"map\":{\"key\":\"value\"},\"enum\":\"REGEX\",\"int\":20}", MapUtils.mapToJson(map, null, null));

    }
}

class TestHasChildMapObject {
    private String str;
    private int itNumber;
    private TestMapObject testMapObject;

    public TestHasChildMapObject() {
        this.testMapObject = new TestMapObject();
    }

    public TestHasChildMapObject(String str, int tNumber) {
        this.str = str;
        this.itNumber = tNumber;
        this.testMapObject = new TestMapObject("456", 789);
        testMapObject.setMap(new HashMap<>());
        testMapObject.getMap().put("data1", "1");
        testMapObject.getMap().put("data2", "2");
    }

    public TestMapObject getTestMapObject() {
        return testMapObject;
    }

    public void setTestMapObject(TestMapObject testMapObject) {
        this.testMapObject = testMapObject;
    }

    public int getItNumber() {
        return itNumber;
    }

    public void setItNumber(int itNumber) {
        this.itNumber = itNumber;
    }

    public String getStr() {
        return str;
    }

    public void setStr(String str) {
        this.str = str;
    }
}

class TestMapObject {
    private String str;
    private int itNumber;
    private Map<String, String> map;

    public TestMapObject() {
    }

    public TestMapObject(String str, int tNumber) {
        this.str = str;
        this.itNumber = tNumber;
        map = new HashMap<>();
        map.put("data1", "1");
        map.put("data2", "2");
    }

    public String getStr() {
        return str;
    }

    public void setStr(String str) {
        this.str = str;
    }

    public int getItNumber() {
        return itNumber;
    }

    public void setItNumber(int itNumber) {
        this.itNumber = itNumber;
    }

    public Map<String, String> getMap() {
        return map;
    }

    public void setMap(Map<String, String> map) {
        this.map = map;
    }
}

class ContainerInfo {
    private Integer id;
    /**
     * 售货机名称
     */
    private String name;
    /**
     * 售货机创建时间
     * 如果项目使用 spring.jackson.time-zone=Asia/Shanghai
     * spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createTime;
    /**
     * 售货机更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    private String mark;

    private List<String> tags;

    private Date markDate;

    public ContainerInfo() {
    }

    public ContainerInfo(Integer id, String name, Date createTime, LocalDateTime updateTime, String mark, List<String> tags, Date markDate) {
        this.id = id;
        this.name = name;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.mark = mark;
        this.tags = tags;
        this.markDate = markDate;
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getMark() {
        return mark;
    }

    public void setMark(String mark) {
        this.mark = mark;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }


    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public Date getMarkDate() {
        return markDate;
    }

    public void setMarkDate(Date markDate) {
        this.markDate = markDate;
    }

    @Override
    public String toString() {
        return "ContainerInfo{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", mark='" + mark + '\'' +
                ", tags=" + tags +
                ", markDate=" + markDate +
                '}';
    }

}