/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.objs;

import static java.time.Duration.ofMillis;
import static org.junit.jupiter.api.Assertions.assertTimeoutPreemptively;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotSame;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Tag;

import java.util.Map;
import java.util.HashMap;


/**
 * 深拷贝对象单元测试类
 *
 * <AUTHOR>
 * @module 对象处理
 * @date 2023/6/9 20:30
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class ObjectDeepCopyTest {

    private final int ONE_MILLION_BITS = 1024 * 1024;

    public class TestObject {
        public String str;
        public int iNumber;
        public Map<String,String> map;

        public TestObject(String str, int iNumber) {
            this.str = str;
            this.iNumber = iNumber;
            map=new HashMap<>();
            map.put("data1", "1");
            map.put("data2", "2");
        }
    }

    public class TestObject2 {
        public String str;
        public long lNumber;
        public TestObject obj;

        public TestObject2(String str, long lNumber) {
            this.str = str;
            this.lNumber = lNumber;
            obj = new TestObject("Test", 2);
        }
    }

    @Test
    @DisplayName("深拷贝对象，不限制对象大小")
    @Tags({
            @Tag("@id:32"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/6/9")
    })
    void testDeepCopy_noLimit() {
        TestObject obj = new TestObject("123", 123);
        TestObject result = (TestObject)ObjectDeepCopy.deepCopy(obj, 0);
		assertNotSame(obj, result);
		
		assertEquals(obj.str, result.str);
		assertEquals(obj.iNumber, result.iNumber);
		assertEquals(obj.map.get("data1"), result.map.get("data1"));
		
        result.str = "456";
        result.iNumber = 456;
        result.map.put("data1", "r1");
		assertNotEquals(obj.str, result.str);
		assertNotEquals(obj.iNumber, result.iNumber);
		assertNotEquals(obj.map.get("data1"), result.map.get("data1"));
    }

    @Test
    @DisplayName("深拷贝empty对象")
    @Tags({
            @Tag("@id:32"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/6/9")
    })
    void testDeepCopy_emptyObj() {
        String obj = "";
        String result = (String)ObjectDeepCopy.deepCopy(obj, 0);
		assertNotSame(obj, result);	
		assertEquals(obj, result);
		
		//修改拷贝后的对象
        result = "456";
		assertNotEquals(obj, result);
    }

    @Test
    @DisplayName("深拷贝对象，对象大小超过限制1M，应该抛出提示对象太大的IllegalArgumentException异常")
    @Tags({
            @Tag("@id:32"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/6/9")
    })
    void testDeepCopy_limit1M_shouldThrowExceptionForSizeTooBig() {
		assertThrows(IllegalArgumentException.class,
				() -> ObjectDeepCopy.deepCopy(new byte[ONE_MILLION_BITS], 1),
				"Object size is too big!");
    }

    @Test
    @DisplayName("深拷贝1M对象性能测试")
    @Tags({
            @Tag("@id:32"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/6/9")
    })
    void testDeepCopy_bigBytesPerformance() {
        assertTimeoutPreemptively(ofMillis(1000), ()->ObjectDeepCopy.deepCopy(new byte[ONE_MILLION_BITS], 2));
    }

    @Test
    @DisplayName("深拷贝null对象应该抛出提示对象为null的IllegalArgumentException异常")
    @Tags({
            @Tag("@id:32"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/6/9")
    })
    void testDeepCopy_shouldThrowExceptionForNull() {   
		assertThrows(IllegalArgumentException.class,
				() -> ObjectDeepCopy.deepCopy(null, 0),
				"Object is null!");
    }

    @Test
    @DisplayName("深拷贝对象，限制对象大小8M")
    @Tags({
            @Tag("@id:32"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/6/9")
    })
    void testDeepCopy_limit8M() {
        TestObject2 obj = new TestObject2("123", 123);
        TestObject2 result = (TestObject2)ObjectDeepCopy.deepCopy(obj);
		assertNotSame(obj, result);
		
		assertEquals(obj.str, result.str);
		assertEquals(obj.lNumber, result.lNumber);
		assertEquals(obj.obj.map.get("data1"), result.obj.map.get("data1"));

		//修改拷贝后的对象
        result.str = "456";
        result.lNumber = 456;
        result.obj.map.put("data1", "r1");
		assertNotEquals(obj.str, result.str);
		assertNotEquals(obj.lNumber, result.lNumber);
		assertNotEquals(obj.obj.map.get("data1"), result.obj.map.get("data1"));
    }

}

