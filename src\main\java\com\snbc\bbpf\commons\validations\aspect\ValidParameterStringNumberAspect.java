/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.validations.aspect;

import com.snbc.bbpf.commons.strings.StringDigitCheck;
import com.snbc.bbpf.commons.strings.StringEmptyCheck;
import com.snbc.bbpf.commons.validations.annotation.ValidStringNumber;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;

/**
 * 字符串是否为整数或浮点数注解的方法参数的切面类
 *
 * <AUTHOR>
 * @module
 * @date 2023/10/30 13:57
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
@Aspect
public class ValidParameterStringNumberAspect {
    //扫描所有配置@ValidStringNumber注解的方法
    @Pointcut("execution(@com.snbc.bbpf.commons.validations.annotation.ValidStringNumber *  *(..))")
    public void myPointcut() {
    }

    /*
     * 方法的环绕切面（对配置字符串是否为整数或浮点数注解的方法参数进行校验）
     *
     * @param joinPoint
     * @return Object
     * @since 1.3.0
     * <AUTHOR>
     * @date 2023/10/30
     */
    @Around("myPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {

        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Parameter[] parameters = method.getParameters();
        // 所有的参数
        Object[] args = joinPoint.getArgs();
        // 校验每个参数
        for (int i = 0; i < parameters.length; i++) {
            Parameter parameter = parameters[i];
            // 获取参数注解
            ValidStringNumber anno = parameter.getAnnotation(ValidStringNumber.class);
            // 存在@ValidStringNumber，忽略
            if (null == anno) {
                continue;
            }
            if (args[i] == null) {
                throw new IllegalArgumentException(StringEmptyCheck.isEmpty(anno.message()) ?
                        ("method[" + joinPoint.getSignature().getName() + "]parameter[" + parameter.getName() + "]can't be null") : anno.message());
            }
            if (!StringDigitCheck.isFloat((String) args[i])) {
                throw new IllegalArgumentException(StringEmptyCheck.isEmpty(anno.message()) ?
                        ("method[" + joinPoint.getSignature().getName() + "]parameter[" + parameter.getName() + "]number check error") : anno.message());
            }
        }
        return joinPoint.proceed();
    }

}
