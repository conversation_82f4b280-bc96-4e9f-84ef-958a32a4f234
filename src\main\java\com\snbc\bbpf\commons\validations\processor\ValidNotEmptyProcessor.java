/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.validations.processor;

import com.google.auto.service.AutoService;
import com.snbc.bbpf.commons.validations.annotation.ValidNotEmpty;

import javax.annotation.processing.AbstractProcessor;
import javax.annotation.processing.Messager;
import javax.annotation.processing.ProcessingEnvironment;
import javax.annotation.processing.Processor;
import javax.annotation.processing.RoundEnvironment;
import javax.lang.model.SourceVersion;
import javax.lang.model.element.Element;
import javax.lang.model.element.ElementKind;
import javax.lang.model.element.TypeElement;
import javax.tools.Diagnostic;
import java.util.LinkedHashSet;
import java.util.Set;

/**
 * 非空验证注解处理器
 * 对属性和方法参数上的非空验证注解校验，编译时检查注解使用是否正确
 *
 * <AUTHOR>
 * @module
 * @date 2023/9/21 9:52
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
@AutoService(Processor.class)
public class ValidNotEmptyProcessor extends AbstractProcessor {
    private Messager messager;

    @Override
    public synchronized void init(ProcessingEnvironment processingEnv) {
        super.init(processingEnv);
        messager = processingEnv.getMessager();
    }

    @Override
    public boolean process(Set<? extends TypeElement> annotations, RoundEnvironment roundEnv) {
        Set<? extends Element> elementsAnnotatedWith = roundEnv.getElementsAnnotatedWith(ValidNotEmpty.class);
        for (Element element : elementsAnnotatedWith) {

            if (element.getKind() == ElementKind.METHOD) {
                continue;
            }
            if (element.getKind() != ElementKind.FIELD && element.getKind() != ElementKind.PARAMETER) {
                messager.printMessage(
                        Diagnostic.Kind.ERROR,
                        String.format("Only FIELD or PARAMETER can be annotated with @%s", ValidNotEmpty.class.getSimpleName()),
                        element);

            }
        }
        return true;
    }

    @Override
    public Set<String> getSupportedAnnotationTypes() {
        Set<String> annotataions = new LinkedHashSet<>();
        annotataions.add(ValidNotEmpty.class.getCanonicalName());
        return annotataions;
    }

    @Override
    public SourceVersion getSupportedSourceVersion() {
        return SourceVersion.latestSupported();
    }
}
