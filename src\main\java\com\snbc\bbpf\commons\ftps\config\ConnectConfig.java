/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.ftps.config;

import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;

/**
 * 【数据传输】 连接服务端参数
 *
 * <AUTHOR>
 * @module 数据传输
 * @date 2023/9/24 17:50
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class ConnectConfig {
    //默认连接超时时间 3s
    private static final long DEFAULT_CONNECT_TIMEOUT = 3000L;
    //默认传输超时时间 默认不超时
    private static final long DEFAULT_TRANSFER_TIMEOUT = -1L;
    //默认编码格式
    private static final String DEFAULT_ENCODING = StandardCharsets.UTF_8.displayName();
    //主机地址
    private String host;
    //端口号
    private int port;
    //用户名
    private String userName;
    //密码
    private String password;
    //连接超时
    private long connectTimeout;
    //数据传输超时
    private long transferTimeout;
    //编码
    private String encoding;
    //数据传输类型
    private String transferFileType;
    //ftp模式 分主动和被动模式
    private String mode;
    //SFTP 使用的密钥文件路径
    private String privateKeyFilePath ;
    //SFTP 认证方式
    private SFTPAuthType authType = SFTPAuthType.SFTP_AUTH_TYPE_PASSWORD;

    protected ConnectConfig(Builder builder) {
        this.host = builder.host;
        this.port = builder.port;
        this.userName = builder.userName;
        this.password = builder.password;
        this.connectTimeout = builder.connectTimeout;
        this.transferTimeout = builder.transferTimeout;
        this.encoding = builder.encoding;
        this.transferFileType = builder.transferFileType;
        this.mode = builder.mode;
        this.privateKeyFilePath = builder.privateKeyFilePath;
        this.authType = builder.authType;
    }

    public String getHost() {
        return host;
    }

    public int getPort() {
        return port;
    }

    public String getUserName() {
        return userName;
    }

    public String getPassword() {
        return password;
    }

    public long getConnectTimeout() {
        return connectTimeout;
    }

    public long getTransferTimeout() {
        return transferTimeout;
    }

    public String getEncoding() {
        return encoding;
    }

    public String getTransferFileType() {
        return transferFileType;
    }

    public String getMode() {
        return mode;
    }

    public String getPrivateKeyFilePath() {
        return privateKeyFilePath;
    }

    public SFTPAuthType getAuthType() {
        return authType;
    }

    public boolean isValid() {
        return !StringUtils.isEmpty(this.host)&&!StringUtils.isEmpty(this.userName)&&!StringUtils.isEmpty(this.password)
                &&this.port>0;
    }

    public static class Builder{
        private String host;
        private int port;
        private String userName;
        private String password;
        private long connectTimeout = DEFAULT_CONNECT_TIMEOUT;

        private long transferTimeout = DEFAULT_TRANSFER_TIMEOUT;
        private String encoding = DEFAULT_ENCODING;

        private String transferFileType = FileTransferType.FILE_TRANSFER_TYPE_BINARY.getType();

        private String mode = FtpMode.POSITIVE_MODE.getMode();

        private String privateKeyFilePath ;
        private SFTPAuthType authType = SFTPAuthType.SFTP_AUTH_TYPE_PASSWORD;

        public Builder host(String host){
            this.host = host;
            return this;
        }

        public Builder port(int port){
            this.port = port;
            return this;
        }

        public Builder userName(String userName){
            this.userName = userName;
            return this;
        }

        public Builder password(String password){
            this.password = password;
            return this;
        }


        public Builder connectTimeout(long connectTimeout){
            this.connectTimeout = connectTimeout;
            return this;
        }

        public Builder transferTimeout(long transferTimeout){
            this.transferTimeout = transferTimeout;
            return this;
        }

        public Builder encoding(String charset){
            this.encoding = charset;
            return this;
        }

        public Builder transferFileType(String transferFileType){
            this.transferFileType = transferFileType;
            return this;
        }

        public Builder mode(String mode){
            this.mode = mode;
            return this;
        }

        public Builder privateKeyFilePath(String privateKeyFilePath){
            this.privateKeyFilePath = privateKeyFilePath;
            return this;
        }

        public Builder authType(SFTPAuthType authType){
            this.authType = authType;
            return this;
        }

        public ConnectConfig build(){
            return new ConnectConfig(this);
        }
    }
}
