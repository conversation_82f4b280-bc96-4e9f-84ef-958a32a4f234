package com.snbc.bbpf.commons.files;

import com.snbc.bbpf.commons.strings.StringEmptyCheck;
import com.snbc.bbpf.commons.system.SystemInfoUtil;
import com.snbc.bbpf.commons.system.OSType;

import java.io.File;
import java.net.URISyntaxException;
import java.nio.file.Files;
import java.nio.file.LinkOption;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.InvalidPathException;

import static com.snbc.bbpf.commons.files.FileIntegrityCheck.PARAMETER_EMPTY;

/**
 * 文件路径处理类<br>
 * 1. 根据文件路径获取文件扩展名<br>
 * 2. 根据文件路径获取文件名<br>
 * 3. 根据目录路径最后是否有目录分隔符返回带目录分隔符或不带目录分隔符的路径<br>
 * 4. 获取文件路径的层级数<br>
 * 5. 判断路径是目录还是文件。上述方法需要进行使用。<br>
 * 6. 获取指定类所在JAR文件的目录路径<br>
 * 7. 根据文件路径获取Path，同时校验路径是否存在<br>
 * 8. 检查文件路径中是否包含非法字符<br>
 * 9. 检查文件路径是否符合操作系统规范<br>
 * 示例：<br>
 * FilePathUtils.getFileExtension(filePath)<br>
 * FilePathUtils.getFileName(filePath);<br>
 * FilePathUtils.getPathHierarchyLevel(filePath);<br>
 * FilePathUtils.isDirectory(filePath);<br>
 * FilePathUtils.formatPath(filePath);<br>
 * FilePathUtils.getPathForClassOfJar(clazz);<br>
 * Path path = FilePathUtils.getFilePath(filePath);<br>
 * boolean hasIllegalChars = FilePathUtils.isContainsIllegalChars(filePath);<br>
 * boolean isValid = FilePathUtils.isValidPath(filePath);<br>
 *
 * <p> {@link java.nio.file.Files} 中存在的方法，请直接使用</p>
 *
 * <AUTHOR>
 * @module 文件路径模块
 * @date 2023/8/25 20:10
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class FilePathUtils {

    public static final String LINUX_FILE_SEPARATOR = "/";
    public static final String WINDOWS_FILE_SEPARATOR = "\\";
    public static final String PARAMETER_NULL = "Input parameter is null";
    public static final String PARAMETER_EMPTY = "Input parameter is empty";
    public static final int MAX_PATH_WIN = 260;
    public static final int MAX_PATH_LINUX = 4096;

    private FilePathUtils() {
        throw new IllegalStateException("Utility class");
    }
    /**
     * 根据文件路径获取文件扩展名。
     *
     * @param filePath 文件路径
     * @return 文件扩展名，如果文件路径为空或没有扩展名，则返回null
     * <AUTHOR>
     * @date 2023/10/08
     * @since 1.3.0
     */
    public static String getFileExtension(String filePath) {
        if (StringEmptyCheck.isBlank(filePath)) {
            return null;
        }
        int dotIndex = filePath.lastIndexOf(".");
        if (dotIndex == -1 || dotIndex == filePath.length() - 1) {
            return null;
        }
        return filePath.substring(dotIndex + 1);
    }

    /**
     * 根据文件路径获取文件名
     *
     * @param filePath 文件路径
     * @return 文件名
     * <AUTHOR>
     * @date 2023/10/08
     * @since 1.3.0
     */
    public static String getFileName(String filePath) {
        if (StringEmptyCheck.isBlank(filePath)) {
            throw new IllegalArgumentException(PARAMETER_EMPTY);
        }

        File file = new File(filePath);
        return file.getName();
    }

    /**
     * 获取文件路径的层级数
     *
     * @param filePath 文件路径
     * @return 层级数，如果文件路径为null或空字符串，则返回0
     * <AUTHOR>
     * @date 2023/10/08
     * @since 1.3.0
     */
    public static int getPathHierarchyLevel(String filePath) {
        if (StringEmptyCheck.isBlank(filePath)) {
            return 0;
        }
        Path path = Paths.get(filePath);
        return path.getNameCount();
    }

    /**
     * 判断路径是目录还是文件
     *
     * @param path 路径
     * @return true表示路径是目录，false表示路径是文件或不存在
     * <AUTHOR>
     * @date 2023/10/08
     * @since 1.3.0
     */
    public static boolean isDirectory(String path) {
        if (StringEmptyCheck.isBlank(path)) {
            return false;
        }
        Path pathObj = Paths.get(path);
        return pathObj.toFile().isDirectory();
    }

    /**
     * 根据目录路径最后是否有目录分隔符返回带目录分隔符或不带目录分隔符的路径
     *
     * @param path 目录路径
     * @return 格式化后的路径
     * <AUTHOR>
     * @date 2023/10/08
     * @since 1.3.0
     */
    public static String formatPath(String path) {
        if (path == null || path.isEmpty()) {
            return path;
        }
        boolean hasSeparator = path.endsWith(LINUX_FILE_SEPARATOR) || path.endsWith(WINDOWS_FILE_SEPARATOR);
        if (hasSeparator) {
            return path;
        } else {
            if(path.contains(LINUX_FILE_SEPARATOR)){
                path = path + LINUX_FILE_SEPARATOR;
            }else if(path.contains(WINDOWS_FILE_SEPARATOR)){
                path = path + WINDOWS_FILE_SEPARATOR;
            }else{
                path = path + File.separator;
            }
            return path;
        }
    }
    /**
     * 获取指定类所在JAR文件的目录路径。
     * 动态加载的jar包，也可以根据对应jar包中的类获取到对应的目录路径。
     * 例如：
     * 方式一：
     *     //直接传入对应jar包中的类
     *     String jarPath = FilePathUtils.getPathForClassOfJar(com.snbc.xxx.A.class);
     *  方式二：
     *     //通过获取对应类的classLoader对象
     *     Class<?> clazz = classLoader.loadClass("org.snbc.sample.Sample");
     *     String jarPath = FilePathUtils.getPathForClassOfJar(clazz);
     *
     * @param clazz 需要获取路径的类
     * @return JAR文件的所在目录路径
     * @throws URISyntaxException 如果类的代码源位置无法转换为URI
     * <AUTHOR>
     * @date 2024/08/08
     * @since 1.5.0
     */
    public static String getPathForClassOfJar(Class<?> clazz) throws URISyntaxException{
        // 获取当前运行的JAR文件的路径
        File jarFile = new File(clazz.getProtectionDomain().getCodeSource().getLocation().toURI());
        // 获取JAR文件的所在目录
        return jarFile.getParent();
    }

    /**
     * 根据文件路径获取Path，同时校验路径是否存在
     *
     * @param filePath  文件路径
     * @return java.nio.file.Path
     * @throws IllegalArgumentException
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/8/9
     */
    public static Path getFilePath(String filePath) {
        if(StringEmptyCheck.isEmpty(filePath)){
            throw new IllegalArgumentException("File path must not be empty");
        }
        Path path = Paths.get(filePath);
        if(!path.toFile().exists()){
            throw new IllegalArgumentException("FilePath: " + filePath + " not found.");
        }
        return path;
    }

    /**
     * 检查文件路径中是否包含非法字符
     * @param filePath 文件路径
     * @return 如果包含非法字符返回true，否则返回false
     * @throws IllegalArgumentException 如果输入参数为空
     * @since 1.6.0
     * <AUTHOR>
     * @date 2025/5/30
     */
    public static boolean isContainsIllegalChars(String filePath) {
        if (StringEmptyCheck.isEmpty(filePath)) {
            throw new IllegalArgumentException(PARAMETER_EMPTY);
        }

        try {
            Paths.get(filePath);
            return false; // 如果能成功创建Path对象，说明没有非法字符
        } catch (InvalidPathException e) {
            return true; // 如果抛出InvalidPathException，说明包含非法字符
        }
    }

    /**
     * 检查文件路径是否符合操作系统规范
     * @param filePath 文件路径
     * @return 如果路径合法返回true，否则返回false
     * @throws IllegalArgumentException 如果输入参数为空
     * @since 1.6.0
     * <AUTHOR>
     * @date 2025/5/30
     */
    public static boolean isValidPath(String filePath) {
        if (StringEmptyCheck.isEmpty(filePath)) {
            throw new IllegalArgumentException(PARAMETER_EMPTY);
        }

        try {
            Path path = Paths.get(filePath);
            // 检查路径长度（Windows下最大260字符，Linux下最大4096字符）
            int maxLength = SystemInfoUtil.getSystemType() == OSType.WINDOWS ? MAX_PATH_WIN : MAX_PATH_LINUX;
            return path.toString().length() <= maxLength;
        } catch (InvalidPathException e) {
            return false;
        }
    }
}
