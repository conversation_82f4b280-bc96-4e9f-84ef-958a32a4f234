/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.validations.utils;


import com.snbc.bbpf.commons.objs.ObjectEmptyCheck;
import com.snbc.bbpf.commons.reflects.ReflectUtils;
import com.snbc.bbpf.commons.strings.StringEmptyCheck;
import com.snbc.bbpf.commons.validations.annotation.ValidStringLength;

import java.lang.reflect.Field;

/**
 * StringLength注解验证工具类
 *
 * <AUTHOR>
 * @module 注解
 * @date 2023/7/27
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class ValidStringLengthUtil {

    private ValidStringLengthUtil() {
        throw new IllegalStateException("Utility class");
    }
    /**
     * 用于校验 JavaBean 中使用StringLength注解的字段
     * 字段必须包含getXXX方法
     *
     * @param object 待验证的JavaBean对象
     * @return void
     * @throws IllegalArgumentException 对象为空或验证失败时抛出异常
     * @since 1.1.0
     * <AUTHOR>
     * @date 2023/7/27
     */
    public static void stringFieldsValidate(Object object) throws IllegalArgumentException {
        if (ObjectEmptyCheck.isEmpty(object)) {
            throw new IllegalArgumentException("object can't be null");
        }

        Field[] fields = object.getClass().getDeclaredFields();
        for (Field field : fields) {
            //获取对应注解
            ValidStringLength annotation = field.getAnnotation(ValidStringLength.class);
            if (null == annotation) {
                continue;
            }
            int min = annotation.min();
            int max = annotation.max();
            String message = annotation.message();
            //根据get方法取出属性值
            validLength(object, field, min, max, message);
        }
    }
    /**
     * 获取字段并进行校验长度
     * @param object 对象
     * @param field  字段
     * @param min  最小长度
     * @param max  最大长度
     * @param message 异常消息
     * @return void
     * @throws IllegalArgumentException 字段值长度不符合要求时抛出
     * @since 1.1.0
     * <AUTHOR>
     * @date 2023/7/27
     */
    private static void validLength(Object object, Field field, int min, int max, String message) {
        String fieldValue = ReflectUtils.getPropertyValue(object,field.getName(),String.class);
        if (fieldValue == null){
            if (min > 0){
                throw new IllegalArgumentException(!StringEmptyCheck.isEmpty(message) ? message : (field.getName() + " value length error"));
            }
        } else {
            if (fieldValue.length() < min || fieldValue.length() > max){
                throw new IllegalArgumentException(!StringEmptyCheck.isEmpty(message) ? message : (field.getName() + " value length error"));
            }
        }
    }

}
