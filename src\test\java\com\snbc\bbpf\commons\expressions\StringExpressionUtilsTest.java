/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.expressions;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.RepeatedTest;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * 解析带有表达式的字符串,并进行运算单元测试类
 *
 * <AUTHOR>
 * @module 表达式模块
 * @date 2023/9/13 20:30
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class StringExpressionUtilsTest {

    @Test
	@DisplayName("对字符串表达式、传入参数后并计算结果,返回BigDecimal类型")
	@Tags({
			@Tag("@id:49"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/9/13")
	})
    void testCalculateBigDecimal() {
		Map<String,Object> args = new HashMap<String, Object>();
		args.put("x",2);
		args.put("y",3);
		
        assertEquals(new BigDecimal(11).doubleValue(), StringExpressionUtils.calculateBigDecimal("x+y+x*y", args).doubleValue());
    }

    @Test
	@DisplayName("对字符串表达式、传入参数后并计算结果")
	@Tags({
			@Tag("@id:49"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/9/13")
	})
    void testCalculate_args() {
		Map<String,Object> args = new HashMap<String, Object>();
		args.put("x",2);
		args.put("y",3);
		
        assertEquals(11, StringExpressionUtils.calculate("x+y+x*y", args));
    }

    @Test
	@DisplayName("传入null参数后并计算结果")
	@Tags({
			@Tag("@id:49"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/9/13")
	})
    void testCalculate_argsNullShouldThrowException() {
		assertThrows(Exception.class,
			() -> StringExpressionUtils.calculate("x+y+x*y", null),
			"Input parameter is null"); 	
    }

    @Test
	@DisplayName("传入empty参数后并计算结果")
	@Tags({
			@Tag("@id:49"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/9/13")
	})
    void testCalculate_argsEmptyShouldThrowException() {
		Map<String,Object> args = new HashMap<String,Object>();
		assertThrows(Exception.class,
			() -> StringExpressionUtils.calculate("x+y+x*y", args),
			"Input parameter is empty"); 	
    }

    @Test
	@DisplayName("传入invalid参数后并计算结果")
	@Tags({
			@Tag("@id:49"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/9/13")
	})
    void testCalculate_argsInvalidShouldThrowException() {
		Map<String,Object> args = new HashMap<String,Object>();
		args.put("x",2);
		args.put("f",3);
		assertThrows(Exception.class,
			() -> StringExpressionUtils.calculate("x+y+x*y", args),
			"Input parameter is invalid"); 	
    }

    @Test
	@DisplayName("传入null字符串表达式应该抛出异常")
	@Tags({
			@Tag("@id:49"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/9/13")
	})
    void testCalculate_argsNullExpressionShouldThrowException() {
		Map<String,Object> args = new HashMap<String,Object>();
		args.put("x",2);
		args.put("y",3);
		
		assertThrows(Exception.class,
				() -> StringExpressionUtils.calculate(null, args),
				"Input parameter is null"); 	
    }

    @Test
	@DisplayName("传入empty字符串表达式应该抛出异常")
	@Tags({
			@Tag("@id:49"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/9/13")
	})
    void testCalculate_argsEmptyExpressionShouldThrowException() {
		Map<String,Object> args = new HashMap<String,Object>();
		args.put("x",2);
		args.put("y",3);
		
		assertThrows(Exception.class,
				() -> StringExpressionUtils.calculate("", args),
				"Input parameter is empty"); 	
    }

    @Test
	@DisplayName("传入invalid字符串表达式应该抛出异常")
	@Tags({
			@Tag("@id:49"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/9/13")
	})
    void testCalculate_argsInvalidExpressionShouldThrowException() {
		Map<String,Object> args = new HashMap<String,Object>();
		args.put("x",2);
		args.put("y",3);
		
		assertThrows(Exception.class,
				() -> StringExpressionUtils.calculate("x)2", args),
				"Input parameter is invalid"); 	
    }


    @Test
	@DisplayName("对算术字符串表达式计算结果")
	@Tags({
			@Tag("@id:49"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/9/13")
	})
    void testCalculate() {
        assertEquals(11, StringExpressionUtils.calculate("2+3+2*3"));
    }


    @Test
	@DisplayName("对字符串表达式、传入参数后并计算，返回保留精度结果")
	@Tags({
			@Tag("@id:49"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/9/13")
	})
    void testCalculate_argsScale() {
		Map<String,Object> args = new HashMap<String,Object>();
		args.put("x",1.0);
		args.put("y",3);
		
        assertEquals(0.34, StringExpressionUtils.calculate("x/y", args, 2, RoundingMode.UP));
    }

	@Test
	@DisplayName("传入null计算模式应该抛出异常")
	@Tags({
			@Tag("@id:49"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/9/13")
	})
    void testCalculate_argsModeNullShouldThrowException() {
		Map<String,Object> args = new HashMap<String,Object>();
		args.put("x",1.0);
		args.put("y",3);
		
		assertThrows(Exception.class,
				() -> StringExpressionUtils.calculate("x/y", args, 2, null),
				"Input parameter is null"); 	
    }

    @Test
	@DisplayName("传入invalid计算模式应该抛出异常")
	@Tags({
			@Tag("@id:49"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/9/13")
	})
    void testCalculate_argsModeInvalidShouldThrowException() {
		Map<String,Object> args = new HashMap<String,Object>();
		args.put("x",1.0);
		args.put("y",3);
		
		assertThrows(Exception.class,
				() -> StringExpressionUtils.calculate("x/y", args, 2, RoundingMode.UNNECESSARY),
				"Input parameter is invalid"); 	
    }

    @Test
	@DisplayName("传入negative保留精度应该抛出异常")
	@Tags({
			@Tag("@id:49"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/9/13")
	})
    void testCalculate_argsScaleNegativeShouldThrowException() {
		Map<String,Object> args = new HashMap<String,Object>();
		args.put("x",1.0);
		args.put("y",3);
		
		assertThrows(Exception.class,
				() -> StringExpressionUtils.calculate("x/y", args, -6, RoundingMode.UP),
				"Input parameter is negative"); 	
    }

    @Test
	@DisplayName("对算术字符串表达式计算，返回保留精度结果")
	@Tags({
			@Tag("@id:49"),
			@Tag("@author:jiangsheng"),
            @Tag("@date:2023/9/13")
	})
    void testCalculate_scale() {
        assertEquals(0.34, StringExpressionUtils.calculate("1.0/3", 2, RoundingMode.UP));
    }

	@RepeatedTest(3)
	@DisplayName("测试性能与资源消耗AVIATOR解析带有表达式的字符串,并进行运算")
	@Tags({
			@Tag("@id:49"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/9/13")
	})
	void testCalculateByAviator() {
		assertEquals(1109.221976, StringExpressionUtils.calculate("1+(3-1)*100+0.5/2+1000+(3.141592*3-98.4528)-(600-3*15)%(((68-9)-3)*2-100)"));
		Map<String,Object> args = new HashMap<String,Object>();
		args.put("x",2.0);
		args.put("y",3.0);
		
        assertEquals(11, StringExpressionUtils.calculate("x+y+x*y", args));
	}
}
