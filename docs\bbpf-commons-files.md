# 文件处理（com.snbc.bbpf.commons.files）

## 类概览 

| 类名 | 功能描述 |
|------|----------|
| FileUtils | 提供文件操作功能，包括文件读写、复制、移动、删除等 |
| FilePathUtils | 提供路径操作功能，包括路径拼接、规范化、获取文件名、判断文件路径的合法性等 |
| FileIntegrityCheck | 提供文件完整性检查功能，支持生成和验证文件校验码 |
| FilePermissionUtils | 提供文件权限操作功能，支持设置和获取文件读写执行权限 |
| FileTypeChecker | 提供文件类型检查功能，根据文件头判断文件类型 |
| FileTypeValidator | 提供文件类型验证功能，根据扩展名验证文件类型 |
| GzipUtils | 提供Gzip压缩和解压功能，支持单文件压缩和解压缩 |
| ZipUtils | 提供Zip压缩和解压功能，支持文件和目录的压缩和解压缩 |
| IniFileFinder | 提供INI文件查找功能，支持根据正则表达式查询INI文件内容 |
| IniFileUtils | 提供INI文件操作功能，支持INI文件的读写、修改和删除操作 |
| PropertiesUtils | 提供属性文件操作功能，读取和写入属性文件 |

## FileUtils - 文件操作工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| read(String filePath) | 读取指定文件返回字节数组 | filePath: 文件路径 | byte[]: 文件内容字节数组 |
| readString(String filePath) | 读取指定文件返回字符串，使用系统默认字符集 | filePath: 文件路径 | String: 文件内容 |
| readString(String filePath, Charset charset) | 读取指定文件返回字符串，使用指定字符集 | filePath: 文件路径<br>charset: 字符编码 | String: 文件内容 |
| write(String filePath, byte[] context) | 向文件写入字节，默认从文件开始位置写入，如不存在则创建文件 | filePath: 文件路径<br>context: 要写入的字节数组 | void |
| write(String filePath, byte[] context, boolean isCreate, boolean isAppend) | 向文件写入字节，可指定是否创建文件和是否追加内容 | filePath: 文件路径<br>context: 要写入的字节数组<br>isCreate: 是否创建文件<br>isAppend: 是否追加 | void |
| write(String filePath, String context) | 向文件写入字符串，使用系统默认字符集 | filePath: 文件路径<br>context: 要写入的字符串 | void |
| write(String filePath, String context, Charset charset) | 向文件写入字符串，使用指定字符集 | filePath: 文件路径<br>context: 要写入的字符串<br>charset: 字符编码 | void |
| write(String filePath, String context, boolean isCreate, boolean isAppend) | 向文件写入字符串，可指定是否创建文件和是否追加内容，使用系统默认字符集 | filePath: 文件路径<br>context: 要写入的字符串<br>isCreate: 是否创建文件<br>isAppend: 是否追加 | void |
| write(String filePath, String context, boolean isCreate, boolean isAppend, Charset charset) | 向文件写入字符串，可指定是否创建文件、是否追加内容和字符集 | filePath: 文件路径<br>context: 要写入的字符串<br>isCreate: 是否创建文件<br>isAppend: 是否追加<br>charset: 字符编码 | void |
| writeAppend(String filePath, byte[] context) | 向文件追加字节，如不存在则不创建文件 | filePath: 文件路径<br>context: 要追加的字节数组 | void |
| writeAppend(String filePath, String context) | 向文件追加字符串，如不存在则不创建文件，使用系统默认字符集 | filePath: 文件路径<br>context: 要追加的字符串 | void |
| delete(String filePath) | 删除指定路径的文件或目录（包括子目录和文件） | filePath: 文件或目录路径 | void |
| delete(String filePath, Predicate<Path> predicate) | 删除符合条件的文件，predicate返回true则删除 | filePath: 目录路径<br>predicate: 文件过滤条件 | void |
| delete(String filePath, Predicate<Path> filePredicate, Predicate<Path> dirPredicate) | 删除符合条件的文件和目录 | filePath: 目录路径<br>filePredicate: 文件过滤条件<br>dirPredicate: 目录过滤条件 | void |
| filter(String filePath, Predicate<Path> predicate) | 查找符合条件的文件 | filePath: 目录路径<br>predicate: 文件过滤条件 | List<Path>: 符合条件的文件路径列表 |
| writeLines(File file, Collection<String> lines) | 将字符串集合写入文件，每个元素占一行 | file: 文件对象<br>lines: 要写入的行集合 | void |
| readLines(File file) | 按行读取文件内容 | file: 文件对象 | List<String>: 文件内容行列表 |
| countFiles(String fileDirectory) | 获取目录中的文件数量，包括所有子目录中的文件 | fileDirectory: 目录路径 | int: 文件数量 |
| countFiles(String fileDirectory, int maxDepth) | 获取目录中的文件数量，可指定最大遍历深度 | fileDirectory: 目录路径<br>maxDepth: 最大遍历深度 | int: 文件数量 |
| move(String source, String destination) | 移动文件或目录到目标位置，目标已存在则抛出异常 | source: 源路径<br>destination: 目标路径 | void |
| move(String source, String destination, boolean isOverwrite) | 移动文件或目录到目标位置，可选是否覆盖已存在的目标 | source: 源路径<br>destination: 目标路径<br>isOverwrite: 是否覆盖 | void |
| move(String source, String destination, boolean isOverwrite, boolean isUnwind) | 移动文件或目录到目标位置，可选是否覆盖、是否移动子文件 | source: 源路径<br>destination: 目标路径<br>isOverwrite: 是否覆盖<br>isUnwind: 是否移动子文件 | void |
| rename(String source, String updateName) | 重命名文件或目录 | source: 源路径<br>updateName: 新名称 | boolean: 是否重命名成功 |

### 使用注意事项

1. 读取大文件时应注意内存占用，对于超大文件建议使用流式读取或分块处理
2. 文件路径支持相对路径和绝对路径，相对路径基于当前工作目录
3. 删除操作会递归删除目录及其内容，使用前应谨慎确认
4. 移动文件时如果目标已存在且未指定覆盖选项，会抛出IOException
5. 过滤器方法(filter)对于大目录可能比较耗时，考虑增加过滤条件限制结果数量
6. 默认使用系统字符集进行字符串操作，对于跨平台应用建议明确指定字符集

### 使用示例

```java
// 读取文件内容为字节数组
byte[] bytes = FileUtils.read("example.jpg");
System.out.println("文件大小: " + bytes.length + " 字节");

// 读取文件内容为字符串
String content = FileUtils.readString("example.txt");
System.out.println(content);

// 读取文件内容为字符串，指定字符编码
String contentUtf8 = FileUtils.readString("example.txt", StandardCharsets.UTF_8);
System.out.println(contentUtf8);

// 将字符串写入文件
FileUtils.write("output.txt", "Hello, World!");

// 将字符串写入文件，指定字符编码
FileUtils.write("output.txt", "Hello, World!", StandardCharsets.UTF_8);

// 将字节数组写入文件
byte[] data = new byte[]{65, 66, 67, 68, 69};  // ABCDE
FileUtils.write("output.bin", data);

// 向文件追加内容
FileUtils.writeAppend("log.txt", "新的日志条目\n");

// 将字符串集合写入文件，每个元素占一行
List<String> lines = Arrays.asList("第一行", "第二行", "第三行");
FileUtils.writeLines(new File("lines.txt"), lines);

// 按行读取文件内容
List<String> readLines = FileUtils.readLines(new File("lines.txt"));
for (String line : readLines) {
    System.out.println(line);
}

// 删除文件或目录
FileUtils.delete("temp.txt");
FileUtils.delete("tempDir");

// 删除指定条件的文件
FileUtils.delete("documents", path -> path.toString().endsWith(".tmp"));

// 删除指定条件的文件和目录
FileUtils.delete("documents", 
    path -> path.toString().endsWith(".tmp"),  // 删除.tmp文件
    path -> path.toString().contains("backup") // 删除包含"backup"的目录
);

// 查找符合条件的文件
List<Path> imageFiles = FileUtils.filter("photos", path -> 
    path.toString().endsWith(".jpg") || path.toString().endsWith(".png")
);
for (Path path : imageFiles) {
    System.out.println("找到图片: " + path);
}

// 获取目录中的文件数量
int fileCount = FileUtils.countFiles("documents");
System.out.println("文件总数: " + fileCount);

// 获取目录中的文件数量，限制遍历深度为2
int fileCountDepth2 = FileUtils.countFiles("documents", 2);
System.out.println("最大深度2的文件数: " + fileCountDepth2);

// 移动文件
try {
    FileUtils.move("source.txt", "destination.txt");
    
    // 移动并覆盖已存在的文件
    FileUtils.move("source.txt", "destination.txt", true);
    
    // 移动目录及其内容
    FileUtils.move("sourceDir", "destinationDir");
    
    // 移动目录下的所有子文件，但不移动目录本身
    FileUtils.move("sourceDir", "destinationDir", true, true);
} catch (IOException e) {
    e.printStackTrace();
}

// 重命名文件或目录
boolean renamed = FileUtils.rename("oldName.txt", "newName.txt");
System.out.println("重命名" + (renamed ? "成功" : "失败"));
```

## FilePathUtils - 路径操作工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| getFileExtension(String filePath) | 获取文件扩展名 | filePath: 文件路径 | String: 文件扩展名 |
| getFileName(String filePath) | 获取文件名 | filePath: 文件路径 | String: 文件名 |
| getPathHierarchyLevel(String filePath) | 获取路径层级数 | filePath: 文件路径 | int: 路径层级数 |
| isDirectory(String path) | 判断路径是否为目录 | path: 路径 | boolean: 是否为目录 |
| formatPath(String path) | 格式化路径，确保以分隔符结尾 | path: 目录路径 | String: 格式化后的路径 |
| getPathForClassOfJar(Class<?> clazz) | 获取指定类所在JAR文件的目录路径 | clazz: 需要获取路径的类 | String: JAR文件所在目录 |
| getFilePath(String filePath) | 获取文件Path对象，同时校验路径是否存在 | filePath: 文件路径 | Path: 文件Path对象 |
| isContainsIllegalChars(String filePath) | 检查文件路径中是否包含非法字符 | filePath: 文件路径 | boolean: 是否包含非法字符 |
| isValidPath(String filePath) | 检查文件路径是否符合操作系统规范 | filePath: 文件路径 | boolean: 是否符合规范 |

### 使用注意事项

1. getFileExtension返回的扩展名不包含点号(.)
2. 在Windows和Linux系统中路径分隔符不同，该工具类会自动处理这种差异
3. getFilePath方法会检查文件是否存在，不存在会抛出IllegalArgumentException异常
4. formatPath方法确保目录路径以分隔符结尾，便于路径拼接
5. getPathForClassOfJar只适用于从JAR文件中加载的类，对于从文件系统加载的类可能返回错误结果
6. isContainsIllegalChars方法用于检查路径中是否包含操作系统不允许的字符，如Windows下的<>:"/\|?*等
7. isValidPath方法会检查路径长度是否符合操作系统限制（Windows最大260字符，Linux最大4096字符），同时也会检查路径格式是否合法

### 使用示例

```java
// 获取文件扩展名
String extension = FilePathUtils.getFileExtension("document.pdf");
System.out.println("文件扩展名: " + extension);  // 输出: pdf

// 获取文件名
String fileName = FilePathUtils.getFileName("C:\\path\\to\\file.txt");
System.out.println("文件名: " + fileName);  // 输出: file.txt

// 获取路径层级数
int levels = FilePathUtils.getPathHierarchyLevel("C:\\path\\to\\file.txt");
System.out.println("路径层级数: " + levels);  // 输出: 3 (path, to, file.txt)

// 判断路径是否为目录
boolean isDir = FilePathUtils.isDirectory("C:\\path\\to");
System.out.println("是否为目录: " + isDir);  // 输出: true

// 格式化路径，确保以分隔符结尾
String formattedPath = FilePathUtils.formatPath("C:\\path\\to");
System.out.println("格式化后的路径: " + formattedPath);  // 输出: C:\path\to\

// 获取指定类所在JAR文件的目录路径
try {
    String jarPath = FilePathUtils.getPathForClassOfJar(MyClass.class);
    System.out.println("JAR文件所在目录: " + jarPath);
} catch (URISyntaxException e) {
    e.printStackTrace();
}

// 获取文件Path对象，同时校验路径是否存在
try {
    Path path = FilePathUtils.getFilePath("C:\\path\\to\\file.txt");
    System.out.println("文件路径有效");
} catch (IllegalArgumentException e) {
    System.out.println("文件路径无效: " + e.getMessage());
}

// 检查文件路径是否包含非法字符
boolean hasIllegalChars = FilePathUtils.isContainsIllegalChars("C:\\path\\to\\file.txt");
System.out.println("是否包含非法字符: " + hasIllegalChars);

// 检查文件路径是否符合操作系统规范
boolean isValid = FilePathUtils.isValidPath("C:\\path\\to\\file.txt");
System.out.println("路径是否合法: " + isValid);
```

## FileIntegrityCheck - 文件完整性检查工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| generateCheckCode(FileInputStream fis, CheckAlgorithm algorithm) | 根据文件流生成校验码 | fis: 文件输入流<br>algorithm: 校验算法 | String: 校验码 |
| generateCheckCode(File file, CheckAlgorithm algorithm) | 根据文件对象生成校验码 | file: 文件对象<br>algorithm: 校验算法 | String: 校验码 |
| generateCheckCode(String fileName, CheckAlgorithm algorithm) | 根据文件路径生成校验码 | fileName: 文件路径<br>algorithm: 校验算法 | String: 校验码 |
| check(FileInputStream fis, CheckAlgorithm algorithm, String checkCode) | 根据文件流校验文件完整性 | fis: 文件输入流<br>algorithm: 校验算法<br>checkCode: 校验码 | boolean: 校验结果 |
| check(File file, CheckAlgorithm algorithm, String checkCode) | 根据文件对象校验文件完整性 | file: 文件对象<br>algorithm: 校验算法<br>checkCode: 校验码 | boolean: 校验结果 |
| check(String fileName, CheckAlgorithm algorithm, String checkCode) | 根据文件路径校验文件完整性 | fileName: 文件路径<br>algorithm: 校验算法<br>checkCode: 校验码 | boolean: 校验结果 |

### 使用注意事项

1. 对于大文件生成校验码可能比较耗时，建议在后台线程中执行
2. 使用FileInputStream方式生成校验码时，流会在方法内部关闭
3. 对于同一文件，不同算法生成的校验码不同，验证时必须使用相同的算法
4. 校验算法的安全强度：SHA512 > SHA384 > SHA256 > SHA1 > MD5，但计算耗时也相应增加
5. 校验码是文件内容的唯一标识，即使文件内容变化一个字节，校验码也会完全不同

### 使用示例

```java
// 支持的校验算法
// FileIntegrityCheck.CheckAlgorithm.MD5
// FileIntegrityCheck.CheckAlgorithm.SHA1
// FileIntegrityCheck.CheckAlgorithm.SHA256
// FileIntegrityCheck.CheckAlgorithm.SHA384
// FileIntegrityCheck.CheckAlgorithm.SHA512

try {
    // 根据文件路径生成MD5校验码
    String md5Code = FileIntegrityCheck.generateCheckCode("document.pdf", FileIntegrityCheck.CheckAlgorithm.MD5);
    System.out.println("文件MD5校验码: " + md5Code);

    // 根据文件对象生成SHA1校验码
    File file = new File("document.pdf");
    String sha1Code = FileIntegrityCheck.generateCheckCode(file, FileIntegrityCheck.CheckAlgorithm.SHA1);
    System.out.println("文件SHA1校验码: " + sha1Code);

    // 使用文件输入流生成SHA256校验码
    try (FileInputStream fis = new FileInputStream("document.pdf")) {
        String sha256Code = FileIntegrityCheck.generateCheckCode(fis, FileIntegrityCheck.CheckAlgorithm.SHA256);
        System.out.println("文件SHA256校验码: " + sha256Code);
    }

    // 根据校验码验证文件完整性
    boolean isValid = FileIntegrityCheck.check("document.pdf", FileIntegrityCheck.CheckAlgorithm.MD5, md5Code);
    System.out.println("文件完整性检查结果: " + (isValid ? "有效" : "无效"));

} catch (Exception e) {
    e.printStackTrace();
}
```

## FilePermissionUtils - 文件权限操作工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| getFilePermissions(String filePath) | 获取文件的权限列表 | filePath: 文件路径 | List<FilePermissionType>: 权限列表 |
| setFilePermissions(String filePath, Map<String,List<FilePermissionType>> windowsPermissions, String linuxPermissions) | 设置文件权限 | filePath: 文件路径<br>windowsPermissions: Windows权限设置<br>linuxPermissions: Linux权限设置 | void |
| setFilePermissions(String filePath, Map<String,List<FilePermissionType>> windowsPermissions, String linuxPermissions, boolean isClear) | 设置文件权限，可选择是否清除原有权限 | filePath: 文件路径<br>windowsPermissions: Windows权限设置<br>linuxPermissions: Linux权限设置<br>isClear: 是否清除原有权限 | void |

### 使用注意事项

1. Windows和Linux系统的权限模型不同，需分别设置windowsPermissions和linuxPermissions参数
2. 在Windows系统中需要管理员权限才能修改某些文件的权限
3. 在Linux系统中权限字符串格式为rwxrwxrwx(用户、组、其他人的读写执行权限)
4. 设置权限时如果isClear参数为true，会先清除文件的所有现有权限
5. 获取权限列表时返回的是当前系统支持的权限类型，不同系统可能返回结果不同

### 使用示例

```java
try {
    // 获取文件的权限列表
    List<FilePermissionType> permissions = FilePermissionUtils.getFilePermissions("example.txt");
    System.out.println("文件权限:");
    for (FilePermissionType permission : permissions) {
        System.out.println("- " + permission.name());
    }

    // 设置Windows文件权限
    Map<String, List<FilePermissionType>> windowsPermissions = new HashMap<>();
    
    // 为"Everyone"用户组设置读写权限
    List<FilePermissionType> everyonePerms = new ArrayList<>();
    everyonePerms.add(FilePermissionType.READ);
    everyonePerms.add(FilePermissionType.WRITE);
    windowsPermissions.put("Everyone", everyonePerms);
    
    // 为当前用户设置完整权限
    List<FilePermissionType> userPerms = new ArrayList<>();
    userPerms.add(FilePermissionType.READ);
    userPerms.add(FilePermissionType.WRITE);
    userPerms.add(FilePermissionType.EXECUTE);
    windowsPermissions.put(System.getProperty("user.name"), userPerms);
    
    // 设置Linux文件权限 (rwxr-xr--)
    String linuxPermissions = "rwxr-xr--";
    
    // 应用权限设置
    FilePermissionUtils.setFilePermissions("example.txt", windowsPermissions, linuxPermissions);
    
    // 清除原有权限并设置新权限
    FilePermissionUtils.setFilePermissions("example.txt", windowsPermissions, linuxPermissions, true);
    
} catch (IOException e) {
    e.printStackTrace();
}
```

## FileTypeChecker - 文件类型检查工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| isFileType(String filePath, FileType fileType) | 检查文件是否为指定类型 | filePath: 文件路径<br>fileType: 文件类型 | boolean: 是否为指定类型 |

### 使用注意事项

1. 文件类型检查基于文件头部特征字节(Magic Numbers)，而非扩展名
2. 即使文件扩展名被修改，仍能正确识别文件实际类型
3. 如果文件头部被破坏，可能导致类型识别错误
4. 支持的文件类型在FileType枚举中定义，包括常见图片、文档、音视频和压缩文件格式
5. 对于加密文件或自定义格式文件可能无法正确识别类型

### 使用示例

```java
// 检查文件是否为JPG图片
boolean isJpg = FileTypeChecker.isFileType("image.jpg", FileType.JPG);
System.out.println("文件是否为JPG图片: " + isJpg);

// 检查文件是否为PDF文档
boolean isPdf = FileTypeChecker.isFileType("document.pdf", FileType.PDF);
System.out.println("文件是否为PDF文档: " + isPdf);

// 检查文件是否为ZIP压缩文件
boolean isZip = FileTypeChecker.isFileType("archive.zip", FileType.ZIP);
System.out.println("文件是否为ZIP压缩文件: " + isZip);

// 检查文件是否为MP3音频文件
boolean isMp3 = FileTypeChecker.isFileType("music.mp3", FileType.MP3);
System.out.println("文件是否为MP3音频文件: " + isMp3);

// 检查文件是否为可执行文件
boolean isExe = FileTypeChecker.isFileType("program.exe", FileType.EXE);
System.out.println("文件是否为可执行文件: " + isExe);
```

## FileTypeValidator - 文件类型验证工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| isValidFileType(String fileName, List<String> fileTypes) | 验证文件是否为允许的类型 | fileName: 文件名<br>fileTypes: 允许的文件类型列表 | boolean: 是否为允许的类型 |

### 使用注意事项

1. 验证基于文件扩展名而非文件内容，不检查文件真实类型
2. 文件扩展名比较不区分大小写，例如"JPG"和"jpg"被视为相同类型
3. 扩展名不包含点号(.)，传入List中的类型也不应包含点号
4. 对于无扩展名文件会返回false
5. 与FileTypeChecker结合使用可以同时验证扩展名和实际文件类型

### 使用示例

```java
// 定义允许的文件类型列表
List<String> allowedImageTypes = Arrays.asList("jpg", "png", "gif", "bmp");

// 验证文件类型
boolean isValidImage1 = FileTypeValidator.isValidFileType("photo.jpg", allowedImageTypes);
System.out.println("photo.jpg 是否为允许的图片类型: " + isValidImage1);  // 输出: true

boolean isValidImage2 = FileTypeValidator.isValidFileType("document.pdf", allowedImageTypes);
System.out.println("document.pdf 是否为允许的图片类型: " + isValidImage2);  // 输出: false

// 定义允许的文档类型列表
List<String> allowedDocTypes = Arrays.asList("doc", "docx", "pdf", "txt");

// 验证文件类型
boolean isValidDoc = FileTypeValidator.isValidFileType("report.pdf", allowedDocTypes);
System.out.println("report.pdf 是否为允许的文档类型: " + isValidDoc);  // 输出: true
```

## GzipUtils - Gzip压缩工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| decompress(String gzipFilePath, String destDir) | 解压Gzip文件到指定目录 | gzipFilePath: Gzip文件路径<br>destDir: 目标目录 | void |
| compress(String sourceFilePath, String gzipFilePath) | 将文件压缩为Gzip格式 | sourceFilePath: 源文件路径<br>gzipFilePath: 目标Gzip文件路径 | void |

### 使用注意事项

1. Gzip格式适用于单个文件的压缩，不支持多文件或目录压缩
2. 压缩比与压缩耗时成正比，对于大文件可能需要较长时间
3. 对二进制文件(如图片、视频)压缩效果有限，文本文件压缩效果较好
4. 解压时目标目录必须存在，否则会抛出异常
5. Gzip文件通常以.gz为扩展名

### 使用示例

```java
// 将文件压缩为Gzip格式
try {
    GzipUtils.compress("D:\\files\\document.txt", "D:\\files\\document.txt.gz");
    System.out.println("文件压缩成功");
} catch (Exception e) {
    System.out.println("文件压缩失败: " + e.getMessage());
}

// 解压Gzip文件到指定目录
try {
    GzipUtils.decompress("D:\\files\\document.txt.gz", "D:\\extracted");
    System.out.println("文件解压成功");
} catch (Exception e) {
    System.out.println("文件解压失败: " + e.getMessage());
}
```

## ZipUtils - Zip压缩工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| decompress(String zipFilePath, String destDir) | 解压Zip文件到指定目录 | zipFilePath: Zip文件路径<br>destDir: 目标目录 | void |
| compress(String sourceFilePath, String zipFilePath, int compressLevel) | 将文件或目录压缩为Zip格式 | sourceFilePath: 源文件或目录路径<br>zipFilePath: 目标Zip文件路径<br>compressLevel: 压缩级别(0-9) | void |

### 使用注意事项

1. 压缩级别范围为0-9，0表示不压缩，9表示最大压缩
2. 超出范围的压缩级别会被调整为默认值(9)
3. 支持整个目录的压缩，会保留目录结构
4. 解压时会自动创建目标目录中不存在的子目录
5. 解压过程中如果发现目标路径已存在同名文件，会覆盖现有文件
6. 对于非常大的目录，压缩和解压过程可能消耗大量内存和时间

### 使用示例

```java
// 将文件或目录压缩为Zip格式
try {
    // 压缩单个文件 - 最高压缩级别(9)
    ZipUtils.compress("D:\\files\\document.txt", "D:\\files\\document.zip", 9);
    
    // 压缩整个目录 - 中等压缩级别(5)
    ZipUtils.compress("D:\\files\\photos", "D:\\archives\\photos.zip", 5);
    
    // 压缩级别超出范围时会默认使用最高级别(9)
    ZipUtils.compress("D:\\files\\data", "D:\\archives\\data.zip", 15);
    
    System.out.println("文件压缩成功");
} catch (Exception e) {
    System.out.println("文件压缩失败: " + e.getMessage());
}

// 解压Zip文件到指定目录
try {
    ZipUtils.decompress("D:\\archives\\document.zip", "D:\\extracted");
    System.out.println("文件解压成功");
} catch (Exception e) {
    System.out.println("文件解压失败: " + e.getMessage());
}
```

## IniFileFinder - INI文件查找工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| getValue(String section, String key) | 获取指定分组和键的值 | section: 分组名<br>key: 键名 | String: 键值 |
| getValuesByKeyRegexWithMultipleSections(String keyRegex) | 根据键的正则表达式获取值 | keyRegex: 键的正则表达式 | List<IniSectionDomain>: 匹配的键值对列表 |
| getValuesByKeyAndGroupRegex(String keyRegex, String groupRegex) | 根据键和分组的正则表达式获取值 | keyRegex: 键的正则表达式<br>groupRegex: 分组的正则表达式 | List<IniSectionDomain>: 匹配的键值对列表 |
| getKeyValuePairsByGroupRegex(String groupRegex) | 根据分组的正则表达式获取键值对 | groupRegex: 分组的正则表达式 | List<IniSectionDomain>: 匹配的键值对列表 |
| findAll() | 获取INI文件中的所有内容 | 无 | List<IniSectionDomain>: 所有键值对列表 |

### 使用注意事项

1. 构造函数需要传入INI文件路径，不存在会抛出异常
2. 正则表达式搜索对大小写敏感
3. 如果指定的section和key不存在，getValue方法会返回null
4. 正则表达式查询方法返回的是匹配结果列表，如果没有匹配项会返回空列表
5. IniSectionDomain对象包含section、key和value三个属性
6. 使用正则表达式时应注意性能，过于复杂的表达式可能导致查询缓慢

### 使用示例

```java
// 创建INI文件查找器
IniFileFinder finder = new IniFileFinder("config.ini");

// 获取指定分组和键的值
String value = finder.getValue("Database", "ConnectionString");
System.out.println("数据库连接字符串: " + value);

// 根据键的正则表达式获取值（可能存在于多个分组）
List<IniSectionDomain> connections = finder.getValuesByKeyRegexWithMultipleSections("Connection.*");
for (IniSectionDomain domain : connections) {
    System.out.println("分组: " + domain.getSection() + ", 键: " + domain.getKey() + ", 值: " + domain.getValue());
}

// 根据键和分组的正则表达式获取值
List<IniSectionDomain> dbSettings = finder.getValuesByKeyAndGroupRegex(".*Port", "Database");
for (IniSectionDomain domain : dbSettings) {
    System.out.println("数据库端口设置 - 键: " + domain.getKey() + ", 值: " + domain.getValue());
}

// 根据分组的正则表达式获取所有键值对
List<IniSectionDomain> logSettings = finder.getKeyValuePairsByGroupRegex("Logging");
System.out.println("日志设置:");
for (IniSectionDomain domain : logSettings) {
    System.out.println("  " + domain.getKey() + " = " + domain.getValue());
}

// 获取INI文件中的所有内容
List<IniSectionDomain> allSettings = finder.findAll();
System.out.println("所有设置:");
for (IniSectionDomain domain : allSettings) {
    System.out.println("[" + domain.getSection() + "] " + domain.getKey() + " = " + domain.getValue());
}
```

## IniFileUtils - INI文件操作工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| load(String filePath) | 加载INI文件 | filePath: 文件路径 | void |
| save() | 保存INI文件 | 无 | void |
| save(String filePath) | 保存INI文件到指定路径 | filePath: 文件路径 | void |
| getValue(String section, String key) | 获取指定分组和键的值 | section: 分组名<br>key: 键名 | String: 键值 |
| setValue(String section, String key, String value) | 设置指定分组和键的值 | section: 分组名<br>key: 键名<br>value: 键值 | void |
| removeValue(String section, String key) | 删除指定分组和键的值 | section: 分组名<br>key: 键名 | void |
| modifyValue(String key, String value) | 修改所有匹配键的值 | key: 键名<br>value: 新的键值 | void |
| modifySection(String section, Map<String, String> values) | 修改指定分组的值 | section: 分组名<br>values: 键值对 | void |
| removeSection(String sectionRegex) | 删除匹配正则表达式的分组 | sectionRegex: 分组的正则表达式 | void |
| modifySectionWithValues(String section, Map<String, String> values) | 修改指定分组的键值对 | section: 分组名<br>values: 键值对 | void |
| removeKey(String key, String section) | 删除指定键 | key: 键名<br>section: 分组名 | void |
| findAll() | 获取INI文件中的所有内容 | 无 | List<IniSectionDomain>: 所有键值对列表 |

### 使用注意事项

1. 构造函数需要传入INI文件路径，如文件不存在则会创建新文件
2. load方法会重新加载文件内容，丢弃尚未保存的修改
3. save方法会将内存中的修改写入文件，未调用save前修改不会生效
4. 删除操作(removeValue、removeSection、removeKey)立即影响内存中的数据，但需调用save才会写入文件
5. modifySectionWithValues方法如果分组不存在会自动创建，而modifySection方法不会创建不存在的分组
6. removeKey方法中section参数为null时会删除所有分组中匹配的键

### 使用示例

```java
// 创建INI文件操作工具
IniFileUtils iniUtils = new IniFileUtils("config.ini");

// 获取指定分组和键的值
String dbHost = iniUtils.getValue("Database", "Host");
System.out.println("数据库主机: " + dbHost);

// 设置指定分组和键的值
iniUtils.setValue("Database", "Port", "3306");
iniUtils.setValue("Database", "Username", "admin");

// 删除指定分组和键的值
iniUtils.removeValue("Logging", "DebugLevel");

// 修改所有匹配键的值
iniUtils.modifyValue("Timeout", "30000");

// 修改指定分组的键值对
Map<String, String> logSettings = new HashMap<>();
logSettings.put("Level", "INFO");
logSettings.put("File", "app.log");
logSettings.put("MaxSize", "10MB");
iniUtils.modifySection("Logging", logSettings);

// 删除匹配正则表达式的分组
iniUtils.removeSection("Temp.*");

// 修改指定分组的键值对（如果分组不存在则创建）
Map<String, String> securitySettings = new HashMap<>();
securitySettings.put("EnableSSL", "true");
securitySettings.put("CertificatePath", "/certs/app.crt");
iniUtils.modifySectionWithValues("Security", securitySettings);

// 删除指定键（如果section为null则删除所有分组中的该键）
iniUtils.removeKey("TempSetting", null);
iniUtils.removeKey("Password", "Database");

// 获取INI文件中的所有内容
List<IniSectionDomain> allSettings = iniUtils.findAll();
for (IniSectionDomain domain : allSettings) {
    System.out.println("[" + domain.getSection() + "] " + domain.getKey() + " = " + domain.getValue());
}

// 保存INI文件
iniUtils.save();

// 保存INI文件到新路径
iniUtils.save("config.backup.ini");
```

## PropertiesUtils - 属性文件工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| getIntValue(String filePath, String key) | 获取整数属性值 | filePath: 属性文件路径<br>key: 键 | Integer: 属性值 |
| getFloatValue(String filePath, String key) | 获取浮点数属性值 | filePath: 属性文件路径<br>key: 键 | Float: 属性值 |
| getDoubleValue(String filePath, String key) | 获取双精度浮点数属性值 | filePath: 属性文件路径<br>key: 键 | Double: 属性值 |
| getBooleanValue(String filePath, String key) | 获取布尔属性值 | filePath: 属性文件路径<br>key: 键 | Boolean: 属性值 |
| getStringValue(String filePath, String key) | 获取字符串属性值 | filePath: 属性文件路径<br>key: 键 | String: 属性值 |
| getArrayStringValue(String filePath, String key) | 获取字符串数组属性值，默认以逗号分割 | filePath: 属性文件路径<br>key: 键 | String[]: 属性值数组 |
| getArrayStringValue(String filePath, String key, String delimiter) | 获取字符串数组属性值，自定义分隔符 | filePath: 属性文件路径<br>key: 键<br>delimiter: 分隔符 | String[]: 属性值数组 |
| getMap(String filePath, String key) | 获取匹配键的所有属性 | filePath: 属性文件路径<br>key: 键模式(可使用正则) | Map<String, String>: 匹配的属性 |
| getMap(String filePath) | 获取所有属性 | filePath: 属性文件路径 | Map<String, String>: 所有属性 |
| isExistsKey(String filePath, String key) | 检查键是否存在 | filePath: 属性文件路径<br>key: 键 | boolean: 是否存在 |
| deleteByKey(String filePath, String key) | 删除指定键的属性 | filePath: 属性文件路径<br>key: 键 | void |
| updateKey(String filePath, String oldkey, String newKey) | 更新属性的键名 | filePath: 属性文件路径<br>oldkey: 旧键名<br>newKey: 新键名 | boolean: 是否更新成功 |
| updateOrCreate(String filePath, String key, String newValue) | 更新或创建属性值 | filePath: 属性文件路径<br>key: 键名<br>newValue: 新值 | void |

### 使用注意事项

1. 每次调用方法都会重新加载属性文件，频繁操作同一文件性能较低
2. 获取数值类型时如果转换失败会返回null
3. 默认的数组分隔符是逗号(,)，可通过重载方法指定自定义分隔符
4. getMap方法的key参数支持正则表达式，匹配所有符合模式的键
5. updateOrCreate方法如果文件不存在会自动创建
6. 多线程环境下同时操作同一文件可能导致数据不一致，需要额外同步机制

### 使用示例

```java
// 获取字符串属性值
String host = PropertiesUtils.getStringValue("config.properties", "database.host");
System.out.println("数据库主机: " + host);

// 获取整数属性值
Integer port = PropertiesUtils.getIntValue("config.properties", "database.port");
System.out.println("数据库端口: " + port);

// 获取浮点数属性值
Float timeout = PropertiesUtils.getFloatValue("config.properties", "connection.timeout");
System.out.println("连接超时时间: " + timeout);

// 获取双精度浮点数属性值
Double rate = PropertiesUtils.getDoubleValue("config.properties", "exchange.rate");
System.out.println("汇率: " + rate);

// 获取布尔属性值
Boolean enabled = PropertiesUtils.getBooleanValue("config.properties", "feature.enabled");
System.out.println("功能是否启用: " + enabled);

// 获取字符串数组属性值，默认以逗号分割
String[] servers = PropertiesUtils.getArrayStringValue("config.properties", "server.list");
System.out.println("服务器列表:");
for (String server : servers) {
    System.out.println("- " + server);
}

// 获取字符串数组属性值，自定义分隔符
String[] roles = PropertiesUtils.getArrayStringValue("config.properties", "user.roles", ":");
System.out.println("用户角色:");
for (String role : roles) {
    System.out.println("- " + role);
}

// 获取匹配键的所有属性
Map<String, String> dbSettings = PropertiesUtils.getMap("config.properties", "database.*");
System.out.println("数据库相关配置:");
for (Map.Entry<String, String> entry : dbSettings.entrySet()) {
    System.out.println(entry.getKey() + " = " + entry.getValue());
}

// 获取所有属性
Map<String, String> allSettings = PropertiesUtils.getMap("config.properties");
System.out.println("所有配置:");
for (Map.Entry<String, String> entry : allSettings.entrySet()) {
    System.out.println(entry.getKey() + " = " + entry.getValue());
}

// 检查键是否存在
boolean hasLoggingConfig = PropertiesUtils.isExistsKey("config.properties", "logging.level");
System.out.println("是否存在日志级别配置: " + hasLoggingConfig);

// 更新属性的键名
boolean renamed = PropertiesUtils.updateKey("config.properties", "old.key", "new.key");
System.out.println("键重命名" + (renamed ? "成功" : "失败"));

// 更新或创建属性值
PropertiesUtils.updateOrCreate("config.properties", "app.version", "1.2.0");
System.out.println("应用版本已更新为1.2.0");

// 删除指定键的属性
PropertiesUtils.deleteByKey("config.properties", "temp.setting");
System.out.println("临时设置已删除");
``` 