package com.snbc.bbpf.commons.bytes.tlv;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TLV工具类单元测试类
 *
 * <AUTHOR>
 * @module 字节处理模块
 * @date 2025/07/25 12:30
 * @copyright 2024 山东新北洋信息技术股份有限公司. All rights reserved
 */
class TlvUtilsTest {

    @Test
    @DisplayName("测试工具类不能实例化")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testUtilityClassCannotBeInstantiated() {
        java.lang.reflect.InvocationTargetException exception = assertThrows(
            java.lang.reflect.InvocationTargetException.class, () -> {
                // 使用反射尝试创建实例
                java.lang.reflect.Constructor<TlvUtils> constructor = 
                    TlvUtils.class.getDeclaredConstructor();
                constructor.setAccessible(true);
                constructor.newInstance();
            });
        
        // 验证内部异常是UnsupportedOperationException
        assertTrue(exception.getCause() instanceof UnsupportedOperationException);
        assertEquals("Utility class cannot be instantiated", exception.getCause().getMessage());
    }

    @Test
    @DisplayName("测试基本数据类型长度计算")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testBasicDataTypeLengthCalculation() {
        // 测试基本整数类型
        assertEquals(1, TlvUtils.calculateValueLength((byte) 42));
        assertEquals(2, TlvUtils.calculateValueLength((short) 1000));
        assertEquals(4, TlvUtils.calculateValueLength(123456));
        assertEquals(8, TlvUtils.calculateValueLength(123456789L));

        // 测试浮点数类型
        assertEquals(4, TlvUtils.calculateValueLength(3.14f));
        assertEquals(8, TlvUtils.calculateValueLength(3.14159));

        // 测试布尔类型
        assertEquals(1, TlvUtils.calculateValueLength(true));
        assertEquals(1, TlvUtils.calculateValueLength(false));

        // 测试BigInteger
        BigInteger bigInt = new BigInteger("123456789");
        assertEquals(bigInt.toByteArray().length, TlvUtils.calculateValueLength(bigInt));

        // 测试BigDecimal
        BigDecimal bigDec = new BigDecimal("123.456");
        String bigDecStr = bigDec.toString();
        assertEquals(bigDecStr.getBytes(StandardCharsets.UTF_8).length, 
                    TlvUtils.calculateValueLength(bigDec));
    }

    @Test
    @DisplayName("测试字符串类型长度计算")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testStringLengthCalculation() {
        String testString = "Hello, 世界!";
        
        // 测试UTF-8长度
        int utf8Length = testString.getBytes(StandardCharsets.UTF_8).length;
        assertEquals(utf8Length, TlvUtils.calculateValueLength(testString, TlvDataType.STRING_UTF8));
        
        // 测试ASCII长度（仅英文部分）
        String asciiString = "Hello, World!";
        int asciiLength = asciiString.getBytes(StandardCharsets.US_ASCII).length;
        assertEquals(asciiLength, TlvUtils.calculateValueLength(asciiString, TlvDataType.STRING_ASCII));
    }

    @Test
    @DisplayName("测试字节数组长度计算")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testByteArrayLengthCalculation() {
        byte[] testArray = {0x01, 0x02, 0x03, 0x04, 0x05};
        assertEquals(5, TlvUtils.calculateValueLength(testArray));
        
        byte[] emptyArray = new byte[0];
        assertEquals(0, TlvUtils.calculateValueLength(emptyArray));
    }

    @Test
    @DisplayName("测试Map类型长度计算")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testMapLengthCalculation() {
        Map<String, Integer> testMap = new HashMap<>();
        testMap.put("key1", 100);
        testMap.put("key2", 200);
        
        int calculatedLength = TlvUtils.calculateValueLength(testMap);
        
        // Map长度 = 4字节(元素数量) + 每个键值对的TLV长度
        int expectedLength = 4; // 元素数量字段
        for (Map.Entry<String, Integer> entry : testMap.entrySet()) {
            expectedLength += TlvUtils.calculateTlvLength(entry.getKey());
            expectedLength += TlvUtils.calculateTlvLength(entry.getValue());
        }
        
        assertEquals(expectedLength, calculatedLength);
    }

    @Test
    @DisplayName("测试数组类型长度计算")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testArrayLengthCalculation() {
        List<Integer> testList = Arrays.asList(1, 2, 3, 4, 5);
        
        int calculatedLength = TlvUtils.calculateValueLength(testList);
        
        // 数组长度 = 4字节(元素数量) + 每个元素的TLV长度
        int expectedLength = 4; // 元素数量字段
        for (Integer element : testList) {
            expectedLength += TlvUtils.calculateTlvLength(element);
        }
        
        assertEquals(expectedLength, calculatedLength);
    }

    @Test
    @DisplayName("测试null值长度计算")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testNullLengthCalculation() {
        assertEquals(0, TlvUtils.calculateValueLength(null));
    }

    @Test
    @DisplayName("测试TLV总长度计算")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testTlvTotalLengthCalculation() {
        int testValue = 12345;
        
        // 默认配置：Type 1字节，Length 4字节，Value 4字节
        int totalLength = TlvUtils.calculateTlvLength(testValue);
        assertEquals(9, totalLength); // 1 + 4 + 4
        
        // 自定义配置：Type 2字节，Length 2字节，Value 4字节
        int customLength = TlvUtils.calculateTlvLength(testValue, 2, 2);
        assertEquals(8, customLength); // 2 + 2 + 4
    }

    @Test
    @DisplayName("测试TLV格式验证")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testTlvFormatValidation() {
        // 创建有效的TLV数据
        TlvEncoder encoder = new TlvEncoder();
        byte[] validTlv = encoder.encode(123);
        
        assertTrue(TlvUtils.validateTlvFormat(validTlv, 1, 4, ByteOrder.BIG_ENDIAN));
        
        // 测试无效数据
        assertFalse(TlvUtils.validateTlvFormat(null, 1, 4, ByteOrder.BIG_ENDIAN));
        assertFalse(TlvUtils.validateTlvFormat(new byte[]{0x01}, 1, 4, ByteOrder.BIG_ENDIAN));
        assertFalse(TlvUtils.validateTlvFormat(new byte[]{0x01, 0x00, 0x00, 0x00, 0x05}, 1, 4, ByteOrder.BIG_ENDIAN));
    }

    @Test
    @DisplayName("测试Type值提取")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testTypeExtraction() {
        TlvEncoder encoder = new TlvEncoder();
        byte[] tlvData = encoder.encode(123);
        
        int extractedType = TlvUtils.extractType(tlvData, 1, ByteOrder.BIG_ENDIAN);
        assertEquals(0x03, extractedType); // INTEGER type
        
        // 测试异常情况
        assertThrows(IllegalArgumentException.class, () -> 
            TlvUtils.extractType(null, 1, ByteOrder.BIG_ENDIAN));
        
        assertThrows(IllegalArgumentException.class, () -> 
            TlvUtils.extractType(new byte[]{}, 1, ByteOrder.BIG_ENDIAN));
    }

    @Test
    @DisplayName("测试Length值提取")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testLengthExtraction() {
        TlvEncoder encoder = new TlvEncoder();
        byte[] tlvData = encoder.encode(123);
        
        long extractedLength = TlvUtils.extractLength(tlvData, 1, 4, ByteOrder.BIG_ENDIAN);
        assertEquals(4, extractedLength); // Integer value length
        
        // 测试异常情况
        assertThrows(IllegalArgumentException.class, () -> 
            TlvUtils.extractLength(null, 1, 4, ByteOrder.BIG_ENDIAN));
        
        assertThrows(IllegalArgumentException.class, () -> 
            TlvUtils.extractLength(new byte[]{0x01}, 1, 4, ByteOrder.BIG_ENDIAN));
    }

    @Test
    @DisplayName("测试Value值提取")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testValueExtraction() {
        TlvEncoder encoder = new TlvEncoder();
        int originalValue = 123;
        byte[] tlvData = encoder.encode(originalValue);
        
        byte[] extractedValue = TlvUtils.extractValue(tlvData, 1, 4);
        assertEquals(4, extractedValue.length);
        
        // 验证提取的值是否正确
        int decodedValue = com.snbc.bbpf.commons.bytes.ByteOrderUtils.toInt(extractedValue, ByteOrder.BIG_ENDIAN);
        assertEquals(originalValue, decodedValue);
        
        // 测试异常情况
        assertThrows(IllegalArgumentException.class, () -> 
            TlvUtils.extractValue(null, 1, 4));
        
        assertThrows(IllegalArgumentException.class, () -> 
            TlvUtils.extractValue(new byte[]{0x01}, 1, 4));
    }

    @Test
    @DisplayName("测试编码器创建方法")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testEncoderCreation() {
        // 测试默认编码器创建
        TlvEncoder defaultEncoder = TlvUtils.createDefaultEncoder();
        assertNotNull(defaultEncoder);
        assertEquals(ByteOrder.BIG_ENDIAN, defaultEncoder.getByteOrder());
        
        // 测试自定义编码器创建
        TlvEncoder customEncoder = TlvUtils.createEncoder(
            ByteOrder.LITTLE_ENDIAN, StandardCharsets.US_ASCII, 2, 2);
        assertNotNull(customEncoder);
        assertEquals(ByteOrder.LITTLE_ENDIAN, customEncoder.getByteOrder());
        assertEquals(StandardCharsets.US_ASCII, customEncoder.getDefaultCharset());
        assertEquals(2, customEncoder.getTypeLength());
        assertEquals(2, customEncoder.getLengthFieldSize());
    }

    @Test
    @DisplayName("测试不支持的数据类型")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testUnsupportedDataTypes() {
        Object unsupportedObject = new Object();
        
        assertThrows(IllegalArgumentException.class, () -> 
            TlvUtils.calculateValueLength(unsupportedObject));
    }
}
