/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.crypt;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import com.snbc.bbpf.commons.system.OSType;
import com.snbc.bbpf.commons.system.SystemInfoUtil;

import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.security.PublicKey;
import java.security.PrivateKey;
import java.security.KeyFactory;
import java.security.KeyPairGenerator;
import java.security.KeyPair;
import java.security.Signature;
import java.security.SecureRandom;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;


/**
 * RSA算法填充方式判断
 * 1. 密钥要求1024位以上
 * 2. 加密的原文要求超过密钥长度的情况下也可以正常加解密
 * 3. 需要实现RSA的签名和验签
 * 4. 提供默认的签名和加密的加密算法，支持可以传入算法。
 * 5. 获取公钥和私钥
 * 6. 使用BouncyCastleProvider（简称BC）扩展可用算法
 * 我们使用公钥进行加密，然后使用私钥解密。理论上反过来也行（私钥加密，公钥解密），
 * 但这不安全且大多数库(包括java.security)也不支持。
 * 使用私钥对消息进行签名，然后使用公钥进行验证签名。
 * 这种机制可以确保消息确实来着公钥创建者(私钥持有者)，使得传输过程消息不会被篡改。
 *
 * <AUTHOR>
 * @module
 * @date 2023/11/26 21:19
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class RSAUtils {


    /***
     * 默认算法
     */
    private static final String BC_ALGORITHM = "BC";
    /**
     * 最大明文大小
     */
    private static final int MAX_ENCRYPT_BLOCK = 117;
    /**
     * 最大2048密文大小
     */
    private static final int MAX_DECRYPT_BLOCK = 256;
    /**
     * 最大1024密文大小
     */
    private static final int MAX_DECRYPT_BC_BLOCK = 128;
    /**
     * 密钥默认长度
     */
    private static final int MAX_KEY_DEFAULT_LEN = 2048;
    /**
     * 密钥默认长度
     */
    private static final int MIN_KEY_LEN = 1024;
    /**
     * 仅在linux或unix下支持的非堵塞算法，用于解决随机是由于CPU熵不够导致的卡死问题
     */
    private static final String ALGORITHM_NATIVEPRNGNONBLOCKING = "NativePRNGNonBlocking";


    /**
     * 公钥加密 默认
     *
     * @param content  加密内容，格式为UTF-8
     * @param publicKey  公钥
     * @return String 加密后的字符串
     * <AUTHOR>
     * @date 2023/12/07
     * @since 1.4.0
     */
    public static String rsaEncrypt(String content, String publicKey) throws Exception {
        if(StringUtils.isAnyBlank(content,publicKey)){
            throw new IllegalArgumentException("The rsaEncrypt (content,publicKey) is Blank");
        }
        PublicKey pubKey = getPublicKeyFromX509(SymmetricAlgorithm.RSA, publicKey);
        Cipher cipher = Cipher.getInstance(SymmetricAlgorithm.RSA.getValue());
        cipher.init(Cipher.ENCRYPT_MODE, pubKey);
        return rsaEncrypt(content,cipher);
    }
    /**
     * 加密 默认
     *
     * @param content  格式为UTF-8加密内容
     * @param strKey  公钥或私钥
     * @param keyType  true 公钥，false 私钥
     * @param algorithm  算法提供者
     *
     * @return String 加密后的字符串
     * <AUTHOR>
     * @date 2023/12/07
     * @since 1.4.0
     */
    public static String rsaEncrypt(String content, String strKey,
                                    boolean keyType,SymmetricAlgorithm algorithm)
            throws Exception {
        if(null==algorithm||StringUtils.isAnyBlank(content,strKey)){
            throw new IllegalArgumentException("The rsaEncrypt (content,strKey,algorithm) is Blank");
        }
        Cipher cipher = Cipher.getInstance(algorithm.getValue());
        cipher.init(Cipher.ENCRYPT_MODE, keyType?getPublicKeyFromX509(algorithm, strKey):
                getPrivateKeyFromPKCS8(algorithm,strKey));
        return rsaEncrypt(content,cipher);
    }
    /**
     * 加密 通用算法
     *
     * @param content  加密内容-格式为UTF-8
     * @param cipher  算法变量
     * @return String 加密后的字符串
     * <AUTHOR>
     * @date 2023/12/07
     * @since 1.4.0
     */
    public static String rsaEncrypt(String content, Cipher cipher) throws Exception {
        if(StringUtils.isBlank(content)){
            throw new IllegalArgumentException("The rsaEncrypt (content) is Blank");
        }
        byte[] data = content.getBytes(StandardCharsets.UTF_8);

        int inputLen = data.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;

        int i = 0;
        while (inputLen - offSet > 0) {
            byte[] cache;
            if (inputLen - offSet > MAX_ENCRYPT_BLOCK) {
                cache = cipher.doFinal(data, offSet, MAX_ENCRYPT_BLOCK);
            } else {
                cache = cipher.doFinal(data, offSet, inputLen - offSet);
            }
            out.write(cache, 0, cache.length);
            i++;
            offSet = i * MAX_ENCRYPT_BLOCK;
        }
        byte[] encryptedData = Base64.encodeBase64(out.toByteArray());
        out.close();
        return  new String(encryptedData, StandardCharsets.UTF_8);

    }
    /**
     * 扩展解密
     *
     * @param content  加密内容，格式为UTF-8
     * @param strKey  公钥或私钥，格式为UTF-8
     * @param keyType  true 公钥，false 私钥
     * @param algorithm  算法提供者
     * @param bras  true 2048 密钥长度，false 为1024密钥长度
     * @return String 解密后的字符串
     * <AUTHOR>
     * @date 2023/12/07
     * @since 1.4.0
     */
    public static String rsaDecrypt(String content, String strKey,
                                    boolean keyType,SymmetricAlgorithm algorithm,Boolean bras)
            throws Exception {
        if(null==algorithm||StringUtils.isAnyBlank(content,strKey)){
            throw new IllegalArgumentException("The rsaDecrypt (content,strKey,algorithm) is Blank");
        }
        Cipher cipher = Cipher.getInstance(algorithm.getValue());
        cipher.init(Cipher.DECRYPT_MODE, keyType?getPublicKeyFromX509(algorithm, strKey):
                getPrivateKeyFromPKCS8(algorithm,strKey));
        return rsaDecrypt(content,cipher,bras);
    }
    /**
     * 私钥解密 默认
     *
     * @param content  加密内容 UTF-8
     * @param privateKey 私钥
     * @return String 解密后的字符串
     * <AUTHOR>
     * @date 2023/12/07
     * @since 1.4.0
     */
    public static String rsaDecrypt(String content, String privateKey) throws Exception {
        if(StringUtils.isAnyBlank(content,privateKey)){
            throw new IllegalArgumentException("The rsaDecrypt (content,privateKey) is Blank");
        }
        PrivateKey priKey = getPrivateKeyFromPKCS8(SymmetricAlgorithm.RSA, privateKey);
        Cipher cipher = Cipher.getInstance(SymmetricAlgorithm.RSA.getValue());
        cipher.init(Cipher.DECRYPT_MODE, priKey);
        return rsaDecrypt(content,cipher,true);
    }
    /**
     * 共用解密
     *
     * @param content  加密内容，格式为UTF-8
     * @param cipher  算法变量
     * @param bRas  为true 表示 256，为false表示128
     * @return String 加密后的字符串
     * <AUTHOR>
     * @date 2023/12/07
     * @since 1.4.0
     */
    public static String rsaDecrypt(String content, Cipher cipher, boolean bRas) throws Exception {
        if(StringUtils.isBlank(content)){
            throw new IllegalArgumentException("The rsaDecrypt (content) is Blank");
        }
        byte[] encryptedData = Base64.decodeBase64(content.getBytes(StandardCharsets.UTF_8));
        int inputLen = encryptedData.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;
        int clen=bRas?MAX_DECRYPT_BLOCK:MAX_DECRYPT_BC_BLOCK;
        int i = 0;
        while (inputLen - offSet > 0) {
            byte[] cache;
            if (inputLen - offSet > clen) {
                cache = cipher.doFinal(encryptedData, offSet, clen);
            } else {
                cache = cipher.doFinal(encryptedData, offSet, inputLen - offSet);
            }
            out.write(cache, 0, cache.length);
            i++;
            offSet = i * clen;
        }
        byte[] decryptedData = out.toByteArray();
        out.close();
        return  new String(decryptedData, StandardCharsets.UTF_8);
    }

    /**
     * 从密文中获取私钥
     *
     * @param algorithm  算法提供者，RSA或BC
     * @return PublicKey 公钥
     * <AUTHOR>
     * @date 2023/12/07
     * @since 1.4.0
     */
    public static PrivateKey getPrivateKeyFromPKCS8(SymmetricAlgorithm algorithm, String privateKey) throws Exception {
        if(null==algorithm||StringUtils.isBlank(privateKey)){
            throw new IllegalArgumentException("The getPrivateKeyFromPKCS8 (algorithm,privateKey) is Blank");
        }
        // 通过PKCS#8编码的Key指令获得私钥对象
        KeyFactory keyFactory = KeyFactory.getInstance(algorithm.getValue());
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKey));
        return keyFactory.generatePrivate(pkcs8KeySpec);
    }
    /**
     * 从密文中获取公钥
     *
     * @return PublicKey 公钥
     * <AUTHOR>
     * @date 2023/12/07
     * @since 1.4.0
     */
    public static PublicKey getPublicKeyFromX509(SymmetricAlgorithm algorithm, String publicKey) throws Exception {
        //判断参数是否为空，如果有一个为空，则包参数异常
        if(null==algorithm||StringUtils.isBlank(publicKey)){
            throw new IllegalArgumentException("The getPublicKeyFromX509 (algorithm,publicKey) is Blank");
        }
        // 通过X509编码的Key指令获得公钥对象
        KeyFactory keyFactory = KeyFactory.getInstance(algorithm.getValue());
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(Base64.decodeBase64(publicKey));
        return keyFactory.generatePublic(x509KeySpec);
    }
    /**
     * 获得默认得2048 RSA 公私钥
     *
     * @return Map<String, String> 公私钥
     * <AUTHOR>
     * @throws NoSuchAlgorithmException
     * @date 2023/12/07
     * @since 1.4.0
     */
    public static Map<String, String> createKeys() throws NoSuchAlgorithmException {
        KeyPairGenerator keyPairGenerator = null;
        try {
            keyPairGenerator = KeyPairGenerator.getInstance(SymmetricAlgorithm.RSA.getValue());
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
        SecureRandom secureRandom = null;
        if(SystemInfoUtil.getSystemType().equals(OSType.LINUX)||
                SystemInfoUtil.getSystemType().equals(OSType.UNIX)||
                SystemInfoUtil.getSystemType().equals(OSType.MACOS)){
            secureRandom = SecureRandom.getInstance(ALGORITHM_NATIVEPRNGNONBLOCKING);
        }else{
            secureRandom = SecureRandom.getInstanceStrong();
        }
        // step2：初始化keyPairGenerator密钥对生成器 ， 确定密钥长度大小与随机源
        keyPairGenerator.initialize(MAX_KEY_DEFAULT_LEN,secureRandom);
        return createKeys(keyPairGenerator);
    }
    /**
     * 获得 公私钥
     *
     * @param len 密钥长度 1024,2048
     * @param providerName 目前只支持 RSA 和
     * @return Map<String, String> 公私钥
     * <AUTHOR>
     * @date 2023/12/07
     * @since 1.4.0
     */
    public static Map<String, String> createKeys(int len,String providerName) {
        // step1 ： 判断长度是否属于1024 或2048
        if(len!=MIN_KEY_LEN && len!=MAX_KEY_DEFAULT_LEN){
            throw new IllegalArgumentException("[RSAUtils][createKeys] len is error mast 1024 or 2048");
        }
        // step2 ： 判断算法是否是目前支持并生成具体对象
        KeyPairGenerator keyPairGenerator=null;
        if(providerName.equalsIgnoreCase(SymmetricAlgorithm.RSA.getValue())){
            try {
                keyPairGenerator = KeyPairGenerator.getInstance(SymmetricAlgorithm.RSA.getValue());
            } catch (NoSuchAlgorithmException e) {
                throw new RuntimeException(e);
            }
        }else if(providerName.equalsIgnoreCase(BC_ALGORITHM)){
            try {
                keyPairGenerator = KeyPairGenerator.getInstance(SymmetricAlgorithm.RSA.getValue(),new BouncyCastleProvider());
            } catch (NoSuchAlgorithmException e) {
                throw new RuntimeException(e);
            }
        }else {
            try {
                keyPairGenerator = KeyPairGenerator.getInstance(SymmetricAlgorithm.RSA.getValue(),providerName);
            } catch (NoSuchAlgorithmException|NoSuchProviderException e) {
                throw new RuntimeException(e);
            }
        }
        if(null==keyPairGenerator){
            throw new IllegalArgumentException("[RSAUtils][createKeys] providerName is error mast RSA or BC");
        }
        // step3：初始化keyPairGenerator密钥对生成器 ， 确定密钥长度大小与随机源
        keyPairGenerator.initialize(len,new SecureRandom());
        return createKeys(keyPairGenerator);
    }
    /**
     * 获得扩展的 公私钥
     *
     * @param keyPairGenerator 扩展得公私钥
     * @return Map<String, String> 公私钥
     * <AUTHOR>
     * @date 2023/12/07
     * @since 1.4.0
     */
    public static Map<String, String> createKeys(KeyPairGenerator keyPairGenerator){
        Map<String,String> keyPairMap = new HashMap<>();
        try{
            // step3：获取密钥对
            KeyPair keyPair = keyPairGenerator.generateKeyPair();
            // step4：获取密钥 encodeBase64URLSafeString
            String privateKey = Base64.encodeBase64URLSafeString(keyPair.getPrivate().getEncoded());
            String publicKey = Base64.encodeBase64URLSafeString(keyPair.getPublic().getEncoded());

            keyPairMap.put("privateKey", privateKey);
            keyPairMap.put("publicKey", publicKey);

        }catch (Exception e){
            throw new IllegalArgumentException("[RSAUtils][createKeys] is error："+e);
        }
        return keyPairMap;
    }

    /**
     * RSA私钥签名
     * @param content 待签名数据 ，格式为UTF-8
     * @param privateKey 私钥
     * @return 签名值
     * <AUTHOR>
     * @date 2023/12/07
     * @since 1.4.0
     */
    public static String sign(String content, String privateKey) throws Exception{
        //判断参数是否为空，如果有一个为空，则包参数异常
        if(StringUtils.isAnyBlank(content,privateKey)){
            throw new IllegalArgumentException("The sign (content,privateKey) is Blank");
        }
        PrivateKey priKey = getPrivateKeyFromPKCS8(SymmetricAlgorithm.RSA, privateKey);
        Signature signature = Signature.getInstance(Padding.MD5WITHRSA.getValue());
        signature.initSign(priKey);
        signature.update(content.getBytes(StandardCharsets.UTF_8));
        byte[] signed = signature.sign();
        return new String(Base64.encodeBase64URLSafe(signed), StandardCharsets.UTF_8);
    }
    /**
     * RSA私钥签名
     * @param content 待签名数据 ，格式为UTF-8
     * @param privateKey 私钥
     * @param algorithm  私钥算法
     * @param padding 签名算法
     * @return 签名值
     * <AUTHOR>
     * @date 2023/12/07
     * @since 1.4.0
     */
    public static String sign(String content, String privateKey, SymmetricAlgorithm algorithm, Padding padding) throws Exception{
        //判断参数是否为空，如果有一个为空，则包参数异常
        if(null==algorithm||null==padding||StringUtils.isAnyBlank(content,privateKey)){
            throw new IllegalArgumentException("The sign (content,privateKey,algorithm,padding) is Blank");
        }
        PrivateKey priKey = getPrivateKeyFromPKCS8(algorithm, privateKey);
        Signature signature = Signature.getInstance(padding.getValue());
        signature.initSign(priKey);
        signature.update(content.getBytes(StandardCharsets.UTF_8));
        byte[] signed = signature.sign();
        return new String(Base64.encodeBase64URLSafe(signed), StandardCharsets.UTF_8);
    }
    /**
     * 通过公钥验签
     * @param content 验签内容 ，格式为BTF-8
     * @param sign  签名 ，格式为BTF-8
     * @param publicKey 公钥
     * @return 验签结果
     * <AUTHOR>
     * @date 2023/12/07
     * @since 1.4.0
     */
    public static boolean verifySign(String content, String sign, String publicKey) throws Exception {
        //判断参数是否为空，如果有一个为空，则包参数异常
        if(StringUtils.isAnyBlank(content,sign,publicKey)){
            throw new IllegalArgumentException("The verifySign (content,sign,publicKey) is Blank");
        }
        PublicKey pubKey = getPublicKeyFromX509(SymmetricAlgorithm.RSA, publicKey);
        Signature signature = Signature.getInstance(Padding.MD5WITHRSA.getValue());
        signature.initVerify(pubKey);
        signature.update(content.getBytes(StandardCharsets.UTF_8));
        return signature.verify(Base64.decodeBase64(sign.getBytes(StandardCharsets.UTF_8)));
    }
    /**
     * 通过公钥验签
     * @param content 验签内容 ，格式为BTF-8
     * @param sign  签名 ，格式为BTF-8
     * @param publicKey 公钥
     * @param algorithm  公钥算法
     * @param padding 签名算法
     * @return 验签结果
     * <AUTHOR>
     * @date 2023/12/07
     * @since 1.4.0
     */
    public static boolean verifySign(String content, String sign, String publicKey
            , SymmetricAlgorithm algorithm, Padding padding) throws Exception {
        //判断参数是否为空，如果有一个为空，则包参数异常
        if(null==algorithm||null==padding||
                StringUtils.isAnyBlank(content,sign,publicKey)){
            throw new IllegalArgumentException("The verifySign (content,sign,publicKey,algorithm,padding) is Blank");
        }
        PublicKey pubKey = getPublicKeyFromX509(algorithm, publicKey);
        Signature signature = Signature.getInstance(padding.getValue());
        signature.initVerify(pubKey);
        signature.update(content.getBytes(StandardCharsets.UTF_8));
        return signature.verify(Base64.decodeBase64(sign.getBytes(StandardCharsets.UTF_8)));
    }
}
