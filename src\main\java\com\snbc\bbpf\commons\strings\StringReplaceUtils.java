/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.strings;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 【字符串处理】 对常用的字符串格式进行校验，去除字符串中指定字符或字符串
 * <p>  1 可以指定去除的字符或字符串</p>
 * <p>  2 可以对指定开始和结束位置中的字符串进行去除操作</p>
 * <p>  3 如果字符串为空或空白，则不做处理</p>
 * <p>  4 可将字符串包含的中文字符串去除</p>
 * <p>  5 可将字符串包含的英文字符串去除</p>
 * <AUTHOR>
 * @module 字符串处理
 * @date 2023/4/26 9:58
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class StringReplaceUtils {

    private static String REGEX_CHINESE = "[\u4e00-\u9fff]";// 中文正则
    private static String REGEX_ENGLISH = "[a-zA-Z]";// 中文正则

    private StringReplaceUtils() {
        throw new IllegalStateException("Utility class");
    }
    /**
     * 对常用的字符串格式进行校验，可将srcString包含在 regStr 任意字符串 替换成  destStr，若要去除则将destStr设置为空
     * 若endIndex为零或大于最大长度默则为该字符串最大长度
     * @param srcString  源操作字符串
     * @param regStr   要替换的字符串
     * @param destStr 替换的目标字符串可为空,为空表示""
     * @param beginIndex 开始位置 默认为开始位置
     * @param endIndex  结束位置  默认为结束位置
     * @return
     * @throws IllegalArgumentException
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/26
     */
    public static String replaceAll(String srcString, String regStr,String destStr,int beginIndex,int endIndex ) {
        if(null==destStr ){
            destStr="";
        }
        endIndex=checkReplaceAll(srcString,  regStr,  beginIndex, endIndex);
        //设置 要进行替换的内同
        Pattern p = Pattern.compile("["+regStr+"]");
        //这里把想要替换的字符串传进来
        Matcher m = p.matcher(srcString.substring(beginIndex,endIndex));
        //输出替换后的字符串
        String newString=m.replaceAll(destStr);
        //进行局部替换
        if(endIndex>beginIndex){
            StringBuilder sb=new StringBuilder(srcString.substring(0,beginIndex));
            sb.append(newString);
            sb.append(srcString.substring(endIndex));
            newString=sb.toString();
        }
        return  newString;
    }
    /**
     * 对常用的字符串格式进行校验，可将srcString包含在 regStr 任意字符串 替换成  destStr，若要去除则将destStr设置为空
     * @param srcString  源操作字符串
     * @param regStr   要替换的字符串
     * @param destStr 替换的目标字符串可为空,为空表示""
     * @return
     * @throws IllegalArgumentException
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/26
     */
    public static String replaceAll(String srcString, String regStr,String destStr ) {
        if(null==destStr ){
            destStr="";
        }
        checkReplace(srcString,  regStr);
        //设置 要进行替换的内同
        Pattern p = Pattern.compile("["+regStr+"]");
        //这里把想要替换的字符串传进来
        Matcher m = p.matcher(srcString);
        //输出替换后的字符串
        return m.replaceAll(destStr);
    }

    /**
     * 对常用的字符串格式进行校验，可将srcString包含在 regStr 字符串 替换成  destStr，若要去除则将destStr设置为空
     * 若endIndex为零或大于最大长度默则为该字符串最大长度
     * @param srcString  源操作字符串
     * @param regStr   要替换的字符串
     * @param destStr 替换的目标字符串可为空，为空表示""
     * @param beginIndex 开始位置 默认为开始位置
     * @param endIndex  结束位置  默认为结束位置
     * @return
     * @throws IllegalArgumentException
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/26
     */
    public static String replaceStringAll(String srcString, String regStr,String destStr,int beginIndex,int endIndex ) {
        endIndex=checkReplaceAll(srcString,  regStr,  beginIndex, endIndex);
        if(null==destStr ){
            destStr="";
        }
        String newString=srcString.substring(beginIndex,endIndex).replace(regStr,destStr);
        //进行局部替换
        if(endIndex>beginIndex){
            StringBuilder sb=new StringBuilder(srcString.substring(0,beginIndex));
            sb.append(newString);
            sb.append(srcString.substring(endIndex));
            newString=sb.toString();
        }
        return  newString;
    }
    /**
     * 对常用的字符串格式进行校验，可将srcString包含在 regStr 字符串 替换成  destStr，若要去除则将destStr设置为空
     * @param srcString  源操作字符串
     * @param regStr   要替换的字符串
     * @param destStr 替换的目标字符串可为空 为空则用""
     * @return
     * @throws IllegalArgumentException
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/26
     */
    public static String replaceStringAll(String srcString, String regStr,String destStr ) {
        checkReplace(srcString,  regStr);
        if(null==destStr ){
            destStr="";
        }
        return  srcString.replace(regStr,destStr);
    }
    /**
     * 对常用的字符串格式进行校验，方法裏的入参进行校验
     * @param srcString  源操作字符串
     * @param regStr   要替换的字符串
     * @param beginIndex 开始位置 默认为开始位置
     * @param endIndex  结束位置  默认为结束位置
     * @return
     * @throws IllegalArgumentException 检查替换的入参
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/26
     */
    public static int checkReplaceAll(String srcString, String regStr,int beginIndex,int endIndex ) {
        checkReplace(srcString,regStr);
        if(0>beginIndex){
            throw new IllegalArgumentException("Begin Replace Index is Error must pass 0");
        }
        if(0>endIndex){
            throw new IllegalArgumentException("End Replace Index is Error must pass 0");
        }
        if(0==endIndex){
            endIndex=srcString.length();
        }
        if(endIndex<=beginIndex ){
            throw new IllegalArgumentException("End Replace Index is Error must pass beginIndex");
        }
        if(endIndex>srcString.length()){
            endIndex=srcString.length();
        }
        return endIndex;
    }
    /**
     * 对常用的字符串格式进行校验，方法裏的入参进行校验
     * @param srcString  源操作字符串
     * @param regStr   要替换的字符串
     * @return
     * @throws IllegalArgumentException 检查替换的入参
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/26
     */
    public static Boolean checkReplace(String srcString, String regStr) {
        if(StringEmptyCheck.isEmpty(srcString)){
            throw new IllegalArgumentException("Source String is null");
        }
        if(StringEmptyCheck.isEmpty(regStr)){
            throw new IllegalArgumentException("Replace reg String is null");
        }
        return true;
    }


    /**
     * 对常用的字符串格式进行校验，可将srcString包含的中文字符串去除
     * 如果字符串的编码和项目编码一致，则中文能够正常去除，否则，结果将不可空
     * 注意：其中中文字符为unicode中2W多基本汉字和基本扩充汉字，如果需要删除其他请自行查找对应unicode编码进行实现。
     * @param srcString  源操作字符串
     * @return 去除中文后的字符串
     * @throws IllegalArgumentException
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/5/22
     */
    public static String removeChinese(String srcString) {
        return  StringReplaceUtils.replaceAll(srcString,REGEX_CHINESE,"");
    }

    /**
     * 对常用的字符串格式进行校验，可将srcString包含的英文字符串去除
     * @param srcString  源操作字符串
     * @return 去除英文后的字符串
     * @throws IllegalArgumentException
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/5/22
     */
    public static String removeEnglish(String srcString) {
        return  StringReplaceUtils.replaceAll(srcString,REGEX_ENGLISH,"");
    }

}
