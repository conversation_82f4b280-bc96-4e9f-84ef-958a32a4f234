/*
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.commons.strings.security.parse;


import org.apache.commons.lang3.StringUtils;

/**
 * @ClassName:  AddressParse
 * 地址脱敏
 * @module:    字符脱敏
 * @Author:    yangweipeng
 * @date:      2023/05/06
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class AddressParse implements IDesensitizedParse {


    /**
     * 最大隐藏字符数
     */
    private static final Integer ADDRESS_DES_LENGTH =6;
    /**
     * 最小隐藏字符
     */
    private static final Integer MINLENGTH=1;
    /**
     * 【地址】只显示到地区，不显示详细地址，比如：北京市海淀区****
     *
     * @param address
     * @return
     */
    @Override
    public String parseString(String address) {
        if(StringUtils.isEmpty(address)){
            return address;
        }
        int length = StringUtils.length(address);
        int clength=(length> ADDRESS_DES_LENGTH)? ADDRESS_DES_LENGTH :MINLENGTH;
        return StringUtils.rightPad(StringUtils.left(address, clength), length, "*");
    }
}

