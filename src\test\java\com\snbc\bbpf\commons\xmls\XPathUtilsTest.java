package com.snbc.bbpf.commons.xmls;

import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * xml工具类单元测试
 *
 * <AUTHOR>
 * @date 2023/9/25 13:30
 */
class XPathUtilsTest {

    private String xmlFormateError;

    private String xmlHaveCDATA;

    private String xml;

    private final String xmlBlank = "";

    @BeforeEach
    void setup() {
        xmlFormateError = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<person> \n" +
                "  <p1> \n" +
                "    <name id=\"1001\">zsdd<name>  \n" +
                "    <age>100</age>  \n" +
                "    <sex>nv</sex> \n" +
                "  </p1>  \n" +
                "  <p1> \n" +
                "    <name id='' code= ''>ls</name>  \n" +
                "    <age>11</age> \n" +
                "  </p1> \n" +
                "</person>";
        xmlHaveCDATA = xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<person> \n" +
                "  <p1> \n" +
                "    <name id=\"1001\"><![CDATA[<张三>]]></name>  \n" +
                "    <age>100</age>  \n" +
                "    <sex>nv</sex> \n" +
                "  </p1>  \n" +
                "  <p1> \n" +
                "    <name id=\"1002\" code=\"lisi\">李四</name>  \n" +
                "    <age>11</age> \n" +
                "  </p1> \n" +
                "</person>";

        xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<person> \n" +
                "  <p1> \n" +
                "    <name id=\"1001\">张三</name>  \n" +
                "    <age>100</age>  \n" +
                "    <sex>nv</sex> \n" +
                "  </p1>  \n" +
                "  <p1> \n" +
                "    <name id=\"1002\" code=\"lisi\">李四</name>  \n" +
                "    <age>11</age> \n" +
                "  </p1> \n" +
                "</person>";
    }


    @Test
    @DisplayName("校验xml字符串格式是否正确 未指定xml字符串")
    @Tags({
            @Tag("@id:55"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/9/25")
    })
    void testVerfyXmlFormat_blank() {
        Assertions.assertThrows(IllegalArgumentException.class,()-> XPathUtils.verfyXmlFormat(xmlBlank));
    }

    @Test
    @DisplayName("校验xml字符串格式是否正确 xml格式错误")
    @Tags({
            @Tag("@id:55"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/9/25")
    })
    void testVerfyXmlFormat_error() {
        Assertions.assertThrows(RuntimeException.class,()-> XPathUtils.verfyXmlFormat(xmlFormateError));
    }

    @Test
    @DisplayName("校验xml字符串格式是否正确 xml格式正确")
    @Tags({
            @Tag("@id:55"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/9/25")
    })
    void testVerfyXmlFormat_success() {
        Assertions.assertAll(()->XPathUtils.verfyXmlFormat(xml));
    }

    @Test
    @DisplayName("校验带有CDATA的xml字符串格式是否正确 xml格式正确")
    @Tags({
            @Tag("@id:55"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/9/25")
    })
    void testVerfyXmlFormat_haveCDATA() {
        Assertions.assertAll(()->XPathUtils.verfyXmlFormat(xmlHaveCDATA));
    }


    @Test
    @DisplayName("从指定字符串中查找满足条件的节点对应的字符串 xpath格式错误")
    @Tags({
            @Tag("@id:55"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/9/25")
    })
    void testGetChildStringByXpath_xpathError() {
        String xpath = "//persons[]";
        Assertions.assertThrows(RuntimeException.class,()-> XPathUtils.getChildStringByXpath(xml,xpath));
    }

    @Test
    @DisplayName("从指定字符串中查找满足条件的节点对应的字符串 获取所有p1节点数据")
    @Tags({
            @Tag("@id:55"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/9/25")
    })
    void testGetChildStringByXpath_allp1() {
        String xpath = "//p1";
        Assertions.assertEquals(2,XPathUtils.getChildStringByXpath(xml,xpath).size());
    }

    @Test
    @DisplayName("从指定字符串中查找满足条件的节点对应的字符串 获取包含code属性的name节点")
    @Tags({
            @Tag("@id:55"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/9/25")
    })
    void testGetChildStringByXpath_namecode() {
        String xpath = "//name[@code]";
        Assertions.assertEquals(1,XPathUtils.getChildStringByXpath(xml,xpath).size());
    }


    @Test
    @DisplayName("从指定字符串中查找满足条件的节点对应的字符串 待获取节点不存在")
    @Tags({
            @Tag("@id:55"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/9/25")
    })
    void testGetChildStringByXpath_nodenotexit() {
        String xpath = "//name2";
        Assertions.assertEquals(0,XPathUtils.getChildStringByXpath(xml,xpath).size());
    }

    @Test
    @DisplayName("从指定字符串中查找满足条件的节点对应的字符串 待获取节点不存在")
    @Tags({
            @Tag("@id:55"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/9/25")
    })
    void testGetChildStringByXpath_getnodecdata() {
        String xpath = "//name[@id='1001']";
        List<String> result = XPathUtils.getChildStringByXpath(xml, xpath);
        result.forEach(str-> System.out.println(str));
        Assertions.assertEquals(1,result.size());
    }

    @Test
    @DisplayName("从指定字符串中查找满足条件的节点对应的字符串 待获取节点不存在")
    @Tags({
            @Tag("@id:55"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/9/25")
    })
    void testGetChildStringByXpath_noxmlstr() {
        String xpath = "//name[@id='1001']";
        String xmlContent = "abc";
        Assertions.assertThrows(RuntimeException.class,()->XPathUtils.getChildStringByXpath(xmlContent, xpath));
    }
    /********************************************************************************************************************************************************/

    @ParameterizedTest
    @CsvSource({
            "nameError, id, java.lang.RuntimeException: No nodes found that meet the criteria",
            "name, idError,java.lang.RuntimeException: No nodes found that meet the criteria",
            " , id, Node name cannot be empty",
            "name, , Property name cannot be empty"
    })
    @DisplayName("参数异常校验")
    @Tags({
            @Tag("@id:70"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/10/27")
    })
    void testValidateArgs_argsError(String nodeName, String attributeName,String message) {
        RuntimeException getAllNodeException = Assertions.assertThrows(RuntimeException.class, () -> XPathUtils.getAllNodeValue(xml, nodeName, attributeName));
        Assertions.assertEquals(message,getAllNodeException.getMessage());
        RuntimeException getFirstNodeException = Assertions.assertThrows(RuntimeException.class, () -> XPathUtils.getFirstNodeValue(xml, nodeName,
                attributeName));
        Assertions.assertEquals(message,getFirstNodeException.getMessage());
        RuntimeException getAllNodeXmlExeption = Assertions.assertThrows(RuntimeException.class, () -> XPathUtils.getAllNodeXmlStr(xml, nodeName,
                attributeName));
        Assertions.assertEquals(message,getAllNodeXmlExeption.getMessage());
        RuntimeException getFirstNodeXmlException = Assertions.assertThrows(RuntimeException.class, () -> XPathUtils.getFirstNodeXmlStr(xml, nodeName,
                attributeName));
        Assertions.assertEquals(message,getFirstNodeXmlException.getMessage());
        RuntimeException getAllNodeAttributeValueException = Assertions.assertThrows(RuntimeException.class, () -> XPathUtils.getAllNodeAttributeValue(xml,
                nodeName, attributeName));
        Assertions.assertEquals(message,getAllNodeAttributeValueException.getMessage());
        RuntimeException getFirstNodeAttributeValueException = Assertions.assertThrows(RuntimeException.class, () -> XPathUtils.getFirstNodeAttributeValue(xml,
                nodeName,
                attributeName));
        Assertions.assertEquals(message,getFirstNodeAttributeValueException.getMessage());
    }

    /*****************************************************************************************************************************************/

    @Test
    @DisplayName("获取第一个指定名称且包含指定属性的节点的取值 成功")
    @Tags({
            @Tag("@id:70"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/10/27")
    })
    void testGetFirstNodeValue_success() {
        String nodeName = "name";
        String attributeName = "id";
        Assertions.assertEquals("张三",XPathUtils.getFirstNodeValue(xml,nodeName,attributeName));
    }


    /*****************************************************************************************************************************************/

    @Test
    @DisplayName("获取所有指定名称且包含指定属性的节点的取值 成功")
    @Tags({
            @Tag("@id:70"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/10/27")
    })
    void testGetAllNodeValue_success() {
        String nodeName = "name";
        String attributeName = "id";
        List<String> allNodeValue = XPathUtils.getAllNodeValue(xml, nodeName, attributeName);
        Assertions.assertEquals(2, allNodeValue.size());
        Assertions.assertEquals("张三",allNodeValue.get(0));
        Assertions.assertEquals("李四",allNodeValue.get(1));
    }

    /*****************************************************************************************************************************************/

    @Test
    @DisplayName("获取第一个指定名称且包含指定属性的节点的xml字符串 成功")
    @Tags({
            @Tag("@id:70"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/10/27")
    })
    void testGetFirstNodeXmlStr_success() {
        String nodeName = "name";
        String attributeName = "id";
        Assertions.assertEquals("<name id=\"1001\">张三</name>",XPathUtils.getFirstNodeXmlStr(xml,nodeName, attributeName));
    }

    /*****************************************************************************************************************************************/

    @Test
    @DisplayName("获取所有指定名称且包含指定属性的节点的xml字符串 成功")
    @Tags({
            @Tag("@id:70"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/10/27")
    })
    void testGetAllNodeXmlStr_success() {
        String nodeName = "name";
        String attributeName = "id";
        List<String> allNodeXmlStr = XPathUtils.getAllNodeXmlStr(xml, nodeName, attributeName);
        Assertions.assertEquals(2, allNodeXmlStr.size());
        Assertions.assertEquals("<name id=\"1001\">张三</name>",allNodeXmlStr.get(0));
        Assertions.assertEquals("<name id=\"1002\" code=\"lisi\">李四</name>",allNodeXmlStr.get(1));
    }

    /*****************************************************************************************************************************************/

    @Test
    @DisplayName("获取第一个指定名称且包含指定属性的节点的该属性取值 成功")
    @Tags({
            @Tag("@id:70"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/10/27")
    })
    void testGetFirstNodeAttributeValue_success() {
        String nodeName = "name";
        String attributeName = "id";
        Assertions.assertEquals("1001",XPathUtils.getFirstNodeAttributeValue(xml,nodeName, attributeName));
    }

    /*****************************************************************************************************************************************/

    @Test
    @DisplayName("获取所有指定名称且包含指定属性的节点的该属性取值 成功")
    @Tags({
            @Tag("@id:70"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/10/27")
    })
    void testGetAllNodeAttributeValue_success() {
        String nodeName = "name";
        String attributeName = "id";
        List<String> allNodeAttributeValue = XPathUtils.getAllNodeAttributeValue(xml, nodeName, attributeName);
        Assertions.assertEquals(2, allNodeAttributeValue.size());
        Assertions.assertEquals("1001",allNodeAttributeValue.get(0));
        Assertions.assertEquals("1002",allNodeAttributeValue.get(1));
    }


    @Test
    @DisplayName("删除满足条件的节点 成功")
    @Tags({
            @Tag("@id:101"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2024/06/07")
    })
    void testRemoveNodeSuccess() {
        String xpath = "//p1";
        String removeNode = XPathUtils.removeNode(xml, xpath);
        Assertions.assertEquals(0,XPathUtils.getChildStringByXpath(removeNode,xpath).size());
    }

    @Test
    @DisplayName("删除满足条件的节点 成功")
    @Tags({
            @Tag("@id:101"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2024/06/07")
    })
    void testRemoveNodeSuccess_NodeNotExist() {
        String xpath = "//p2";
        String removeNode = XPathUtils.removeNode(xml, xpath);
        Assertions.assertEquals(0,XPathUtils.getChildStringByXpath(removeNode,xpath).size());
    }

    @Test
    @DisplayName("删除满足条件的节点 失败 不允许删除根节点")
    @Tags({
            @Tag("@id:101"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2024/06/07")
    })
    void testRemoveNodeError_RootNode() {
        String xpath = "//person";
        Assertions.assertThrows(RuntimeException.class, () -> XPathUtils.removeNode(xml, xpath));
    }

    @Test
    @DisplayName("删除满足条件的属性 成功")
    @Tags({
            @Tag("@id:101"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2024/06/07")
    })
    void testRemoveAttributeSuccess() {
        String xpath = "//name[@code]";
        String removeNode = XPathUtils.removeAttribute(xml, xpath,"code");
        Assertions.assertEquals(0,XPathUtils.getChildStringByXpath(removeNode,xpath).size());
    }

    @Test
    @DisplayName("删除满足条件的属性 成功")
    @Tags({
            @Tag("@id:101"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2024/06/07")
    })
    void testRemoveAttributeError() {
        String xpath = "//name[@code]";
        String removeNode = XPathUtils.removeAttribute(xml, xpath,"code2");
        Assertions.assertEquals(1,XPathUtils.getChildStringByXpath(removeNode,xpath).size());
    }

    @Test
    @DisplayName("删除满足条件的属性 成功")
    @Tags({
            @Tag("@id:101"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2024/06/07")
    })
    void testGetNodeTextSuccess() {
        String xpath = "//name[@code]";
        List<String> nodeText = XPathUtils.getNodeText(xml, xpath, false);
        Assertions.assertEquals(1,nodeText.size());
    }

    @Test
    @DisplayName("删除满足条件的节点Text值 成功 存在匹配的节点")
    @Tags({
            @Tag("@id:101"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2024/08/10")
    })
    void testGetNodeTextSuccess_NoMatch() {
        String xpath = "//name[@CODE]";
        List<String> nodeText = XPathUtils.getNodeText(xml, xpath, false);
        Assertions.assertEquals(0,nodeText.size());
    }

    @Test
    @DisplayName("删除满足条件的节点Text值 成功 不存在匹配的节点")
    @Tags({
            @Tag("@id:101"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2024/08/10")
    })
    void testRemoveNodeTextSuccess_NoMatch() {
        String xpath = "//name[@code33]";
        String removeNodeText = XPathUtils.removeNodeText(xml, xpath);
        List<String> nodeText = XPathUtils.getNodeText(removeNodeText, xpath, false);
        Assertions.assertEquals(0,nodeText.size());
    }

    @Test
    @DisplayName("修改满足条件的属性 成功")
    @Tags({
            @Tag("@id:101"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2024/08/10")
    })
    void testUpdateAttributeSuccess() {
        String xpath = "//name[@code]";
        String removeNode = XPathUtils.updateAttribute(xml, xpath,"code","newCodeValue",true);
        String firstNodeAttributeValue = XPathUtils.getFirstNodeAttributeValue(removeNode, "name", "code");
        Assertions.assertEquals("newCodeValue",firstNodeAttributeValue);
    }

    @Test
    @DisplayName("修改节点text 成功")
    @Tags({
            @Tag("@id:101"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2024/08/10")
    })
    void testUpdateSuccess() {
        String xpath = "//name[@code]";
        String updatedSuccess = XPathUtils.updateNodeText(xml, xpath,"newTextValue",true);
        List<String> nodeText = XPathUtils.getNodeText(updatedSuccess, xpath, true);
        Assert.assertEquals(1,nodeText.size());
        Assertions.assertEquals("newTextValue",nodeText.get(0));
    }

    @Test
    @DisplayName("根据条件新增子节点 成功 为满足条件的第一个节点新增")
    @Tags({
            @Tag("@id:101"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2024/06/07")
    })
    void testAddChildSuccess_FIRST() {
        String xpath = "//p1";
        Map<String,String> map = new HashMap<>();
        map.put("age","40");
        map.put("gender","男");
        String newXml = XPathUtils.addChildNode(xml,xpath,"mars","zhangyegong",map,false);
        System.out.println(newXml);
        String xpath2 = "//p1//mars";
        Assertions.assertEquals(1,XPathUtils.getChildStringByXpath(newXml,xpath2).size());
    }

    @Test
    @DisplayName("根据条件新增子节点 成功 为满足条件的所有节点新增")
    @Tags({
            @Tag("@id:101"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2024/06/07")
    })
    void testAddChildSuccess_ALL() {
        String xpath = "//p1";
        Map<String,String> map = new HashMap<>();
        map.put("age","40");
        map.put("gender","男");
        String newXml = XPathUtils.addChildNode(xml,xpath,"mars","zhangyegong",map,true);
        System.out.println(newXml);
        String xpath2 = "//p1//mars";
        Assertions.assertEquals(2,XPathUtils.getChildStringByXpath(newXml,xpath2).size());
    }

    @Test
    @DisplayName("根据条件新增子节点 成功 为满足条件的所有节点新增")
    @Tags({
            @Tag("@id:101"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2024/06/07")
    })
    void testinsertNodeSuccess_ALL() {
        String xpath = "//p1";
        Map<String,String> map = new HashMap<>();
        map.put("age","40");
        map.put("gender","男");
        String newXml = XPathUtils.insertNode(xml,xpath,"mars","zhangyegong",null,true,true);
        System.out.println(newXml);
        String xpath2 = "//mars";
        Assertions.assertEquals(1,XPathUtils.getChildStringByXpath(newXml,xpath2).size());
    }

}