package com.snbc.bbpf.commons.validations.validator;

import com.snbc.bbpf.commons.validations.ValidException;
import org.junit.jupiter.api.Test;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import static org.junit.jupiter.api.Assertions.*;
class IpAddressValidatorTest {
    private final IpAddressValidator validator = new IpAddressValidator();
    @Test
    @DisplayName("输入非空")
    @Tags({
            @Tag("@id:36"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/6/28")
    })
    void testValidateWithValidIpAddress() {
        String validIpAddress = "***********";
        String validMessage = "Valid IP address";
        assertDoesNotThrow(() -> validator.validate(validIpAddress, validMessage));
    }
    @Test
    @DisplayName("输入为空")
    @Tags({
            @Tag("@id:36"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/6/28")
    })
    void testValidateWithNullIpAddress() {
        String nullIpAddress = null;
        String validMessage = "Valid IP address";
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                validator.validate(nullIpAddress, validMessage));
        assertEquals("IP address is empty or null", exception.getMessage());
    }
    @Test
    @DisplayName("IP地址格式错误")
    @Tags({
            @Tag("@id:36"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/6/28")
    })
    void testValidateWithInvalidIpAddressFormat() {
        String invalidIpAddress = "256.168.0.1";
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                 validator.validate(invalidIpAddress, null));
        assertEquals("IP address format error", exception.getMessage());
    }
}
