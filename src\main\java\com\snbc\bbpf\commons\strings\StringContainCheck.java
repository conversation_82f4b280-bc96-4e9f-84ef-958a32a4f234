/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.strings;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

/**
 * 【字符串处理】 判断字符串是否包含指定字符
 * <p> 1.验证是否包含字母。</p>
 * <p> 2.验证是否包含数字（整数，浮点数）。</p>
 * <p> 3.验证是否包含空格。</p>
 *
 * <AUTHOR>
 * @module 字符串处理
 * @date 2023/5/4 10:42
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class StringContainCheck {

    /**
     * 包含字母的正则表达式
     */
    private static final Pattern PATTERN_LETTER = Pattern.compile(".*[a-zA-Z]+.*");

    /**
     * 包含浮点数的正则表达式
     * 十进制数形式，必须含有小数点。例如，3.14、314.0、0.314
     */
    private static final Pattern PATTERN_FLOAT_DECIMAL_SYSTEM = Pattern.compile(".*[\\d]+[\\.][\\d]+.*");

    /**
     * 包含浮点数的正则表达式
     * 科学记数法形式。例如，3.14e2、3.14E2、314E2。
     */
    private static final Pattern PATTERN_FLOAT_SCIENTIFIC_NOTATION = Pattern.compile(".*[\\d]+[\\.]?[\\d]*[Ee][+-]?[\\d]+.*");

    private StringContainCheck(){
        //do nothing
    }

    /**
     * 字符串是否包含字母
     *
     * @param value 待验证文本
     * @return {@code true}: 包含<br>{@code false}: 不包含
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/4
     */
    public static boolean containsLetter(final String value) {
        return !StringUtils.isEmpty(value) && PATTERN_LETTER.matcher(value).matches();
    }

    /**
     * 字符串是否包含空格
     *
     * @param value 待验证文本
     * @return {@code true}: 包含<br>{@code false}: 不包含
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/4
     */
    public static boolean containsBlank(final String value) {
        if (StringUtils.isEmpty(value)) {
            return false;
        }
        return value.contains(" ");
    }

    /**
     * 字符串是否包含数字
     *
     * @param value 待验证文本
     * @return {@code true}: 包含<br>{@code false}: 不包含
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/4
     */
    public static boolean containsNumber(final String value) {
        if (StringUtils.isEmpty(value)) {
            return false;
        }
        final int strLen = value.length();
        for (int i = 0; i < strLen; i++) {
            if (StringDigitCheck.isNumber(String.valueOf(value.charAt(i)))) {
                return true;
            }
        }
        return false;
    }

    /**
     * 字符串是否包含浮点数
     *
     * @param value 待验证文本
     * @return {@code true}: 包含<br>{@code false}: 不包含
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/4
     */
    public static boolean containsFloat(final String value) {
        return !StringUtils.isEmpty(value)
                && (PATTERN_FLOAT_DECIMAL_SYSTEM.matcher(value).matches()
                ||PATTERN_FLOAT_SCIENTIFIC_NOTATION.matcher(value).matches());
    }
}
