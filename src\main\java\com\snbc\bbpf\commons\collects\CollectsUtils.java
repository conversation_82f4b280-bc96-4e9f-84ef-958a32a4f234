package com.snbc.bbpf.commons.collects;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 类描述:多个集合的操作
 * 前提是集合中对象要统一
 * 1. 对多个集合进行交集操作
 * 2. 对多个集合进行并集操作
 * 3. 集合A中去掉集合B中的数据
 * 4. 集中中去掉相同的元素
 * <p>
 * List<Integer> intersection = CollectsUtils.intersection(list1, list2, list3);
 * List<Integer> intersection = CollectsUtils.intersection(list1, null, null, list2, list3);
 * List<Integer> union = CollectsUtils.union(list1, list2, list3);
 * List<Integer> union = CollectsUtils.removeElements(listA, listB);
 * List<Integer> distinct = CollectsUtils.distinct(listA);
 *
 * <AUTHOR>
 * 创建时间:  [2023/11/7 09:00]
 */
public class CollectsUtils {

    /**
     * 对多个集合进行交集操作
     *
     * @param collections 多个集合
     * @return 交集结果
     * <AUTHOR>
     * @date 2023/11/7
     * @since 1.4.0
     */
    public static <T> List<T> intersection(List<T>... collections) {
        if (collections.length == 0) {
            return Collections.emptyList();
        }
        List<List<T>> collectionsList = Arrays.stream(collections)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return collectionsList.stream()
                .reduce((list1, list2) -> list1.stream().filter(list2::contains).collect(Collectors.toList()))
                .orElse(new ArrayList<>());
    }


    /**
     * 对多个集合进行并集操作
     *
     * @param collections 多个集合
     * @return 并集结果
     * <AUTHOR>
     * @date 2023/11/7
     * @since 1.4.0
     */
    public static <T> List<T> union(List<T>... collections) {
        if (collections == null || collections.length == 0) {
            return Collections.emptyList();
        }

        List<List<T>> collectionsList = Arrays.stream(collections)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return collectionsList.stream()
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 从集合A中移除集合B中的元素
     *
     * @param listA 集合A
     * @param listB 集合B
     * @return 移除后的集合A
     * <AUTHOR>
     * @date 2023/11/7
     * @since 1.4.0
     */
    public static <T> List<T> removeElements(List<T> listA, List<T> listB) {
        if (listA == null) {
            return Collections.emptyList();
        }
        if (listB == null) {
            return listA;
        }
        List<T> result = new ArrayList<>(listA);
        result.removeAll(listB);
        return result;
    }

    /**
     * 去掉列表中重复数据
     * 根据hashcode 是否相同
     * 如果传入为null或空集合，返回空集合Collections.emptyList()
     * @param list 原始集合
     * @return 去掉重复元素的集合
     * <AUTHOR>
     * @date 2024/5/27
     * @since 1.5.0
     */
    public static <T> List<T> distinct(List<T> list) {
        if (list == null) {
            return Collections.emptyList();
        }
        return list
                .stream().distinct().collect(Collectors.toList());
    }

}
