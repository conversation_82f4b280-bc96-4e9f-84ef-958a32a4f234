/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.strings;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * 类描述:    .
 *
 * <AUTHOR>
 * 创建时间:  [2023/6/6 15:28]
 */
class StringIndexUtilsTest {

    @DisplayName("字符测试")
    @Tags({
            @Tag("@id:15"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/5/16")
    })
    @Test
    void testCountOccurrences() {

        String text = "Hello, hello, hello!";
        char pattern = 'o';
        int charOccurrences = StringIndexUtils.countOccurrences(text, pattern);
        assertEquals(3, charOccurrences);
    }

    @DisplayName("字符测试")
    @Tags({
            @Tag("@id:15"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/5/16")
    })
    @Test
    void testCountOccurrences_string() {
        String searchText = "hello";
        String text = "hello, hello, hello!";
        int charOccurrences = StringIndexUtils.countOccurrences(text, searchText);
        assertEquals(3, charOccurrences);
    }

    @Test
    @DisplayName("字符测试")
    @Tags({
            @Tag("@id:15"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/5/16")
    })
    void testCountOccurrences_test2() {
        // 准备测试数据
        String text = "Hello, hello, hello!";
        char pattern = 'o';
        int startIndex = 7;
        int expectedOccurrences = 2;

        // 调用待测试方法
        int actualOccurrences = StringIndexUtils.countOccurrences(text, pattern, startIndex);

        // 断言结果
        assertEquals(expectedOccurrences, actualOccurrences);
    }

    @Test
    @DisplayName("字符串测试")
    @Tags({
            @Tag("@id:15"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/5/16")
    })
    void testCountOccurrences_test3() {
        // 准备测试数据
        String text = "Hello, hello, hello!";
        String searchText = "hello";
        int startIndex = 7;
        int expectedOccurrences = 2;

        // 调用待测试方法
        int actualOccurrences = StringIndexUtils.countOccurrences(text, searchText, startIndex);

        // 断言结果
        assertEquals(expectedOccurrences, actualOccurrences);
    }

    @Test
    @DisplayName("字符或字符串测试")
    @Tags({
            @Tag("@id:15"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/5/16")
    })
    void testCountOccurrences_test4() {
        // 准备测试数据
        String text = "hello, hello, hello!";
        String pattern = "hello";
        int startIndex = 0;
        int endIndex = text.length();
        int expectedOccurrences = 3;

        // 调用待测试方法
        int actualOccurrences = StringIndexUtils.countOccurrences(text, pattern, startIndex, endIndex);

        // 断言结果
        assertEquals(expectedOccurrences, actualOccurrences);
    }

    @Test
    @DisplayName("字符测试")
    @Tags({
            @Tag("@id:15"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/5/16")
    })
    void testCountOccurrences_test5() {
        // 准备测试数据
        String text = "hello, hello, hello!";
        char pattern = 'o';
        int startIndex = 0;
        int endIndex = text.length();
        int expectedOccurrences = 3;

        // 调用待测试方法
        int actualOccurrences = StringIndexUtils.countOccurrences(text, pattern, startIndex, endIndex);

        // 断言结果
        assertEquals(expectedOccurrences, actualOccurrences);
    }

    @Test
    @DisplayName("指定字符出现指定次数后的索引位置测试")
    @Tags({
            @Tag("@id:15"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/5/16")
    })
    void testGetIndexAfterOccurrences_test5() {
        // 准备测试数据
        String text = "hello, hello, hello!";
        char pattern = 'o';
        int occurrence = 2;
        int startIndex = 0;
        int expectedIndex = 11;
        // 调用待测试方法
        int actualIndex = StringIndexUtils.getIndexAfterOccurrences(text, pattern, occurrence, startIndex);

        // 断言结果
        assertEquals(expectedIndex, actualIndex);
    }

    @Test
    @DisplayName("指定字符串出现指定次数后的索引位置测试")
    @Tags({
            @Tag("@id:15"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/5/16")
    })
    void testGetIndexAfterOccurrences_test6() {
        // 准备测试数据
        String text = "hello, hello, hello!";
        String searchText = "lo";
        int occurrence = 2;
        int startIndex = 0;
        int expectedIndex = 10;

        // 调用待测试方法
        int actualIndex = StringIndexUtils.getIndexAfterOccurrences(text, searchText, occurrence, startIndex);

        // 断言结果
        assertEquals(expectedIndex, actualIndex);
    }

    @Test
    @DisplayName("查找指定字符在字符串中指定次数后的索引位置")
    @Tags({
            @Tag("@id:15"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/5/16")
    })
    void testGetIndexAfterOccurrences_test7() {
        String text = "hello, hello, hello!";
        char pattern = 'o';
        int count = 2;
        int startIndex = 0;
        int endIndex = text.length();

        int index = StringIndexUtils.getIndexAfterOccurrences(text, pattern, count, startIndex, endIndex);

        assertEquals(12, index);
    }


    @Test
    @DisplayName("查找指定字符串在字符串中指定次数后的索引位置")
    @Tags({
            @Tag("@id:15"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/5/16")
    })
    void testGetIndexAfterOccurrences_test8() {
        String text = "Hello, hello, hello!";
        String searchText = "lo";
        int count = 2;
        int startIndex = 0;
        int endIndex = text.length();

        int index = StringIndexUtils.getIndexAfterOccurrences(text, searchText, count, startIndex, endIndex);

        assertEquals(12, index);
    }
}
