/*
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.commons.strings.security.parse;


/**
 * @ClassName:  IDesensitizedParse
 *   脱敏接口
 * @module:    字符脱敏
 * @Author:    yangweipeng
 * @date:      2023/05/06
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public interface IDesensitizedParse {
    String parseString(String srcStr);
}

