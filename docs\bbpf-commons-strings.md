# 字符串处理 (com.snbc.bbpf.commons.strings)

## 类概览 

| 类名 | 功能描述 |
|------|----------|
| StringEmptyCheck | 提供字符串空值检查功能，包括判断是否为空或空白字符等 |
| StringContainCheck | 提供字符串包含关系检查功能，包括是否包含字母、数字、特殊字符等 |
| StringPadUtils | 提供字符串填充功能，包括左填充、右填充和居中填充 |
| StringZipUtils | 提供字符串压缩和解压缩功能，用于大文本压缩处理 |
| StringRegexUtils | 提供正则表达式相关功能，包括正则匹配、替换和提取等 |
| DesensitizedUtils | 提供数据脱敏功能，适用于身份证号、手机号、银行卡等敏感信息处理 |
| HexUtils | 提供字节数组和十六进制字符串互相转换功能 |
| ShrinkHandler | 提供字符串修剪功能，对过长的字符串按指定长度进行缩略处理 |
| SplitHandler | 提供字符串分隔处理功能，可根据各种分隔符提取子字符串 |
| StringDigitCheck | 提供数字字符串检查功能，判断字符串是否为整数或浮点数 |
| StringEscapeUtils | 提供字符串转义和反转义功能，支持Java、XML、HTML、CSV等多种格式 |
| StringIndexUtils | 提供字符串索引工具，计算字符出现次数和查找指定位置 |
| StringReplaceUtils | 提供字符串替换功能，替换或去除指定字符和字符串 |
| VersionUtil | 提供版本号比较功能，支持各种版本号格式的大小比较 |
| StringFormatUtils | 对通用字符串进行格式化，包括账号、证件号、手机号、固话、金额 |
| StringStartEndCheck | 提供字符串开始和结束检查功能，判断字符串是否以指定子字符串开头或结尾 |

## StringEmptyCheck - 字符串空值检查

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| isEmpty(String value) | 判断字符串是否为空 | value: 要检查的字符串 | boolean: 如果字符串为null或长度为0，返回true，否则返回false |
| isEmptyOrNull(String value) | 判断字符串是否为空或"null"字符串 | value: 要检查的字符串 | Boolean: 如果字符串为null、空或"null"字符串，返回true，否则返回false |
| isAnyEmpty(String... values) | 判断多个字符串中是否有任何一个为空 | values: 要检查的字符串数组 | boolean: 如果任何一个字符串为null或长度为0，返回true，否则返回false |
| isAnyEmptyOrNull(String... values) | 判断多个字符串中是否有任何一个为空或"null"字符串 | values: 要检查的字符串数组 | Boolean: 如果任何一个字符串为null、空或"null"字符串，返回true，否则返回false |
| isBlank(String value) | 判断字符串是否为空白 | value: 要检查的字符串 | boolean: 如果字符串为null、长度为0或只包含空白字符，返回true，否则返回false |
| isBlankOrNull(String value) | 判断字符串是否为空、空白或"null"字符串 | value: 要检查的字符串 | Boolean: 如果字符串为null、空、只包含空白字符或"null"字符串，返回true，否则返回false |
| isAnyBlank(String... values) | 判断多个字符串中是否有任何一个为空白 | values: 要检查的字符串数组 | boolean: 如果任何一个字符串为null、长度为0或只包含空白字符，返回true，否则返回false |
| isAnyBlankOrNull(String... values) | 判断多个字符串中是否有任何一个为空、空白或"null"字符串 | values: 要检查的字符串数组 | Boolean: 如果任何一个字符串为null、空、只包含空白字符或"null"字符串，返回true，否则返回false |

### 使用注意事项

- 所有方法都会对null值进行检查，当输入为null时，isEmpty和isEmptyOrNull会返回true
- 当检查"null"字符串时，使用isEmptyOrNull方法会忽略大小写和前后空格
- 在多参数方法中(如isAnyEmpty)，如果传入的是null数组，会抛出NullPointerException
- isBlank方法会检查空白字符，包括空格、制表符、换行符等，而isEmpty只检查null和空字符串

### 使用示例

```java
// 判断字符串是否为空
boolean isEmpty = StringEmptyCheck.isEmpty("");  // 返回true
isEmpty = StringEmptyCheck.isEmpty(null);        // 返回true
isEmpty = StringEmptyCheck.isEmpty(" ");         // 返回false

// 判断字符串是否为空或"null"字符串
boolean isEmptyOrNull = StringEmptyCheck.isEmptyOrNull("");        // 返回true
isEmptyOrNull = StringEmptyCheck.isEmptyOrNull(null);              // 返回true
isEmptyOrNull = StringEmptyCheck.isEmptyOrNull("null");            // 返回true
isEmptyOrNull = StringEmptyCheck.isEmptyOrNull("NULL");            // 返回true
isEmptyOrNull = StringEmptyCheck.isEmptyOrNull(" NULL ");          // 返回true

// 判断多个字符串中是否有任何一个为空
boolean isAnyEmpty = StringEmptyCheck.isAnyEmpty("a", "", "c");  // 返回true
isAnyEmpty = StringEmptyCheck.isAnyEmpty("a", "b", "c");         // 返回false

// 判断多个字符串中是否有任何一个为空或"null"字符串
boolean isAnyEmptyOrNull = StringEmptyCheck.isAnyEmptyOrNull("a", "", "c");        // 返回true
isAnyEmptyOrNull = StringEmptyCheck.isAnyEmptyOrNull("a", "null", "c");            // 返回true
isAnyEmptyOrNull = StringEmptyCheck.isAnyEmptyOrNull("a", "b", "c");               // 返回false

// 判断字符串是否为空白
boolean isBlank = StringEmptyCheck.isBlank(" ");    // 返回true
isBlank = StringEmptyCheck.isBlank("\t\n");         // 返回true
isBlank = StringEmptyCheck.isBlank("abc");          // 返回false

// 判断字符串是否为空、空白或"null"字符串
boolean isBlankOrNull = StringEmptyCheck.isBlankOrNull(" ");       // 返回true
isBlankOrNull = StringEmptyCheck.isBlankOrNull("null");            // 返回true
isBlankOrNull = StringEmptyCheck.isBlankOrNull("abc");             // 返回false

// 判断多个字符串中是否有任何一个为空白
boolean isAnyBlank = StringEmptyCheck.isAnyBlank("a", " ", "c");  // 返回true
isAnyBlank = StringEmptyCheck.isAnyBlank("a", "b", "c");          // 返回false

// 判断多个字符串中是否有任何一个为空、空白或"null"字符串
boolean isAnyBlankOrNull = StringEmptyCheck.isAnyBlankOrNull("a", " ", "c");       // 返回true
isAnyBlankOrNull = StringEmptyCheck.isAnyBlankOrNull("a", "null", "c");            // 返回true
isAnyBlankOrNull = StringEmptyCheck.isAnyBlankOrNull("a", "b", "c");               // 返回false
```

## StringContainCheck - 字符串包含检查

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| containsLetter(String value) | 判断字符串是否包含字母 | value: 要检查的字符串 | boolean: 如果字符串包含任何字母，返回true，否则返回false |
| containsBlank(String value) | 判断字符串是否包含空格 | value: 要检查的字符串 | boolean: 如果字符串包含空格，返回true，否则返回false |
| containsNumber(String value) | 判断字符串是否包含数字 | value: 要检查的字符串 | boolean: 如果字符串包含任何数字，返回true，否则返回false |
| containsFloat(String value) | 判断字符串是否包含浮点数 | value: 要检查的字符串 | boolean: 如果字符串包含浮点数，返回true，否则返回false |

### 使用注意事项

- 所有方法都对null值进行了处理，传入null会返回false而不抛出异常
- containsLetter只检查英文字母，不检查其他语言的字母字符
- containsBlank只检查空格字符，不包括制表符、换行符等其他空白字符
- containsFloat方法可识别各种浮点数格式，包括科学计数法

### 使用示例

```java
// 判断字符串是否包含字母
boolean containsLetter = StringContainCheck.containsLetter("abc123");    // 返回true
containsLetter = StringContainCheck.containsLetter("123");               // 返回false

// 判断字符串是否包含空格
boolean containsBlank = StringContainCheck.containsBlank("abc 123");     // 返回true
containsBlank = StringContainCheck.containsBlank("abc123");              // 返回false

// 判断字符串是否包含数字
boolean containsNumber = StringContainCheck.containsNumber("abc123");    // 返回true
containsNumber = StringContainCheck.containsNumber("abc");               // 返回false

// 判断字符串是否包含浮点数
boolean containsFloat = StringContainCheck.containsFloat("abc3.14");      // 返回true
containsFloat = StringContainCheck.containsFloat("abc3.14e2");            // 返回true
containsFloat = StringContainCheck.containsFloat("abc123");               // 返回false
```

## StringPadUtils - 字符串填充

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| leftPadTotalLength(String str, int size, String padStr) | 左填充至总长度 | str: 原始字符串<br>size: 字符串总长度<br>padStr: 填充字符串 | String: 填充后的字符串 |
| rightPadTotalLength(String str, int size, String padStr) | 右填充至总长度 | str: 原始字符串<br>size: 字符串总长度<br>padStr: 填充字符串 | String: 填充后的字符串 |
| centerPadTotalLength(String str, int size, String padStr) | 左右均填充至总长度 | str: 原始字符串<br>size: 字符串总长度<br>padStr: 填充字符串 | String: 填充后的字符串 |
| leftPadAddLength(String str, int pads, String padStr) | 按需要增加的长度向左填充 | str: 原始字符串<br>pads: 字符串增加的长度<br>padStr: 填充字符串 | String: 填充后的字符串 |
| rightPadAddLength(String str, int pads, String padStr) | 按需要增加的长度向右填充 | str: 原始字符串<br>pads: 字符串增加的长度<br>padStr: 填充字符串 | String: 填充后的字符串 |
| leftAndRightPadAddLength(String str, int leftPads, String leftPadStr, int rightPads, String rightPadStr) | 左侧和右侧都按增加的长度填充 | str: 原始字符串<br>leftPads: 左侧字符串增加的长度<br>leftPadStr: 左侧待填充字符串<br>rightPads: 右侧字符串增加的长度<br>rightPadStr: 右侧待填充字符串 | String: 填充后的字符串 |

### 使用注意事项

- 当输入字符串为null时，所有方法会返回null，不会抛出异常
- 如果size参数小于原始字符串长度，方法会返回原始字符串，不会截断
- 填充字符串padStr会被重复使用直到达到指定长度，如果无法均匀填充，centerPadTotalLength方法会偏向右侧多填充
- 对于大量字符串处理场景，可能会影响性能，建议预先计算好所需长度

### 使用示例

```java
// 左填充至总长度
String leftPadded = StringPadUtils.leftPadTotalLength("123", 5, "0");    // 返回"00123"
leftPadded = StringPadUtils.leftPadTotalLength("abc", 7, "xyz");         // 返回"xyzxabc"

// 右填充至总长度
String rightPadded = StringPadUtils.rightPadTotalLength("123", 5, "0");  // 返回"12300"
rightPadded = StringPadUtils.rightPadTotalLength("abc", 7, "xyz");       // 返回"abcxyzx"

// 左右均填充至总长度
String centered = StringPadUtils.centerPadTotalLength("abc", 7, "0");    // 返回"00abc00"
centered = StringPadUtils.centerPadTotalLength("abc", 8, "-");           // 返回"--abc---"

// 按需要增加的长度向左填充
String leftPaddedAdd = StringPadUtils.leftPadAddLength("123", 2, "0");     // 返回"00123"
leftPaddedAdd = StringPadUtils.leftPadAddLength("abc", 6, "xyz");          // 返回"xyzxyzabc"

// 按需要增加的长度向右填充
String rightPaddedAdd = StringPadUtils.rightPadAddLength("123", 2, "0");   // 返回"12300"
rightPaddedAdd = StringPadUtils.rightPadAddLength("abc", 6, "xyz");        // 返回"abcxyzxyz"

// 左侧和右侧都按增加的长度填充
String bothPadded = StringPadUtils.leftAndRightPadAddLength("123", 2, "0", 3, "#");  // 返回"00123###"
bothPadded = StringPadUtils.leftAndRightPadAddLength("abc", 3, "x", 2, "y");         // 返回"xxxabcyy"
```

## StringZipUtils - 字符串压缩与解压缩

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| compressToByteArray(String str) | 对大字符串进行压缩，默认使用最佳压缩级别 | str: 待压缩的字符串 | byte[]: 压缩后的字节数组 |
| compressToByteArray(String str, int level) | 对大字符串进行压缩，使用指定的压缩级别 | str: 待压缩的字符串<br>level: 压缩级别(0-9) | byte[]: 压缩后的字节数组 |
| compressToString(String str) | 对大字符串进行压缩，默认使用最佳压缩级别 | str: 待压缩的字符串 | String: 压缩后的字符串 |
| compressToString(String str, int level) | 对大字符串进行压缩，使用指定的压缩级别 | str: 待压缩的字符串<br>level: 压缩级别(0-9) | String: 压缩后的字符串 |
| uncompress(byte[] byData) | 对被压缩过的大字符串进行解压 | byData: 待解压的字节数组 | String: 解压后的字符串 |
| uncompress(String str) | 对被压缩过的大字符串进行解压 | str: 待解压的字符串 | String: 解压后的字符串 |

### 使用注意事项

- 压缩空字符串或null时会返回相应的空结果，不会抛出异常
- 压缩级别范围为0-9，0表示不压缩，9表示最大压缩，默认使用值为-1，表示最佳压缩级别
- 对于较小的字符串，压缩后可能比原始字符串更大
- 压缩/解压缩过程可能会抛出IOException，需要在使用时进行异常处理
- compressToString方法使用Base64编码，可能会增加输出字符串长度

### 使用示例

```java
// 压缩字符串为字节数组
String original = "This is a long string that will be compressed...";
byte[] compressedBytes = StringZipUtils.compressToByteArray(original);

// 使用特定压缩级别压缩字符串
byte[] compressedBytesWithLevel = StringZipUtils.compressToByteArray(original, 9);

// 压缩字符串为Base64编码的字符串
String compressedString = StringZipUtils.compressToString(original);
System.out.println("压缩后: " + compressedString);

// 使用特定压缩级别压缩字符串为Base64编码的字符串
String compressedStringWithLevel = StringZipUtils.compressToString(original, 9);

// 解压缩字节数组
String uncompressedFromBytes = StringZipUtils.uncompress(compressedBytes);

// 解压缩字符串
String uncompressed = StringZipUtils.uncompress(compressedString);
System.out.println("解压后: " + uncompressed);
assert original.equals(uncompressed);  // 确认解压后的字符串与原始字符串相同
```

## StringRegexUtils - 正则表达式工具

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| isMatch(String regex, CharSequence input) | 判断字符串是否匹配正则表达式 | regex: 正则表达式<br>input: 要检查的字符串 | boolean: 如果匹配返回true，否则返回false |
| isIDCard15(CharSequence input) | 验证身份证号码15位 | input: 待验证文本 | boolean: 若为15位身份证号码格式返回true，否则返回false |
| isIDCard18(CharSequence input) | 验证身份证号码18位 | input: 待验证文本 | boolean: 若为18位身份证号码格式返回true，否则返回false |
| isEmail(CharSequence input) | 验证邮箱 | input: 待验证文本 | boolean: 若为邮箱格式返回true，否则返回false |
| isZh(CharSequence input) | 验证汉字 | input: 待验证文本 | boolean: 若为汉字返回true，否则返回false |
| isIP(CharSequence input) | 验证IP地址 | input: 待验证文本 | boolean: 若为IP地址格式返回true，否则返回false |
| isMobileExact(CharSequence input) | 验证手机号（精确） | input: 待验证文本 | boolean: 若为手机号格式返回true，否则返回false |
| isTel(CharSequence input) | 验证电话号码 | input: 待验证文本 | boolean: 若为电话号码格式返回true，否则返回false |
| isURL(CharSequence input) | 验证URL | input: 待验证文本 | boolean: 若为URL格式返回true，否则返回false |
| isUsername(CharSequence input) | 验证用户名 | input: 待验证文本 | boolean: 若为用户名格式返回true，否则返回false |
| isBankCard(String cardId) | 判断是否是银行卡号 | cardId: 银行卡号 | boolean: 若为银行卡号格式返回true，否则返回false |

### 使用注意事项

- 所有方法当输入为null时返回false，不会抛出空指针异常
- 正则表达式匹配性能可能受输入字符串长度影响，处理大文本时需谨慎
- 身份证、手机号等验证仅检查格式合法性，不验证实际有效性
- isIP方法只支持IPv4地址格式验证，不支持IPv6
- isBankCard方法会执行Luhm算法校验，性能略低于简单的格式校验

### 使用示例

```java
// 判断字符串是否匹配正则表达式
boolean isMatch = StringRegexUtils.isMatch("\\d+", "12345");              // 返回true
isMatch = StringRegexUtils.isMatch("^[a-z]+$", "abc123");                // 返回false

// 验证15位身份证号码
boolean isIDCard15 = StringRegexUtils.isIDCard15("*************45");      // 根据格式验证返回结果

// 验证18位身份证号码
boolean isIDCard18 = StringRegexUtils.isIDCard18("*************4567X");   // 根据格式验证返回结果

// 验证邮箱
boolean isEmail = StringRegexUtils.isEmail("<EMAIL>");         // 返回true
isEmail = StringRegexUtils.isEmail("invalid-email");                       // 返回false

// 验证汉字
boolean isZh = StringRegexUtils.isZh("中文");                              // 返回true
isZh = StringRegexUtils.isZh("Chinese");                                  // 返回false

// 验证IP地址
boolean isIP = StringRegexUtils.isIP("***********");                       // 返回true
isIP = StringRegexUtils.isIP("256.256.256.256");                          // 返回false

// 验证手机号
boolean isMobile = StringRegexUtils.isMobileExact("***********");          // 返回true
isMobile = StringRegexUtils.isMobileExact("1381234");                     // 返回false

// 验证电话号码
boolean isTel = StringRegexUtils.isTel("021-********");                    // 返回true

// 验证URL
boolean isURL = StringRegexUtils.isURL("http://example.com");              // 返回true
isURL = StringRegexUtils.isURL("invalid-url");                            // 返回false

// 验证用户名
boolean isUsername = StringRegexUtils.isUsername("username123");           // 返回true
isUsername = StringRegexUtils.isUsername("user");                         // 返回false (长度不够)

// 验证银行卡号
boolean isBankCard = StringRegexUtils.isBankCard("622202*************");   // 根据Luhm算法验证返回结果
```

## DesensitizedUtils - 数据脱敏工具

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| getJson(Object javaBean) | 对对象进行脱敏处理，返回脱敏后的对象 | javaBean: 要脱敏的对象 | Object: 脱敏后的对象 |
| setNewValueForField(Object javaBean, Field field, Object value) | 为对象的指定字段设置脱敏后的值 | javaBean: 对象<br>field: 字段<br>value: 字段值 | void |
| desensitize(String valueStr, SensitiveType type) | 根据指定的脱敏类型对字符串进行脱敏 | valueStr: 待脱敏的字符串<br>type: 脱敏类型 | String: 脱敏后的字符串 |
| desensitizeCustom(String valueStr, int prefixKeep, int suffixKeep) | 自定义脱敏（保留前几位和后几位，其余用*替换） | valueStr: 待脱敏的字符串<br>prefixKeep: 保留前几位<br>suffixKeep: 保留后几位 | String: 脱敏后的字符串 |
| desensitizeCustom(String valueStr, int prefixKeep, int suffixKeep, String maskSymbol) | 自定义脱敏（保留前几位和后几位，其余用指定符号替换） | valueStr: 待脱敏的字符串<br>prefixKeep: 保留前几位<br>suffixKeep: 保留后几位<br>maskSymbol: 脱敏符号 | String: 脱敏后的字符串 |
| desensitizedList(List<?> list) | 对列表中的对象进行脱敏处理 | list: 要脱敏的对象列表 | List<?>: 脱敏后的对象列表 |
| desensitizedObject(Object content) | 对单个对象进行脱敏处理 | content: 要脱敏的对象 | Object: 脱敏后的对象 |
| desensitizeByPosition(String src, int start, int end) | 脱敏方法一：指定开始和结束位置，使用默认脱敏符号进行脱敏。 | src: 待脱敏字符串<br>start: 脱敏开始位置（从0开始）<br>end: 脱敏结束位置（不包含） | String: 脱敏后的字符串 |
| desensitizeByLength(String src, int start, int maskLength) | 脱敏方法二：指定开始位置和脱敏长度，使用默认脱敏符号进行脱敏。 | src: 待脱敏字符串<br>start: 脱敏开始位置（从0开始）<br>maskLength: 脱敏长度 | String: 脱敏后的字符串 |
| desensitizeByRange(String src, int start, int end, String maskSymbol) | 脱敏方法三：指定开始和结束，指定脱敏符号 | src: 待脱敏字符串<br>start: 脱敏开始位置（从0开始）<br>end: 脱敏结束位置（不包含）<br>maskSymbol: 脱敏符号 | String: 脱敏后的字符串 |
| desensitizeByLength(String src, int start, int maskLength, String maskSymbol) | 脱敏方法四：指定开始位置和脱敏长度，使用指定脱敏符号进行脱敏。 | src: 待脱敏字符串<br>start: 脱敏开始位置（从0开始）<br>maskLength: 脱敏长度<br>maskSymbol: 脱敏符号 | String: 脱敏后的字符串 |

### 脱敏类型说明

| 脱敏类型 | 说明 | 脱敏规则 | 示例 |
|----------|------|----------|------|
| CHINESE_NAME | 中文姓名 | 保留第一个字符，其余用*替换 | 张三丰 → 张** |
| ID_CARD | 身份证号 | 保留前6位和后4位，其余用*替换 | 350111199001011234 → 350111**********1234 |
| MOBILE_PHONE | 手机号 | 保留前3位和后4位，其余用*替换 | *********** → 138****5678 |
| FIXED_PHONE | 固定电话 | 保留后4位，其余用*替换 | *********** → *******3943 |
| EMAIL | 邮箱地址 | 保留第一个字符和@后的内容，其余用*替换 | <EMAIL> → e******@example.com |
| BANK_CARD | 银行卡号 | 保留前6位和后4位，其余用*替换 | 622202************* → 622202*************0123 |
| ADDRESS | 地址 | 保留前6个字符，其余用*替换 | 北京市海淀区西土城路10号 → 北京市海淀区****** |
| PASSWORD | 密码 | 全部用*替换 | password123 → *********** |
| CUSTOM | 自定义脱敏 | 根据prefixKeep、suffixKeep、maskSymbol参数自定义 | ********** → 123****890（前3后3） |

### 使用注意事项

- 类的主要功能依赖于序列化反射机制，需确保对象的字段可访问
- 使用@Desensitized注解需要确保有正确的setter/getter方法
- 脱敏处理不会修改原始对象，而是返回新的对象
- 对于不需要脱敏的字段，应避免使用@Desensitized注解以提高性能
- 在处理大量对象时可能影响性能，建议批量处理
- 自定义脱敏（CUSTOM类型）支持指定保留前几位和后几位，如果字符串长度不足（小于等于前缀+后缀长度），则不进行脱敏
- 自定义脱敏的maskSymbol参数支持多字符，如"**"会重复使用该符号进行替换

### 使用示例

```java
// 银行卡号脱敏
String bankCard = "622202*************";
String desensitizedBankCard = DesensitizedUtils.desensitize(bankCard, SensitiveType.BANK_CARD);  // 返回"622202******0123"

// 身份证号脱敏
String idCard = "350111199001011234";
String desensitizedIdCard = DesensitizedUtils.desensitize(idCard, SensitiveType.ID_CARD);  // 返回"350111********1234"

// 手机号脱敏
String phone = "***********";
String desensitizedPhone = DesensitizedUtils.desensitize(phone, SensitiveType.MOBILE_PHONE);  // 返回"138****5678"

// 自定义脱敏（保留前几位和后几位）
String customData = "*************";
String customDesensitized1 = DesensitizedUtils.desensitizeCustom(customData, 3, 4);        // 返回"123******0123"
String customDesensitized2 = DesensitizedUtils.desensitizeCustom(customData, 2, 3, "#");   // 返回"12########123"

// 自定义位置脱敏
String custom = "ABCDEFGHIJKLMN";
String desensitized1 = DesensitizedUtils.desensitizeByPosition(custom, 3, 7);  // 返回"ABC****HIJKLMN"
String desensitized2 = DesensitizedUtils.desensitizeByLength(custom, 3, 4);    // 返回"ABC****HIJKLMN"
String desensitized3 = DesensitizedUtils.desensitizeByRange(custom, 3, 7, "#"); // 返回"ABC####HIJKLMN"
String desensitized4 = DesensitizedUtils.desensitizeByLength(custom, 3, 4, "#"); // 返回"ABC####HIJKLMN"

// 邮箱地址脱敏
String email = "<EMAIL>";
String desensitizedEmail = DesensitizedUtils.desensitize(email, SensitiveType.EMAIL);  // 返回"e*****@example.com"

// 中文姓名脱敏
String name = "张三丰";
String desensitizedName = DesensitizedUtils.desensitize(name, SensitiveType.CHINESE_NAME);  // 返回"张**"

// 地址脱敏
String address = "北京市海淀区西土城路10号";
String desensitizedAddress = DesensitizedUtils.desensitize(address, SensitiveType.ADDRESS);  // 返回"北京市海淀区******"

// 对象脱敏处理
User user = new User("张三", "***********", "<EMAIL>");
User desensitizedUser = (User) DesensitizedUtils.desensitizedObject(user);
System.out.println(desensitizedUser.getName());      // 输出"张*"
System.out.println(desensitizedUser.getPhone());     // 输出"138****5678"
System.out.println(desensitizedUser.getEmail());     // 输出"e*****@example.com"

// 列表脱敏处理
List<User> users = Arrays.asList(
    new User("张三", "***********", "<EMAIL>"),
    new User("李四", "13987654321", "<EMAIL>")
);
List<User> desensitizedUsers = DesensitizedUtils.desensitizedList(users);

// 使用注解方式进行脱敏
public class User {
    @Desensitized(type = SensitiveType.CHINESE_NAME)
    private String name;

    @Desensitized(type = SensitiveType.MOBILE_PHONE)
    private String phone;

    @Desensitized(type = SensitiveType.EMAIL)
    private String email;

    // 自定义脱敏：保留前3位后4位，用*替换
    @Desensitized(type = SensitiveType.CUSTOM, prefixKeep = 3, suffixKeep = 4)
    private String customField1;

    // 自定义脱敏：保留前2位后3位，用#替换
    @Desensitized(type = SensitiveType.CUSTOM, prefixKeep = 2, suffixKeep = 3, maskSymbol = "#")
    private String customField2;

    // 构造函数、getter和setter省略
}
```

## HexUtils - 十六进制转换工具

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| byteArrayToHexString(byte[] byteArray) | 将字节数组转换为十六进制字符串 | byteArray: 字节数组 | String: 转换后的十六进制字符串 |
| hexStringToByteArray(String hexString) | 将十六进制字符串转换为字节数组 | hexString: 十六进制字符串 | byte[]: 转换后的字节数组 |

### 使用注意事项

- 十六进制字符串应该包含偶数个字符，否则转换为字节数组时会抛出异常
- 支持大小写字母，例如"ABCDEF"和"abcdef"被视为等价
- 方法不会对null值进行处理，传入null时会抛出NullPointerException
- 非法十六进制字符(如"GHIJK")会导致异常

### 使用示例

```java
// 将字节数组转换为十六进制字符串
byte[] bytes = new byte[]{0x12, 0x34, 0x56, 0x78};
String hexString = HexUtils.byteArrayToHexString(bytes);  // 返回"********"

// 将十六进制字符串转换为字节数组
String hex = "ABCDEF";
byte[] byteArray = HexUtils.hexStringToByteArray(hex);  // 返回字节数组 [0xAB, 0xCD, 0xEF]
```

## ShrinkHandler - 字符串修剪工具

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| shrinkRight(String str, int length) | 字符串按指定保留长度进行右侧缩略，右侧补修饰符（右修剪） | str: 待处理的字符串<br>length: 保留长度 | String: 处理后的字符串 |
| shrinkLeft(String str, int length) | 字符串按指定保留长度进行左侧缩略，左侧补修饰符（左修剪） | str: 待处理的字符串<br>length: 保留长度 | String: 处理后的字符串 |
| shrinkLR(String str, int length, int leftOffset) | 字符串按左偏移、指定保留长度进行两侧缩略，两侧补修饰符（两侧修剪） | str: 待处理的字符串<br>length: 保留长度<br>leftOffset: 左偏移量 | String: 处理后的字符串 |
| shrinkLR(String str, int leftOffset, String leftPadding, int rightOffset, String rightPadding) | 字符串按左右偏移进行两侧缩略，两侧补修饰符（两侧修剪） | str: 待处理的字符串<br>leftOffset: 左偏移量<br>leftPadding: 左缩略符<br>rightOffset: 右偏移量<br>rightPadding: 右缩略符 | String: 处理后的字符串 |

### 使用注意事项

- 默认使用"..."作为省略标记，可以通过设置自定义缩略符替换
- 当length参数小于等于0时，将返回空字符串
- 当length参数大于等于字符串长度时，将返回原始字符串
- 当处理包含多字节字符(如中文)的字符串时，方法按字符单元而非字节计数

### 使用示例

```java
// 右侧缩略
String rightShrinked = ShrinkHandler.shrinkRight("**********0987654321", 4);  // 返回"1234..."

// 左侧缩略
String leftShrinked = ShrinkHandler.shrinkLeft("**********0987654321", 4);  // 返回"...4321"

// 两侧缩略（根据左偏移和保留长度）
String lrShrinked1 = ShrinkHandler.shrinkLR("**********0987654321", 4, 4);  // 返回"...5678..."

// 两侧缩略（根据左右偏移和自定义缩略符）
String lrShrinked2 = ShrinkHandler.shrinkLR("**********0987654321", 4, "++", 5, "===");  // 返回"++56789009876==="
```

## SplitHandler - 字符串分隔处理工具

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| firstAfter(String str, String separator) | 返回第一个出现分隔符之后的子字符串 | str: 待处理的字符串<br>separator: 分隔符号 | String: 处理后的字符串 |
| firstBefore(String str, String separator) | 返回第一个出现分隔符之前的子字符串 | str: 待处理的字符串<br>separator: 分隔符号 | String: 处理后的字符串 |
| lastBefore(String str, String separator) | 返回最后一个出现分隔符之前的子字符串 | str: 待处理的字符串<br>separator: 分隔符号 | String: 处理后的字符串 |
| lastAfter(String str, String separator) | 返回最后一个出现分隔符之后的子字符串 | str: 待处理的字符串<br>separator: 分隔符号 | String: 处理后的字符串 |
| firstMiddle(String str, String separatorLeft, String separatorRight) | 获取首次两个分隔符成对出现之间的子字符串 | str: 待处理的字符串<br>separatorLeft: 起始分隔符号<br>separatorRight: 结束分隔符号 | String: 处理后的字符串 |
| firstMiddle(String str, int indexBegin, String separatorLeft, String separatorRight) | 从指定位置开始获取首次两个分隔符成对出现之间的子字符串 | str: 待处理的字符串<br>indexBegin: 指定位置开始<br>separatorLeft: 起始分隔符号<br>separatorRight: 结束分隔符号 | String: 处理后的字符串 |
| firstMiddle(String str, int indexBegin, int indexEnd, String separatorLeft, String separatorRight) | 从指定开始和结束位置获取首次两个分隔符成对出现之间的子字符串 | str: 待处理的字符串<br>indexBegin: 指定开始位置<br>indexEnd: 指定结束位置<br>separatorLeft: 起始分隔符号<br>separatorRight: 结束分隔符号 | String: 处理后的字符串 |
| middleAr(String str, String separatorLeft, String separatorRight) | 获取两个分隔符成对出现之间的子字符串数组 | str: 待处理的字符串<br>separatorLeft: 起始分隔符号<br>separatorRight: 结束分隔符号 | String[]: 处理后的字符串数组 |
| middleAr(String str, int indexBegin, String separatorLeft, String separatorRight) | 从指定位置开始获取两个分隔符成对出现之间的子字符串数组 | str: 待处理的字符串<br>indexBegin: 指定位置开始<br>separatorLeft: 起始分隔符号<br>separatorRight: 结束分隔符号 | String[]: 处理后的字符串数组 |
| middleAr(String str, int indexBegin, int indexEnd, String separatorLeft, String separatorRight) | 从指定开始和结束位置获取两个分隔符成对出现之间的子字符串数组 | str: 待处理的字符串<br>indexBegin: 指定开始位置<br>indexEnd: 指定结束位置<br>separatorLeft: 起始分隔符号<br>separatorRight: 结束分隔符号 | String[]: 处理后的字符串数组 |
| splitToList(String str, String separator) | 将带分隔符的字符串转成List | str: 待处理的字符串<br>separator: 分隔符 | List<String>: 字符串列表 |

### 使用注意事项

- 当字符串中不包含分隔符时，firstAfter和lastAfter方法将返回空字符串
- firstBefore和lastBefore方法在未找到分隔符时会返回原始字符串
- 当处理成对出现的分隔符时，若左侧分隔符找不到对应的右侧分隔符，将返回null
- splitToList方法在输入为null时会返回空列表，而不是null
- 当分隔符为null时，方法会抛出NullPointerException

### 使用示例

```java
// 返回第一个出现分隔符之后的子字符串
String after = SplitHandler.firstAfter("name=value", "=");  // 返回"value"

// 返回第一个出现分隔符之前的子字符串
String before = SplitHandler.firstBefore("name=value", "=");  // 返回"name"

// 返回最后一个出现分隔符之前的子字符串
String lastBefore = SplitHandler.lastBefore("path/to/file.txt", "/");  // 返回"path/to"

// 返回最后一个出现分隔符之后的子字符串
String lastAfter = SplitHandler.lastAfter("path/to/file.txt", "/");  // 返回"file.txt"

// 获取首次两个分隔符成对出现之间的子字符串
String middle = SplitHandler.firstMiddle("<tag>content</tag>", "<tag>", "</tag>");  // 返回"content"

// 从指定位置开始获取首次两个分隔符成对出现之间的子字符串
String middleFromPos = SplitHandler.firstMiddle("<ignore><tag>content</tag>", 9, "<tag>", "</tag>");  // 返回"content"

// 从指定开始和结束位置获取首次两个分隔符成对出现之间的子字符串
String middleFromRange = SplitHandler.firstMiddle("<tag>content</tag><tag>other</tag>", 0, 20, "<tag>", "</tag>");  // 返回"content"

// 获取两个分隔符成对出现之间的子字符串数组
String[] middles = SplitHandler.middleAr("<tag>content1</tag><tag>content2</tag>", "<tag>", "</tag>");  // 返回["content1", "content2"]

// 从指定位置开始获取两个分隔符成对出现之间的子字符串数组
String[] middlesFromPos = SplitHandler.middleAr("<ignore><tag>content</tag>", 9, "<tag>", "</tag>");  // 返回["content"]

// 从指定开始和结束位置获取两个分隔符成对出现之间的子字符串数组
String[] middlesFromRange = SplitHandler.middleAr("<tag>content1</tag><tag>content2</tag>", 0, 22, "<tag>", "</tag>");  // 返回["content1"]

// 将带分隔符的字符串转成List
List<String> list = SplitHandler.splitToList("a,b,c,d", ",");  // 返回列表包含["a", "b", "c", "d"]
```

## StringDigitCheck - 数字字符串检查工具

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| isNumber(String str) | 判断字符串是否为整数，支持带有正负号的数值和0 | str: 待检查的字符串 | boolean: 如果是整数返回true，否则返回false |
| isFloat(String str) | 判断字符串是否为浮点数，支持带有正负号的数值、科学计数法和0 | str: 待检查的字符串 | boolean: 如果是浮点数返回true，否则返回false |

### 使用注意事项

- 方法不会对null值进行处理，传入null会返回null，不会抛出异常
- isNumber方法只判断整数格式，浮点数需使用isFloat方法
- isFloat方法支持科学计数法(如1.23E+4)，但要求指数部分必须有符号
- 这些方法只检查字符串格式，不验证数值是否在特定范围内(如整数溢出)

### 使用示例

```java
// 判断字符串是否为整数
boolean isNumber = StringDigitCheck.isNumber("123");      // 返回true
isNumber = StringDigitCheck.isNumber("-456");             // 返回true
isNumber = StringDigitCheck.isNumber("123.45");           // 返回false

// 判断字符串是否为浮点数
boolean isFloat = StringDigitCheck.isFloat("123.45");     // 返回true
isFloat = StringDigitCheck.isFloat("-123.45");            // 返回true
isFloat = StringDigitCheck.isFloat("1.23E+4");            // 返回true
isFloat = StringDigitCheck.isFloat("abc");                // 返回false
```

## StringEscapeUtils - 字符串转义和反转义工具

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| escapeJava(String input) | 转义Java字符串 | input: 待转义字符串 | String: 转义后的字符串 |
| escapeJson(String input) | 转义Json字符串 | input: 待转义字符串 | String: 转义后的字符串 |
| escapeXml10(String input) | 转义XML1.0字符串 | input: 待转义字符串 | String: 转义后的字符串 |
| escapeXml11(String input) | 转义XML1.1字符串 | input: 待转义字符串 | String: 转义后的字符串 |
| escapeCsv(String input) | 转义CSV字符串 | input: 待转义字符串 | String: 转义后的字符串 |
| escapeHtml3(String input) | 转义Html3.0字符串 | input: 待转义字符串 | String: 转义后的字符串 |
| escapeHtml4(String input) | 转义Html4.0字符串 | input: 待转义字符串 | String: 转义后的字符串 |
| escapeXSI(String input) | 转义XSI字符串 | input: 待转义字符串 | String: 转义后的字符串 |
| escapeEcmaScript(String input) | 转义EcmaScript(js)字符串 | input: 待转义字符串 | String: 转义后的字符串 |
| unescapeJava(String input) | 反转义Java字符串 | input: 待反转义字符串 | String: 反转义后的字符串 |
| unescapeJson(String input) | 反转义Json字符串 | input: 待反转义字符串 | String: 反转义后的字符串 |
| unescapeXml(String input) | 反转义XML字符串 | input: 待反转义字符串 | String: 反转义后的字符串 |
| unescapeCsv(String input) | 反转义CSV字符串 | input: 待反转义字符串 | String: 反转义后的字符串 |
| unescapeHtml3(String input) | 反转义Html3.0字符串 | input: 待反转义字符串 | String: 反转义后的字符串 |
| unescapeHtml4(String input) | 反转义Html4.0字符串 | input: 待反转义字符串 | String: 反转义后的字符串 |
| unescapeXSI(String input) | 反转义XSI字符串 | input: 待反转义字符串 | String: 反转义后的字符串 |
| unescapeEcmaScript(String input) | 反转义EcmaScript(js)字符串 | input: 待反转义字符串 | String: 反转义后的字符串 |

### 使用注意事项

- 对null值的处理：传入null会返回null，不会抛出异常
- 转义和反转义操作对原始字符串不会产生影响，而是返回新的字符串
- HTML和XML转义会处理特殊字符(如<、>、&等)，但不会处理所有非ASCII字符
- escapeJava和escapeEcmaScript方法处理结果略有不同，应根据实际需求选择
- CSV转义主要处理引号和逗号字符，与特定CSV实现可能存在差异

### 使用示例

```java
// 转义Java字符串
String escaped = StringEscapeUtils.escapeJava("He didn't say, \"Stop!\"");  // 返回"He didn't say, \\\"Stop!\\\""

// 转义XML字符串
String escapedXml = StringEscapeUtils.escapeXml10("\"bread\" & \"butter\"");  // 返回"&quot;bread&quot; &amp; &quot;butter&quot;"

// 转义CSV字符串
String escapedCsv = StringEscapeUtils.escapeCsv("Venture \"Extended Edition\"");  // 返回"\"Venture \"\"Extended Edition\"\"\""

// 反转义Java字符串
String unescaped = StringEscapeUtils.unescapeJava("He didn\\'t say, \\\"Stop!\\\"");  // 返回"He didn't say, \"Stop!\""

// 反转义HTML字符串
String unescapedHtml = StringEscapeUtils.unescapeHtml4("&lt;p&gt;Hello&lt;/p&gt;");  // 返回"<p>Hello</p>"
```

## StringIndexUtils - 字符串索引工具

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| countOccurrences(String str, char pattern) | 计算指定字符在字符串中出现的次数 | str: 给定字符串<br>pattern: 指定字符 | int: 出现次数 |
| countOccurrences(String str, String searchText) | 计算指定字符串在字符串中出现的次数 | str: 给定字符串<br>searchText: 指定字符串 | int: 出现次数 |
| countOccurrences(String str, char pattern, int startIndex) | 计算指定字符在字符串中从起始位置开始出现的次数 | str: 给定字符串<br>pattern: 指定字符<br>startIndex: 起始位置 | int: 出现次数 |
| countOccurrences(String str, String searchText, int startIndex) | 计算指定字符串在字符串中从起始位置开始出现的次数 | str: 给定字符串<br>searchText: 指定字符串<br>startIndex: 起始位置 | int: 出现次数 |
| countOccurrences(String str, char pattern, int startIndex, int endIndex) | 计算指定字符在字符串指定范围内出现的次数 | str: 给定字符串<br>pattern: 指定字符<br>startIndex: 起始位置<br>endIndex: 结束位置 | int: 出现次数 |
| countOccurrences(String str, String pattern, int startIndex, int endIndex) | 计算指定字符串在字符串指定范围内出现的次数 | str: 给定字符串<br>pattern: 指定字符或字符串<br>startIndex: 起始位置<br>endIndex: 结束位置 | int: 出现次数 |
| getIndexAfterOccurrences(String str, char pattern, int occurrence, int startIndex) | 返回指定字符出现指定次数后的索引位置 | str: 给定字符串<br>pattern: 指定字符<br>occurrence: 指定次数<br>startIndex: 起始位置 | int: 索引位置，未找到则返回-1 |
| getIndexAfterOccurrences(String str, String searchText, int occurrence, int startIndex) | 返回指定字符串出现指定次数后的索引位置 | str: 给定字符串<br>searchText: 指定字符串<br>occurrence: 指定次数<br>startIndex: 起始位置 | int: 索引位置，未找到则返回-1 |
| getIndexAfterOccurrences(String str, char pattern, int count, int startIndex, int endIndex) | 查找指定字符在字符串中指定次数后的索引位置 | str: 给定字符串<br>pattern: 指定字符<br>count: 指定字符出现次数<br>startIndex: 开始查找的起始位置<br>endIndex: 结束查找的位置 | int: 索引位置，未找到则返回-1 |
| getIndexAfterOccurrences(String str, String searchText, int count, int startIndex, int endIndex) | 查找指定字符串在字符串中指定次数后的索引位置 | str: 给定字符串<br>searchText: 指定字符串<br>count: 指定字符串出现次数<br>startIndex: 开始查找的起始位置<br>endIndex: 结束查找的位置 | int: 索引位置，未找到则返回-1 |

### 使用注意事项

- 所有方法都会对null值进行检查，当输入为null时，isEmpty和isEmptyOrNull会返回true
- 当检查"null"字符串时，使用isEmptyOrNull方法会忽略大小写和前后空格
- 在多参数方法中(如isAnyEmpty)，如果传入的是null数组，会抛出NullPointerException
- isBlank方法会检查空白字符，包括空格、制表符、换行符等，而isEmpty只检查null和空字符串

### 使用示例

```java
// 计算指定字符在字符串中出现的次数
int count1 = StringIndexUtils.countOccurrences("hello", 'l');  // 返回2

// 计算指定字符串在字符串中出现的次数
int count2 = StringIndexUtils.countOccurrences("hello hello", "lo");  // 返回2

// 计算指定字符在字符串中从起始位置开始出现的次数
int count3 = StringIndexUtils.countOccurrences("hello", 'l', 3);  // 返回1

// 计算指定字符在字符串指定范围内出现的次数
int count4 = StringIndexUtils.countOccurrences("hello world", 'l', 0, 5);  // 返回2

// 计算指定字符串在字符串指定范围内出现的次数 
int count5 = StringIndexUtils.countOccurrences("hello hello", "lo", 0, 6);  // 返回1

// 返回指定字符出现指定次数后的索引位置
int index1 = StringIndexUtils.getIndexAfterOccurrences("hello", 'l', 2, 0);  // 返回3

// 返回指定字符串出现指定次数后的索引位置
int index2 = StringIndexUtils.getIndexAfterOccurrences("hello hello", "lo", 1, 0);  // 返回4

// 查找指定字符在字符串中指定次数后的索引位置(带范围)
int index3 = StringIndexUtils.getIndexAfterOccurrences("hello world", 'l', 2, 0, 7);  // 返回3

// 查找指定字符串在字符串中指定次数后的索引位置(带范围)
int index4 = StringIndexUtils.getIndexAfterOccurrences("hello hello", "lo", 2, 0, 11);  // 返回10
```

## StringReplaceUtils - 字符串替换工具

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| replaceAll(String srcString, String regStr, String destStr) | 替换源字符串中包含指定任意字符的部分 | srcString: 源操作字符串<br>regStr: 要替换的字符串<br>destStr: 替换的目标字符串 | String: 替换后的字符串 |
| replaceAll(String srcString, String regStr, String destStr, int beginIndex, int endIndex) | 在指定范围内替换源字符串中包含指定任意字符的部分 | srcString: 源操作字符串<br>regStr: 要替换的字符串<br>destStr: 替换的目标字符串<br>beginIndex: 开始位置<br>endIndex: 结束位置 | String: 替换后的字符串 |
| replaceStringAll(String srcString, String regStr, String destStr) | 替换源字符串中包含指定字符串的部分 | srcString: 源操作字符串<br>regStr: 要替换的字符串<br>destStr: 替换的目标字符串 | String: 替换后的字符串 |
| replaceStringAll(String srcString, String regStr, String destStr, int beginIndex, int endIndex) | 在指定范围内替换源字符串中包含指定字符串的部分 | srcString: 源操作字符串<br>regStr: 要替换的字符串<br>destStr: 替换的目标字符串<br>beginIndex: 开始位置<br>endIndex: 结束位置 | String: 替换后的字符串 |
| removeChinese(String srcString) | 去除字符串中的中文字符 | srcString: 源操作字符串 | String: 去除中文后的字符串 |
| removeEnglish(String srcString) | 去除字符串中的英文字符 | srcString: 源操作字符串 | String: 去除英文后的字符串 |

### 使用注意事项

- 替换方法不会修改原始字符串，而是返回新的字符串
- 当源字符串为null时，方法会返回null而不抛出异常
- 替换字符串可以是空字符串，此时相当于删除匹配内容
- 在指定范围替换时，如果范围参数不合法(如beginIndex<0或endIndex>字符串长度)，会抛出异常
- removeChinese和removeEnglish方法使用正则表达式，处理大字符串时性能较低

### 使用示例

```java
// 替换源字符串中包含指定任意字符的部分
String replaced1 = StringReplaceUtils.replaceAll("hello123", "123", "abc");  // 返回"helloabc"

// 在指定范围内替换源字符串中包含指定任意字符的部分
String replaced2 = StringReplaceUtils.replaceAll("hello123world", "123", "abc", 0, 8);  // 返回"helloabcworld"

// 替换源字符串中包含指定字符串的部分
String replaced3 = StringReplaceUtils.replaceStringAll("hello world", "world", "everyone");  // 返回"hello everyone"

// 去除字符串中的中文字符
String noChinese = StringReplaceUtils.removeChinese("Hello你好World世界");  // 返回"HelloWorld"

// 去除字符串中的英文字符
String noEnglish = StringReplaceUtils.removeEnglish("Hello你好World世界");  // 返回"你好世界"
```

## VersionUtil - 版本号比较工具

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| build(String versionLeft) | 创建VersionUtil实例 | versionLeft: 第一个版本号 | VersionUtil: VersionUtil实例 |
| compare(String version1, String version2) | 比较两个版本号 | version1: 第一个版本号<br>version2: 第二个版本号 | int: 1(version1>version2), 0(version1=version2), -1(version1<version2) |
| greaterThan(String versionRight) | 检查versionLeft是否大于versionRight | versionRight: 第二个版本号 | boolean: 如果versionLeft大于versionRight返回true，否则返回false |
| greaterThanOrEqualTo(String versionRight) | 检查versionLeft是否大于等于versionRight | versionRight: 第二个版本号 | boolean: 如果versionLeft大于等于versionRight返回true，否则返回false |
| lessThan(String versionRight) | 检查versionLeft是否小于versionRight | versionRight: 第二个版本号 | boolean: 如果versionLeft小于versionRight返回true，否则返回false |
| lessThanOrEqualTo(String versionRight) | 检查versionLeft是否小于等于versionRight | versionRight: 第二个版本号 | boolean: 如果versionLeft小于等于versionRight返回true，否则返回false |
| equalTo(String versionRight) | 检查versionLeft是否等于versionRight | versionRight: 第二个版本号 | boolean: 如果versionLeft等于versionRight返回true，否则返回false |

### 使用注意事项

- 版本号格式应为点分隔的数字(如"1.2.3")，非标准格式可能导致意外结果
- 空版本号和null值会导致NullPointerException，使用前应验证输入
- 版本号比较会自动补零对齐(如"1.2"等同于"1.2.0")
- 版本号中的前导零会被忽略(如"01.02"等同于"1.2")
- 字母和其他特殊字符可能导致异常或不准确的比较结果

### 使用示例

```java
// 使用静态方法比较两个版本号
int compareResult = VersionUtil.compare("1.2.3", "1.2.4");  // 返回-1，表示"1.2.3"小于"1.2.4"

// 使用实例方法比较版本号
VersionUtil versionUtil = VersionUtil.build("1.2.3");
boolean isGreater = versionUtil.greaterThan("1.2.2");             // 返回true
boolean isLess = versionUtil.lessThan("1.2.4");                   // 返回true
boolean isEqual = versionUtil.equalTo("1.2.3");                   // 返回true
boolean isGreaterOrEqual = versionUtil.greaterThanOrEqualTo("1.2.3");  // 返回true
boolean isLessOrEqual = versionUtil.lessThanOrEqualTo("1.2.3");        // 返回true
```

## StringFormatUtils - 字符串格式化工具

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| formatBankCard(String input) | 格式化银行卡号（使用默认空格作为占位符） | input: 输入的银行卡号 | String: 格式化后的银行卡号 |
| formatBankCard(String input, String placeholder) | 使用正则表达式格式化银行卡号（每4位插入一个占位符） | input: 输入的银行卡号<br>placeholder: 占位符，若为空则使用默认空格 | String: 格式化后的银行卡号 |
| formatIDCard(String input) | 格式化身份证号（使用默认空格作为占位符） | input: 输入的身份证号 | String: 格式化后的身份证号 |
| formatIDCard(String input, String placeholder) | 使用正则表达式格式化身份证号（每4位插入一个占位符） | input: 输入的身份证号<br>placeholder: 占位符，若为空则使用默认空格 | String: 格式化后的身份证号 |
| formatPhoneNumber(String input) | 格式化手机号（使用默认空格作为占位符） | input: 输入的手机号 | String: 格式化后的手机号 |
| formatPhoneNumber(String input, String placeholder) | 使用正则表达式格式化手机号（3-4-4结构） | input: 输入的手机号<br>placeholder: 占位符，若为空则使用默认空格 | String: 格式化后的手机号 |
| formatAmount(double amount) | 格式化金额（使用默认参数） | amount: 金额 | String: 格式化后的金额字符串 |
| formatAmount(Locale locale, double amount, boolean showSymbol, int decimalPlaces, boolean roundingMode, String separator, String unit) | 格式化金额（自定义参数） | locale: 本地化设置<br>amount: 金额<br>showSymbol: 是否显示货币符号<br>decimalPlaces: 保留小数位数<br>roundingMode: 是否四舍五入<br>separator: 千位分隔符<br>unit: 单位 | String: 格式化后的金额字符串 |

### 使用注意事项

- 输入字符串为空时（通过StringEmptyCheck.isEmpty校验），直接返回原输入
- 占位符参数为空时自动使用默认值（银行卡/身份证默认空格，金额默认逗号，固话默认横线）
- 金额格式化支持本地化配置，小数位数自动校正（小于0时使用默认2位）
- 区号判断使用正则表达式校验，非标准固话格式返回0
- 所有格式化方法均不修改原始输入字符串，返回新的格式化字符串

### 使用示例

```java
// 格式化银行卡号（默认空格）
String formattedCard = StringFormatUtils.formatBankCard("****************");  // 返回"6222 5210 515 3430"

// 格式化银行卡号（自定义占位符）
String formattedCard2 = StringFormatUtils.formatBankCard("6222--5210--3515--3430", "--");  // 返回"6222--5210--3515--3430"

// 格式化身份证号（默认空格）
String formattedID = StringFormatUtils.formatIDCard("37108119930301966X"));  // 返回"3710 8119 9303 0196 6X"

// 格式化身份证号（自定义占位符）
String formattedID2 = StringFormatUtils.formatIDCard("37108119930301966X", "-");  // 返回"3710-8119-9303-0196-6X"

// 格式化手机号（默认空格）
String formattedPhone = StringFormatUtils.formatPhoneNumber("***********");  // 返回"178 6272 4771"

// 格式化手机号（自定义占位符）
String formattedPhone2 = StringFormatUtils.formatPhoneNumber("***********", "-");  // 返回"178-6272-4771"

// 格式化金额（默认参数）
String formattedAmount = StringFormatUtils.formatAmount(1234567.89);  // 返回"1,234,567.89"

// 格式化金额（自定义参数）
String formattedAmount2 = StringFormatUtils.formatAmount(Locale.US, 1234567.89, true, 0, false, ",", "美元");  // 返回"$1,234,567美元"
```
## StringStartEndCheck - 字符串开始和结束检测

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| startsWith(String value, String prefix) | 判断字符串是否以指定子字符串开头 | value: 待验证文本<br>prefix: 子字符串 | boolean: 如果字符串以子字符串开头返回true，否则返回false |
| endsWith(String value, String suffix) | 判断字符串是否以指定子字符串结尾 | value: 待验证文本<br>suffix: 子字符串 | boolean: 如果字符串以子字符串结尾返回true，否则返回false |

### 使用注意事项

- 所有方法都对null值进行了处理，传入null会返回false而不抛出异常
- startsWith和endsWith方法只检查字符串是否以指定子字符串开始或结束

### 使用示例
```java
// 判断字符串是否以指定子字符串开头 
boolean startsWith = StringStartEndCheck.startsWith("hello", "he"); // 返回true 
startsWith = StringStartEndCheck.startsWith("hello", "lo"); // 返回false
// 判断字符串是否以指定子字符串结尾 
boolean endsWith = StringStartEndCheck.endsWith("hello", "lo"); // 返回true
endsWith = StringStartEndCheck.endsWith("hello", "he"); // 返回false
```