package com.snbc.bbpf.commons.strings;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class StringStartEndCheckTest {

    @Test
    @DisplayName("测试以字符串开头")
    @Tags({
            @Tag("@id:126"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/6/27")
    })
    void testStartsWith() {
        assertTrue(StringStartEndCheck.startsWith("hello", "he"));
        assertFalse(StringStartEndCheck.startsWith("hello", "lo"));
        assertFalse(StringStartEndCheck.startsWith("", "he"));
        assertFalse(StringStartEndCheck.startsWith(null, "he"));
        assertFalse(StringStartEndCheck.startsWith("hello", null));
    }

    @Test
    @DisplayName("测试以字符串结尾")
    @Tags({
            @Tag("@id:126"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/6/27")
    })
    void testEndsWith() {
        assertTrue(StringStartEndCheck.endsWith("hello", "lo"));
        assertFalse(StringStartEndCheck.endsWith("hello", "he"));
        assertFalse(StringStartEndCheck.endsWith("", "lo"));
        assertFalse(StringStartEndCheck.endsWith(null, "lo"));
        assertFalse(StringStartEndCheck.endsWith("hello", null));
    }
}