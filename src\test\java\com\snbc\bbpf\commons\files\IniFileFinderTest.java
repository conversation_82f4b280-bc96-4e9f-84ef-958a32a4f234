package com.snbc.bbpf.commons.files;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.net.URL;
import java.nio.file.Paths;
import java.util.List;

/**
 * 类描述:   测试IniFileUtils
 *
 * <AUTHOR>
 * 创建时间:  [2023/9/1 15:02]
 */
public class IniFileFinderTest {
    private IniFileFinder iniFileFinder;

    /**
     * 获取文件路径
     *
     * @param filePath
     * @return
     */
    public static String findFile(String filePath) {
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        File file = new File(classLoader.getResource(filePath).getFile());
        if(!file.exists()){
            throw new IllegalArgumentException("Resource not found: " + filePath);
        }
        return file.getAbsolutePath();
    }

    /**
     * 在每个测试方法执行前执行的方法，用于初始化测试环境
     */
    @Test
    @DisplayName("在每个测试方法执行前执行的方法，用于初始化测试环境")
    @Tags({
            @Tag("@id:74"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/11/7")
    })
    @BeforeEach
    public void setup() {

        iniFileFinder = new IniFileFinder(findFile("files/z1.ini"));
//        iniFileFinder = new IniFileFinder(Paths.get("files/z1.ini").toFile().getPath());

    }

    @Test
    @DisplayName("根据正则表达式匹配的键和分组查找对应的值 testGetValuesByKeyAndGroupRegex）")
    @Tags({
            @Tag("@id:74"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/30")
    })
    public void testGetValuesByKeyAndGroupRegex() {


        List<IniSectionDomain> result = iniFileFinder.getValuesByKeyAndGroupRegex("Key1", "Section1");

        Assertions.assertEquals(1, result.size());
        IniSectionDomain section = result.get(0);
        Assertions.assertEquals("Section1", section.getSection());
        Assertions.assertEquals("Key1", section.getKey());
        Assertions.assertEquals("NewValue", section.getValue());
    }

    @Test
    @DisplayName("根据正则表达式匹配的键和分组查找对应的值 getValuesByKeyRegexWithMultipleSections）")
    @Tags({
            @Tag("@id:74"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/30")
    })
    public void testGetValuesByKeyRegexWithMultipleSections() {

        List<IniSectionDomain> result = iniFileFinder.getValuesByKeyRegexWithMultipleSections("Key1");
        Assertions.assertEquals(1, result.size());

    }

    @Test
    @DisplayName("根据正则表达式匹配的键和分组查找对应的值 null ）")
    @Tags({
            @Tag("@id:74"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/30")
    })
    public void testGetValuesByKeyRegexWithMultipleSections_Null() {

        List<IniSectionDomain> result = iniFileFinder.getValuesByKeyRegexWithMultipleSections(null);
        Assertions.assertEquals(0, result.size());

    }

    @Test
    @DisplayName("测试删除指定键（未指定分组）")
    @Tags({
            @Tag("@id:74"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/30")
    })
    public void testGetValuesByKeyAndGroupRegexWithNoMatch() {

        List<IniSectionDomain> result = iniFileFinder.getValuesByKeyAndGroupRegex("Section001", "Key1");

        Assertions.assertEquals(0, result.size());
    }

    @Test
    @DisplayName("测试删除指定键（未指定分组）")
    @Tags({
            @Tag("@id:74"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/30")
    })
    public void testGetValuesByKeyAndGroupRegexWithEmptyInput() {

        List<IniSectionDomain> result = iniFileFinder.getValuesByKeyAndGroupRegex(null, "Key1");

        Assertions.assertEquals(0, result.size());
    }


}
