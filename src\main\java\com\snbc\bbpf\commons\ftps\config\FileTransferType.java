package com.snbc.bbpf.commons.ftps.config;

/**
 * ftp传输的类型
 *
 * <AUTHOR>
 * @module 数据传输
 * @date 2023/9/27 13:10
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public enum FileTransferType {
    //二进制格式
    FILE_TRANSFER_TYPE_BINARY("binary"),
    //ascii码格式
    FILE_TRANSFER_TYPE_ASCII("ascii");

    private final String type;

    FileTransferType(String type) {
        this.type = type;
    }

    public String getType() { return type; }
}
