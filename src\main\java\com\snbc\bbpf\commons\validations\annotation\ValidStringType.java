/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.validations.annotation;

/**
 * 验证类型枚举类
 *
 * <AUTHOR>
 * @module 字符串校验模块
 * @date 2023/7/16
 * @since 1.1.0
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public enum ValidStringType {
    // 身份证号码验证
    IDENTITY_CARD,
    // 邮箱验证
    EMAIL,
    // 汉字验证
    CHINESE_CHARACTER,
    // 银行卡号验证
    BANK_CARD,
    // IP地址验证
    IP_ADDRESS,
    //电话号码
    PHONE_NUMBER,
    //网址
    URL,
    //正则表达式
    REGEX
}
