package com.snbc.bbpf.commons.validations.utils;

import com.snbc.bbpf.commons.validations.annotation.ValidString;
import com.snbc.bbpf.commons.validations.annotation.ValidStringType;
import org.junit.jupiter.api.Test;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;

import static org.junit.jupiter.api.Assertions.*;
class ValidStringUtilTest {
    @Test
    @DisplayName("验证字符串满足指定的验证器")
    @Tags({
            @Tag("@id:36"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/6/28")
    })
    void testValidateString() {
        // 正常情况
        assertDoesNotThrow(() -> {
            ValidStringUtil.validateString("abc123", "输入不合法", "[a-zA-Z0-9]+", ValidStringType.REGEX);
        });
        // 抛出异常：没有定义正则表达式
        assertThrows(IllegalArgumentException.class, () -> {
            ValidStringUtil.validateString("abc123", "输入不合法", "", ValidStringType.REGEX);
        });
        // 抛出异常：输入不合法
        assertThrows(IllegalArgumentException.class, () -> {
            ValidStringUtil.validateString("abc", "输入不合法", "[0-9]+", ValidStringType.REGEX);
        });
    }
    @Test
    @DisplayName("验证对象的属性满足注解定义的验证规则")
    @Tags({
            @Tag("@id:36"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/6/28")
    })
     void testValidateObject() {
        // 正常情况
        assertDoesNotThrow(() -> {
            User obj = new User();
            obj.setField("abc123");
            obj.setEmail("<EMAIL>");
            obj.setCnName("欧阳");
            obj.setPhone("13693388173");
            obj.setIdCard("342128195010010689");
            ValidStringUtil.validateObject(obj);
        });
        // 抛出异常：参数为空
        assertThrows(IllegalArgumentException.class, () -> {
            ValidStringUtil.validateObject(null);
        });
        // 抛出异常：没有定义正则表达式
        assertThrows(IllegalArgumentException.class, () -> {
            User obj = new User();
            obj.setField("abc123");
            ValidStringUtil.validateObject(obj);
        });
        // 抛出异常：字段不合法
        assertThrows(IllegalArgumentException.class, () -> {
            User obj = new User();
            obj.setField("abc");
            ValidStringUtil.validateObject(obj);
        });
    }
    class childObject extends User {
        public String getBankCard() {
            return bankCard;
        }

        public void setBankCard(String bankCard) {
            this.bankCard = bankCard;
        }

        @ValidString(validStringType = ValidStringType.BANK_CARD)
        private String bankCard;
    }
    // 测试类
    class User {
        @ValidString(validStringType = ValidStringType.REGEX, regex = "[a-zA-Z0-9]+", message = "字段不合法")
        private String field;
        @ValidString(validStringType = ValidStringType.CHINESE_CHARACTER)
        private String cnName;
        @ValidString(validStringType = ValidStringType.EMAIL)
        private String email;
        @ValidString(validStringType = ValidStringType.PHONE_NUMBER)
        private String phone;
        @ValidString(validStringType = ValidStringType.IDENTITY_CARD)
        private String idCard;
        public String getCnName() {
            return cnName;
        }

        public void setCnName(String cnName) {
            this.cnName = cnName;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }

        public String getIdCard() {
            return idCard;
        }

        public void setIdCard(String idCard) {
            this.idCard = idCard;
        }

        public void setField(String field) {
            this.field = field;
        }
    }
}


