/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.dates;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 对LocalDate进行时间计算  单元测试类
 * <p>计算当前时间一定时间（天，小时，分钟，秒，毫秒）之前或之后的时间</p>
 * <p>计算两个时间之间间隔时差（天，小时，分钟，秒，毫秒）<p>
 * <p>计算当前时间一定天数之前或之后的00点00分00秒或23点59分59秒的时间</p>
 * <p>计算当前时间所在当前年/季/月/周第一天的起始时间和最后一天的起始时间，可区分00点00分00秒或23点59分59秒</p>
 * <p>计算当前时间的（天，小时，分钟，秒，毫秒）数</p>
 * <p>根据（天，小时，分钟，秒，毫秒）数转换成时间</p>
 *
 * <AUTHOR>
 * @module 日期处理
 * @date 2023-05-16 13:04
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class LocalDateUtilsTest {

    LocalDate localDate;
    LocalDate targetDate;

    @BeforeEach
    void setUp() {
        localDate = LocalDate.of(2023, 5, 16);
        targetDate = LocalDate.of(2024, 5, 16);
    }

    @Test
    @DisplayName("测试LocalDateTime的加任意年月日时分秒毫秒")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testPlus() {
        final LocalDate plusReslut = LocalDateUtils.plus(localDate, 1, 1, 1);
        assertEquals(2024, LocalDateUtils.year(plusReslut));
        assertEquals(6, LocalDateUtils.month(plusReslut));
        assertEquals(17, LocalDateUtils.day(plusReslut));

    }


    @Test
    @DisplayName("测试LocalDate的diff")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testDiff() {
        assertEquals(366, LocalDateUtils.diff(localDate, targetDate, TimeUnit.DAYS));
        assertEquals(8784, LocalDateUtils.diff(localDate, targetDate, TimeUnit.HOURS));
        assertEquals(527040, LocalDateUtils.diff(localDate, targetDate, TimeUnit.MINUTES));
    }

    @Test
    @DisplayName("测试LocalDateTime的加任意年")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void plusYear() {
        assertEquals(2024, LocalDateUtils.year(LocalDateUtils.plusYears(localDate, 1)));
    }

    @Test
    @DisplayName("测试LocalDateTime的加任意月")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void plusMonths() {
        assertEquals(6, LocalDateUtils.month(LocalDateUtils.plusMonths(localDate, 1)));
    }

    @Test
    @DisplayName("测试LocalDateTime的加任意日")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void plusDays() {
        assertEquals(17, LocalDateUtils.day(LocalDateUtils.plusDays(localDate, 1)));
    }


    @Test
    @DisplayName("获取LocalDate的年份")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testYear() {
        assertEquals(2023, LocalDateUtils.year(localDate));
    }

    @Test
    @DisplayName("获取LocalDate的月份")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testMonth() {
        assertEquals(5, LocalDateUtils.month(localDate));
    }

    @Test
    @DisplayName("获取LocalDate的天数")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testDay() {
        assertEquals(16, LocalDateUtils.day(localDate));
    }


    @Test
    @DisplayName("获取LocaleDate的firstDayOfYear")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void firstDayOfYear() {
        final LocalDate reslut = LocalDateUtils.firstDayOfYear(localDate);
        assertEquals(2023, LocalDateUtils.year(reslut));
        assertEquals(1, LocalDateUtils.month(reslut));
        assertEquals(1, LocalDateUtils.day(reslut));

    }


    @Test
    @DisplayName("获取LocaleDate的lastDayOfYear")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void lastDayOfYear() {
        final LocalDate reslut = LocalDateUtils.lastDayOfYear(localDate);
        assertEquals(2023, LocalDateUtils.year(reslut));
        assertEquals(12, LocalDateUtils.month(reslut));
        assertEquals(31, LocalDateUtils.day(reslut));

    }


    @Test
    @DisplayName("获取LocaleDate的firstDayOfMonth")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void firstDayOfMonth() {
        final LocalDate reslut = LocalDateUtils.firstDayOfMonth(localDate);
        assertEquals(2023, LocalDateUtils.year(reslut));
        assertEquals(5, LocalDateUtils.month(reslut));
        assertEquals(1, LocalDateUtils.day(reslut));

    }

    @Test
    @DisplayName("获取LocaleDate的lastDayOfMonth")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void lastDayOfMonth() {
        final LocalDate reslut = LocalDateUtils.lastDayOfMonth(localDate);
        assertEquals(2023, LocalDateUtils.year(reslut));
        assertEquals(5, LocalDateUtils.month(reslut));
        assertEquals(31, LocalDateUtils.day(reslut));
    }

    @Test
    @DisplayName("获取LocalDate的ofLocalDate")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void ofLocalDate() {
        final LocalDate result = LocalDateUtils.ofLocalDate(2023, 5, 16);
        assertEquals(2023, LocalDateUtils.year(result));
        assertEquals(5, LocalDateUtils.month(result));
        assertEquals(16, LocalDateUtils.day(result));

    }

    @Test
    @DisplayName("判断时间是否处于start和end时间之间")
    @Tags({
            @Tag("@id:23"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/4/21")
    })
    void test_isEffectiveDate(){
        LocalDate nowDate = LocalDateUtils.ofLocalDate(2023, 5, 16);
        LocalDate startDate = LocalDateUtils.ofLocalDate(2023, 5, 16);
        LocalDate endDate = LocalDateUtils.ofLocalDate(2023, 5, 17);
        assertTrue(LocalDateUtils.isEffectiveDate(nowDate, startDate, endDate));
        startDate = LocalDateUtils.ofLocalDate(2023, 5, 17);
        endDate = LocalDateUtils.ofLocalDate(2023, 5, 18);
        assertFalse(LocalDateUtils.isEffectiveDate(nowDate, startDate, endDate));
    }

    @Test
    @DisplayName("LocalDate转换为Date")
    @Tags({
            @Tag("@id:20"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/5/24")
    })
    void test_toDate(){
        LocalDate nowDate = LocalDateUtils.ofLocalDate(2023, 5, 16);
        Date date = LocalDateUtils.toDate(nowDate);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int year = calendar.get(Calendar.YEAR);
        int monthValue = calendar.get(Calendar.MONTH)+1;
        int dayOfMonth = calendar.get(Calendar.DAY_OF_MONTH);
        assertEquals(year,2023);
        assertEquals(monthValue,5);
        assertEquals(dayOfMonth,16);
    }

    @Test
    @DisplayName("LocalDate转换为LocalDateTime")
    @Tags({
            @Tag("@id:20"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/5/24")
    })
    void test_toLocalDateTime(){
        LocalDate nowDate = LocalDateUtils.ofLocalDate(2023, 5, 16);
        LocalDateTime localDateTime = LocalDateUtils.toLoacalDateTime(nowDate);
        int year = localDateTime.getYear();
        int monthValue = localDateTime.getMonthValue();
        int dayOfMonth = localDateTime.getDayOfMonth();
        assertEquals(year,2023);
        assertEquals(monthValue,5);
        assertEquals(dayOfMonth,16);
    }

    @Test
    @DisplayName("LocalDate转换为ZonedDateTime")
    @Tags({
            @Tag("@id:20"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/5/24")
    })
    void test_toZonedDateTime(){
        LocalDate nowDate = LocalDateUtils.ofLocalDate(2023, 5, 16);
        ZonedDateTime zonedDateTime = LocalDateUtils.toZonedDateTime(nowDate);
        int year = zonedDateTime.getYear();
        int monthValue = zonedDateTime.getMonthValue();
        int dayOfMonth = zonedDateTime.getDayOfMonth();
        assertEquals(year,2023);
        assertEquals(monthValue,5);
        assertEquals(dayOfMonth,16);
    }

    @Test
    @DisplayName("获取两个时间之间的日期")
    @Tags({
            @Tag("@id:104"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/6/20")
    })
    void testGetBetweenDate_tenDay(){
        LocalDate start = LocalDateUtils.ofLocalDate(2024,6,21);
        LocalDate end = LocalDateUtils.ofLocalDate(2024,7,1);
        List<LocalDate> betweenDate = LocalDateUtils.getBetweenDate(start, end, ChronoUnit.DAYS, 1);
        assertEquals(betweenDate.size(),11);
    }

    @Test
    @DisplayName("获取两个时间之间的日期,不满足条件测试")
    @Tags({
            @Tag("@id:104"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/6/20")
    })
    void testGetBetweenDate_parmError(){
        LocalDate start = LocalDateUtils.ofLocalDate(2024,6,21);
        LocalDate end = LocalDateUtils.ofLocalDate(2024,7,1);
        assertThrows(IllegalArgumentException.class,
                () -> LocalDateUtils.getBetweenDate(start, end, ChronoUnit.SECONDS, 1));
        assertThrows(IllegalArgumentException.class,
                () -> LocalDateUtils.getBetweenDate(start, end, ChronoUnit.MILLENNIA, 1));
        assertThrows(IllegalArgumentException.class,
                () -> LocalDateUtils.getBetweenDate(start, end, ChronoUnit.MILLIS, 1));
        assertThrows(IllegalArgumentException.class,
                () -> LocalDateUtils.getBetweenDate(start, end, ChronoUnit.FOREVER, 1));
        assertThrows(IllegalArgumentException.class,
                () -> LocalDateUtils.getBetweenDate(null, end, ChronoUnit.DAYS, 1));
        assertThrows(IllegalArgumentException.class,
                () -> LocalDateUtils.getBetweenDate(start, null, ChronoUnit.DAYS, 1));
        assertThrows(IllegalArgumentException.class,
                () -> LocalDateUtils.getBetweenDate(start, end, ChronoUnit.DAYS, 0));
    }

    @Test
    @DisplayName("获取某个时间之前或之后的时间")
    @Tags({
            @Tag("@id:104"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/6/20")
    })
    void testGetNextDates_tenDay(){
        LocalDate start = LocalDateUtils.ofLocalDate(2024,6,21);
        List<LocalDate> nextAfterDates = LocalDateUtils.getNextDates(start, ChronoUnit.DAYS, 10, false);
        assertEquals(nextAfterDates.size(),10);
        List<LocalDate> nextBeforDates = LocalDateUtils.getNextDates(start, ChronoUnit.DAYS, -10, false);
        assertEquals(nextBeforDates.size(),10);
    }

    @Test
    @DisplayName("获取某个时间之前或之后的时间,错误参数")
    @Tags({
            @Tag("@id:104"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/6/20")
    })
    void testGetNextDates_parmError(){
        LocalDate start = LocalDateUtils.ofLocalDate(2024,6,21);
        assertThrows(IllegalArgumentException.class,
                () -> LocalDateUtils.getNextDates(null, ChronoUnit.DAYS, 10, false));
        assertThrows(IllegalArgumentException.class,
                () -> LocalDateUtils.getNextDates(start, ChronoUnit.SECONDS, 10, false));
        assertThrows(IllegalArgumentException.class,
                () -> LocalDateUtils.getNextDates(start, null, 10, false));
    }

    @Test
    @DisplayName("获取两个时间之间的日期,不包括开始和结束时间")
    @Tags({
            @Tag("@id:104"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/6/20")
    })
    void testGetBetweenDate_noIncludeStartAndEnd(){
        LocalDate start = LocalDateUtils.ofLocalDate(2024,6,21);
        LocalDate end = LocalDateUtils.ofLocalDate(2024,7,1);
        List<LocalDate> betweenDate = LocalDateUtils.getBetweenDate(start, end, ChronoUnit.DAYS, 1,false,false);
        assertEquals(betweenDate.size(),9);
        assertNotEquals(betweenDate.get(0),start);
        assertNotEquals(betweenDate.get(betweenDate.size()-1),end);
    }

    @Test
    @DisplayName("获取两个时间之间的日期,倒序列表")
    @Tags({
            @Tag("@id:104"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/6/20")
    })
    void testGetBetweenDate_reverse(){
        LocalDate start = LocalDateUtils.ofLocalDate(2024,6,21);
        LocalDate end = LocalDateUtils.ofLocalDate(2024,7,1);
        List<LocalDate> betweenDate = LocalDateUtils.getBetweenDate(start, end, ChronoUnit.DAYS, -1);
        assertEquals(betweenDate.size(),11);
        assertEquals(betweenDate.get(0),end);
        assertEquals(betweenDate.get(betweenDate.size()-1),start);
    }
}