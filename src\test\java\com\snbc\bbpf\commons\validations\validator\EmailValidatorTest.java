package com.snbc.bbpf.commons.validations.validator;

import com.snbc.bbpf.commons.validations.ValidException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;

 import static org.junit.jupiter.api.Assertions.*;
class EmailValidatorTest {
    private final IStringValidator validate = new EmailValidator();
    @Test
    @DisplayName("输入非空")
    @Tags({
            @Tag("@id:36"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/6/28")
    })
    void testValidateWithValidEmail() {
        String validEmail = "<EMAIL>";
        String validMessage = "Valid email";
        assertDoesNotThrow(() -> validate.validate(validEmail, validMessage));
    }
     @Test
    @DisplayName("输入为空")
    @Tags({
            @Tag("@id:36"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/6/28")
    })
    void testValidateWithNullEmail() {
        String nullEmail = null;
        String validMessage = "Valid email";
         IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                validate.validate(nullEmail, validMessage));
        assertEquals("Email is empty or null", exception.getMessage());
    }
    @Test
    @DisplayName("邮箱格式错误")
    @Tags({
            @Tag("@id:36"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/6/28")
    })
    void testValidateWithInvalidEmailFormat() {

        String invalidEmail = "test@example";
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                validate.validate(invalidEmail, null));
        assertEquals("Email format error", exception.getMessage());
    }
}
