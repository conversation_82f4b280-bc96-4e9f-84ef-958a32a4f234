/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.strings;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 字符串分隔处理类
 * <p>根据相应的分隔符对字符串进行分隔成子串</p>
 * <p>功能列表</p>
 * <p>1.返回第一个出现分隔符之后的子字符串</p>
 * <p>2.返回第一个出现分隔符之前的子字符串</p>
 * <p>3.返回最后一个出现分隔符之前的子字符串</p>
 * <p>4.返回最后一个出现分隔符之后的子字符串</p>
 * <p>5.获取首次两个分隔符（可以相同也可以不相同）成对出现之间的子字符串</p>
 * <p>6.获取两个分隔符（可以相同也可以不相同）成对出现之间的子字符串数组</p>
 * <p>7.从指定位置开始获取首次两个分隔符（可以相同也可以不相同）成对出现之间的子字符串或子字符串数组</p>
 * <p>8.从指定开始和结束位置获取首次两个分隔符（可以相同也可以不相同）成对出现之间的子字符串或子字符串数组</p>
 * <p>9.将带分隔符的字符串转成List</p>
 * <AUTHOR>
 * @module 字符串处理
 * @date 2023/4/23 19:18
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class SplitHandler {

    public static final String PARAMETER_EMPTY = "Input parameter is empty";
    public static final String PARAMETER_INVALID = "Index parameter invalid";

    private SplitHandler() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 返回第一个出现分隔符之后的子字符串
     * @param str 待处理的字符串
     * @param separator 分隔符号
     * @return 处理后的字符
     * @throws IllegalArgumentException 如果输入字符串或分隔符为空
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/23
     */
    public static String firstAfter(String str, String separator){
		if (StringEmptyCheck.isEmpty(str) || StringEmptyCheck.isEmpty(separator)) {
            throw new IllegalArgumentException(PARAMETER_EMPTY);
		}

        int index = str.indexOf(separator);
        if (-1 == index){
            return "";
        }
        return str.substring(index + 1);
    }

    /**
     * 返回第一个出现分隔符之前的子字符串
     * @param str 待处理的字符串
     * @param separator 分隔符号
     * @return 处理后的字符
     * @throws IllegalArgumentException 如果输入字符串或分隔符为空
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/23
     */
    public static String firstBefore(String str, String separator){
		if (StringEmptyCheck.isEmpty(str) || StringEmptyCheck.isEmpty(separator)) {
            throw new IllegalArgumentException(PARAMETER_EMPTY);
		}

        int index = str.indexOf(separator);
        if (-1 == index){
            return "";
        }
        return str.substring(0, index);
    }

    /**
     * 返回最后一个出现分隔符之前的子字符串
     * @param str 待处理的字符串
     * @param separator 分隔符号
     * @return 处理后的字符
     * @throws IllegalArgumentException 如果输入字符串或分隔符为空
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/23
     */
    public static String lastBefore(String str, String separator){
		if (StringEmptyCheck.isEmpty(str) || StringEmptyCheck.isEmpty(separator)) {
            throw new IllegalArgumentException(PARAMETER_EMPTY);
		}

        int index = str.lastIndexOf(separator);
        if (-1 == index){
            return "";
        }
        return str.substring(0, index);
    }

    /**
     * 返回最后一个出现分隔符之后的子字符串
     * @param str 待处理的字符串
     * @param separator 分隔符号
     * @return 处理后的字符
     * @throws IllegalArgumentException 如果输入字符串或分隔符为空
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/23
     */
    public static String lastAfter(String str, String separator){
		if (StringEmptyCheck.isEmpty(str) || StringEmptyCheck.isEmpty(separator)) {
            throw new IllegalArgumentException(PARAMETER_EMPTY);
		}

        int index = str.lastIndexOf(separator);
        if (-1 == index){
            return "";

        }
        return str.substring(index + 1);
    }

    /**
     * 获取首次两个分隔符（可以相同也可以不相同）成对出现之间的子字符串
     * @param str 待处理的字符串
     * @param separatorLeft 起始分隔符号
     * @param separatorRight 结束分隔符号
     * @return 处理后的字符
     * @throws IllegalArgumentException 如果输入字符串或分隔符为空
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/23
     */
    public static String firstMiddle(String str, String separatorLeft, String separatorRight){
		if (StringEmptyCheck.isEmpty(str) || 
			StringEmptyCheck.isEmpty(separatorLeft) || 
			StringEmptyCheck.isEmpty(separatorRight)) {
            throw new IllegalArgumentException(PARAMETER_EMPTY);
		}

        int indexLeft = str.indexOf(separatorLeft);
        if (-1 == indexLeft){
            return "";
        }

        int indexRight = str.indexOf(separatorRight, indexLeft + 1);
        if (-1 == indexRight){
            return "";
        }


        return str.substring(indexLeft + 1, indexRight);
    }

    /**
     * 从指定位置开始获取首次两个分隔符（可以相同也可以不相同）成对出现之间的子字符串
     * @param str 待处理的字符串
     * @param indexBegin 指定位置开始
     * @param separatorLeft 起始分隔符号
     * @param separatorRight 结束分隔符号
     * @return 处理后的字符
     * @throws IllegalArgumentException 如果输入字符串或分隔符为空、指定位置无效
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/23
     */
    public static String firstMiddle(String str, int indexBegin, 
    										String separatorLeft, String separatorRight){
		if (StringEmptyCheck.isEmpty(str) || 
			StringEmptyCheck.isEmpty(separatorLeft) || 
			StringEmptyCheck.isEmpty(separatorRight)) {
            throw new IllegalArgumentException(PARAMETER_EMPTY);
		}
		
        if (indexBegin < 0 || indexBegin >= str.length()){
            throw new IllegalArgumentException(PARAMETER_INVALID);
        }

        return firstMiddle(str.substring(indexBegin), separatorLeft, separatorRight);
    }


    /**
     * 从指定开始和结束位置获取首次两个分隔符（可以相同也可以不相同）成对出现之间的子字符串
     * @param str 待处理的字符串
     * @param indexBegin 指定开始位置
     * @param indexEnd 指定结束位置
     * @param separatorLeft 起始分隔符号
     * @param separatorRight 结束分隔符号
     * @return 处理后的字符
     * @throws IllegalArgumentException 如果输入字符串或分隔符为空、指定位置无效
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/23
     */
    public static String firstMiddle(String str, int indexBegin, int indexEnd, 
    									   String separatorLeft, String separatorRight){
		if (StringEmptyCheck.isEmpty(str) || 
			StringEmptyCheck.isEmpty(separatorLeft) || 
			StringEmptyCheck.isEmpty(separatorRight)) {
            throw new IllegalArgumentException(PARAMETER_EMPTY);
		}
		
        if (indexBegin < 0 || indexEnd < 0 || indexBegin >= indexEnd){
            throw new IllegalArgumentException(PARAMETER_INVALID);
        }

		if (indexBegin >= str.length() || indexEnd >= str.length()) {
			throw new IllegalArgumentException(PARAMETER_INVALID);		
		}

        return firstMiddle(str.substring(indexBegin, indexEnd), separatorLeft, separatorRight);
    }


    /**
     * 获取两个分隔符（可以相同也可以不相同）成对出现之间的子字符串数组
     * @param str 待处理的字符串
     * @param separatorLeft 起始分隔符号
     * @param separatorRight 结束分隔符号
     * @return 处理后的字符
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/23
     */
    public static String[] middleAr(String str, String separatorLeft, String separatorRight){
		if (StringEmptyCheck.isEmpty(str) || 
			StringEmptyCheck.isEmpty(separatorLeft) || 
			StringEmptyCheck.isEmpty(separatorRight)) {
            throw new IllegalArgumentException(PARAMETER_EMPTY);
		}

        ArrayList<String> arStr = new ArrayList<>();
        int index1 = 0;
        int index2 = 0;
        while (index1 >= 0 && index2 >= 0) {
            index1 = str.indexOf(separatorLeft, index1);

            if (index1 >= 0) {
                ++index1;
                index2 = str.indexOf(separatorRight, index1);

                if (index2 >= 0) {
                    String strTemp = str.substring(index1, index2);

                    if (!strTemp.isEmpty()) {
                        arStr.add(strTemp);
                    }

                    index1 = index2 + 1;
                }
            }
        }

		/**
		//正则表达式匹配方式性能方面与上面的相比，上面的性能表现更好一些		
		String regx = "<(.*?)>";
		Pattern pattern = Pattern.compile(regx);
		Matcher matcher = pattern.matcher(str);
        ArrayList<String> arStr = new ArrayList<String>();
		while(matcher.find()){
            arStr.add(matcher.group(1));
		}
		*/

        return arStr.toArray(new String[arStr.size()]);
    }



    /**
     * 从指定位置开始获取两个分隔符（可以相同也可以不相同）成对出现之间的子字符串数组
     * @param str 待处理的字符串
     * @param indexBegin 指定位置开始
     * @param separatorLeft 起始分隔符号
     * @param separatorRight 结束分隔符号
     * @return 处理后的字符
     * @throws IllegalArgumentException 如果输入字符串或分隔符为空、指定位置无效
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/23
     */
    public static String[] middleAr(String str, int indexBegin,
    									String separatorLeft, String separatorRight){
    
		if (StringEmptyCheck.isEmpty(str) || 
			StringEmptyCheck.isEmpty(separatorLeft) || 
			StringEmptyCheck.isEmpty(separatorRight)) {
            throw new IllegalArgumentException(PARAMETER_EMPTY);
		}
        if (indexBegin < 0 || indexBegin >= str.length()){
            throw new IllegalArgumentException(PARAMETER_INVALID);
        }

        return middleAr(str.substring(indexBegin), separatorLeft, separatorRight);
    }


    /**
     * 从指定开始和结束位置获取两个分隔符（可以相同也可以不相同）成对出现之间的子字符串数组
     * @param str 待处理的字符串
     * @param indexBegin 指定开始位置
     * @param indexEnd 指定结束位置
     * @param separatorLeft 起始分隔符号
     * @param separatorRight 结束分隔符号
     * @return 处理后的字符
     * @throws IllegalArgumentException 如果输入字符串或分隔符为空、指定位置无效
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/23
     */
    public static String[] middleAr(String str, int indexBegin, int indexEnd, 
    									String separatorLeft, String separatorRight){
		if (StringEmptyCheck.isEmpty(str) || 
			StringEmptyCheck.isEmpty(separatorLeft) || 
			StringEmptyCheck.isEmpty(separatorRight)) {
            throw new IllegalArgumentException(PARAMETER_EMPTY);
		}

		if (indexBegin < 0 || indexEnd < 0 || indexBegin >= indexEnd) {
			throw new IllegalArgumentException(PARAMETER_INVALID);
		}
		
        if (indexBegin >= str.length() || indexEnd >= str.length()){
            throw new IllegalArgumentException(PARAMETER_INVALID);
        }

        return middleAr(str.substring(indexBegin, indexEnd), separatorLeft, separatorRight);
    }


    /**
     * 将带分隔符的字符串转成List
     * @param str 待处理的字符串
     * @param separator 分隔符
     * @return 分隔后的集合
     * @throws IllegalArgumentException 输入字符串或分隔符为空
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/5/21
     */
    public static List<String> splitToList(String str, String separator){
        if (StringEmptyCheck.isEmpty(str) || StringEmptyCheck.isEmpty(separator)) {
            throw new IllegalArgumentException(PARAMETER_EMPTY);
        }
        return Arrays.asList(str.split(separator));
    }

}
