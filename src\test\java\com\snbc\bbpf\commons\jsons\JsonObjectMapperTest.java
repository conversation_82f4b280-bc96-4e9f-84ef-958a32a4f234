/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.jsons;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.google.common.collect.Lists;
import com.snbc.bbpf.commons.dates.LocalDateTimeUtils;
import com.snbc.bbpf.commons.validations.annotation.ValidStringType;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * JsonTool测试类
 *
 * <AUTHOR>
 * @module JSON模块
 * @date 2023-09-07 16:13
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */

class JsonObjectMapperTest {

    List<Object> list = new ArrayList<>();
    final LocalDateTime now = LocalDateTime.of(2023, 9, 5, 15, 30, 45, 0);
    HasChildMapObject hasChildMapObject1 = new HasChildMapObject("1", 1);
    HasChildMapObject hasChildMapObject2 = new HasChildMapObject("2", 2);
    Map<String, Object> map = new HashMap<>();

    ContainerInfo containerInfo;
    UnmashallInfo unmashallInfo;

    @BeforeEach
    void setup() {
        final LocalDateTime now = LocalDateTime.of(2023, 9, 5, 15, 30, 45, 0);
        containerInfo = new ContainerInfo(12,
                "新北洋1号售货机",
                LocalDateTimeUtils.toDate(now)
                ,
                now, null, null, LocalDateTimeUtils.toDate(now));
        unmashallInfo = new UnmashallInfo(12);
    }

    @DisplayName("测试序列化对象为json")
    @Tags({
            @Tag("@id:51"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testMarshall() {
        String xmlStr = JsonObjectMapper.marshall(containerInfo);
        assertEquals("{\"id\":12,\"name\":\"新北洋1号售货机\",\"createTime\":\"2023-09-05 15:30:45\",\"updateTime\":\"2023-09-05 15:30:45\",\"mark\":null,\"tags\":null,\"markDate\":1693899045000}",
                xmlStr);
    }

    @DisplayName("测试序列化对象为null的测试")
    @Tags({
            @Tag("@id:51"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/9/15")
    })
    @Test
    void testMarshall_param_is_null() {
        NullPointerException thrown = Assertions.assertThrows(NullPointerException.class, () -> {
            JsonObjectMapper.marshall(null);
        });
        Assertions.assertEquals("object is not allowed null", thrown.getMessage());

    }

    @DisplayName("测试序列化对象为json带有特性")
    @Tags({
            @Tag("@id:51"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testMarshall_withfeatures() {
        final ArrayList<SerializationFeature> enableFeatures = Lists.newArrayList(SerializationFeature.WRAP_ROOT_VALUE);
        final ArrayList<SerializationFeature> disableFeatures = Lists.newArrayList(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        String xmlStr = JsonObjectMapper.marshall(containerInfo, enableFeatures, disableFeatures
        );
        assertEquals("{\"ContainerInfo\":{\"id\":12,\"name\":\"新北洋1号售货机\",\"createTime\":\"2023-09-05 15:30:45\",\"updateTime\":\"2023-09-05 15:30:45\",\"mark\":null,\"tags\":null,\"markDate\":\"2023-09-05T07:30:45.000+00:00\"}}",
                xmlStr);


    }


    @DisplayName("测试json字符串反序列化为对象")
    @Tags({
            @Tag("@id:51"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testUnmarshall() {
        String jsonStr = "{\"id\":12,\"name\":\"新北洋1号售货机\",\"createTime\":\"2023-09-05 15:30:45\",\"updateTime\":\"2023-09-05 15:30:45\",\"mark\":null,\"tags\":null,\"markDate\":1693899045000,\"other\":\"otherInfo\"}";
        final ContainerInfo targetContainerInfo = JsonObjectMapper.unmarshall(jsonStr, ContainerInfo.class);
        assertEquals(containerInfo.getId(), targetContainerInfo.getId());
        assertEquals(containerInfo.getMark(), targetContainerInfo.getMark());
        assertEquals(containerInfo.getName(), targetContainerInfo.getName());
        assertEquals(containerInfo.getCreateTime(), targetContainerInfo.getCreateTime());
        assertEquals(containerInfo.getUpdateTime(), targetContainerInfo.getUpdateTime());
        assertEquals(containerInfo.getTags(), targetContainerInfo.getTags());
        assertEquals(containerInfo.getMarkDate(), targetContainerInfo.getMarkDate());

    }

    @DisplayName("测试json字符串反序列化为对象带有多特性")
    @Tags({
            @Tag("@id:51"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/9/07")
    })
    @Test
    void testUnmarshall_with_features() {
        final ArrayList<DeserializationFeature> enablesFeatures = Lists.newArrayList(DeserializationFeature.UNWRAP_ROOT_VALUE);
        final ArrayList<DeserializationFeature> disblaeFeatures = Lists.newArrayList(DeserializationFeature.EAGER_DESERIALIZER_FETCH);
        String jsonStr = "{\"UnmashallInfo\":{\"count\":12.12}}";
        final UnmashallInfo target = JsonObjectMapper.unmarshallWithFeatures(jsonStr, UnmashallInfo.class, enablesFeatures, disblaeFeatures);
        assertEquals(unmashallInfo.getCount(), target.getCount());
    }

    @Test
    @DisplayName("json转list异常")
    @Tags({
            @Tag("@id:52"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/14")
    })
    void testunmarshall_exception() {
        assertThrows(RuntimeException.class, () -> {
            JsonObjectMapper.unmarshall(" ", new TypeReference<List<Object>>() {
            });
        });
    }

    @Test
    @DisplayName("空字符串转list，结果为null")
    @Tags({
            @Tag("@id:52"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/14")
    })
    void testunmarshall_emptyStrToList() {
        assertEquals(null, JsonObjectMapper.unmarshall("", new TypeReference<List<Object>>() {
        }));

    }

    @Test
    @DisplayName("json转嵌套list")
    @Tags({
            @Tag("@id:52"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/14")
    })
    void testunmarshall_jsonToNestedList() {
        List<HasChildMapObject> list1 = new ArrayList<>();
        list1.add(hasChildMapObject1);
        List<HasChildMapObject> list2 = new ArrayList<>();
        list2.add(hasChildMapObject2);
        List<List<HasChildMapObject>> list3 = new ArrayList<>();
        list3.add(list1);
        list3.add(list2);

        String str = "[[{\"str\":\"1\",\"itNumber\":1,\"testMapObject\":{\"str\":\"456\",\"itNumber\":789,\"map\":{\"data2\":\"2\",\"data1\":\"1\"}}}],[{\"str\":\"2\",\"itNumber\":2,\"testMapObject\":{\"str\":\"456\",\"itNumber\":789,\"map\":{\"data2\":\"2\",\"data1\":\"1\"}}}]]";
        List<List<HasChildMapObject>> toList = JsonObjectMapper.unmarshall(str, new TypeReference<List<List<HasChildMapObject>>>() {
        });
        assertEquals(list3, toList);
    }

    @Test
    @DisplayName("json转enumlist")
    @Tags({
            @Tag("@id:52"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/14")
    })
    void testunmarshall_jsonToEnumList() {
        list.add(ValidStringType.REGEX);
        list.add(ValidStringType.PHONE_NUMBER);

        String str = "[\"REGEX\",\"PHONE_NUMBER\"]";
        List<ValidStringType> toList = JsonObjectMapper.unmarshall(str, new TypeReference<List<ValidStringType>>() {
        });
        assertEquals(list, toList);
    }

    @Test
    @DisplayName("json转value为Object类型list")
    @Tags({
            @Tag("@id:52"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/14")
    })
    void testunmarshall_jsonToFixTypeList() {
        list.add(Lists.newArrayList("1", "2"));
        Map<String, String> map1 = new HashMap<>();
        map1.put("key", "value");
        list.add(map1);
        list.add("str");
        list.add(20);

        String str = "[[\"1\",\"2\"],{\"key\":\"value\"},\"str\",20]";
        List<Object> toList = JsonObjectMapper.unmarshall(str, new TypeReference<List<Object>>() {
        });
        assertEquals(list, toList);
    }

    @Test
    @DisplayName("json转value为属性未初始化的Beanlist")
    @Tags({
            @Tag("@id:52"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/14")
    })
    void testunmarshall_jsonToEmptyValueBeanList() {
        list.add(new HasChildMapObject());

        String str = "[{\"str\":null,\"itNumber\":0,\"testMapObject\":{\"str\":null,\"itNumber\":0,\"map\":null}}]";
        List<HasChildMapObject> toList = JsonObjectMapper.unmarshall(str, new TypeReference<List<HasChildMapObject>>() {
        });
        assertEquals(list, toList);
    }

    @Test
    @DisplayName("json转含有注解的Beanlist，反序列化忽略某个属性")
    @Tags({
            @Tag("@id:52"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/14")
    })
    void testunmarshall_jsonToJaksonAnnotionList() {
        list.add(containerInfo);

        String str = "[{\"id\":12,\"name\":\"新北洋1号售货机\",\"createTime\":\"2023-09-05 15:30:45\",\"updateTime\":\"2023-09-05 15:30:45\",\"mark\":null,\"tags\":null,\"markDate\":1693899045000}]";
        List<ContainerInfo> toList = JsonObjectMapper.unmarshall(str, new TypeReference<List<ContainerInfo>>() {
        });
        assertEquals(null, toList.get(0).getIgnoreStr());
    }

    @Test
    @DisplayName("使用自定义Feature，json转list异常")
    @Tags({
            @Tag("@id:52"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/14")
    })
    void testunmarshall_exceptionFeatures() {
        assertThrows(RuntimeException.class, () -> {
            JsonObjectMapper.unmarshallWithFeatures(" ", new TypeReference<List<String>>() {
            }, null, null);
        });
    }

    @Test
    @DisplayName("使用自定义Feature，空字符串转list，结果为null")
    @Tags({
            @Tag("@id:52"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/14")
    })
    void testunmarshall_emptyStrToListFeatures() {
        assertEquals(null, JsonObjectMapper.unmarshallWithFeatures("", new TypeReference<String>() {
        }, null, null));

    }

    @Test
    @DisplayName("使用自定义Feature，json转嵌套list")
    @Tags({
            @Tag("@id:52"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/14")
    })
    void testunmarshall_jsonToNestedListFeatures() {
        List<HasChildMapObject> list1 = new ArrayList<>();
        list1.add(hasChildMapObject1);
        List<HasChildMapObject> list2 = new ArrayList<>();
        list2.add(hasChildMapObject2);
        List<List<HasChildMapObject>> list3 = new ArrayList<>();
        list3.add(list1);
        list3.add(list2);

        String str = "[[{\"str\":\"1\",\"itNumber\":1,\"testMapObject\":{\"str\":\"456\",\"itNumber\":789,\"map\":{\"data2\":\"2\",\"data1\":\"1\"}}}],[{\"str\":\"2\",\"itNumber\":2,\"testMapObject\":{\"str\":\"456\",\"itNumber\":789,\"map\":{\"data2\":\"2\",\"data1\":\"1\"}}}]]";
        List<List<HasChildMapObject>> toList = JsonObjectMapper.unmarshallWithFeatures(str, new TypeReference<List<List<HasChildMapObject>>>() {
        }, null, null);
        assertEquals(list3, toList);
    }

    @Test
    @DisplayName("使用自定义Feature，json转enumlist")
    @Tags({
            @Tag("@id:52"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/14")
    })
    void testunmarshall_jsonToEnumListFeatures() {
        final ArrayList<DeserializationFeature> enablesFeatures = Lists.newArrayList(DeserializationFeature.READ_ENUMS_USING_TO_STRING);
        list.add(ValidStringType.REGEX);
        list.add(ValidStringType.PHONE_NUMBER);

        String str = "[\"REGEX\",\"PHONE_NUMBER\"]";
        List<ValidStringType> toList = JsonObjectMapper.unmarshallWithFeatures(str, new TypeReference<List<ValidStringType>>() {
        }, enablesFeatures, null);
        assertEquals(list, toList);
    }

    @Test
    @DisplayName("json转map异常")
    @Tags({
            @Tag("@id:52"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/14")
    })
    void testunmarshall_toMapException() {
        assertThrows(RuntimeException.class, () -> {
            JsonObjectMapper.unmarshall(" ", new TypeReference<Map<String, Object>>() {
            });
        });
    }

    @Test
    @DisplayName("空字符串转map，结果为null")
    @Tags({
            @Tag("@id:52"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/14")
    })
    void testunmarshall_emptyStrToMap() {
        assertEquals(null, JsonObjectMapper.unmarshall("", new TypeReference<Map<String, Object>>() {
        }));

    }

    @Test
    @DisplayName("json转嵌套map")
    @Tags({
            @Tag("@id:52"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/14")
    })
    void testunmarshall_jsonToNestedMap() {
        Map<String, String> map1 = new HashMap<>();
        map1.put("map1_str2", "2");
        map1.put("map1_str1", "1");
        Map<String, Object> map2 = new HashMap<>();
        map2.put("map2_str", "22str");
        map2.put("map2_itNumber", 2222);
        map2.put("map2_map", map1);
        Map<String, Object> map3 = new HashMap<>();
        map3.put("map3_str", "33str");
        map3.put("map3_itNumber", 333);
        map3.put("map3_map", map2);

        String str = "{\"map3_map\":{\"map2_str\":\"22str\",\"map2_map\":{\"map1_str2\":\"2\",\"map1_str1\":\"1\"},\"map2_itNumber\":2222},\"map3_itNumber\":333,\"map3_str\":\"33str\"}";
        Map<String, Object> toMap = JsonObjectMapper.unmarshall(str, new TypeReference<Map<String, Object>>() {
        });
        assertEquals(map3, toMap);
    }

    @Test
    @DisplayName("json转value为list的map")
    @Tags({
            @Tag("@id:52"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/14")
    })
    void testunmarshall_jsonToListMap() {
        map.put("list1", Lists.newArrayList("1", "2", "3"));

        String str = "{\"list1\":[\"1\",\"2\",\"3\"]}";
        Map<String, ArrayList<String>> toMap = JsonObjectMapper.unmarshall(str, new TypeReference<Map<String, ArrayList<String>>>() {
        });
        assertEquals(map, toMap);
    }

    @Test
    @DisplayName("json转value为JavaBean的map")
    @Tags({
            @Tag("@id:52"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/14")
    })
    void testunmarshall_jsonToJavaBeanMap() {

        map.put("data1", hasChildMapObject1);
        map.put("data2", hasChildMapObject2);

        String str = "{\"data2\":{\"str\":\"2\",\"itNumber\":2,\"testMapObject\":{\"str\":\"456\",\"itNumber\":789,\"map\":{\"data2\":\"2\",\"data1\":\"1\"}}},\"data1\":{\"str\":\"1\",\"itNumber\":1,\"testMapObject\":{\"str\":\"456\",\"itNumber\":789,\"map\":{\"data2\":\"2\",\"data1\":\"1\"}}}}";
        Map<String, HasChildMapObject> toMap = JsonObjectMapper.unmarshall(str, new TypeReference<Map<String, HasChildMapObject>>() {
        });
        assertEquals(map, toMap);
    }

    @Test
    @DisplayName("json转value为Object的map")
    @Tags({
            @Tag("@id:52"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/14")
    })
    void testunmarshall_jsonToObjMap() {

        Map<String, String> map1 = new HashMap<>();
        map1.put("key", "value");
        map.put("map", map1);
        map.put("string", "str");
        map.put("int", 20);

        String str = "{\"string\":\"str\",\"map\":{\"key\":\"value\"},\"int\":20}";
        Map<String, Object> toMap = JsonObjectMapper.unmarshall(str, new TypeReference<Map<String, Object>>() {
        });
        assertEquals(map, toMap);
    }

    @Test
    @DisplayName("json转value为Enum的map")
    @Tags({
            @Tag("@id:52"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/14")
    })
    void testunmarshall_jsonToEnumMap() {

        map.put("REGEX", ValidStringType.REGEX);
        map.put("PHONE_NUMBER", ValidStringType.PHONE_NUMBER);

        String str = "{\"REGEX\":\"REGEX\",\"PHONE_NUMBER\":\"PHONE_NUMBER\"}";
        Map<String, ValidStringType> toMap = JsonObjectMapper.unmarshall(str, new TypeReference<Map<String, ValidStringType>>() {
        });
        assertEquals(map, toMap);
    }

    @Test
    @DisplayName("json转value为属性未初始化的Beanmap")
    @Tags({
            @Tag("@id:52"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/14")
    })
    void testunmarshall_jsonToEmptyBeanMap() {

        map.put("bean", new HasChildMapObject());

        String str = "{\"bean\":{\"str\":null,\"itNumber\":0,\"testMapObject\":{\"str\":null,\"itNumber\":0,\"map\":null}}}";
        Map<String, HasChildMapObject> toMap = JsonObjectMapper.unmarshall(str, new TypeReference<Map<String, HasChildMapObject>>() {
        });
        assertEquals(map, toMap);
    }

    @Test
    @DisplayName("json转含有注解的BeanMap，反序列化忽略某个属性")
    @Tags({
            @Tag("@id:52"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/9/14")
    })
    void testunmarshall_jsonToJaksonAnnotionMap() {
        map.put("annotion", containerInfo);
        String str = "{\"annotion\":{\"id\":12,\"name\":\"新北洋1号售货机\",\"createTime\":\"2023-09-05 15:30:45\",\"updateTime\":\"2023-09-05 15:30:45\",\"mark\":null,\"tags\":null,\"markDate\":1693899045000}}";
        Map<String, ContainerInfo> toMap = JsonObjectMapper.unmarshall(str, new TypeReference<Map<String, ContainerInfo>>() {
        });
        assertEquals(null, toMap.get("annotion").getIgnoreStr());
    }
}

/**
 * JsonTool测试用Bean
 *
 * <AUTHOR>
 * @module 序列化/反序列化
 * @date 2023-09-05 16:15
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */

class ContainerInfo {

    @JsonIgnore
    private String ignoreStr;
    private Integer id;
    /**
     * 售货机名称
     */
    private String name;
    /**
     * 售货机创建时间
     * 如果项目使用 spring.jackson.time-zone=Asia/Shanghai
     * spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createTime;
    /**
     * 售货机更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    private String mark;

    private List<String> tags;

    private Date markDate;

    public ContainerInfo() {
    }

    public String getIgnoreStr() {
        return ignoreStr;
    }

    public void setIgnoreStr(String ignoreStr) {
        this.ignoreStr = ignoreStr;
    }

    public ContainerInfo(Integer id, String name, Date createTime, LocalDateTime updateTime, String mark, List<String> tags, Date markDate) {
        this.id = id;
        this.name = name;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.mark = mark;
        this.tags = tags;
        this.markDate = markDate;
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getMark() {
        return mark;
    }

    public void setMark(String mark) {
        this.mark = mark;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }


    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public Date getMarkDate() {
        return markDate;
    }

    public void setMarkDate(Date markDate) {
        this.markDate = markDate;
    }

    @Override
    public String toString() {
        return "ContainerInfo{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", mark='" + mark + '\'' +
                ", tags=" + tags +
                ", markDate=" + markDate +
                '}';
    }


}

/**
 * JsonTool测试用Bean
 *
 * <AUTHOR>
 * @module 序列化/反序列化
 * @date 2023-09-05 16:15
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */

class UnmashallInfo {
    /**
     * float->Integer转化
     */
    private Integer count;

    public UnmashallInfo() {
    }


    public UnmashallInfo(Integer count) {
        this.count = count;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    @Override
    public String toString() {
        return "UnmashallInfo{" +
                "count=" + count +
                '}';
    }
}

class HasChildMapObject {
    private String str;
    private int itNumber;
    private MapObject testMapObject;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        HasChildMapObject that = (HasChildMapObject) o;
        return itNumber == that.itNumber && Objects.equals(str, that.str) && Objects.equals(testMapObject, that.testMapObject);
    }

    @Override
    public int hashCode() {
        return Objects.hash(str, itNumber, testMapObject);
    }

    @Override
    public String toString() {
        return "HasChildMapObject{" +
                "str='" + str + '\'' +
                ", itNumber=" + itNumber +
                ", testMapObject=" + testMapObject +
                '}';
    }

    public MapObject getTestMapObject() {
        return testMapObject;
    }

    public void setTestMapObject(MapObject testMapObject) {
        this.testMapObject = testMapObject;
    }

    public int getItNumber() {
        return itNumber;
    }

    public String getStr() {
        return str;
    }

    public void setItNumber(int itNumber) {
        this.itNumber = itNumber;
    }

    public void setStr(String str) {
        this.str = str;
    }

    public HasChildMapObject() {
        this.testMapObject = new MapObject();
    }

    public HasChildMapObject(String str, int tNumber) {
        this.str = str;
        this.itNumber = tNumber;
        this.testMapObject = new MapObject("456", 789);
        testMapObject.setMap(new HashMap<>());
        testMapObject.getMap().put("data1", "1");
        testMapObject.getMap().put("data2", "2");
    }
}

class MapObject {
    private String str;
    private int itNumber;
    private Map<String, String> map;

    @Override
    public String toString() {
        return "MapObject{" +
                "str='" + str + '\'' +
                ", itNumber=" + itNumber +
                ", map=" + map +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MapObject mapObject = (MapObject) o;
        return itNumber == mapObject.itNumber && Objects.equals(str, mapObject.str) && Objects.equals(map, mapObject.map);
    }

    @Override
    public int hashCode() {
        return Objects.hash(str, itNumber, map);
    }

    public void setStr(String str) {
        this.str = str;
    }

    public void setItNumber(int itNumber) {
        this.itNumber = itNumber;
    }

    public String getStr() {
        return str;
    }

    public int getItNumber() {
        return itNumber;
    }

    public Map<String, String> getMap() {
        return map;
    }

    public void setMap(Map<String, String> map) {
        this.map = map;
    }

    public MapObject() {
    }

    public MapObject(String str, int tNumber) {
        this.str = str;
        this.itNumber = tNumber;
        map = new HashMap<>();
        map.put("data1", "1");
        map.put("data2", "2");
    }

}