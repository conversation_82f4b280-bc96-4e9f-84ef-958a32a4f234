/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.objs;

/**
 * 【对象处理】适用场景：VO和DO互转 ，BO和DO互转等场景
 *
 *   <p>1.分严格模式和非严格模式，默认非严格模式，严格模式下必须所有属性一一对应，否则抛出异常。</p>
 *   <p>2.需要对带有这两个对象的集合也要进行转换。</p>
 *   <p>3.需要考虑对象有父类的情况</p>
 *   <p></p>
 *   <p>这里对使用MapStruc类库进行对象间的转换进行了一层封装，可以在该接口的基础上进行进一步实现自己的转换方法。</p>
 *   <p>使用说明：</p>
 *   <p>1. 首先定义一个接口</p>
 *   <p>@Mapper</p>
 *   <p>interface UserCover extends IConvert<User, UserVO> {</p>
 *   <p>    IConvert INSTANCE = Mappers.getMapper(UserCover.class);</p>
 *   <p>}</p>
 *   <p>UserVO userVO = (UserVO)UserCover.INSTANCE.to(user);</p>
 *   <p>User user = (User)UserCover.INSTANCE.from(userVO);</p>
 *   <p></p>
 *   <p>注意事项：</p>
 *   <p>1. 如果对转换的对象属性名不同的情况,需要参考本方式实现</p>
 *   <p>需在对应的转换方法上增加 @Mappings({}）注解，其中内容如下：</p>
 *   <p>       @Mappings({  </p>
 *   <p>           @Mapping(source = "userName", target = "name"), </p>
 *   <p>           @Mapping(target = "createTime", expression = "java(java.time.format.DateTimeFormatter</p>
 *   <p>.ofPattern(\"yyyy-MM-dd HH:mm:ss\").format(source.getCreateTime()))") </p>
 *   <p>  }) </p>
 *   <p> 若需要进行类属性名称从 userName 转 到 name ,则需要 </p>
 *   <p> 增加@Mapping(source = "userName", target = "name") </p>
 *   <p> 若需要对属性值进行转换 如createTime 进行格式转换 </p>
 *   <p> 需要增加 @Mapping(target = "createTime", expression = "java(java.time.format.DateTimeFormatter</p>
 *   <p>.ofPattern(\"yyyy-MM-dd HH:mm:ss\").format(source.getCreateTime()))") </p>
 *   <p></p>
 *   <p> 2.不可用于将对象转换成Map对象，此外具体接口不能使用Map，需要使用具体的实现HashMap进行定义。</p>
 *   <p> 3. 对于严格模式和非严格模式mqpStruct需要在@Mappings注解上设置 unmappedSourcePolicy 默认是非严格模式，如果严格模式可以设置ReportingPolicy.ERROR </p>
 * <AUTHOR>
 * @module
 * @date 2023/7/4 20:41
 * @since 1.1.0
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */

public interface IConvert<S,T> {
    /**
     * 将源对象转换为目标类型对象
     * @param source 源对象
     * @return T 目标类型对象
     * @since 1.1.0
     * <AUTHOR>
     * @date 2023/7/4
     */
    T to(S source);
    /**
     * 将目标类型对象转换回源对象
     * @param source 目标类型对象
     * @return S 源类型对象
     * @since 1.1.0
     * <AUTHOR>
     * @date 2023/7/4
     */
    S from(T source);
}
