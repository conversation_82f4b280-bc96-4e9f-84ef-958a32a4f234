/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.expressions;

import com.snbc.bbpf.commons.objs.ObjectEmptyCheck;
import com.snbc.bbpf.commons.strings.StringEmptyCheck;

//Aviator
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;


import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;


/**
 * 解析带有表达式的字符串,并进行运算处理类
 * <p>功能列表</p>
 * <p>1.可以支持对表达式传入参数后并计算结果</p>
 * <p>2.如果结果是小数，  可支持精度计算， 可传入相关参数，  保证精度</p>
 *
 * <p>要选择一种开源的实现进行整合（需要说明理由和对比情况， 需要考虑对复杂表达式计算的性能和资源消耗情况。
 * 可以对exp4j 0.4.8，Aviator 5.3.3，Fel 0.8，janino 3.1.10，JSEL 0.1.0）， 可以处理和执行表达式，  并计算结果。</p>
 * <p>经验证测试，Aviator从性能与资源消耗方面表现较好，且Aviator是直接将表达式编译成Java字节码，交给JVM去执行，是轻量级和高性能，活跃度高，网上资料比较多。
 * fel次之，但fel不支持乘方运算，需要结合spring的el表达式来进行，不支持变量嵌套。
 * 其他表达式引擎表现无以上两种优秀</p>
 * <AUTHOR>
 * @module 表达式模块
 * @date 2023/9/11 20:10
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class StringExpressionUtils {


	private static final String RESULT_NULL = "Result is null";
	private static final String PARAMETER_NULL = "Input parameter is null";
	private static final String PARAMETER_EMPTY = "Input parameter is empty";
	private static final String PARAMETER_INVALID = "Index parameter invalid";
    private StringExpressionUtils() {
        throw new IllegalStateException("Utility class");
    }


    /**
     * 把Map的值Object类型转换为BigDecimal Object
     * @param result 待转换为BigDecimal的对象
     * @return 结果
	 * @throws Exception 如果参数异常或计算异常
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/9/11
     */
	private static Map<String, Object> toBigDecimalObject(Map<String, Object> args) {
		if (args == null) {
			throw new IllegalArgumentException(PARAMETER_NULL);
		}
		Map<String, Object> ret = new HashMap<>();
		for (Map.Entry<String, Object> entry : args.entrySet()) {
			Object value = entry.getValue();
			if (value instanceof BigDecimal) {
			} else if (value instanceof String) {
				value = new BigDecimal((String) value);
			} else if (value instanceof BigInteger) {
				value = new BigDecimal((BigInteger) value);
			} else if (value instanceof Number) {
				value = BigDecimal.valueOf(((Number) value).doubleValue());
			} else {
				throw new IllegalArgumentException(PARAMETER_INVALID);
			}
			ret.put(entry.getKey(), value);
		}
		return ret;
	}
	
    /**
     * 对字符串表达式、传入参数后并计算结果
     * 例如：expression="x+y+x*y", args={"x" : 2, "y" : 3}, 结果为：11
     * @param expression 字符串表达式
     * @param args key为变量名，value为变量值
     * @return 计算结果(BigDecimal类型)
	 * @throws Exception 如果参数异常或计算异常
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/9/11
     */
	public static BigDecimal calculateBigDecimal(String expression, Map<String, Object> args) {
		if (StringEmptyCheck.isEmpty(expression)) {
			throw new IllegalArgumentException(PARAMETER_EMPTY);
		}
		// 检查参数
		Map<String, Object> realArgs = null;
		if (null != args) {
			if (ObjectEmptyCheck.isEmpty(args)) {
				throw new IllegalArgumentException(PARAMETER_EMPTY);
			}
			realArgs = toBigDecimalObject(args);
		}

		try {
			Expression compiledExp = AviatorEvaluator.compile(expression);
			Object result = null != realArgs ? compiledExp.execute(realArgs) : compiledExp.execute();
			if (result == null) {
				throw new IllegalArgumentException(RESULT_NULL);
			}

			if (result instanceof BigDecimal) {
				return (BigDecimal) result;
			} else if (result instanceof Number) {
				return BigDecimal.valueOf(((Number) result).doubleValue());
			} else {
				throw new IllegalArgumentException(PARAMETER_INVALID);
			}
        } catch (Exception e) {
			throw new RuntimeException(e);
        }
	}

	/**
	 * 对字符串表达式、传入参数后并计算结果
	 * 例如：expression="x+y+x*y", args={"x" : 2, "y" : 3}, 结果为：11
	 * @param expression 字符串表达式
	 * @param args key为变量名，value为变量值
	 * @return 计算结果(double类型)
	 * @throws Exception 如果参数异常或计算异常
	 * @since 1.2.0
	 * <AUTHOR>
	 * @date 2023/9/11
	 */
	public static double calculate(String expression, Map<String, Object> args) {
		return calculateBigDecimal(expression, args).doubleValue();
	}

    /**
     * 对算术字符串表达式计算结果
     * 例如：expression="2+3+2*3", 结果为：11
     * @param expression 字符串表达式
     * @return 计算结果(double类型)
	 * @throws Exception 如果参数异常或计算异常
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/9/11
     */
	public static double calculate(String expression) {
		return calculate(expression, null);
	}

    /**
     * 对字符串表达式、传入参数后并计算，返回保留精度结果
     * 例如：expression="x/y", args={"x" : 1.0, "y" : 3}, newScale=2, roundingMode=RoundingMode.UP, 结果为：0.34
     * @param expression 字符串表达式
     * @param args key为变量名，value为变量值
     * @param newScale 精度
     * @param roundingMode 精确计算模式
     * @return 计算结果(double类型)
	 * @throws Exception 如果参数异常或计算异常
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/9/11
     */
	public static double calculate(String expression, Map<String, Object> args, int newScale, RoundingMode roundingMode) {
		if (newScale < 0) {
			throw new IllegalArgumentException(PARAMETER_INVALID);
		}
		return calculateBigDecimal(expression, args).setScale(newScale, roundingMode).doubleValue();
	}

    /**
     * 对算术字符串表达式计算，返回保留精度结果
     * 例如：expression="1.0/3", newScale=2, roundingMode=RoundingMode.UP, 结果为：0.34
     * @param expression 字符串表达式
     * @param newScale 精度
     * @param roundingMode 精确计算模式
     * @return 计算结果(double类型)
     * @throws Exception 如果参数异常或计算异常
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/9/11
     */
	public static double calculate(String expression, int newScale, RoundingMode roundingMode) {
		return calculate(expression, null, newScale, roundingMode);		
	}

}
