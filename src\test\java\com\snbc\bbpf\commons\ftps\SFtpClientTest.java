package com.snbc.bbpf.commons.ftps;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.SftpException;
import com.snbc.bbpf.commons.ftps.config.ConnectConfig;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Predicate;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

/**
 * sftp工具类单元测试类
 *
 * <AUTHOR>
 * @module sftp模块
 * @date 2023/11/01 20:30
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
@ExtendWith(MockitoExtension.class)
public class SFtpClientTest {
    @Mock
    private ChannelSftp channelSftp;
    private ConnectConfig connectConfig;
    @InjectMocks
    private SFTPClient underTest;

    @Mock
    private AtomicBoolean isConnect;

    private final String filePath = "root";

    @BeforeEach
    @DisplayName("初始化配置，方便后续测试")
    @Tags({
            @Tag("@id:71"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/11/05")
    })
    void init(){
        connectConfig = new ConnectConfig.Builder()
                .host("127.0.0.1")
                .port(22)
                .userName("tester")
                .password("password")
                .build();
        underTest.init(connectConfig);
    }

    @Test
    @DisplayName("测试远程下载文件不存在，抛出异常")
    @Tags({
            @Tag("@id:71"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/8/23")
    })
    public void testPathNoExist_download(){
        try {
            Mockito.when(underTest.isConnected()).thenReturn(true);
            Mockito.when(channelSftp.stat(filePath)).thenThrow(new SftpException(1,""));
        } catch (SftpException e) {
            throw new RuntimeException(e);
        }
        Assertions.assertThrows(IllegalArgumentException.class,() -> underTest.download(filePath,filePath));
    }

    @Test
    @DisplayName("测试文件是否存在，存在")
    @Tags({
            @Tag("@id:71"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/11/05")
    })
    public void testPathNoExist_existFileInRemote(){
        try {
            Mockito.when(channelSftp.stat(filePath)).thenReturn(null);
        } catch (SftpException e) {
            throw new RuntimeException(e);
        }
        Assertions.assertTrue(underTest.existFileInRemote(filePath));
    }

    @Test
    @DisplayName("列出远程默认目录的所有文件")
    @Tags({
            @Tag("@id:71"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/11/05")
    })
    public void testlistAll_listFiles(){
        try {
            List<String> fileNames = new ArrayList<String>();
            fileNames.add("file1");
            fileNames.add("file2");
            Mockito.when(channelSftp.pwd()).thenReturn(filePath);
            List<String> ftpFiles = underTest.listFiles();
            verify(channelSftp, times(1)).ls(eq(filePath),any());
        } catch (SftpException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testBatchDeleteRemoteFiles_ShouldReturnSuccessMap() {
        String remoteDirPath = "path/to/remote/directory";
        Predicate<String> predicate = fileName -> fileName.endsWith(".txt");
        Map<String, Boolean> result = underTest.batchDeleteRemoteFiles(remoteDirPath, predicate);
        Assertions.assertNotNull(result, "Expected non-null result map");
    }

    @Test
    @DisplayName("创建远程目录成功")
    @Tags({
            @Tag("@id:102"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2024/08/23")
    })
    void testCreateRemoteDir_ShouldCreateSuccessfully() {
        String remoteDirPath = "path/to/remote/directory";
        String dirName = "newdir";
        boolean result = underTest.createRemoteDir(remoteDirPath, dirName);
        Assertions.assertTrue(result, "Expected to create remote directory successfully");
    }
    @Test
    @DisplayName("创建远程目录失败")
    @Tags({
            @Tag("@id:102"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2024/08/23")
    })
    void testCreateRemoteDir_Failure() throws IOException, SftpException {
        doThrow(SftpException.class).when(channelSftp).mkdir(anyString());

        boolean result = underTest.createRemoteDir("remote/path", "dirName");

        assertFalse(result);
        verify(channelSftp).mkdir(anyString());
    }
}
