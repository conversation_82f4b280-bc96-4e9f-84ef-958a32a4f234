package com.snbc.bbpf.commons.bytes.tlv;

import com.snbc.bbpf.commons.bytes.ByteOrderUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TLV编码器单元测试类
 *
 * <AUTHOR>
 * @module 字节处理模块
 * @date 2025/07/25 12:00
 * @copyright 2024 山东新北洋信息技术股份有限公司. All rights reserved
 */
class TlvEncoderTest {

    private TlvEncoder encoder;

    @BeforeEach
    void setUp() {
        encoder = new TlvEncoder();
    }

    @Test
    @DisplayName("测试编码器构造函数")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testEncoderConstructor() {
        // 测试默认构造函数
        TlvEncoder defaultEncoder = new TlvEncoder();
        assertEquals(ByteOrder.BIG_ENDIAN, defaultEncoder.getByteOrder());
        assertEquals(StandardCharsets.UTF_8, defaultEncoder.getDefaultCharset());
        assertEquals(1, defaultEncoder.getTypeLength());
        assertEquals(4, defaultEncoder.getLengthFieldSize());

        // 测试自定义构造函数
        TlvEncoder customEncoder = new TlvEncoder(ByteOrder.LITTLE_ENDIAN, StandardCharsets.US_ASCII, 2, 2);
        assertEquals(ByteOrder.LITTLE_ENDIAN, customEncoder.getByteOrder());
        assertEquals(StandardCharsets.US_ASCII, customEncoder.getDefaultCharset());
        assertEquals(2, customEncoder.getTypeLength());
        assertEquals(2, customEncoder.getLengthFieldSize());
    }

    @Test
    @DisplayName("测试构造函数参数验证")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testConstructorValidation() {
        // 测试null参数
        assertThrows(IllegalArgumentException.class, () -> 
            new TlvEncoder(null, StandardCharsets.UTF_8, 1, 4));
        
        assertThrows(IllegalArgumentException.class, () -> 
            new TlvEncoder(ByteOrder.BIG_ENDIAN, null, 1, 4));

        // 测试无效的Type长度
        assertThrows(IllegalArgumentException.class, () -> 
            new TlvEncoder(ByteOrder.BIG_ENDIAN, StandardCharsets.UTF_8, 0, 4));
        
        assertThrows(IllegalArgumentException.class, () -> 
            new TlvEncoder(ByteOrder.BIG_ENDIAN, StandardCharsets.UTF_8, 5, 4));

        // 测试无效的Length字段长度
        assertThrows(IllegalArgumentException.class, () -> 
            new TlvEncoder(ByteOrder.BIG_ENDIAN, StandardCharsets.UTF_8, 1, 0));
        
        assertThrows(IllegalArgumentException.class, () -> 
            new TlvEncoder(ByteOrder.BIG_ENDIAN, StandardCharsets.UTF_8, 1, 9));
    }

    @Test
    @DisplayName("测试基本数据类型编码")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testBasicDataTypeEncoding() {
        // 测试byte编码
        byte[] byteResult = encoder.encode((byte) 42);
        assertEquals(6, byteResult.length); // 1(type) + 4(length) + 1(value)
        assertEquals(0x01, byteResult[0]); // Type: BYTE
        assertEquals(42, byteResult[5]); // Value

        // 测试short编码
        byte[] shortResult = encoder.encode((short) 1000);
        assertEquals(7, shortResult.length); // 1(type) + 4(length) + 2(value)
        assertEquals(0x02, shortResult[0]); // Type: SHORT

        // 测试int编码
        byte[] intResult = encoder.encode(123456);
        assertEquals(9, intResult.length); // 1(type) + 4(length) + 4(value)
        assertEquals(0x03, intResult[0]); // Type: INTEGER

        // 测试long编码
        byte[] longResult = encoder.encode(123456789L);
        assertEquals(13, longResult.length); // 1(type) + 4(length) + 8(value)
        assertEquals(0x04, longResult[0]); // Type: LONG

        // 测试boolean编码
        byte[] boolResult = encoder.encode(true);
        assertEquals(6, boolResult.length); // 1(type) + 4(length) + 1(value)
        assertEquals(0x20, boolResult[0]); // Type: BOOLEAN
        assertEquals(1, boolResult[5]); // Value: true

        byte[] boolFalseResult = encoder.encode(false);
        assertEquals(0, boolFalseResult[5]); // Value: false
    }

    @Test
    @DisplayName("测试浮点数类型编码")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testFloatingPointEncoding() {
        // 测试float编码
        byte[] floatResult = encoder.encode(3.14f);
        assertEquals(9, floatResult.length); // 1(type) + 4(length) + 4(value)
        assertEquals(0x10, floatResult[0]); // Type: FLOAT

        // 测试double编码
        byte[] doubleResult = encoder.encode(3.14159);
        assertEquals(13, doubleResult.length); // 1(type) + 4(length) + 8(value)
        assertEquals(0x11, doubleResult[0]); // Type: DOUBLE

        // 测试BigInteger编码
        BigInteger bigInt = new BigInteger("123456789012345678901234567890");
        byte[] bigIntResult = encoder.encode(bigInt);
        assertEquals(0x05, bigIntResult[0]); // Type: BIG_INTEGER

        // 测试BigDecimal编码
        BigDecimal bigDec = new BigDecimal("123.456789");
        byte[] bigDecResult = encoder.encode(bigDec);
        assertEquals(0x12, bigDecResult[0]); // Type: BIG_DECIMAL
    }

    @Test
    @DisplayName("测试字符串编码")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testStringEncoding() {
        String testString = "Hello, TLV!";
        
        // 测试默认UTF-8编码
        byte[] utf8Result = encoder.encode(testString);
        assertEquals(0x30, utf8Result[0]); // Type: STRING_UTF8
        
        // 验证长度字段
        int expectedLength = testString.getBytes(StandardCharsets.UTF_8).length;
        assertEquals(expectedLength, ByteOrderUtils.toInt(
            Arrays.copyOfRange(utf8Result, 1, 5), ByteOrder.BIG_ENDIAN));

        // 测试指定ASCII编码
        byte[] asciiResult = encoder.encode(testString, TlvDataType.STRING_ASCII);
        assertEquals(0x31, asciiResult[0]); // Type: STRING_ASCII
    }

    @Test
    @DisplayName("测试字节数组编码")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testByteArrayEncoding() {
        byte[] testData = {0x01, 0x02, 0x03, 0x04, 0x05};
        byte[] result = encoder.encode(testData);
        
        assertEquals(0x50, result[0]); // Type: BYTE_ARRAY
        
        // 验证长度字段
        assertEquals(5, ByteOrderUtils.toInt(
            Arrays.copyOfRange(result, 1, 5), ByteOrder.BIG_ENDIAN));
        
        // 验证数据内容
        byte[] valueData = Arrays.copyOfRange(result, 5, result.length);
        assertArrayEquals(testData, valueData);
    }

    @Test
    @DisplayName("测试Map编码")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testMapEncoding() {
        Map<String, Integer> testMap = new HashMap<>();
        testMap.put("key1", 100);
        testMap.put("key2", 200);
        
        byte[] result = encoder.encode(testMap);
        assertEquals(0x40, result[0]); // Type: MAP
        
        // 验证结果不为空且包含正确的结构
        assertTrue(result.length > 5); // 至少包含Type + Length + 元素数量
    }

    @Test
    @DisplayName("测试数组编码")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testArrayEncoding() {
        List<Integer> testList = Arrays.asList(1, 2, 3, 4, 5);
        
        byte[] result = encoder.encode(testList);
        assertEquals(0x41, result[0]); // Type: ARRAY
        
        // 验证结果不为空且包含正确的结构
        assertTrue(result.length > 5); // 至少包含Type + Length + 元素数量
    }

    @Test
    @DisplayName("测试null值编码")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testNullEncoding() {
        byte[] result = encoder.encode(null);
        
        assertEquals(5, result.length); // 1(type) + 4(length)
        assertEquals(0x00, result[0]); // Type: NULL
        
        // 验证长度为0
        assertEquals(0, ByteOrderUtils.toInt(
            Arrays.copyOfRange(result, 1, 5), ByteOrder.BIG_ENDIAN));
    }

    @Test
    @DisplayName("测试不支持的数据类型")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testUnsupportedDataType() {
        Object unsupportedObject = new Object();
        
        assertThrows(IllegalArgumentException.class, () -> encoder.encode(unsupportedObject));
    }

    @Test
    @DisplayName("测试指定数据类型编码")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testEncodeWithSpecifiedType() {
        String testString = "Test";
        
        // 指定为ASCII编码
        byte[] asciiResult = encoder.encode(testString, TlvDataType.STRING_ASCII);
        assertEquals(0x31, asciiResult[0]); // Type: STRING_ASCII
        
        // 指定为UTF-8编码
        byte[] utf8Result = encoder.encode(testString, TlvDataType.STRING_UTF8);
        assertEquals(0x30, utf8Result[0]); // Type: STRING_UTF8
        
        // 测试null数据类型
        assertThrows(IllegalArgumentException.class, () -> 
            encoder.encode(testString, null));
    }

    @Test
    @DisplayName("测试小端字节序编码")
    @Tags({
            @Tag("@id:125"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2025/07/25")
    })
    void testLittleEndianEncoding() {
        TlvEncoder littleEndianEncoder = new TlvEncoder(
            ByteOrder.LITTLE_ENDIAN, StandardCharsets.UTF_8, 1, 4);
        
        int testValue = 0x12345678;
        byte[] result = littleEndianEncoder.encode(testValue);
        
        assertEquals(0x03, result[0]); // Type: INTEGER
        
        // 验证长度字段（小端）
        int length = ByteOrderUtils.toInt(
            Arrays.copyOfRange(result, 1, 5), ByteOrder.LITTLE_ENDIAN);
        assertEquals(4, length);
        
        // 验证值字段（小端）
        int decodedValue = ByteOrderUtils.toInt(
            Arrays.copyOfRange(result, 5, 9), ByteOrder.LITTLE_ENDIAN);
        assertEquals(testValue, decodedValue);
    }
}
