/*
 * 版权所有 2009-2023 山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.crypt;

import org.apache.commons.lang3.StringUtils;

import java.util.Base64;

/**
 * Base64算法加解密工具类
 *
 * <AUTHOR>
 * @date 2023/11/28 18:11
 * @since 1.4.0
 */
public final class Base64Utils {


    private Base64Utils(){
        throw new IllegalStateException("Utility class");
    }

    private static final Base64.Decoder DECODER = Base64.getDecoder();
    private static final Base64.Encoder ENCODER = Base64.getEncoder();

    /**
     * <p>数据加密</p>
     *
     * @param plainTextByteArray 明文字节数组
     * @return java.lang.String 密文字符串
     * <AUTHOR>
     * @date 2023/11/28 18:15
     * @since 1.4.0
     */
    public static String encode(byte[] plainTextByteArray)  {
        //判断入参不能为空
        if (null == plainTextByteArray || plainTextByteArray.length == 0) {
            throw new IllegalArgumentException("The argument plainTextByteArray is Blank");
        }
        return ENCODER.encodeToString(plainTextByteArray);
    }

    /**
     * <p>数据解密</p>
     *
     * @param cipherTextStr 密文字符串
     * @return byte[] 解密后的字节数组
     * <AUTHOR>
     * @date 2023/11/28 18:22
     * @since 1.4.0
     */
    public static byte[] decode(String cipherTextStr) {
        if (StringUtils.isBlank(cipherTextStr)) {
            throw new IllegalArgumentException("The cipherTextStr is Blank");
        }
        return DECODER.decode(cipherTextStr);
    }


}
