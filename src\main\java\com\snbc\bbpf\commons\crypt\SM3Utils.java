/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.crypt;

import com.snbc.bbpf.commons.strings.StringEmptyCheck;
import org.bouncycastle.crypto.digests.SM3Digest;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.util.encoders.Hex;

import java.nio.charset.StandardCharsets;
import java.security.Security;
import java.util.Objects;

/**
 * sm3散列算法
 * 1. 实现sm3的加密算法
 * 2. 使用BouncyCastleProvider（简称BC）扩展可用算法
 *
 * <AUTHOR>
 * @module 加解密
 * @date 2023-12-03 20:10
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class SM3Utils {
    private static final String PARAMETER_IS_NULL = "parameter is null";
	private static final String PARAMETER_EMPTY = "parameter is empty";
    private SM3Utils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 使用BouncyCastleProvider来实现sm3值计算
     *
     * @param rawStr 原始计算字符串
     * @return sm3计算值
     * <AUTHOR>
     * @date 2023/12/03
     * @since 1.4.0
     */
    public static String sm3(String rawStr) {
		// 检查参数
		if (StringEmptyCheck.isEmpty(rawStr)) {
			throw new IllegalArgumentException(PARAMETER_EMPTY);
		}
		
        return sm3(rawStr.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 使用BouncyCastleProvider来实现sm3值计算
     *
     * @param rawBytes 原始字节数组
     * @return sm3计算值
     * <AUTHOR>
     * @date 2023/12/03
     * @since 1.4.0
     */
    public static String sm3(byte[] rawBytes) {
        Objects.requireNonNull(rawBytes, PARAMETER_IS_NULL);
        Security.addProvider(new BouncyCastleProvider());
        SM3Digest sm3Digest = new SM3Digest();
        sm3Digest.update(rawBytes, 0, rawBytes.length);
        byte[] digested = new byte[sm3Digest.getDigestSize()];
        sm3Digest.doFinal(digested, 0);
        return new String(Hex.encode(digested), StandardCharsets.UTF_8);
    }
}
