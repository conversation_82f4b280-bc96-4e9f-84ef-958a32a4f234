/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.validations.annotation;


import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 验证字符串是否是整数或浮点数注解
 *
 * <AUTHOR>
 * @module 注解
 * @date 2023/7/26
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 * @since 1.2.0
 */
@Target(value = {ElementType.FIELD,ElementType.METHOD,ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ValidStringNumber {
    String message() default ""  ;
}
