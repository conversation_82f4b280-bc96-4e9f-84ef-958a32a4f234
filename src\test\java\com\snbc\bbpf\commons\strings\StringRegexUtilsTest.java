/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.strings;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import static org.junit.jupiter.api.Assertions.assertEquals;

class StringRegexUtilsTest {



    @Test
    @DisplayName("正确的15位身份证验证")
    @Tags({
            @Tag("@id:10"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testisIDCard15_isright() {
        assertEquals( StringRegexUtils.isIDCard15("130503670401001"),
                true);
    }
    @Test
    @DisplayName("错误的15位身份证验证")
    @Tags({
            @Tag("@id:10"),
            @Tag("@author:liangjun<PERSON>"),
            @Tag("@date:2023/4/26")
    })
    void testisIDCard15_iserror() {
        assertEquals( StringRegexUtils.isIDCard15("130503671401001"),
                false);
    }
    @Test
    @DisplayName("正确的18位身份证验证")
    @Tags({
            @Tag("@id:10"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testisIDCard18_isright() {
        assertEquals( StringRegexUtils.isIDCard18("332627198811111216"),
                true);
    }
    @Test
    @DisplayName("错误的18位身份证验证")
    @Tags({
            @Tag("@id:10"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testisIDCard18_iserror() {
        assertEquals( StringRegexUtils.isIDCard18("332627298821111216"),
                false);
    }

    @Test
    @DisplayName("正确的Email")
    @Tags({
            @Tag("@id:10"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testisEmail_isright() {
        assertEquals( StringRegexUtils.isEmail("<EMAIL>"),
                true);
    }
    @Test
    @DisplayName("错误的Email")
    @Tags({
            @Tag("@id:10"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testisEmail_iserror() {
        assertEquals( StringRegexUtils.isEmail("332627298821.com"),
                false);
    }
    @Test
    @DisplayName("正确的中文")
    @Tags({
            @Tag("@id:10"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testisZh_isright() {
        assertEquals( StringRegexUtils.isZh("中国人發"),
                true);
    }
    @Test
    @DisplayName("正确的中文生僻字")
    @Tags({
            @Tag("@id:10"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testisZh_noncharisright() {
        assertEquals( StringRegexUtils.isZh("両麤弢發嘦"),
                true);
    }
    @Test
    @DisplayName("错误的中文")
    @Tags({
            @Tag("@id:10"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testisZh_iserror() {
        assertEquals( StringRegexUtils.isZh("33262总7298821.com"),
                false);
    }
    @Test
    @DisplayName("正确的IP")
    @Tags({
            @Tag("@id:10"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testisIP_isright() {
        assertEquals( StringRegexUtils.isIP("**************"),
                true);
    }
    @Test
    @DisplayName("错误的IP")
    @Tags({
            @Tag("@id:10"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testisIP_iserror() {
        assertEquals( StringRegexUtils.isIP("**************.33"),
                false);
    }
    @Test
    @DisplayName("正确的手机号")
    @Tags({
            @Tag("@id:10"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testisMobileExact_isright() {
        assertEquals( StringRegexUtils.isMobileExact("13423221111"),
                true);
    }
    @Test
    @DisplayName("错误的手机号")
    @Tags({
            @Tag("@id:10"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testisMobileExact_iserror() {
        assertEquals( StringRegexUtils.isMobileExact("19215514444"),
                false);
    }
    @Test
    @DisplayName("正确的URL")
    @Tags({
            @Tag("@id:10"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testisURL_isright() {
        assertEquals( StringRegexUtils.isURL("http://www.baidu.com"),
                true);
    }
    @Test
    @DisplayName("错误的URL")
    @Tags({
            @Tag("@id:10"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testisURL_iserror() {
        assertEquals( StringRegexUtils.isURL("httpwww.baidu.com"),
                false);
    }

    @Test
    @DisplayName("正确的银行卡号")
    @Tags({
            @Tag("@id:10"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testIsBankCard_isright() {
        assertEquals( StringRegexUtils.isBankCard("6222600260001072006"),
                true);
    }
    @Test
    @DisplayName("错误的银行卡号")
    @Tags({
            @Tag("@id:10"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testIsBankCard_iserror() {
        assertEquals( StringRegexUtils.isBankCard("6221386102180111444"),
                false);
    }
    @Test
    @DisplayName("错误的银行卡号含有字母")
    @Tags({
            @Tag("@id:10"),
            @Tag("@author:liangjunbin"),
            @Tag("@date:2023/4/26")
    })
    void testIsBankCard_iserrorbychar() {
        assertEquals( StringRegexUtils.isBankCard("6D221386102180111444"),
                false);
    }
}