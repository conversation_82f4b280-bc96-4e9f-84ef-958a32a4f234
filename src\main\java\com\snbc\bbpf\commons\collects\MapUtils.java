/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.collects;


import com.fasterxml.jackson.databind.SerializationFeature;
import com.snbc.bbpf.commons.jsons.JsonObjectMapper;
import com.snbc.bbpf.commons.objs.ObjectEmptyCheck;
import com.snbc.bbpf.commons.reflects.ReflectUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * # 将Map和对象进行互相转换
 * map的key是对象的属性，String类型，map的value是对应属性值。类型不固定。
 * 可能存在嵌套的对象和map转换，需要考虑。
 * 直接通过set方法对对象属性值设置
 * 可以通过标记设置是否严格模式，如果严格模式，则表示所有属性和所有map的key必须都能对应， 如果有存在不对应情况则抛出异常。
 * 非严格模式下可以忽略此情况，只对匹配的进行设置和转换。默认情况是非严格模式。
 * 另外需要考虑有父类的对象的情况。
 * Map<String，T> 转换成JSON String，需要考虑日期格式 ，空属性的转换场景。
 *
 * <AUTHOR>
 * @module MAP
 * @date 2023/7/13 15:02 下午
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class MapUtils {

    private MapUtils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 将对象转成MAP 对象
     *
     * @param object 要转的对象
     * @return Map<String, Object> 返回MAP对象
     * @throws
     * <AUTHOR>
     * @date 2023/7/25
     * @since 1.1.0
     */
    public static Map<String, Object> objectToMap(Object object) {
        if (object == null) {
            return Collections.emptyMap();
        }
        Map<String, Object> dataMap = new HashMap<>();
        try {
            Field[] fields = object.getClass().getDeclaredFields();
            for (Field field : fields) {
                if (null != field.getType().getClassLoader()) {
                    dataMap.put(field.getName(), objectToMap(
                            ReflectUtils.getPropertyValue(object,field.getName(), Object.class)));
                } else {
                    dataMap.put(field.getName(), ReflectUtils.getPropertyValue(object,field.getName(), Object.class));
                }
            }
        } catch (Exception ex) {
            throw new RuntimeException(ex.getMessage(), ex);
        }
        return dataMap;
    }


    /**
     * 将MAP转成 对象 非严格模式
     * 可以通过标记设置是否严格模式，如果严格模式，则表示所有属性和所有map的key必须都能对应， 如果有存在不对应情况则抛出异常。
     * 非严格模式下可以忽略此情况，只对匹配的进行设置和转换。默认情况是非严格模式。
     *
     * @param map  要转的Map对象
     * @param bean 要转成的对象
     * @return <T> T  返回对象
     * @throws
     * <AUTHOR>
     * @date 2023/7/25
     * @since 1.1.0
     */
    public static <T> T mapToClass(Map<String, Object> map, Class bean) {
        if (null == bean || null == map) {
            return null;
        }
        try {
            T result = (T) bean.getDeclaredConstructor().newInstance();
            Field[] fields = result.getClass().getDeclaredFields();
            for (Field field : fields) {
                int mod = field.getModifiers();
                if (Modifier.isStatic(mod) || Modifier.isFinal(mod)) {
                    continue;
                }
                if (map.containsKey(field.getName())) {
                    field.setAccessible(true);
                    if (null != field.getType().getClassLoader()) {
                        field.set(result, mapToClass((Map<String, Object>) map.get(field.getName()), field.getType()));
                    } else {
                        field.set(result, map.get(field.getName()));
                    }
                }
            }
            return result;
        } catch (Exception ex) {
            throw new RuntimeException(ex.getMessage(), ex);
        }
    }

    /**
     * 将MAP转成 对象 严格模式
     * 可以通过标记设置是否严格模式，如果严格模式，则表示所有属性和所有map的key必须都能对应， 如果有存在不对应情况则抛出异常。
     * 非严格模式下可以忽略此情况，只对匹配的进行设置和转换。默认情况是非严格模式。
     *
     * @param map  要转的Map对象
     * @param bean 要转成的对象
     * @return <T> T  返回对象
     * @throws
     * <AUTHOR>
     * @date 2023/7/25
     * @since 1.1.0
     */
    public static <T> T mapToClassStrict(Map<String, Object> map, Class bean) {
        if (null == bean || null == map) {
            return null;
        }
        try {
            T result = (T) bean.newInstance();
            Field[] fields = result.getClass().getDeclaredFields();
            List<String> beanKeysd = Arrays.stream(fields).map(Field::getName).collect(Collectors.toList());
            //通过分别获取bean 和map 对象的属性进行对比，如果发现有不一致则不通过
            List<String> beanKeys = getSortKeys(beanKeysd.stream().sorted());
            List<String> mpaKeys = getSortKeys(map.keySet().stream());
            if (!mpaKeys.equals(beanKeys)) {
                throw new RuntimeException("Inconsistent properties of the conversion object");
            }
            for (Field field : fields) {
                int mod = field.getModifiers();
                if (Modifier.isStatic(mod) || Modifier.isFinal(mod)) {
                    continue;
                }
                if (map.containsKey(field.getName())) {
                    field.setAccessible(true);
                    if (null != field.getType().getClassLoader()) {
                        field.set(result, mapToClassStrict((Map<String, Object>) map.get(field.getName()),
                                field.getType()));
                    } else {
                        field.set(result, map.get(field.getName()));
                    }
                }
            }
            return result;
        } catch (Exception ex) {
            throw new RuntimeException(ex.getMessage(), ex);
        }
    }

    /**
     * 获得对应的KEY
     *
     * @param orderStream key 的对象
     * @return List<String>  返回对象
     * @throws
     * <AUTHOR>
     * @date 2023/7/25
     * @since 1.1.0
     */
    private static List<String> getSortKeys(Stream orderStream) {
        List<String> result = new ArrayList<>();
        orderStream.sorted().forEach(o -> result.add(o.toString()));
        return result;
    }

    /**
     * map转为json
     * <p>在此方法基础上封装{@link com.snbc.bbpf.commons.jsons.JsonObjectMapper#marshall(Object)}</p>
     *
     * @param map 要转换的map
     * @return String 转换后的json串
     * @throws RuntimeException
     * <AUTHOR>
     * @date 2023/9/19
     * @since 1.2.0
     */
    public static String mapToJson(Map<String, ?> map) {
        if (ObjectEmptyCheck.isEmpty(map)) {
            return null;
        }
        return JsonObjectMapper.marshall(map);
    }

    /**
     * map转为json（按自定义特性转换）
     * <p>在此方法基础上封装{@link com.snbc.bbpf.commons.jsons.JsonObjectMapper#marshall(Object, List, List)}</p>
     *
     * @param map             要转换的map
     * @param enableFeatures  允许的特性集合，若不需设置则传null
     * @param disableFeatures 禁止的特性集合，若不需设置则传null
     * @return String 转换后的json串
     * @throws RuntimeException
     * <AUTHOR>
     * @date 2023/9/19
     * @since 1.2.0
     */
    public static String mapToJson(Map<String, ?> map, List<SerializationFeature> enableFeatures,
                                   List<SerializationFeature> disableFeatures) {
        if (ObjectEmptyCheck.isEmpty(map)) {
            return null;
        }
        return JsonObjectMapper.marshall(map, enableFeatures, disableFeatures);
    }
}
