/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.validations.validator;
import com.snbc.bbpf.commons.strings.StringEmptyCheck;
import com.snbc.bbpf.commons.validations.ValidException;

import java.util.regex.Pattern;

/**
 * 该类实现了 IStringValidator 接口，
 * 用于验证银行卡号的格式是否正确以及是否符合 Luhn 校验算法
 *
 * <AUTHOR>
 * @module 字符串校验模块
 * @date 2023/7/16
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class BankCardValidator implements IStringValidator {
    private static final Pattern BANK_CARD_PATTERN = Pattern.compile("^[0-9]+$");
    //Luhn 校验算法的校验和
    private static final int LUNH_CHECKSUM = 10;
    //Luhn 校验算法的校验数
    private static final int LUNH_CHECKNUM = 9;
    //Luhn 校验算法的乘数。
    private static final int MULTIPLIER = 2;

    /**
     * 验证银行卡号的格式是否正确以及是否符合 Luhn 校验算法。
     * 如果银行卡号为空或格式不正确，则抛出 ValidateException 异常。
     * 具体的银行卡号格式验证逻辑是：从银行卡号的末尾开始，
     * 将每一位数字与一个乘数相乘，乘数为 2，如果乘积大于 9，
     * 则将乘积的个位数和十位数相加。将所有乘积的结果相加，
     * 如果相加的结果不是 Luhn 校验算法的校验和的倍数，
     * 则银行卡号不符合 Luhn 校验算法，抛出 ValidateException 异常。
     * 如果传入了 message 参数，则使用该参数作为异常信息，否则使用默认的异常信息。
     *
     * @param bankCard 银行卡号
     * @param message 异常消息
     * @throws ValidException 异常
     * <AUTHOR>
     * @date 2023/7/16
     * @since 1.1.0
     **/
    @Override
    public void validate(String bankCard, String message) throws IllegalArgumentException {
        if (bankCard == null || !BANK_CARD_PATTERN.matcher(bankCard).matches()) {
            throw new IllegalArgumentException("Bank card number is empty or formatted incorrectly");
        }
        // 验证银行卡号格式的具体逻辑
        int sum = 0;
        boolean isEven = false;
        for (int i = bankCard.length() - 1; i >= 0; i--) {
            int digit = bankCard.charAt(i) - '0';
            if (isEven) {
                digit *= MULTIPLIER;
                if (digit > LUNH_CHECKNUM) {
                    digit -= LUNH_CHECKNUM;
                }
            }
            sum += digit;
            isEven = !isEven;
        }
        if (sum % LUNH_CHECKSUM != 0) {
            throw new IllegalArgumentException(!StringEmptyCheck.isEmpty(message)? message :
                    "The bank card number does not match the luhn verification algorithm");
        }
    }
}