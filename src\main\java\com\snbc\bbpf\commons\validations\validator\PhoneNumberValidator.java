/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.validations.validator;

import com.snbc.bbpf.commons.strings.StringEmptyCheck;
import com.snbc.bbpf.commons.validations.ValidException;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

/**
 * 验证手机号码格式
 *
 * <AUTHOR>
 * @module 字符串校验模块
 * @date 2023/7/16
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class PhoneNumberValidator implements IStringValidator {
    public static final Pattern PHONE_PATTERN = Pattern.compile("^(\\+\\d{1,3})?(13[0-9]|14[5-9]|15[0-3,5-9]|16[6]|17[0-8]|18[0-9]|19[1,8,9])\\d{8}$");

    /**
     * 验证手机号码格式
     *
     * @param phone   手机号码字符串
     * @param message 错误消息
     * @throws ValidException 如果手机号码为空或格式错误
     * <AUTHOR>
     * @date 2023/7/16
     * @since 1.1.0
     */
    @Override
    public void validate(String phone, String message) throws IllegalArgumentException {
        if (StringUtils.isEmpty(phone)) {
            throw new IllegalArgumentException("Mobile number is empty or null");
        }
        // 验证手机号码地址格式
        if (!PHONE_PATTERN.matcher(phone).matches()) {
            throw new IllegalArgumentException(!StringEmptyCheck.isEmpty(message)? message : "The phone number format is incorrect");
        }
    }
}
