/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.strings;

import org.apache.commons.lang3.StringUtils;

/**
 * 字符串补全处理
 *
 * <p>可对字符串进行补全操作</p>
 * <p>可以根据需要填充的字符，补充的方式（左侧，右侧，左侧和右侧都填充），填充的长度（可分左右侧），填充后的长度进行组合条件填充操作</p>
 * <p>填充后的长度要大于字符串的长度</p>
 * <p>注意字符串的编码格式</p>
 * <p>需要完成此需求下所有API</p>
 *
 * <AUTHOR>
 * @module
 * @date 2023/5/8 13:08
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class StringPadUtils {

    private StringPadUtils() {
        throw new IllegalStateException("Utility class");
    }

    /*
     * 左填充至总长度
     *
     *<pre>
     * leftPadTotalLength(null, *, *)      = null
     * leftPadTotalLength("", 3, "z")      = "zzz"
     * leftPadTotalLength("bat", 3, "yz")  = "bat"
     * leftPadTotalLength("bat", 5, "yz")  = "yzbat"
     * leftPadTotalLength("bat", 8, "yz")  = "yzyzybat"
     * leftPadTotalLength("bat", 1, "yz")  = "bat"
     * leftPadTotalLength("bat", -1, "yz") = "bat"
     * leftPadTotalLength("bat", 5, null)  = "  bat"
     * leftPadTotalLength("bat", 5, "")    = "  bat"
     *</pre>
     *
     * @param str 原始字符串
     * @param size 字符串总长度
     * @param padStr 待填充字符串
     * @return String
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/8
     */
    public static String leftPadTotalLength(final String str, final int size, String padStr) {

        return StringUtils.leftPad(str, size, padStr);
    }

    /*
     * 右填充至总长度
     *
     * <pre>
     * rightPadTotalLength(null, *, *)      = null
     * rightPadTotalLength("", 3, "z")      = "zzz"
     * rightPadTotalLength("bat", 3, "yz")  = "bat"
     * rightPadTotalLength("bat", 5, "yz")  = "batyz"
     * rightPadTotalLength("bat", 8, "yz")  = "batyzyzy"
     * rightPadTotalLength("bat", 1, "yz")  = "bat"
     * rightPadTotalLength("bat", -1, "yz") = "bat"
     * rightPadTotalLength("bat", 5, null)  = "bat  "
     * rightPadTotalLength("bat", 5, "")    = "bat  "
     * </pre>
     *
     * @param str 原始字符串
     * @param size 字符串总长度
     * @param padStr 待填充字符串
     * @return String
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/8
     */
    public static String rightPadTotalLength(final String str, final int size, String padStr) {

        return StringUtils.rightPad(str, size, padStr);
    }

    /*
     * 左右均填充至总长度
     *
     * <pre>
     * centerPadTotalLength(null, *, *)     = null
     * centerPadTotalLength("", 4, " ")     = "    "
     * centerPadTotalLength("ab", -1, " ")  = "ab"
     * centerPadTotalLength("ab", 4, " ")   = " ab "
     * centerPadTotalLength("abcd", 2, " ") = "abcd"
     * centerPadTotalLength("a", 4, " ")    = " a  "
     * centerPadTotalLength("a", 4, "yz")   = "yayz"
     * centerPadTotalLength("abc", 7, null) = "  abc  "
     * centerPadTotalLength("abc", 7, "")   = "  abc  "
     * </pre>
     *
     * @param str 原始字符串
     * @param size 字符串总长度
     * @param padStr 待填充字符串
     * @return String
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/8
     */
    public static String centerPadTotalLength(final String str, final int size, String padStr) {

        return StringUtils.center(str, size, padStr);
    }

    /*
     * 按需要增加的长度向左填充
     *
     * <pre>
     * leftPadAddLength(null, 10, "")      = null
     * leftPadAddLength("sr", 10, null)      = "          sr"
     * leftPadAddLength("sr", 10, "")  = "          sr"
     * leftPadAddLength("asd", 9, "asd")  = "asdasdasdasd"
     * leftPadAddLength("asd", 0, "asd")  = "asd""
     * leftPadAddLength("-0", -4, "ad")  = "-0"
     * leftPadAddLength("中文", 5, "汉字") = "汉字汉字汉中文"
     * leftPadAddLength("-0", 7, "1234567890")  = "1234567-0"
     * </pre>
     *
     * @param str 原始字符串
     * @param pads 字符串增加的长度
     * @param padStr 待填充字符串
     * @return String
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/8
     */
    public static String leftPadAddLength(final String str, final int pads, String padStr) {

        if (str == null) {
            return null;
        }
        if (StringEmptyCheck.isEmpty(padStr)) {
            padStr = " ";
        }
        // returns original String when possible
        if (pads <= 0) {
            return str;
        }

        final int padLen = padStr.length();
        if (pads == padLen) {
            return padStr.concat(str);
        } else if (pads < padLen) {
            return padStr.substring(0, pads).concat(str);
        } else {
            final char[] padding = new char[pads];
            final char[] padChars = padStr.toCharArray();
            for (int i = 0; i < pads; i++) {
                padding[i] = padChars[i % padLen];
            }
            return new String(padding).concat(str);
        }
    }

    /*
     * 按需要增加的长度向右填充
     *
     * <pre>
     * rightPadAddLength(null, 10, "")      = null
     * rightPadAddLength("sr", 10, null)      = "sr          "
     * rightPadAddLength("sr", 1, " ")  = "sr "
     * rightPadAddLength("asd", 9, "asd")  = "asdasdasdasd"
     * rightPadAddLength("中文", 13, "asd")  = "中文asdasdasdasda"
     * rightPadAddLength("中文", 5, "汉字")  = "中文汉字汉字汉"
     * rightPadAddLength("asd", 0, "asd") = "asd"
     * rightPadAddLength("-0", -4, "ad")  = "-0"
     * rightPadAddLength("-0", 7, "1234567890")    = "-01234567"
     * </pre>
     *
     * @param str 原始字符串
     * @param pads 字符串增加的长度
     * @param padStr 待填充字符串
     * @return String
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/18
     */
    public static String rightPadAddLength(final String str, final int pads, String padStr) {

        if (str == null) {
            return null;
        }
        if (StringEmptyCheck.isEmpty(padStr)) {
            padStr = " ";
        }
        if (pads <= 0) {
            return str;
        }

        final int padLen = padStr.length();
        if (pads == padLen) {
            return str.concat(padStr);
        } else if (pads < padLen) {
            return str.concat(padStr.substring(0, pads));
        } else {
            final char[] padding = new char[pads];
            final char[] padChars = padStr.toCharArray();
            for (int i = 0; i < pads; i++) {
                padding[i] = padChars[i % padLen];
            }
            return str.concat(new String(padding));
        }
    }

    /*
     * 左侧和右侧都按增加的长度填充
     *
     * <pre>
     * leftAndRightPadAddLength(null, 10, "", 10, "")      = null
     * leftAndRightPadAddLength("sr", 0, null, 10, null)      = "sr          "
     * leftAndRightPadAddLength("sr", 1, "", 1, "")  = " sr "
     * leftAndRightPadAddLength("sr", 2, " ", 3, "1")  = "  sr111"
     * leftAndRightPadAddLength("asd", 9, "asd", 4, "we")  = "asdasdasdasdwewe"
     * leftAndRightPadAddLength("中文", 13, "asd", 13, "we")  = "asdasdasdasda中文wewewewewewew""
     * leftAndRightPadAddLength("中文", 5, "汉字", 5, "汉字") = "汉字汉字汉中文汉字汉字汉"
     * leftAndRightPadAddLength("asd", 0, "asda", 0, "asd")  = "asd"
     * leftAndRightPadAddLength("-0", -4, "ad", 5, "$")    = "-0$$$$$"
     * leftAndRightPadAddLength("-0", 7, "1234567890", 7, "9876543210")    = "1234567-09876543"
     * </pre>
     *
     * @param str 原始字符串
     * @param leftPads 左侧字符串增加的长度
     * @param leftPadStr 左侧待填充字符串
     * @param rightPads 右侧字符串增加的长度
     * @param rightPadStr 右侧待填充字符串
     * @return String
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/5/20
     */
    public static String leftAndRightPadAddLength(final String str, final int leftPads, String leftPadStr, final int rightPads, String rightPadStr) {

        return rightPadAddLength(leftPadAddLength(str, leftPads, leftPadStr), rightPads, rightPadStr);
    }
}
