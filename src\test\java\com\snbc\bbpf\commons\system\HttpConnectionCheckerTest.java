package com.snbc.bbpf.commons.system;

import org.apache.http.StatusLine;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.impl.client.CloseableHttpClient;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

public class HttpConnectionCheckerTest {

    @Mock
    private CloseableHttpClient mockHttpClient;

    @Mock
    private CloseableHttpResponse mockResponse;

    @Mock
    private StatusLine mockStatusLine;

    private ExecutorService executorService;
    private HttpConnectionChecker checker;

    @BeforeEach
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        executorService = Executors.newFixedThreadPool(2);
        checker = new HttpConnectionChecker(executorService, mockHttpClient);

        // 设置模拟响应
        when(mockResponse.getStatusLine()).thenReturn(mockStatusLine);
        when(mockHttpClient.execute(any(HttpUriRequest.class))).thenReturn(mockResponse);
    }

    @AfterEach
    public void tearDown() throws Exception {
        if (checker != null) {
            checker.close();
        }
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdownNow();
        }
    }

    @Test
    @DisplayName("检查连接成功时返回true")
    @Tags({
            @Tag("@id:112"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2025/5/12")
    })
    public void testCheckConnection_Success() throws IOException {
        // 设置返回200状态码
        when(mockStatusLine.getStatusCode()).thenReturn(200);

        boolean result = checker.checkConnection("https://www.example.com");

        assertTrue(result, "应该返回true表示连接成功");
        verify(mockHttpClient).execute(any(HttpUriRequest.class));
    }

    @Test
    @DisplayName("检查连接失败时返回false")
    @Tags({
            @Tag("@id:112"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2025/5/12")
    })
    public void testCheckConnection_Failure() throws IOException {
        // 设置返回404状态码
        when(mockStatusLine.getStatusCode()).thenReturn(404);

        boolean result = checker.checkConnection("https://www.example.com/notfound");

        assertFalse(result, "应该返回false表示连接失败");
        verify(mockHttpClient).execute(any(HttpUriRequest.class));
    }

    @Test
    @DisplayName("连接异常时返回false")
    @Tags({
            @Tag("@id:112"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2025/5/12")
    })
    public void testCheckConnection_Exception() throws IOException {
        // 模拟抛出IOException
        when(mockHttpClient.execute(any(HttpUriRequest.class))).thenThrow(new IOException("模拟网络错误"));

        boolean result = checker.checkConnection("https://invalid.example.com");

        assertFalse(result, "发生异常时应该返回false");
        verify(mockHttpClient).execute(any(HttpUriRequest.class));
    }

    @Test
    @DisplayName("URL为null时返回false")
    @Tags({
            @Tag("@id:112"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2025/5/12")
    })
    public void testCheckConnection_NullUrl() {
        boolean result = checker.checkConnection(null);
        assertFalse(result, "空URL应该返回false");
        //verify(mockHttpClient, never()).execute(any(HttpUriRequest.class));
    }

    @Test
    @DisplayName("URL为空字符串时返回false")
    @Tags({
            @Tag("@id:112"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2025/5/12")
    })
    public void testCheckConnection_EmptyUrl() {
        boolean result = checker.checkConnection("");
        assertFalse(result, "空URL应该返回false");
    }

    @Test
    @DisplayName("URL列表中找到可访问URL时返回true")
    @Tags({
            @Tag("@id:112"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2025/5/12")
    })
    public void testCheckConnections_FindsAccessibleUrl() throws IOException {
        // 设置第二个URL是可访问的
        when(mockStatusLine.getStatusCode())
                .thenReturn(404) // 第一个URL失败
                .thenReturn(200); // 第二个URL成功

        List<String> urls = Arrays.asList(
                "https://www.example1.com", // 不可访问
                "https://www.example2.com", // 可访问
                "https://www.example3.com" // 不应该被检查
        );

        boolean result = checker.checkConnections(urls);

        assertTrue(result, "应该返回true表示找到了可访问的URL");

        // 验证execute只被调用了2次，找到可访问URL后停止检查
        verify(mockHttpClient, times(2)).execute(any(HttpUriRequest.class));
    }

    @Test
    @DisplayName("URL列表中所有URL都不可访问时返回false")
    @Tags({
            @Tag("@id:112"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2025/5/12")
    })
    public void testCheckConnections_AllNotAccessible() throws IOException {
        // 设置所有URL都不可访问
        when(mockStatusLine.getStatusCode())
                .thenReturn(404) // 第一个URL失败
                .thenReturn(500) // 第二个URL失败
                .thenReturn(403); // 第三个URL失败

        List<String> urls = Arrays.asList(
                "https://www.example1.com",
                "https://www.example2.com",
                "https://www.example3.com");

        boolean result = checker.checkConnections(urls);

        assertFalse(result, "应该返回false表示没有可访问的URL");

        // 验证execute被调用了3次，检查了所有URL
        verify(mockHttpClient, times(3)).execute(any(HttpUriRequest.class));
    }

    @Test
    @DisplayName("URL列表中第一个URL可访问时返回true")
    @Tags({
            @Tag("@id:112"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2025/5/12")
    })
    public void testCheckConnections_FirstAccessible() throws IOException {
        // 设置第一个URL就是可访问的
        when(mockStatusLine.getStatusCode()).thenReturn(200);

        List<String> urls = Arrays.asList(
                "https://www.example1.com", // 可访问
                "https://www.example2.com", // 不应该被检查
                "https://www.example3.com" // 不应该被检查
        );

        boolean result = checker.checkConnections(urls);

        assertTrue(result, "应该返回true表示找到了可访问的URL");

        // 验证execute只被调用了1次，因为第一个URL就是可访问的
        verify(mockHttpClient, times(1)).execute(any(HttpUriRequest.class));
    }

    @Test
    @DisplayName("空URL列表时返回false")
    @Tags({
            @Tag("@id:112"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2025/5/12")
    })
    public void testCheckConnections_EmptyList() {
        boolean result = checker.checkConnections(Collections.<String>emptyList());

        assertFalse(result, "空列表应该返回false");
    }

    @Test
    @DisplayName("URL列表为null时返回false")
    @Tags({
            @Tag("@id:112"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2025/5/12")
    })
    public void testCheckConnections_NullList() {
        boolean result = checker.checkConnections(null);

        assertFalse(result, "null列表应该返回false");
    }

    @Test
    @DisplayName("关闭后再检查连接应抛出IllegalStateException异常")
    @Tags({
            @Tag("@id:112"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2025/5/12")
    })
    public void testCheckConnection_AfterClose() {
        checker.close();
        assertThrows(IllegalStateException.class, () -> 
            checker.checkConnection("https://www.example.com")
        );
    }

    @Test
    @DisplayName("关闭后再检查连接列表应抛出IllegalStateException异常")
    @Tags({
            @Tag("@id:112"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2025/5/12")
    })
    public void testCheckConnections_AfterClose() {
        checker.close();
        assertThrows(IllegalStateException.class, () -> 
            checker.checkConnections(Arrays.asList("https://www.example.com"))
        );
    }

    @Test
    @DisplayName("多次调用close()不应抛出异常")
    @Tags({
            @Tag("@id:112"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2025/5/12")
    })
    public void testClose_MultipleTimes() {
        // 多次调用close()应该不会抛出异常
        checker.close();
        checker.close();
    }

    @Test
    @DisplayName("构造函数传入null的ExecutorService应抛出IllegalArgumentException异常")
    @Tags({
            @Tag("@id:112"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2025/5/12")
    })
    public void testConstructor_NullExecutorService() {
        assertThrows(IllegalArgumentException.class, () -> 
            new HttpConnectionChecker(null, mockHttpClient)
        );
    }

    @Test
    @DisplayName("构造函数传入null的HttpClient应抛出IllegalArgumentException异常")
    @Tags({
            @Tag("@id:112"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2025/5/12")
    })
    public void testConstructor_NullHttpClient() {
        assertThrows(IllegalArgumentException.class, () -> 
            new HttpConnectionChecker(executorService, null)
        );
    }

    @Test
    @DisplayName("请求包含Accept-Encoding头")
    @Tags({
            @Tag("@id:112"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2025/5/12")
    })
    public void testRequestContainsAcceptEncodingHeader() throws IOException {
        // 配置模拟的HttpUriRequest来捕获请求头
        when(mockStatusLine.getStatusCode()).thenReturn(200);

        checker.checkConnection("https://www.example.com");

        // 验证执行了请求
        verify(mockHttpClient).execute(any(HttpUriRequest.class));
    }
}