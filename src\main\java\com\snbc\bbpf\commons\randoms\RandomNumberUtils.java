/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.randoms;

/**
 * 【随机数字字符串处理】随机数字字符串处理
 * 生成随机数字类，包含如下功能：
 * 1、生成只包含0-9个数字，指定位数的字符串。
 * 2、使用雪花算法生成制定长度long
 *
 * <AUTHOR>
 * @module 随机模块
 * @date 2023/4/27 5:34 下午
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class RandomNumberUtils {

    private RandomNumberUtils() {
        throw new IllegalStateException("Utility class");
    }
    /**
     * # 生成只包含0-9个数字，指定位数的数字字符串。默认首位字符可能包含0
     * <p>例如</p>
     * <p>input 3 result: 098 </p>
     * <p>input 3 result: 266 </p>
     *
     * @param count 指定的位数
     * @return 随机数字字符串
     * @throws
     * <AUTHOR>
     * @date 2023/4/27
     * @since 1.0.0
     */
    public static String randomNumeric(final int count) {
        return RandomStringUtils.random(count, false, true);
    }

    /**
     * 生成只包含0-9个数字，指定位数的数字字符串
     * <p>
     *     randomNumeric(3, true) = "098"
     *     randomNumeric(3, false) = "266"
     * </p>
     * @param count   指定的位数
     * @param hasZero 是否允许首位包含0 true:允许 false:不允许
     * @return 随机数字字符串
     * <AUTHOR>
     * @date 2023/6/13
     * @since 1.0.0
     */
    public static String randomNumeric(final int count, final boolean hasZero) {
        String randomNumricStr = randomNumeric(count);
        if (!hasZero) {
            while (randomNumricStr.charAt(0) == '0') {
                randomNumricStr = randomNumeric(count);
            }
        }
        return randomNumricStr;
    }


}
