package com.snbc.bbpf.commons.validations.validator;

import com.snbc.bbpf.commons.validations.annotation.ValidStringType;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;

import static org.junit.jupiter.api.Assertions.*;
class StringValidationStrategyFactoryTest {
    @Test
    @DisplayName("根据策略名称获取对应的验证器")
    @Tags({
            @Tag("@id:36"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/6/28")
    })
    void testGetValidator() {
        IStringValidator validator1 = StringValidationStrategyFactory.getValidator(ValidStringType.IDENTITY_CARD);
        assertTrue(validator1 instanceof IdentityCardValidator);
         IStringValidator validator2 = StringValidationStrategyFactory.getValidator(ValidStringType.EMAIL);
        assertTrue(validator2 instanceof EmailValidator);
         IStringValidator validator3 = StringValidationStrategyFactory.getValidator(ValidStringType.CHINESE_CHARACTER);
        assertTrue(validator3 instanceof ChineseCharacterValidator);
         IStringValidator validator4 = StringValidationStrategyFactory.getValidator(ValidStringType.BANK_CARD);
        assertTrue(validator4 instanceof BankCardValidator);
         IStringValidator validator5 = StringValidationStrategyFactory.getValidator(ValidStringType.IP_ADDRESS);
        assertTrue(validator5 instanceof IpAddressValidator);
         IStringValidator validator6 = StringValidationStrategyFactory.getValidator(ValidStringType.PHONE_NUMBER);
        assertTrue(validator6 instanceof PhoneNumberValidator);
         IStringValidator validator7 = StringValidationStrategyFactory.getValidator(ValidStringType.URL);
        assertTrue(validator7 instanceof UrlValidator);
    }
}
