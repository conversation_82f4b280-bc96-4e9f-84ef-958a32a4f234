/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.files;

/**
 * 自定义枚举类，表示文件类型。
 *
 * <AUTHOR>
 * @module 文件
 * @date 2023/4/29 9:58
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 * @since 1.2.0
 */
public enum FileType {
    // 图片文件类型
    // JPEG
    JPG("jpg", "FFD8FF"),
    // BMP
    BMP("bmp", "424D"),
    // PNG
    PNG("png", "89504E47"),
    // GIF
    GIF("gif", "47494638"),
    // SVG
    SVG("svg", "3C737667"),
    // 音频文件类型
    // WAV
    WAV("wav", "52494646"),
    // MP3
    MP3("mp3", "494433"),
    // WMA
    WMA("wma", "3026B2758E66CF11"),
    // AMR
    AMR("amr", "2321414D52"),
    // 视频文件类型
    // MP4
    MP4("mp4", "00000020667479706973"),
    // AVI
    AVI("avi", "52494646"),
    // 文档文件类型
    // MS Word
    DOC("doc", Constants.DOC_CODE),
    // MS Word
    DOCX("docx", Constants.DOCX_CODE),
    // MS Excel
    XLS("xls", Constants.DOC_CODE),
    // MS Excel
    XLSX("xlsx", Constants.DOCX_CODE),
    // MS PowerPoint
    PPT("ppt", Constants.DOC_CODE),
    // MS PowerPoint
    PPTX("pptx", Constants.DOCX_CODE),
    // PDF
    PDF("pdf", "25504446"),
    // Text
    TXT("txt", "2f557365"),
    // 压缩文件类型
    // ZIP
    ZIP("zip", Constants.DOCX_CODE),
    // RAR
    RAR("rar", "52617221"),
    // GZIP
    GZ("gz", "1F8B08"),
    // 镜像文件类型
    // ISO Image
    ISO("iso", "0000000000"),
    // 可执行文件类型
    // Executable
    EXE("exe", "4D5A"),
    // Batch
    BAT("bat", "4065636820"),
    // Command
    CMD("cmd", "4049"),
    // 安装文件类型
    // Android APK
    APK("apk", Constants.DOCX_CODE);

    private final String extension;
    private final String hexHeader;

    /**
     * 构造方法。
     *
     * @param extension 文件扩展名
     * @param hexHeader 对应的文件头十六进制字符串
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/4/29 9:58
     */
    FileType(String extension, String hexHeader) {
        this.extension = extension;
        this.hexHeader = hexHeader;
    }

    /**
     * 获取文件扩展名。
     *
     * @return 文件扩展名
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/4/29 9:58
     */
    public String getExtension() {
        return extension;
    }

    /**
     * 获取文件头的十六进制字符串。
     *
     * @return 文件头的十六进制字符串
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/4/29 9:58
     */
    public String getHexHeader() {
        return hexHeader;
    }

    private static class Constants {
        public static final String DOC_CODE = "D0CF11E0A1B11AE1";
        public static final String DOCX_CODE = "504B0304";
    }
}
