# JSON处理 (com.snbc.bbpf.commons.jsons)

## 类概览 

| 类名 | 功能描述 |
|------|----------|
| JsonObjectMapper | 提供JSON字符串和对象之间的转换功能，支持各种日期格式和空属性 |
| JSONPathUtils | 通过JSONPath查询JSON中符合条件的数据 |
| JsonXmlMapper | 提供JSON和XML之间的转换功能 |

## JsonObjectMapper - JSON对象转换工具类

### 默认特性

JsonObjectMapper基于Jackson库实现，默认配置了以下特性:

1. **注册了JavaTimeModule** - 支持Java 8日期时间类型(如LocalDate、LocalDateTime)的序列化和反序列化
2. **FAIL_ON_UNKNOWN_PROPERTIES设为false** - 反序列化时，JSON中有未知属性不会导致失败

这些默认特性使得JsonObjectMapper能够满足大多数常见场景的需求，无需额外配置。

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| marshall(Object object) | 将对象转化为JSON | object: 要转化为JSON的对象 | String: JSON字符串 |
| marshall(Object object, List<SerializationFeature> enableFeatures, List<SerializationFeature> disableFeatures) | 将对象转化为JSON，支持自定义序列化特性 | object: 要转化为JSON的对象<br>enableFeatures: 要启用的特性<br>disableFeatures: 要禁用的特性 | String: JSON字符串 |
| unmarshall(String jsonStr, Class<T> clazz) | 将JSON字符串转化为对象 | jsonStr: 要转化的JSON文本<br>clazz: 目标类 | <T>: 范型目标对象 |
| unmarshallWithFeatures(String jsonStr, Class<T> clazz, List<DeserializationFeature> enableFeatures, List<DeserializationFeature> disableFeatures) | 将JSON字符串转化为对象，支持自定义反序列化特性 | jsonStr: 要转化的JSON文本<br>clazz: 目标类<br>enableFeatures: 要启用的特性<br>disableFeatures: 要禁用的特性 | <T>: 范型目标对象 |
| unmarshall(String jsonStr, TypeReference<T> typeReference) | 将JSON字符串转化为复杂对象，适用于集合等 | jsonStr: 要转化的JSON文本<br>typeReference: 定义的目标对象泛型 | <T>: 范型目标对象 |
| unmarshallWithFeatures(String jsonStr, TypeReference<T> typeReference, List<DeserializationFeature> enableFeatures, List<DeserializationFeature> disableFeatures) | 将JSON字符串转化为复杂对象，支持自定义反序列化特性 | jsonStr: 要转化的JSON文本<br>typeReference: 定义的目标对象泛型<br>enableFeatures: 要启用的特性<br>disableFeatures: 要禁用的特性 | <T>: 范型目标对象 |

### 使用注意事项

1. **空值处理**
   - `marshall`方法不允许传入null值，否则会抛出NullPointerException异常
   - 默认情况下，对象属性为null时将在JSON中表示为null值

2. **日期时间处理**
   - 默认支持标准格式的日期时间(如ISO 8601)
   - 处理非标准日期格式时，需要使用自定义反序列化器，仅使用特性调整可能不足以解决所有日期格式问题

3. **集合类型处理**
   - 处理List、Map等集合类型时，需要使用TypeReference，如`new TypeReference<List<User>>() {}`
   - 嵌套泛型时要特别注意，例如`Map<String, List<User>>`

4. **枚举值处理**
   - 默认情况下，JSON中的未知枚举值会导致异常
   - 可以通过启用READ_UNKNOWN_ENUM_VALUES_AS_NULL特性解决

5. **性能考虑**
   - 每次调用unmarshallWithFeatures方法会创建新的ObjectMapper实例，对性能敏感场景应谨慎使用
   - 处理大型对象或集合时，注意内存使用

6. **异常处理**
   - 大多数操作失败时会抛出RuntimeException或其子类
   - 对于生产环境，建议包装所有JSON操作在try-catch块中

7. **循环引用**
   - 默认情况下，对象之间的循环引用会导致异常
   - 可以通过启用WRITE_SELF_REFERENCES_AS_NULL特性处理循环引用

### unmarshallWithFeatures方法详解

`unmarshallWithFeatures`方法允许创建一个具有特定反序列化特性的ObjectMapper实例，用于处理特殊场景下的JSON转换需求。每次调用此方法时，会创建一个新的ObjectMapper实例，并根据传入的参数设置特性，而不是使用类中的默认实例。

这个方法特别适用于以下场景:

1. **处理特殊格式的JSON** - 当JSON数据格式与默认期望不同时
2. **处理日期时间格式** - 需要自定义日期时间解析方式时
3. **处理枚举值** - 需要特殊的枚举处理时
4. **性能调优** - 针对特定场景优化序列化/反序列化性能

Jackson库提供了多种DeserializationFeature，以下是一些常用的特性：

- **ACCEPT_SINGLE_VALUE_AS_ARRAY** - 允许将单个值作为数组处理
- **ACCEPT_EMPTY_STRING_AS_NULL_OBJECT** - 将空字符串视为null对象
- **FAIL_ON_UNKNOWN_PROPERTIES** - 遇到未知属性时是否失败(默认为false)
- **FAIL_ON_NULL_FOR_PRIMITIVES** - 原始类型字段遇到null值时是否失败
- **READ_UNKNOWN_ENUM_VALUES_AS_NULL** - 将未知的枚举值读取为null
- **USE_BIG_DECIMAL_FOR_FLOATS** - 使用BigDecimal表示浮点数

更多特性请参考[Jackson官方文档](https://www.javadoc.io/doc/com.fasterxml.jackson.core/jackson-databind/latest/com/fasterxml/jackson/databind/DeserializationFeature.html)，也可以参考文章末尾的Jackson序列化和反序列化的特性完整列表

### 使用示例

```java
// 创建一个测试对象
User user = new User();
user.setId(1);
user.setName("张三");
user.setAge(30);
user.setBirthday(LocalDate.of(1993, 5, 15));

// 对象转JSON
String json = JsonObjectMapper.marshall(user);
System.out.println("User JSON: " + json);
// 输出类似: User JSON: {"id":1,"name":"张三","age":30,"birthday":"1993-05-15"}

// 启用美化输出的序列化特性
List<SerializationFeature> enableFeatures = new ArrayList<>();
enableFeatures.add(SerializationFeature.INDENT_OUTPUT);
String prettyJson = JsonObjectMapper.marshall(user, enableFeatures, null);
System.out.println("Pretty JSON:");
System.out.println(prettyJson);
// 输出美化后的JSON格式

// JSON转对象
String userJson = "{\"id\":2,\"name\":\"李四\",\"age\":25,\"birthday\":\"1998-10-20\"}";
User parsedUser = JsonObjectMapper.unmarshall(userJson, User.class);
System.out.println("Parsed User: " + parsedUser.getName() + ", Age: " + parsedUser.getAge());
// 输出: Parsed User: 李四, Age: 25

// 使用自定义特性处理JSON
String jsonWithExtra = "{\"id\":3,\"name\":\"王五\",\"age\":40,\"extra\":\"额外字段\",\"birthday\":\"2000-01-01\"}";

// 默认情况下处理未知字段"extra"
User user1 = JsonObjectMapper.unmarshall(jsonWithExtra, User.class);
System.out.println("Default handling: " + user1.getName()); // 正常工作，因为默认FAIL_ON_UNKNOWN_PROPERTIES为false

// 使用自定义特性 - 启用FAIL_ON_UNKNOWN_PROPERTIES
List<DeserializationFeature> enableList = new ArrayList<>();
enableList.add(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
try {
    // 这将抛出异常，因为我们启用了FAIL_ON_UNKNOWN_PROPERTIES
    JsonObjectMapper.unmarshallWithFeatures(jsonWithExtra, User.class, enableList, null);
} catch (RuntimeException e) {
    System.out.println("Expected error: " + e.getMessage());
    // 输出类似: Expected error: Unrecognized field "extra"...
}

// 处理日期格式问题
String oddDateFormat = "{\"id\":4,\"name\":\"赵六\",\"age\":35,\"birthday\":\"01/01/2022\"}";

// 默认情况下可能会解析失败
try {
    JsonObjectMapper.unmarshall(oddDateFormat, User.class);
} catch (RuntimeException e) {
    System.out.println("Date parse error: " + e.getMessage());
}

// 使用自定义特性允许从字符串创建日期
List<DeserializationFeature> dateFeatures = new ArrayList<>();
dateFeatures.add(DeserializationFeature.READ_DATE_TIMESTAMPS_AS_NANOSECONDS);

List<DeserializationFeature> disableFeatures = new ArrayList<>();
disableFeatures.add(DeserializationFeature.ADJUST_DATES_TO_CONTEXT_TIME_ZONE);

// 注意：仅调整特性可能不足以处理所有自定义日期格式，可能还需要自定义日期反序列化器

// 处理集合类型
String listJson = "[{\"id\":1,\"name\":\"张三\"},{\"id\":2,\"name\":\"李四\"}]";
List<User> userList = JsonObjectMapper.unmarshall(listJson, new TypeReference<List<User>>() {});
System.out.println("User count: " + userList.size());
userList.forEach(u -> System.out.println("User ID: " + u.getId() + ", Name: " + u.getName()));
// 输出: 
// User count: 2
// User ID: 1, Name: 张三
// User ID: 2, Name: 李四

// 处理Map类型
String mapJson = "{\"1\":{\"id\":1,\"name\":\"张三\"},\"2\":{\"id\":2,\"name\":\"李四\"}}";
Map<String, User> userMap = JsonObjectMapper.unmarshall(mapJson, new TypeReference<Map<String, User>>() {});
System.out.println("Map User 1: " + userMap.get("1").getName());
// 输出: Map User 1: 张三

// 异常情况示例
try {
    // 对象为null时抛出异常
    JsonObjectMapper.marshall(null);
} catch (NullPointerException e) {
    System.out.println("Exception: " + e.getMessage());  // 输出: Exception: object is not allowed null
}
```

### unmarshallWithFeatures详细使用示例

以下是unmarshallWithFeatures方法的详细使用示例，展示了如何在不同场景下使用自定义反序列化特性：

```java
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.snbc.bbpf.commons.jsons.JsonObjectMapper;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class UnmarshallWithFeaturesDemo {
    
    static class Product {
        private Integer id;
        private String name;
        private Double price;
        private LocalDate createDate;
        private String[] tags;
        private Status status;
        
        // getters and setters...
        
        @Override
        public String toString() {
            return "Product{" +
                    "id=" + id +
                    ", name='" + name + '\'' +
                    ", price=" + price +
                    ", createDate=" + createDate +
                    ", status=" + status +
                    '}';
        }
    }
    
    enum Status {
        ACTIVE, INACTIVE, DELETED
    }
    
    public static void main(String[] args) {
        // 示例1: 处理包含未知属性的JSON
        handleUnknownProperties();
        
        // 示例2: 处理类型不匹配的情况
        handleTypeConversion();
        
        // 示例3: 处理枚举值
        handleEnumValues();
        
        // 示例4: 处理单值和数组转换
        handleSingleValueAsArray();
        
        // 示例5: 复杂对象与特性组合
        handleComplexObjectWithFeatures();
    }
    
    private static void handleUnknownProperties() {
        System.out.println("\n=== 示例1: 处理包含未知属性的JSON ===");
        
        // 含有未知属性"extraField"的JSON
        String json = "{\"id\":1,\"name\":\"笔记本电脑\",\"price\":5999.99,\"createDate\":\"2023-01-15\",\"extraField\":\"额外数据\"}";
        
        // 默认情况下，JsonObjectMapper已配置为忽略未知属性
        Product product = JsonObjectMapper.unmarshall(json, Product.class);
        System.out.println("默认处理结果: " + product);
        
        // 启用FAIL_ON_UNKNOWN_PROPERTIES特性，使遇到未知属性时抛出异常
        List<DeserializationFeature> enableFeatures = new ArrayList<>();
        enableFeatures.add(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        
        try {
            JsonObjectMapper.unmarshallWithFeatures(json, Product.class, enableFeatures, null);
            System.out.println("不应该执行到这里");
        } catch (Exception e) {
            System.out.println("预期的异常: " + e.getMessage());
        }
    }
    
    private static void handleTypeConversion() {
        System.out.println("\n=== 示例2: 处理类型不匹配的情况 ===");
        
        // price字段使用字符串而非数字
        String json = "{\"id\":1,\"name\":\"笔记本电脑\",\"price\":\"5999.99\",\"createDate\":\"2023-01-15\"}";
        
        // 默认情况下，可以将字符串转换为数字
        Product product = JsonObjectMapper.unmarshall(json, Product.class);
        System.out.println("默认类型转换结果: " + product);
        
        // 禁用ACCEPT_FLOAT_AS_INT特性，数值类型转换更严格
        List<DeserializationFeature> disableFeatures = new ArrayList<>();
        disableFeatures.add(DeserializationFeature.ACCEPT_FLOAT_AS_INT);
        
        // 启用USE_BIG_DECIMAL_FOR_FLOATS，使用BigDecimal表示浮点数
        List<DeserializationFeature> enableFeatures = new ArrayList<>();
        enableFeatures.add(DeserializationFeature.USE_BIG_DECIMAL_FOR_FLOATS);
        
        Product product2 = JsonObjectMapper.unmarshallWithFeatures(json, Product.class, enableFeatures, disableFeatures);
        System.out.println("自定义类型转换结果: " + product2);
    }
    
    private static void handleEnumValues() {
        System.out.println("\n=== 示例3: 处理枚举值 ===");
        
        // 包含有效和无效的枚举值
        String validJson = "{\"id\":1,\"name\":\"笔记本电脑\",\"price\":5999.99,\"status\":\"ACTIVE\"}";
        String invalidJson = "{\"id\":2,\"name\":\"平板电脑\",\"price\":3999.99,\"status\":\"PENDING\"}";
        
        // 默认情况下，无效的枚举值会导致异常
        Product validProduct = JsonObjectMapper.unmarshall(validJson, Product.class);
        System.out.println("有效枚举值结果: " + validProduct);
        
        try {
            JsonObjectMapper.unmarshall(invalidJson, Product.class);
            System.out.println("不应该执行到这里");
        } catch (Exception e) {
            System.out.println("默认处理无效枚举值异常: " + e.getMessage());
        }
        
        // 启用READ_UNKNOWN_ENUM_VALUES_AS_NULL特性，将未知枚举值读取为null
        List<DeserializationFeature> enableFeatures = new ArrayList<>();
        enableFeatures.add(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_AS_NULL);
        
        Product nullEnumProduct = JsonObjectMapper.unmarshallWithFeatures(invalidJson, Product.class, enableFeatures, null);
        System.out.println("未知枚举值转为null结果: " + nullEnumProduct);
    }
    
    private static void handleSingleValueAsArray() {
        System.out.println("\n=== 示例4: 处理单值和数组转换 ===");
        
        // tags字段应该是数组，但JSON中是单个值
        String json = "{\"id\":1,\"name\":\"笔记本电脑\",\"price\":5999.99,\"tags\":\"笔记本\"}";
        
        // 默认情况下，无法将单个值作为数组处理
        try {
            JsonObjectMapper.unmarshall(json, Product.class);
            System.out.println("不应该执行到这里");
        } catch (Exception e) {
            System.out.println("默认处理单值作为数组异常: " + e.getMessage());
        }
        
        // 启用ACCEPT_SINGLE_VALUE_AS_ARRAY特性
        List<DeserializationFeature> enableFeatures = new ArrayList<>();
        enableFeatures.add(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY);
        
        Product product = JsonObjectMapper.unmarshallWithFeatures(json, Product.class, enableFeatures, null);
        System.out.println("单值作为数组处理结果: " + product + ", 标签: " + String.join(",", product.tags));
    }
    
    private static void handleComplexObjectWithFeatures() {
        System.out.println("\n=== 示例5: 复杂对象与特性组合 ===");
        
        // 包含多个产品的JSON，结构复杂且包含各种特殊情况
        String json = "{" +
                "\"products\": [" +
                "  {\"id\":1,\"name\":\"笔记本电脑\",\"price\":5999.99,\"createDate\":\"2023-01-15\",\"tags\":[\"电子\",\"笔记本\"],\"status\":\"ACTIVE\"}," +
                "  {\"id\":2,\"name\":\"平板电脑\",\"price\":\"3999.99\",\"createDate\":\"不规范日期\",\"tags\":\"平板\",\"status\":\"INVALID_STATUS\",\"extraField\":\"额外数据\"}" +
                "]," +
                "\"total\": 2," +
                "\"timestamp\": \"2023-10-25T14:30:00\"" +
                "}";
        
        // 配置多个特性来处理各种特殊情况
        List<DeserializationFeature> enableFeatures = new ArrayList<>();
        enableFeatures.add(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY);
        enableFeatures.add(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_AS_NULL);
        enableFeatures.add(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);
        
        List<DeserializationFeature> disableFeatures = new ArrayList<>();
        disableFeatures.add(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        disableFeatures.add(DeserializationFeature.FAIL_ON_INVALID_SUBTYPE);
        disableFeatures.add(DeserializationFeature.FAIL_ON_NULL_FOR_PRIMITIVES);
        
        // 使用TypeReference处理复杂结构
        try {
            Map<String, Object> result = JsonObjectMapper.unmarshallWithFeatures(json, 
                    new TypeReference<Map<String, Object>>() {}, enableFeatures, disableFeatures);
            
            System.out.println("复杂对象处理结果:");
            System.out.println("总数: " + result.get("total"));
            System.out.println("时间戳: " + result.get("timestamp"));
            
            // 提取并处理产品列表
            List<Map<String, Object>> products = (List<Map<String, Object>>) result.get("products");
            System.out.println("产品数量: " + products.size());
            
            for (int i = 0; i < products.size(); i++) {
                System.out.println("产品 " + (i+1) + ": " + products.get(i));
            }
        } catch (Exception e) {
            System.out.println("处理复杂对象异常: " + e.getMessage());
        }
        
        // 实际应用场景: 先解析到Map，再转换为具体对象
        try {
            Map<String, Object> genericMap = JsonObjectMapper.unmarshallWithFeatures(json, 
                    new TypeReference<Map<String, Object>>() {}, enableFeatures, disableFeatures);
            
            // 将产品列表部分单独转换为强类型对象
            List<Map<String, Object>> productMaps = (List<Map<String, Object>>) genericMap.get("products");
            
            // 重新序列化后转换为强类型对象
            List<Product> products = new ArrayList<>();
            for (Map<String, Object> productMap : productMaps) {
                String productJson = JsonObjectMapper.marshall(productMap);
                try {
                    Product product = JsonObjectMapper.unmarshallWithFeatures(productJson, 
                            Product.class, enableFeatures, disableFeatures);
                    products.add(product);
                } catch (Exception e) {
                    System.out.println("转换个别产品失败: " + e.getMessage());
                }
            }
            
            System.out.println("\n成功转换的产品:");
            for (Product product : products) {
                System.out.println(product);
            }
            
        } catch (Exception e) {
            System.out.println("分步转换异常: " + e.getMessage());
        }
    }
}
```

上述示例展示了以下几种常见场景中unmarshallWithFeatures方法的应用：

1. **处理未知属性**：默认情况下，JsonObjectMapper配置为忽略未知属性。通过enableFeatures参数可以启用FAIL_ON_UNKNOWN_PROPERTIES特性，使未知属性导致解析失败。
  
2. **类型转换处理**：展示了如何处理类型不匹配的情况，例如JSON中使用字符串表示数字。

3. **枚举值处理**：展示了如何使用READ_UNKNOWN_ENUM_VALUES_AS_NULL特性处理无效的枚举值。

4. **单值作为数组处理**：使用ACCEPT_SINGLE_VALUE_AS_ARRAY特性允许将单个值转换为数组。

5. **复杂对象与多特性组合**：处理包含多种边缘情况的复杂JSON结构，并展示了实际应用中先解析为Map再转换为具体对象的方法。

在实际应用中，可以根据具体的JSON数据特点，选择性地启用或禁用相应的DeserializationFeature，以实现更加灵活和健壮的JSON处理能力。

### Jackson序列化/反序列化特性完整列表

#### SerializationFeature（序列化特性）

| 特性名称 | 描述 | 默认值 | 使用场景 |
|---------|------|--------|---------|
| WRAP_ROOT_VALUE | 是否环绕根值（在JSON中添加一个表示根名称的属性） | false | 需要输出带有根对象名称的JSON |
| INDENT_OUTPUT | 美化输出，使JSON格式化并易于阅读 | false | 调试或需要可读性强的输出 |
| WRITE_DATES_AS_TIMESTAMPS | 将日期写为时间戳（数字）而非字符串 | true | 控制日期时间表示形式 |
| WRITE_DATE_KEYS_AS_TIMESTAMPS | 将日期类型的Map键写为时间戳 | false | Map键为日期类型时的输出控制 |
| WRITE_DATES_WITH_ZONE_ID | 在序列化日期/时间类型时包含时区信息 | false | 需要保留时区信息的场景 |
| WRITE_DURATIONS_AS_TIMESTAMPS | 将Duration写为时间戳而非字符串 | true | 控制Duration类型的序列化形式 |
| WRITE_CHAR_ARRAYS_AS_JSON_ARRAYS | 将char[]作为JSON数组而非字符串输出 | false | 特殊字符数组处理需求 |
| WRITE_ENUMS_USING_TO_STRING | 使用toString()方法序列化枚举 | false | 自定义枚举值输出格式 |
| WRITE_ENUMS_USING_INDEX | 使用枚举索引值（序号）序列化枚举 | false | 使用枚举的序号而非名称 |
| WRITE_SINGLE_ELEM_ARRAYS_UNWRAPPED | 单元素数组不使用数组表示方式 | false | 简化单元素数组输出 |
| WRITE_NULL_MAP_VALUES | 是否输出Map中的null值 | true | 控制空值是否在输出中显示 |
| WRITE_EMPTY_JSON_ARRAYS | 是否写入空数组 | true | 控制是否输出空数组字段 |
| WRITE_DATE_TIMESTAMPS_AS_NANOSECONDS | 使用纳秒而非毫秒表示时间戳 | true | 高精度时间表示需求 |
| ORDER_MAP_ENTRIES_BY_KEYS | 按键名排序Map条目 | false | 需要确定性输出顺序的场景 |
| EAGER_SERIALIZER_FETCH | 预先获取序列化器以提高性能 | true | 性能优化场景 |
| USE_EQUALITY_FOR_OBJECT_ID | 使用equals()方法而非==比较对象ID | false | 自定义对象标识比较逻辑 |
| FAIL_ON_EMPTY_BEANS | 序列化空对象（无可序列化属性）时是否抛出异常 | true | 控制空对象序列化行为 |
| FAIL_ON_SELF_REFERENCES | 遇到循环引用时是否抛出异常 | true | 处理循环引用的对象图 |
| WRAP_EXCEPTIONS | 是否包装低级异常为JsonMappingException | true | 异常处理策略 |
| WRITE_SELF_REFERENCES_AS_NULL | 循环引用处理为null而非抛出异常 | false | 处理复杂对象图 |
| CLOSE_CLOSEABLE | 自动关闭Closeable类型的值 | true | 资源管理策略 |
| FLUSH_AFTER_WRITE_VALUE | 每次写入后刷新生成器 | true | 控制输出缓冲行为 |
| WRITE_BIGDECIMAL_AS_PLAIN | 以纯字符串形式（非科学计数法）输出BigDecimal | false | 精确数字表示需求 |
| USE_TYPED_HINTS | 在JSON中包含类型信息 | true | 多态类型反序列化支持 |

#### DeserializationFeature（反序列化特性）

| 特性名称 | 描述 | 默认值 | 使用场景 |
|---------|------|--------|---------|
| USE_BIG_DECIMAL_FOR_FLOATS | 将浮点数解析为BigDecimal而非Double | false | 需要精确数值计算 |
| USE_BIG_INTEGER_FOR_INTS | 将整数解析为BigInteger而非Integer/Long | false | 处理超大整数值 |
| USE_JAVA_ARRAY_FOR_JSON_ARRAY | 优先使用Java数组而非集合类型 | false | 特定数组处理需求 |
| FAIL_ON_UNKNOWN_PROPERTIES | 遇到未知属性时是否抛出异常 | true(但JsonObjectMapper默认设为false) | 严格的数据验证需求 |
| FAIL_ON_NULL_FOR_PRIMITIVES | 原始类型（int等）遇到null值时是否抛出异常 | false | 类型安全验证 |
| FAIL_ON_NUMBERS_FOR_ENUMS | 使用数字索引值映射枚举时是否抛出异常 | false | 枚举处理策略控制 |
| FAIL_ON_INVALID_SUBTYPE | 无效子类型标识时是否抛出异常 | true | 多态类型处理控制 |
| FAIL_ON_READING_DUP_TREE_KEY | 遇到重复树节点键时是否抛出异常 | true | 严格树结构验证 |
| FAIL_ON_IGNORED_PROPERTIES | 遇到被@JsonIgnore标记的属性时是否抛出异常 | false | 严格输入验证 |
| FAIL_ON_UNRESOLVED_OBJECT_IDS | 无法解析对象ID引用时是否抛出异常 | true | 对象图处理策略 |
| FAIL_ON_MISSING_CREATOR_PROPERTIES | 缺少必需的创建者属性时是否抛出异常 | false | 构造器参数验证 |
| FAIL_ON_NULL_CREATOR_PROPERTIES | 创建者属性为null时是否抛出异常 | false | 构造器参数验证 |
| FAIL_ON_MISSING_EXTERNAL_TYPE_ID_PROPERTY | 缺少外部类型ID时是否抛出异常 | true | 多态类型处理 |
| FAIL_ON_TRAILING_TOKENS | 解析后仍有多余令牌时是否抛出异常 | false | 严格输入格式验证 |
| WRAP_EXCEPTIONS | 是否包装低级异常为JsonMappingException | true | 异常处理策略 |
| ACCEPT_SINGLE_VALUE_AS_ARRAY | 接受单个值作为数组 | false | 灵活数组处理需求 |
| UNWRAP_ROOT_VALUE | 接受带根包装的JSON | false | 处理带根对象的JSON |
| ACCEPT_EMPTY_STRING_AS_NULL_OBJECT | 将空字符串当作null对象处理 | false | 空值处理策略 |
| ACCEPT_EMPTY_ARRAY_AS_NULL_OBJECT | 将空数组当作null对象处理 | false | 空值处理策略 |
| ACCEPT_FLOAT_AS_INT | 允许将浮点数转为整数 | true | 数值灵活处理需求 |
| READ_UNKNOWN_ENUM_VALUES_AS_NULL | 将未知枚举值解析为null | false | 处理不兼容的枚举值 |
| READ_UNKNOWN_ENUM_VALUES_USING_DEFAULT_VALUE | 使用默认枚举值处理未知值 | false | 处理不兼容的枚举值 |
| READ_DATE_TIMESTAMPS_AS_NANOSECONDS | 以纳秒精度读取日期时间戳 | true | 高精度时间解析 |
| ADJUST_DATES_TO_CONTEXT_TIME_ZONE | 调整日期到上下文时区 | true | 时区处理策略 |
| EAGER_DESERIALIZER_FETCH | 预先获取反序列化器以提高性能 | true | 性能优化 |
| USE_LONG_FOR_INTS | 优先使用Long表示整数 | false | 大整数处理策略 |
| USE_OBJECTS_FOR_NAMED_ARRAYS | 将命名数组解析为对象 | false | 特定结构处理需求 |
| ACCEPT_CASE_INSENSITIVE_PROPERTIES | 不区分属性名大小写 | false | 提高字段匹配灵活性 |
| ACCEPT_CASE_INSENSITIVE_ENUMS | 不区分枚举值大小写 | false | 提高枚举匹配灵活性 |
| ACCEPT_CASE_INSENSITIVE_VALUES | 不区分值大小写 | false | 值比较策略 |

通过了解这些特性的作用和默认值，开发者可以根据具体需求配置JsonObjectMapper，以适应各种复杂的JSON处理场景。

## JSONPathUtils - JSONPath查询工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| findDataByJSONPath(String json, String jsonPath) | 通过JSONPath查询JSON中符合条件的数据 | json: JSON字符串<br>jsonPath: JSONPath表达式 | List<LinkedHashMap<String, Object>>: 符合条件的数据列表 |

### 使用注意事项

1. **路径表达式有效性**
   - JSONPath表达式不能为空，否则会抛出IllegalArgumentException异常
   - 无效的路径表达式会导致PathNotFoundException异常
   - 建议在调用前先验证表达式的有效性

2. **返回结果处理**
   - 返回值始终是List<LinkedHashMap<String, Object>>类型，即使只有单个结果
   - 查询无匹配时会返回空列表，而不是null
   - 返回的LinkedHashMap维持JSON中的属性顺序

3. **大型JSON处理**
   - 处理大型JSON数据时，注意内存使用
   - 对于特别复杂的查询可能影响性能，考虑优化JSONPath表达式

4. **类型转换**
   - 结果中的数值类型通常会转换为Long或Double
   - 需要特定类型时，需要手动进行类型转换

5. **异常处理**
   - 处理异常时应捕获IllegalArgumentException，包含输入参数错误和路径错误
   - 对于不确定是否存在的路径，应使用try-catch包装调用

6. **嵌套查询**
   - 复杂的嵌套结构可能需要多次调用处理
   - 考虑先获取父节点，再依次处理子节点

### 使用示例

```java
// 准备JSON数据
String jsonData = "{ " +
    "\"employees\": [" +
    "    {\"id\": 1, \"name\": \"John\", \"department\": \"IT\"}," +
    "    {\"id\": 2, \"name\": \"Jane\", \"department\": \"HR\"}," +
    "    {\"id\": 3, \"name\": \"Bob\", \"department\": \"IT\"}" +
    "  ]" +
    "}";

// 查询id为1的员工
String path1 = "$.employees[?(@.id == 1)]";
List<LinkedHashMap<String, Object>> result1 = JSONPathUtils.findDataByJSONPath(jsonData, path1);
System.out.println("员工ID为1的记录:");
result1.forEach(System.out::println);
// 输出: 员工ID为1的记录:
// {id=1, name=John, department=IT}

// 查询IT部门的所有员工
String path2 = "$.employees[?(@.department == 'IT')]";
List<LinkedHashMap<String, Object>> result2 = JSONPathUtils.findDataByJSONPath(jsonData, path2);
System.out.println("IT部门的所有员工:");
result2.forEach(System.out::println);
// 输出: IT部门的所有员工:
// {id=1, name=John, department=IT}
// {id=3, name=Bob, department=IT}

// 获取所有员工名称
String path3 = "$.employees[*].name";
List<LinkedHashMap<String, Object>> result3 = JSONPathUtils.findDataByJSONPath(jsonData, path3);
System.out.println("所有员工名称:");
result3.forEach(System.out::println);
// 输出: 所有员工名称:
// John
// Jane
// Bob

// 异常情况示例
try {
    // JSONPath为空时抛出异常
    JSONPathUtils.findDataByJSONPath(jsonData, "");
} catch (IllegalArgumentException e) {
    System.out.println("Exception: " + e.getMessage());  // 输出: Exception: Input parameter is empty
}

try {
    // 无效的路径抛出异常
    JSONPathUtils.findDataByJSONPath(jsonData, "$.invalid.path");
} catch (IllegalArgumentException e) {
    System.out.println("Exception: " + e.getMessage());  // 输出: Exception: PathNotFoundException
}
```

## JsonXmlMapper - JSON和XML转换工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| marshall(String jsonStrStr, Class<T> clazz) | 将JSON字符串转换为XML | jsonStrStr: JSON字符串<br>clazz: 对象类型 | String: XML字符串 |

### 使用注意事项

1. **XML标签名称**
   - JSON属性名会直接转换为XML标签名
   - 不符合XML命名规范的属性名可能导致转换失败
   - 包含特殊字符的属性名需要特别注意

2. **数据类型映射**
   - JSON的数据类型会在转换过程中尝试映射为对应XML的数据表示
   - 复杂对象需确保其结构适合XML表示

3. **性能考虑**
   - 转换大型JSON文档时可能占用较多内存和处理时间
   - 对性能敏感场景可能需要考虑替代方案

4. **错误处理**
   - 无效的JSON输入会导致RuntimeException异常
   - 始终使用try-catch块处理可能的转换错误

5. **嵌套对象处理**
   - 嵌套对象会转换为嵌套XML元素
   - 数组会转换为重复的XML元素
   - 过深的嵌套可能导致XML可读性降低

6. **字符编码**
   - 输出的XML默认使用UTF-8编码
   - 包含非ASCII字符时需特别注意编码问题

### 使用示例

```java
// 定义一个简单的用户类
public class User {
    private int id;
    private String name;
    private int age;
    
    // Getters and setters...
}

// 准备JSON数据
String jsonStr = "{\"id\":1,\"name\":\"张三\",\"age\":30}";

// 将JSON转换为XML
String xml = JsonXmlMapper.marshall(jsonStr, User.class);
System.out.println("转换后的XML:");
System.out.println(xml);
// 输出类似:
// <?xml version="1.0" encoding="UTF-8"?>
// <User>
//   <id>1</id>
//   <name>张三</name>
//   <age>30</age>
// </User>

// 异常情况示例
try {
    // 无效的JSON会抛出异常
    JsonXmlMapper.marshall("{invalid json}", User.class);
} catch (RuntimeException e) {
    System.out.println("Exception: " + e.getMessage());  // 输出相关异常信息
}
``` 