/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.crypt;

import org.bouncycastle.crypto.digests.MD5Digest;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.util.encoders.Hex;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.Security;
import java.util.Objects;

/**
 * 【加解密】MD5散列算法
 * 1. 实现MD5加密算法
 * 2. 使用BouncyCastleProvider（简称BC）扩展可用算法
 *
 * <AUTHOR>
 * @module 加解密
 * @date 2023-12-01 10:03
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class MD5Utils {


    private static final String PARAMETER_IS_NULL = "parameter is null";
    public static final int END_INDEX = 3;

    /**
     * 使用BouncyCastleProvider来实现md5值计算
     *
     * @param rawStr 原始计算字符串
     * @return md5计算值
     * <AUTHOR>
     * @date 2023/12/01
     * @since 1.4.0
     */
    public static String md5WithBouncyCastle(String rawStr) {
        Objects.requireNonNull(rawStr, PARAMETER_IS_NULL);
        return md5WithBouncyCastle(rawStr.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 使用BouncyCastleProvider来实现md5值计算
     *
     * @param rawBytes 原始字节数组
     * @return md5计算值
     * <AUTHOR>
     * @date 2023/12/01
     * @since 1.4.0
     */
    public static String md5WithBouncyCastle(byte[] rawBytes) {
        Objects.requireNonNull(rawBytes, PARAMETER_IS_NULL);
        Security.addProvider(new BouncyCastleProvider());
        MD5Digest md5Digest = new MD5Digest();
        md5Digest.update(rawBytes, 0, rawBytes.length);
        byte[] digested = new byte[md5Digest.getDigestSize()];
        md5Digest.doFinal(digested, 0);
        return new String(Hex.encode(digested));
    }

    /**
     * 原生JDK来实现md5值计算
     *
     * @param rawBytes 原始字节数组
     * @return md5计算值
     * <AUTHOR>
     * @date 2023/12/01
     * @since 1.4.0
     */
    public static String md5(byte[] rawBytes) {
        Objects.requireNonNull(rawBytes, PARAMETER_IS_NULL);
        String md5Result;
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] array = md.digest(rawBytes);
            StringBuilder sb = new StringBuilder();
            for (byte item : array) {
                sb.append(Integer.toHexString((item & 0xFF) | 0x100).substring(1, END_INDEX));
            }
            md5Result = sb.toString();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return md5Result;
    }

    /**
     * 原生JDK来实现md5值计算
     *
     * @param rawStr 原始计算字符串
     * @return md5计算值
     * <AUTHOR>
     * @date 2023/12/01
     * @since 1.4.0
     */
    public static String md5(String rawStr) {
        Objects.requireNonNull(rawStr, PARAMETER_IS_NULL);
        return md5(rawStr.getBytes(StandardCharsets.UTF_8));
    }
}
