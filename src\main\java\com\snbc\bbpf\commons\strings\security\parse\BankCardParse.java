/*
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.commons.strings.security.parse;

import org.apache.commons.lang3.StringUtils;

/**
 * @ClassName:  BankCardParse
 * 银行卡脱敏
 * @module:    字符脱敏
 * @Author:    yangweipeng
 * @date:      2023/05/06
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class BankCardParse implements IDesensitizedParse {

    /**
     * 左边显示字符数
     */
    private static final Integer LEFT_LENGTH =6;
    /**
     * 右边显示字符数
     */
    private static final Integer RIGHT_LENGTH =4;
    /**
     * 【银行卡号】前六位，后四位，其他用星号隐藏每位1个星号，比如：622260**********1234>
     *
     * @param cardNum
     * @return
     */
    @Override
    public String parseString(String cardNum) {
        if(StringUtils.isEmpty(cardNum)){
            return cardNum;
        }
        return StringUtils.left(cardNum, LEFT_LENGTH).concat(StringUtils.
                removeStart(StringUtils.leftPad(StringUtils.right(cardNum, RIGHT_LENGTH), StringUtils.length(cardNum), "*"), "******"));
    }
}

