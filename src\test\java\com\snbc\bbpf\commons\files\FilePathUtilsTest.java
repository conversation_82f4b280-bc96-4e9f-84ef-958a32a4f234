package com.snbc.bbpf.commons.files;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledOnOs;
import org.junit.jupiter.api.condition.OS;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.io.File;
import java.net.URL;
import java.net.URLClassLoader;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 文件工具类单元测试类
 *
 * <AUTHOR>
 * @module 文件处理模块
 * @date 2023/9/25 20:30
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class FilePathUtilsTest {

    /**
     * 文件内容
     */

    private static final String fileName = "./test_fileUtils.txt";

    @Test
    @DisplayName("获取文件扩展名 - 文件路径为空")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/9/25")
    })
    void testGetFileExtension_null() {
        assertNull(FilePathUtils.getFileExtension(null));
    }

    @Test
    @DisplayName("获取文件扩展名 - 文件路径不为空")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/08")
    })
    void testGetFileExtension_notNull() {
        String filePath = "path/to/file.txt";
        String extension = FilePathUtils.getFileExtension(filePath);
        assertEquals("txt", extension);
    }

    @Test
    @DisplayName("根据文件路径获取文件名 - 文件路径为null")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/08")
    })
    void testGetFileName_null() {
        assertThrows(IllegalArgumentException.class, () -> {
            FilePathUtils.getFileName(null);
        });
    }

    @Test
    @DisplayName("根据文件路径获取文件名 - 文件路径不为null")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/08")
    })
    void testGetFileName_notNull() {
        String filePath = "path/to/file.txt";
        String fileName = FilePathUtils.getFileName(filePath);
        assertEquals("file.txt", fileName);
    }

    @Test
    @DisplayName("获取文件路径的层级数 - 文件路径为null")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/08")
    })
    void testGetPathHierarchyLevel_null() {
        int level = FilePathUtils.getPathHierarchyLevel(null);
        assertEquals(0, level);
    }

    @Test
    @DisplayName("获取文件路径的层级数 - 文件路径为空字符串")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/08")
    })
    void testGetPathHierarchyLevel_empty() {
        int level = FilePathUtils.getPathHierarchyLevel("");
        assertEquals(0, level);
    }

    @Test
    @DisplayName("获取文件路径的层级数 - 文件路径为相对路径")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/08")
    })
    void testGetPathHierarchyLevel_relativePath() {
        String filePath = "path/to/file.txt";
        int level = FilePathUtils.getPathHierarchyLevel(filePath);
        assertEquals(3, level);
    }

    @Test
    @DisplayName("获取文件路径的层级数 - 文件路径为绝对路径")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/08")
    })
    void testGetPathHierarchyLevel_absolutePath() {
        String filePath = "/path/to/file.txt";
        int level = FilePathUtils.getPathHierarchyLevel(filePath);
        assertEquals(3, level);
    }

    @Test
    @DisplayName("获取文件路径的层级数 - 文件路径为绝对路径")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/08")
    })
    void testGetPathHierarchyLevel_absolutePath2() {
        String filePath = "/path/to/";
        int level = FilePathUtils.getPathHierarchyLevel(filePath);
        assertEquals(2, level);
    }


    @Test
    @DisplayName("判断路径是目录还是文件 - 路径为null")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/08")
    })
    void testIsDirectory_nullPath() {
        boolean result = FilePathUtils.isDirectory(null);
        assertFalse(result);
    }

    @Test
    @DisplayName("判断路径是目录还是文件 - 路径为空字符串")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/08")
    })
    void testIsDirectory_emptyPath() {
        boolean result = FilePathUtils.isDirectory("");
        assertFalse(result);
    }

    @Test
    @DisplayName("根据目录路径最后是否有目录分隔符返回带目录分隔符或不带目录分隔符的路径")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/08")
    })
    void testIsDirectory_directoryPath() {
        String path = ".";
        boolean result = FilePathUtils.isDirectory(path);
        assertTrue(result);
    }

    @Test
    @DisplayName("根据目录路径最后是否有目录分隔符返回带目录分隔符或不带目录分隔符的路径")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/08")
    })
    void testIsDirectory_filePath() {
        String path = fileName;
        boolean result = FilePathUtils.isDirectory(path);
        assertFalse(result);
    }

    @DisplayName("根据目录路径最后是否有目录分隔符返回带目录分隔符或不带目录分隔符的路径,测试linux的路径")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/08")
    })
    @Test
    void testFormatPath_FormatPathWithSeparator() {
        String path = "/sss/";
        String formattedPath = FilePathUtils.formatPath(path);
        assertEquals("/sss/", formattedPath);
    }

    @DisplayName("根据目录路径最后是否有目录分隔符返回带目录分隔符或不带目录分隔符的路径,测试windows的路径")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/08")
    })
    @Test
    void testFormatPath_FormatPathWithSeparator1() {
        String path = "C:\\sss\\";
        String formattedPath = FilePathUtils.formatPath(path);
        assertEquals("C:\\sss\\", formattedPath);
    }

    @DisplayName("根据目录路径最后是否有目录分隔符返回带目录分隔符或不带目录分隔符的路径")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/08")
    })
    @Test
    void testFormatPath_FormatPathWithoutSeparator() {
        String path = "/sss";
        String formattedPath = FilePathUtils.formatPath(path);
        assertEquals("/sss/", formattedPath);
    }

    @DisplayName("根据windows目录路径最后是否有目录分隔符返回带目录分隔符或不带目录分隔符的路径")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/08")
    })
    @Test
    void testFormatPath_FormatWindowsPathWithoutSeparator() {
        String path = "c:\\sss";
        String formattedPath = FilePathUtils.formatPath(path);
        assertEquals("c:\\sss\\", formattedPath);
    }

    @DisplayName("根据目录路径最后是否有目录分隔符返回带目录分隔符或不带目录分隔符的路径")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/08")
    })
    @Test
    void testFormatPath_FormatEmptyPath() {
        String path = "";
        String formattedPath = FilePathUtils.formatPath(path);
        assertEquals("", formattedPath);
    }

    @DisplayName("根据目录路径最后是否有目录分隔符返回带目录分隔符或不带目录分隔符的路径")
    @Tags({
            @Tag("@id:77"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/08")
    })
    @Test
    void testFormatPath_NullPath() {
        String path = null;
        String formattedPath = FilePathUtils.formatPath(path);
        assertNull(formattedPath);
    }

    @DisplayName("动态加载jar包，传入对应jar中的类获取对应jar包所在的目录")
    @Tags({
            @Tag("@id:109"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2024/08/07")
    })
    @Test
    void testGetJarPathByRuntime_Positive() throws Exception {
        URL[] urls = new URL[]{new File(Thread.currentThread().getContextClassLoader()
                .getResource("files/sample.jar").getFile()).toURI().toURL()};
        URLClassLoader cl = new URLClassLoader(urls);
        Class<?> clazz = cl.loadClass("org.snbc.sample.Sample");

        String jarPath = FilePathUtils.getPathForClassOfJar(clazz);
        assertNotNull(jarPath, "The jar path should not be null");
        assertTrue(jarPath.contains("files"));
    }

    @DisplayName("动态加载jar包，传入null的class，返回null指针异常")
    @Tags({
            @Tag("@id:109"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2024/08/07")
    })
    @Test
    void testGetJarPathByRuntime_NullClass() throws Exception {
        assertThrows(NullPointerException.class, () -> {
            FilePathUtils.getPathForClassOfJar(null);
        }, "Should throw NullPointerException when class is null.");
    }
    @DisplayName("获取本项目的类的路径，返回target目录")
    @Tags({
            @Tag("@id:109"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2024/08/07")
    })
    @Test
    void testGetJarPathByRuntime_LocalClass() throws Exception {
        String jarPath = FilePathUtils.getPathForClassOfJar(FilePathUtils.class);
        assertTrue(jarPath.contains("target"));
    }
    @DisplayName("获取本项目的Junit类所在jar包的路径，返回target目录")
    @Tags({
            @Tag("@id:109"),
            @Tag("@author:suiweihua"),
            @Tag("@date:2024/08/07")
    })
    @Test
    void testGetJarPathByRuntime_JunitClass() throws Exception {
        String jarPath = FilePathUtils.getPathForClassOfJar(org.junit.jupiter.api.Test.class);
        assertNotNull(jarPath);
        assertTrue(jarPath.contains("junit"));
    }

    @Test
    @DisplayName("测试空路径")
    @Tags({
            @Tag("@id:115"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2025/5/30")
    })
    void testEmptyPath() {
        assertThrows(IllegalArgumentException.class, () -> {
            FilePathUtils.isContainsIllegalChars("");
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            FilePathUtils.isValidPath("");
        });
    }

    @Test
    @DisplayName("测试null路径")
    @Tags({
            @Tag("@id:115"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2025/5/30")
    })
    void testNullPath() {
        assertThrows(IllegalArgumentException.class, () -> {
            FilePathUtils.isContainsIllegalChars(null);
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            FilePathUtils.isValidPath(null);
        });
    }

    @EnabledOnOs(OS.WINDOWS)
    @ParameterizedTest
    @ValueSource(strings = {
        "C:\\test\\file.txt",           // Windows标准路径
        "/home/<USER>/test.txt",          // Linux标准路径
        "test.txt",                     // 相对路径
        "C:/test/file.txt",             // Windows路径使用正斜杠
        "..\\test\\file.txt",           // 使用相对路径
        "C:\\Program Files\\test.txt",  // 包含空格的路径
        "C:\\test\\中文路径\\test.txt",  // 包含中文的路径
        "C:\\test\\<EMAIL>",       // 包含特殊字符的路径
        "C:\\test\\test-123.txt",       // 包含连字符的路径
        "C:\\test\\test_123.txt"        // 包含下划线的路径
    })
    @DisplayName("测试合法路径")
    @Tags({
            @Tag("@id:115"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2025/5/30")
    })
    void testValidPaths(String path) {
        assertFalse(FilePathUtils.isContainsIllegalChars(path), 
            "合法路径不应该包含非法字符: " + path);
        assertTrue(FilePathUtils.isValidPath(path), 
            "合法路径应该通过验证: " + path);
    }

    @EnabledOnOs(OS.WINDOWS)
    @ParameterizedTest
    @ValueSource(strings = {
        "C:\\test\\file*.txt",          // 包含星号
        "C:\\test\\file?.txt",          // 包含问号
        "C:\\test\\file<>.txt",         // 包含尖括号
        "C:\\test\\file|.txt",          // 包含竖线
        "C:\\test\\file\".txt",         // 包含引号
        "C:\\test\\file:.txt",          // 包含冒号
        "C:\\test\\file*.txt",          // 包含星号
        "C:\\test\\file?.txt",          // 包含问号
        "C:\\test\\file<>.txt",         // 包含尖括号
        "C:\\test\\file|.txt"           // 包含竖线
    })
    @DisplayName("测试包含非法字符的路径")
    @Tags({
            @Tag("@id:115"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2025/5/30")
    })
    void testPathsWithIllegalChars(String path) {
        assertTrue(FilePathUtils.isContainsIllegalChars(path), 
            "包含非法字符的路径应该被检测到: " + path);
        assertFalse(FilePathUtils.isValidPath(path), 
            "包含非法字符的路径不应该通过验证: " + path);
    }

    @EnabledOnOs(OS.WINDOWS)
    @Test
    @DisplayName("测试超长路径")
    @Tags({
            @Tag("@id:115"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2025/5/30")
    })
    void testLongPath() {
        // 创建一个超过Windows最大长度限制的路径
        StringBuilder longPath = new StringBuilder("C:\\");
        for (int i = 0; i < 100; i++) {
            longPath.append("very_long_directory_name_").append(i).append("\\");
        }
        longPath.append("test.txt");

        String path = longPath.toString();
        if (System.getProperty("os.name").toLowerCase().contains("windows")) {
            assertFalse(FilePathUtils.isValidPath(path), 
                "超过Windows路径长度限制的路径不应该通过验证");
        }
    }

    @EnabledOnOs(OS.WINDOWS)
    @Test
    @DisplayName("测试路径规范化")
    @Tags({
            @Tag("@id:115"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2025/5/30")
    })
    void testPathNormalization() {
        // 测试包含多个分隔符的路径
        String path = "C:\\\\test\\\\file.txt";
        assertFalse(FilePathUtils.isContainsIllegalChars(path), 
            "包含多个分隔符的路径应该被规范化");
        assertTrue(FilePathUtils.isValidPath(path), 
            "包含多个分隔符的路径应该通过验证");

        // 测试包含点号的路径
        path = "C:\\test\\.\\file.txt";
        assertFalse(FilePathUtils.isContainsIllegalChars(path), 
            "包含点号的路径应该被规范化");
        assertTrue(FilePathUtils.isValidPath(path), 
            "包含点号的路径应该通过验证");
    }

    @EnabledOnOs(OS.WINDOWS)
    @Test
    @DisplayName("测试特殊路径")
    @Tags({
            @Tag("@id:115"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2025/5/30")
    })
    void testSpecialPaths() {
        // 测试UNC路径
        String uncPath = "\\\\server\\share\\file.txt";
        assertFalse(FilePathUtils.isContainsIllegalChars(uncPath), 
            "UNC路径不应该包含非法字符");
        assertTrue(FilePathUtils.isValidPath(uncPath), 
            "UNC路径应该通过验证");

        // 测试网络路径
        String networkPath = "\\\\192.168.1.1\\share\\file.txt";
        assertFalse(FilePathUtils.isContainsIllegalChars(networkPath), 
            "网络路径不应该包含非法字符");
        assertTrue(FilePathUtils.isValidPath(networkPath), 
            "网络路径应该通过验证");
    }

    @EnabledOnOs(OS.LINUX)
    @ParameterizedTest
    @ValueSource(strings = {
        "/home/<USER>/test.txt",          // Linux标准路径
        "/var/log/syslog",              // 系统日志路径
        "/etc/passwd",                  // 系统配置文件
        "~/documents/test.txt",         // 使用波浪号的家目录
        "./test.txt",                   // 相对路径
        "../test.txt",                  // 上级目录
        "/mnt/data/test.txt",           // 挂载点路径
        "/opt/app/test.txt",            // 应用程序路径
        "/usr/local/bin/test.sh",       // 可执行文件路径
        "/tmp/test.txt"                 // 临时文件路径
    })
    @DisplayName("测试Linux系统下的合法路径")
    @Tags({
            @Tag("@id:115"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2025/6/18")
    })
    void testValidLinuxPaths(String path) {
        assertFalse(FilePathUtils.isContainsIllegalChars(path), 
            "Linux合法路径不应该包含非法字符: " + path);
        assertTrue(FilePathUtils.isValidPath(path), 
            "Linux合法路径应该通过验证: " + path);
    }

    /**linux暂无有效测试案例，在windows下的非法字符在Linux下合法
    @EnabledOnOs(OS.LINUX)
    @ParameterizedTest
    @ValueSource(strings = {
        "/home/<USER>/file//.txt"        // 包含/
    })
    @DisplayName("测试Linux系统下包含非法字符的路径")
    @Tags({
            @Tag("@id:115"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2025/6/18")
    })
    void testLinuxPathsWithIllegalChars(String path) {
        assertTrue(FilePathUtils.isContainsIllegalChars(path), 
            "Linux路径中包含非法字符应该被检测到: " + path);
        assertFalse(FilePathUtils.isValidPath(path), 
            "Linux路径中包含非法字符不应该通过验证: " + path);
    }
    */

    @EnabledOnOs(OS.MAC)
    @ParameterizedTest
    @ValueSource(strings = {
        "/home/<USER>/file/:.txt"        // 包含:
    })
    @DisplayName("测试Linux系统下包含非法字符的路径")
    @Tags({
            @Tag("@id:115"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2025/6/18")
    })
    void testMacPathsWithIllegalChars(String path) {
        assertTrue(FilePathUtils.isContainsIllegalChars(path), 
            "Linux路径中包含非法字符应该被检测到: " + path);
        assertFalse(FilePathUtils.isValidPath(path), 
            "Linux路径中包含非法字符不应该通过验证: " + path);
    }


    @EnabledOnOs(OS.LINUX)
    @Test
    @DisplayName("测试Linux系统下的特殊路径")
    @Tags({
            @Tag("@id:115"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2025/6/18")
    })
    void testLinuxSpecialPaths() {
        // 测试设备文件路径
        String devicePath = "/dev/sda1";
        assertFalse(FilePathUtils.isContainsIllegalChars(devicePath), 
            "Linux设备文件路径不应该包含非法字符");
        assertTrue(FilePathUtils.isValidPath(devicePath), 
            "Linux设备文件路径应该通过验证");

        // 测试软链接路径
        String symlinkPath = "/var/run/link";
        assertFalse(FilePathUtils.isContainsIllegalChars(symlinkPath), 
            "Linux软链接路径不应该包含非法字符");
        assertTrue(FilePathUtils.isValidPath(symlinkPath), 
            "Linux软链接路径应该通过验证");

        // 测试FIFO管道路径
        String fifoPath = "/var/run/fifo";
        assertFalse(FilePathUtils.isContainsIllegalChars(fifoPath), 
            "Linux FIFO管道路径不应该包含非法字符");
        assertTrue(FilePathUtils.isValidPath(fifoPath), 
            "Linux FIFO管道路径应该通过验证");
    }

    @EnabledOnOs(OS.LINUX)
    @Test
    @DisplayName("测试Linux系统下的超长路径")
    @Tags({
            @Tag("@id:115"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2025/6/18")
    })
    void testLinuxLongPath() {
        // 创建一个超过Linux最大长度限制的路径
        StringBuilder longPath = new StringBuilder("/");
        for (int i = 0; i < 2000; i++) {
            longPath.append("very_long_directory_name_").append(i).append("/");
        }
        longPath.append("test.txt");

        String path = longPath.toString();
        assertFalse(FilePathUtils.isValidPath(path), 
            "超过Linux路径长度限制的路径不应该通过验证");
    }

}
