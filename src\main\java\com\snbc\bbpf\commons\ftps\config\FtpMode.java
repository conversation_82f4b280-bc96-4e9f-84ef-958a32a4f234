package com.snbc.bbpf.commons.ftps.config;

/**
 * Ftp模式的枚举类
 *
 * <AUTHOR>
 * @module 数据传输
 * @date 2023/9/27 13:04
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public enum FtpMode {
    //主动模式
    ACTIVE_MODE("active"),
    //被动模式
    POSITIVE_MODE("positive");

    private final String mode;

    FtpMode(String mode) {
        this.mode = mode;
    }
    public String getMode() {
        return mode;
    }
}
