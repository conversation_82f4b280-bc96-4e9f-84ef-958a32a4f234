# 协议消息编解码器 (com.snbc.bbpf.commons.bytes.protocol)

## 概述

协议消息编解码器提供了完整的自定义协议消息格式的编解码功能，支持固定消息头、扩展消息头和消息体的处理。该协议使用小端字节序和UTF-8字符串编码。

## 协议格式

### 完整报文结构
```
[固定消息头(31字节)] + [扩展消息头(变长)] + [消息体(变长)] + [校验码(可选)]
```

### 固定消息头结构 (31字节)
| 字段名 | 字节长度 | 类型 | 说明 |
|--------|----------|------|------|
| 消息标识 | 4 | BYTE[] | 固定为"STUM" |
| 协议版本 | 1 | BYTE | 当前版本为1 |
| 消息类型 | 2 | UNSIGNED SHORT | 消息类型标识 |
| 消息序号 | 8 | LONG | 消息流水号 |
| 时间戳 | 8 | LONG | 13位毫秒级时间戳 |
| 扩展消息头长度 | 4 | INT | 扩展消息头字节数 |
| 消息体长度 | 4 | INT | 消息体字节数 |

### 扩展消息头结构 (变长)
| 字段名 | 结构 | 说明 |
|--------|------|------|
| 商户编号 | [长度(4字节)] + [内容] | UTF-8编码字符串 |
| 产品编号 | [长度(4字节)] + [内容] | UTF-8编码字符串 |
| 应用编号 | [长度(4字节)] + [内容] | UTF-8编码字符串 |
| 保留信息 | [长度(4字节)] + [内容] | 字节数组 |

## 类概览

| 类名 | 功能描述 |
|------|----------|
| ProtocolMessage | 协议消息数据结构，包含所有消息字段 |
| ProtocolMessageEncoder | 协议消息编码器，将消息对象编码为二进制数据 |
| ProtocolMessageDecoder | 协议消息解码器，将二进制数据解码为消息对象 |

## ProtocolMessage - 协议消息数据结构

### 构造函数
```java
// 默认构造函数
ProtocolMessage()

// 指定关键字段的构造函数
ProtocolMessage(short messageType, long sequenceNumber, long timestamp)
```

### 主要方法
| 方法 | 说明 | 返回值 |
|------|------|--------|
| calculateExtendedHeaderLength() | 计算扩展消息头长度 | int |
| getTotalLength() | 获取完整消息总长度 | int |
| setTenantId(String tenantId) | 设置商户编号 | void |
| setProductId(String productId) | 设置产品编号 | void |
| setAppKey(String appKey) | 设置应用编号 | void |
| setMessageBody(byte[] messageBody) | 设置消息体 | void |

## ProtocolMessageEncoder - 消息编码器

### 主要方法
| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| encode(ProtocolMessage message) | 编码协议消息为二进制数据 | message: 协议消息对象 | byte[]: 编码后的二进制数据 |
| encodeFixedHeader(...) | 编码固定消息头 | 各个字段参数 | byte[]: 固定消息头数据 |
| calculateStringFieldLength(String value) | 计算字符串字段编码长度 | value: 字符串值 | int: 编码后长度 |

## ProtocolMessageDecoder - 消息解码器

### 主要方法
| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| decode(byte[] data) | 解码二进制数据为协议消息 | data: 二进制数据 | ProtocolMessage: 解码后的消息 |
| validateFormat(byte[] data) | 验证数据格式是否有效 | data: 二进制数据 | boolean: 是否有效 |
| extractMessageType(byte[] data) | 提取消息类型 | data: 二进制数据 | short: 消息类型 |
| extractSequenceNumber(byte[] data) | 提取消息序号 | data: 二进制数据 | long: 消息序号 |

## 使用示例

### 基本使用
```java
import com.snbc.bbpf.commons.bytes.protocol.*;

// 创建协议消息
ProtocolMessage message = new ProtocolMessage((short) 101, 10000L, System.currentTimeMillis());
message.setTenantId("tenant_test");
message.setProductId("product_abc");
message.setAppKey("app_key_xyz");
message.setMessageBody("Hello World".getBytes(StandardCharsets.UTF_8));

// 编码消息
byte[] encoded = ProtocolMessageEncoder.encode(message);

// 解码消息
ProtocolMessage decoded = ProtocolMessageDecoder.decode(encoded);

// 验证结果
System.out.println("消息类型: " + decoded.getMessageType());
System.out.println("商户编号: " + decoded.getTenantId());
System.out.println("消息体: " + new String(decoded.getMessageBody(), StandardCharsets.UTF_8));
```

### 处理文档示例数据
```java
// 根据文档示例创建消息
ProtocolMessage message = new ProtocolMessage((short) 101, 10000L, 1734058149000L);
message.setTenantId("tenant_fjlweh");
message.setProductId("");  // 空字符串
message.setAppKey("");     // 空字符串
message.setReservedInfo(new byte[0]);  // 空字节数组
message.setMessageBody(new byte[0]);   // 空消息体

// 编码
byte[] encoded = ProtocolMessageEncoder.encode(message);

// 验证扩展消息头长度（应该是29字节）
int extendedHeaderLength = message.calculateExtendedHeaderLength();
System.out.println("扩展消息头长度: " + extendedHeaderLength);  // 输出: 29

// 验证总长度
int totalLength = message.getTotalLength();
System.out.println("消息总长度: " + totalLength);  // 输出: 60 (31 + 29 + 0)
```

### 处理中文字符串
```java
// 创建包含中文的消息
ProtocolMessage message = new ProtocolMessage((short) 200, 1L, System.currentTimeMillis());
message.setTenantId("商户编号_测试");
message.setProductId("产品编号_测试");
message.setAppKey("应用编号_测试");
message.setMessageBody("消息内容：你好世界！".getBytes(StandardCharsets.UTF_8));

// 编码和解码
byte[] encoded = ProtocolMessageEncoder.encode(message);
ProtocolMessage decoded = ProtocolMessageDecoder.decode(encoded);

// 中文字符串正确处理
System.out.println("商户编号: " + decoded.getTenantId());  // 正确显示中文
```

### 快速提取消息信息
```java
// 不完整解码，只提取关键信息
byte[] data = ...; // 接收到的二进制数据

// 快速验证格式
if (ProtocolMessageDecoder.validateFormat(data)) {
    // 提取消息类型和序号
    short messageType = ProtocolMessageDecoder.extractMessageType(data);
    long sequenceNumber = ProtocolMessageDecoder.extractSequenceNumber(data);
    
    System.out.println("消息类型: " + messageType);
    System.out.println("消息序号: " + sequenceNumber);
    
    // 根据需要决定是否完整解码
    if (messageType == 101) {
        ProtocolMessage fullMessage = ProtocolMessageDecoder.decode(data);
        // 处理完整消息...
    }
}
```

### 批量处理消息
```java
public class MessageProcessor {
    
    public void processMessages(List<byte[]> messageDataList) {
        for (byte[] data : messageDataList) {
            try {
                // 验证格式
                if (!ProtocolMessageDecoder.validateFormat(data)) {
                    System.err.println("无效的消息格式");
                    continue;
                }
                
                // 解码消息
                ProtocolMessage message = ProtocolMessageDecoder.decode(data);
                
                // 根据消息类型处理
                switch (message.getMessageType()) {
                    case 101:
                        handleHeartbeat(message);
                        break;
                    case 200:
                        handleDataReport(message);
                        break;
                    default:
                        System.out.println("未知消息类型: " + message.getMessageType());
                }
                
            } catch (Exception e) {
                System.err.println("处理消息失败: " + e.getMessage());
            }
        }
    }
    
    private void handleHeartbeat(ProtocolMessage message) {
        System.out.println("处理心跳消息，序号: " + message.getSequenceNumber());
    }
    
    private void handleDataReport(ProtocolMessage message) {
        System.out.println("处理数据上报，商户: " + message.getTenantId());
    }
}
```

## 使用注意事项

### 1. 字节序
- 协议使用**小端字节序**（Little Endian）
- 编解码器已自动处理字节序转换

### 2. 字符串编码
- 所有字符串使用**UTF-8编码**
- 支持中文和其他Unicode字符

### 3. 长度字段
- 扩展消息头中每个字段都有4字节的长度前缀
- 长度为0表示该字段为空

### 4. 消息体
- 消息体内容由具体业务定义
- 可以是任意二进制数据

### 5. 校验码
- 校验码是可选的
- 如果存在，会作为消息的最后部分

### 6. 性能考虑
- 对于只需要消息类型的场景，使用`extractMessageType()`避免完整解码
- 批量处理时先验证格式再解码
- 重用ProtocolMessage对象以减少内存分配

### 7. 异常处理
- 编解码过程中可能抛出`IllegalArgumentException`
- 建议在生产环境中进行适当的异常处理

## 与TLV编码器的关系

虽然您的协议格式与标准TLV不同，但如果需要在消息体中使用TLV格式，可以这样结合使用：

```java
// 在消息体中使用TLV编码
TlvEncoder tlvEncoder = new TlvEncoder(
    ByteOrder.LITTLE_ENDIAN,  // 与协议保持一致的字节序
    StandardCharsets.UTF_8,   // 与协议保持一致的字符编码
    1, 4
);

// 编码消息体数据
Map<String, Object> bodyData = new HashMap<>();
bodyData.put("temperature", 25.5);
bodyData.put("humidity", 60);
byte[] tlvBody = tlvEncoder.encode(bodyData);

// 设置到协议消息中
ProtocolMessage message = new ProtocolMessage((short) 300, 1L, System.currentTimeMillis());
message.setMessageBody(tlvBody);

// 编码整个协议消息
byte[] protocolData = ProtocolMessageEncoder.encode(message);
```

这样可以在您的协议框架内灵活使用TLV格式处理复杂的消息体数据。

## 实际消息解析验证

我们已经成功验证了协议编解码器能够正确解析您提供的实际消息数据：

### 测试消息1 - 升级信息获取
```
消息类型: 303 (0x012F)
消息序号: 13
商户编号: tenant_zsitvq
产品编号: (空)
应用编号: 055ddbfddc9448c9bd
消息体: 包含"升级信息：获取成功"等中文内容
```

### 测试消息2 - 下载完成
```
消息类型: 303 (0x012F)
消息序号: 14
商户编号: tenant_zsitvq
产品编号: (空)
应用编号: 055ddbfddc9448c9bd
消息体: 包含"下载：云打印升级包：完成"等中文内容
```

### 测试消息3 - 安装成功
```
消息类型: 303 (0x012F)
消息序号: 15
商户编号: tenant_zsitvq
产品编号: (空)
应用编号: 055ddbfddc9448c9bd
消息体: 包含"安装：云打印升级包：成功"等中文内容
```

### 测试消息4 - 复杂TLV消息体
```
消息类型: 304 (0x0130)
消息序号: 16
商户编号: tenant_zsitvq
产品编号: (空)
应用编号: 055ddbfddc9448c9bd
消息体长度: 202字节，包含复杂的TLV结构数据
```

所有测试消息都能正确解析，验证了编解码器的正确性和可靠性。
