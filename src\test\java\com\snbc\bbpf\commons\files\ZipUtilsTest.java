/*
 * 版权所有 2009-2023 山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.files;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.io.File;

/**
 * ZIP文件工具类测试类
 *
 * <AUTHOR>
 * @date 2023/12/2 14:55
 */
public class ZipUtilsTest {

    private final String sourcZipFile = "zipUtilsFile/idea_settings.zip";
    private final String errorTypeFile = "zipUtilsFile/test.7z";

    private final String destDir = "zipUtilsFile";

    private final String sourceDirPath = "zipUtilsFile/idea_settings";

    private final String destZipFilePath = "zipUtilsFile/new.zip";

    private final String destZipFileTypeError = "zipUtilsFile/new.rar";

    private static String filePathPrefix = "";


    /**
     * 尝试从 resource 中读取文件信息
     *
     * @return .
     */
    @BeforeAll
    public static void findFileInputStream() {
        filePathPrefix = ZipUtilsTest.class.getClassLoader().getResource("").getPath();
        System.out.println(filePathPrefix);
    }

    @Test
    @DisplayName("解压源文件地址为空")
    @Tags({
            @Tag("@id:79"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/12/2")
    })
    void testDecompress_zipFilePathBlank() {
        IllegalArgumentException illegalArgumentException = Assertions.assertThrows(IllegalArgumentException.class, () -> ZipUtils.decompress("", destDir));
        Assertions.assertEquals("The Source Zip File Path is Blank",illegalArgumentException.getLocalizedMessage());
    }

    @Test
    @DisplayName("解压目的地址为空")
    @Tags({
            @Tag("@id:79"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/12/2")
    })
    void testDecompress_destDirBlank() {
        IllegalArgumentException illegalArgumentException = Assertions.assertThrows(IllegalArgumentException.class, () -> ZipUtils.decompress(sourcZipFile, ""));
        Assertions.assertEquals("The Destination Directory is Blank",illegalArgumentException.getLocalizedMessage());
    }

    @Test
    @DisplayName("待解压文件后缀名错误")
    @Tags({
            @Tag("@id:79"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/12/2")
    })
    void testDecompress_zipFileTypeError() {

        IllegalArgumentException illegalArgumentException = Assertions.assertThrows(IllegalArgumentException.class,
                () -> ZipUtils.decompress(filePathPrefix+ errorTypeFile, filePathPrefix + destDir));
        Assertions.assertEquals("The Source File Suffix Name is Error ,Only Support .zip Type File",illegalArgumentException.getLocalizedMessage());
    }

    @Test
    @DisplayName("解压目的地址不存在")
    @Tags({
            @Tag("@id:79"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/12/2")
    })
    void testDecompress_destDirNotExists() {
        String destDirError = "/zipUtilsFileError";
        IllegalArgumentException illegalArgumentException = Assertions.assertThrows(IllegalArgumentException.class,
                () -> ZipUtils.decompress(filePathPrefix + sourcZipFile, filePathPrefix + destDirError));
        Assertions.assertEquals("The Destination Directory is Illegal",illegalArgumentException.getLocalizedMessage());
    }

    @Test
    @DisplayName("解压成功")
    @Tags({
            @Tag("@id:79"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/12/2")
    })
    void testDecompress_success() {
        ZipUtils.decompress(filePathPrefix + sourcZipFile, filePathPrefix + destDir);
        File file = new File(filePathPrefix + sourceDirPath);
        Assertions.assertEquals(true,file.exists());
    }

    /**************************************************************************************************************************************************************/

    @Test
    @DisplayName("待压缩目录为空")
    @Tags({
            @Tag("@id:79"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/12/2")
    })
    void testCompress_sourceFilePathBlank() {
        IllegalArgumentException illegalArgumentException = Assertions.assertThrows(IllegalArgumentException.class, () -> ZipUtils.compress(
                "", destZipFilePath,9));
        Assertions.assertEquals("The Source File or Directory is Blank",illegalArgumentException.getLocalizedMessage());
    }

    @Test
    @DisplayName("压缩目标文件路径为空")
    @Tags({
            @Tag("@id:79"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/12/2")
    })
    void testCompress_destFilePathBlank() {
        IllegalArgumentException illegalArgumentException = Assertions.assertThrows(IllegalArgumentException.class, () -> ZipUtils.compress(
                sourceDirPath, "",9));
        Assertions.assertEquals("The Destination File Path is Blank",illegalArgumentException.getLocalizedMessage());
    }

    @Test
    @DisplayName("压缩目标文件后缀名错误")
    @Tags({
            @Tag("@id:79"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/12/2")
    })
    void testCompress_destFileTypeError() {
        IllegalArgumentException illegalArgumentException = Assertions.assertThrows(IllegalArgumentException.class, () -> ZipUtils.compress(
                sourceDirPath, destZipFileTypeError,9));
        Assertions.assertEquals("The Destination File Suffix Name is Error ,Only Support .zip Type File",illegalArgumentException.getLocalizedMessage());
    }

    @Test
    @DisplayName("待压缩目录不存在")
    @Tags({
            @Tag("@id:79"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/12/2")
    })
    void testCompress_sourcFileNotExist() {
        String sourceDirPathNotExists = "/zipUtilsFile/noExists";
        IllegalArgumentException illegalArgumentException = Assertions.assertThrows(IllegalArgumentException.class, () -> ZipUtils.compress(
                sourceDirPathNotExists, destZipFilePath,9));
        Assertions.assertEquals("The Source File or Directory is Not Exists",illegalArgumentException.getLocalizedMessage());
    }

    @Test
    @DisplayName("压缩等级超出范围，正常压缩")
    @Tags({
            @Tag("@id:79"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/12/2")
    })
    void testCompress_zipLevelOutOfRange() {
        ZipUtils.compress(filePathPrefix + sourceDirPath,filePathPrefix + destZipFilePath,9);
        File file = new File(filePathPrefix + destZipFilePath);
        Assertions.assertEquals(true,file.exists());
    }

    @Test
    @DisplayName("压缩文件成功")
    @Tags({
            @Tag("@id:79"),
            @Tag("@author:zhangyegong"),
            @Tag("@date:2023/12/2")
    })
    void testCompress_success() {
        ZipUtils.compress(filePathPrefix + sourceDirPath,filePathPrefix + destZipFilePath,9);
        File file = new File(filePathPrefix + destZipFilePath);
        Assertions.assertEquals(true,file.exists());
    }


}
