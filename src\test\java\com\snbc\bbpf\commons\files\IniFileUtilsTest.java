package com.snbc.bbpf.commons.files;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

/**
 * 类描述:   测试IniFileUtils
 *
 * <AUTHOR>
 * 创建时间:  [2023/9/1 15:02]
 */
public class IniFileUtilsTest {
    private IniFileUtils iniFileUtils;

    /**
     * 获取文件路径
     *
     * @param filePath
     * @return
     */
    public static String findFile(String filePath) {
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        File file = new File(classLoader.getResource(filePath).getFile());
        if(!file.exists()){
            throw new IllegalArgumentException("Resource not found: " + filePath);
        }
        return file.getAbsolutePath();
    }

    /**
     * 在每个测试方法执行前执行的方法，用于初始化测试环境
     */
    @Test
    @DisplayName("在每个测试方法执行前执行的方法，用于初始化测试环境")
    @Tags({
            @Tag("@id:74"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/11/7")
    })
    @BeforeEach
    public void setup() {
        iniFileUtils = new IniFileUtils(findFile("files/z.ini"));
        iniFileUtils.setValue("Section1", "Key1", "NewValue");
        iniFileUtils.setValue("Section2", "Key2", "NewValue2");
        iniFileUtils.setValue("Section2", "Key2", "NewValue2");
        iniFileUtils.save(findFile("files/z.ini"));
    }

    @Test
    @DisplayName("测试获取指定分组和键的值")
    @Tags({
            @Tag("@id:74"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/30")
    })
    public void testGetValue() {
        String value = iniFileUtils.getValue("Section1", "Key1");
        Assertions.assertEquals("NewValue", value);
    }

    @Test
    @DisplayName("测试设置指定分组和键的值")
    @Tags({
            @Tag("@id:74"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/30")
    })
    public void testSetValue() {
        iniFileUtils.setValue("Section1", "Key1", "NewValue");
        String value = iniFileUtils.getValue("Section1", "Key1");
        Assertions.assertEquals("NewValue", value);
    }

    @Test
    @DisplayName("测试删除指定分组和键的值")
    @Tags({
            @Tag("@id:74"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/30")
    })
    public void testRemoveValue() {
        iniFileUtils.removeValue("Section1", "Key1");
        String value = iniFileUtils.getValue("Section1", "Key1");
        Assertions.assertNull(value);
    }

    @Test
    @DisplayName("测试保存修改后的 INI 文件")
    @Tags({
            @Tag("@id:74"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/30")
    })

    public void testSave() {
        iniFileUtils.setValue("Section2", "Key2", "Value2");
        iniFileUtils.save(findFile("files/config_updated.ini"));

        IniFileUtils updatedIniFileUtils = new IniFileUtils(findFile("files/config_updated.ini"));


        String value = updatedIniFileUtils.getValue("Section2", "Key2");
        Assertions.assertEquals("Value2", value);
    }

    @Test
    @DisplayName("modifyValue INI 文件")
    @Tags({
            @Tag("@id:74"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/30")
    })
    public void testModifyValue() {
        iniFileUtils.modifyValue("Key1", "NewValue");
        String value1 = iniFileUtils.getValue("Section1", "Key1");
        String value2 = iniFileUtils.getValue("Section2", "Key2");

        Assertions.assertEquals("NewValue", value1);
        Assertions.assertEquals("NewValue2", value2);
    }

    /**
     * 测试删除指定分组及其下的所有键值对
     */
    @Test
    @DisplayName("测试删除指定分组及其下的所有键值对")
    @Tags({
            @Tag("@id:74"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/30")
    })
    public void testRemoveSection() {
        iniFileUtils.removeSection("^Section[12]$");

        String value1 = iniFileUtils.getValue("Section1", "Key1");
        String value2 = iniFileUtils.getValue("Section1", "Key2");
        String value3 = iniFileUtils.getValue("Section2", "Key1");
        String value4 = iniFileUtils.getValue("Section2", "Key2");

        Assertions.assertNull(value1);
        Assertions.assertNull(value2);
        Assertions.assertNull(value3);
        Assertions.assertNull(value4);
    }

    /**
     * 测试修改指定分组的键值对（存在的分组）
     */
    @Test
    @DisplayName("测试修改指定分组的键值对（存在的分组）")
    @Tags({
            @Tag("@id:74"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/30")
    })
    public void testModifySectionWithValues_ExistingSection() {
        Map<String, String> values = new HashMap<>();
        values.put("Key1", "NewValue1");
        values.put("Key2", "NewValue2");

        iniFileUtils.modifySectionWithValues("Section1", values);

        String value1 = iniFileUtils.getValue("Section1", "Key1");
        String value2 = iniFileUtils.getValue("Section1", "Key2");

        Assertions.assertEquals("NewValue1", value1);
        Assertions.assertEquals("NewValue2", value2);
    }

    /**
     * 测试修改指定分组的键值对（新增分组）
     */
    @Test
    @DisplayName("测试修改指定分组的键值对（新增分组）")
    @Tags({
            @Tag("@id:74"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/30")
    })
    public void testModifySectionWithValues_NewSection() {
        Map<String, String> values = new HashMap<>();
        values.put("Key3", "Value3");
        values.put("Key4", "Value4");

        iniFileUtils.modifySectionWithValues("Section3", values);

        String value3 = iniFileUtils.getValue("Section3", "Key3");
        String value4 = iniFileUtils.getValue("Section3", "Key4");

        Assertions.assertEquals("Value3", value3);
        Assertions.assertEquals("Value4", value4);
    }

    /**
     * 测试删除指定键（指定分组）
     */
    @Test
    @DisplayName(" 根据正则表达式匹配的键和分组查找对应的值 测试删除指定键（指定分组）")
    @Tags({
            @Tag("@id:74"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/30")
    })
    public void testRemoveKey_WithSection() {
        iniFileUtils.removeKey("Key1", "Section1");

        String value1 = iniFileUtils.getValue("Section1", "Key1");

        Assertions.assertNull(value1);
    }


    /**
     * 测试删除指定键（未指定分组）
     */
    @Test
    @DisplayName("测试删除指定键（未指定分组）")
    @Tags({
            @Tag("@id:74"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/10/30")
    })
    public void testRemoveKey_WithoutSection() {
        iniFileUtils.removeKey("Key1", null);

        String value1Section1 = iniFileUtils.getValue("Section1", "Key1");
        String value1Section2 = iniFileUtils.getValue("Section2", "Key1");

        Assertions.assertNull(value1Section1);
        Assertions.assertNull(value1Section2);

    }

}
