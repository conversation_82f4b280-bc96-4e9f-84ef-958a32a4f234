/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.dates;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 对Date进行时间计算  单元测试类
 * <p>计算当前时间一定时间（天，小时，分钟，秒，毫秒）之前或之后的时间</p>
 * <p>计算两个时间之间间隔时差（天，小时，分钟，秒，毫秒）<p>
 * <p>计算当前时间一定天数之前或之后的00点00分00秒或23点59分59秒的时间</p>
 * <p>计算当前时间所在当前年/季/月/周第一天的起始时间和最后一天的起始时间，可区分00点00分00秒或23点59分59秒</p>
 * <p>计算当前时间的（天，小时，分钟，秒，毫秒）数</p>
 * <p>根据（天，小时，分钟，秒，毫秒）数转换成时间</p>
 *
 * <AUTHOR>
 * @module 日期处理
 * @date 2023-05-16 13:04
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class DateUtilsTest {

    Date date;
    Date targetDate;

    @BeforeEach
    void setUp() {
        ZonedDateTime zonedDateTime = ZonedDateTime.of(2023, 5, 16, 17, 18, 19, 20000000, ZoneId.systemDefault());
        ZonedDateTime targetZonedDateTime = ZonedDateTime.of(2024, 5, 16, 17, 18, 19, 20000000, ZoneId.systemDefault());
        date = Timestamp.from(zonedDateTime.toInstant());
        targetDate = Timestamp.from(targetZonedDateTime.toInstant());
    }

    @Test
    @DisplayName("测试Date的加任意年月日时分秒毫秒")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testPlus() {
        final Date plusReslut = DateUtils.plus(date, 1, 1, 1, 1, 1, 1, 1);
        assertEquals(2024, DateUtils.year(plusReslut));
        assertEquals(6, DateUtils.month(plusReslut));
        assertEquals(17, DateUtils.day(plusReslut));
        assertEquals(18, DateUtils.hour(plusReslut));
        assertEquals(19, DateUtils.minute(plusReslut));
        assertEquals(20, DateUtils.second(plusReslut));
        assertEquals(21, DateUtils.millisecond(plusReslut));
    }

    @Test
    @DisplayName("测试Date的diff")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testDiff() {
        assertEquals(366, DateUtils.diff(date, targetDate, TimeUnit.DAYS));
        assertEquals(8784, DateUtils.diff(date, targetDate, TimeUnit.HOURS));
        assertEquals(527040, DateUtils.diff(date, targetDate, TimeUnit.MINUTES));
    }


    @Test
    @DisplayName("测试Date的加任意年")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void plusYear() {
        assertEquals(2024, DateUtils.year(DateUtils.plusYears(date, 1)));
    }

    @Test
    @DisplayName("测试Date的加任意月")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void plusMonth() {
        assertEquals(6, DateUtils.month(DateUtils.plusMonths(date, 1)));
    }

    @Test
    @DisplayName("测试Date的加任意日")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void plusDays() {
        assertEquals(17, DateUtils.day(DateUtils.plusDays(date, 1)));
    }


    @Test
    @DisplayName("测试Date的加任意小时")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testPlusHours() {
        assertEquals(18, DateUtils.hour(DateUtils.plusHours(date, 1)));
    }

    @Test
    @DisplayName("测试Date的加任意分钟")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testPlusMinutes() {
        assertEquals(19, DateUtils.minute(DateUtils.plusMinutes(date, 1)));
    }

    @Test
    @DisplayName("测试Date的加任意秒")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testPlusSeconds() {
        assertEquals(20, DateUtils.second(DateUtils.plusSeconds(date, 1)));
    }

    @Test
    @DisplayName("测试Date的加任意毫秒")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testPlusMilliseconds() {
        assertEquals(21, DateUtils.millisecond(DateUtils.plusMilliseconds(date, 1)));
    }


    @Test
    @DisplayName("获取Date的年份")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testYear() {
        assertEquals(2023, DateUtils.year(date));
    }

    @Test
    @DisplayName("获取Date的月份")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testMonth() {
        assertEquals(5, DateUtils.month(date));
    }

    @Test
    @DisplayName("获取Date的天数")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testDay() {
        assertEquals(16, DateUtils.day(date));
    }

    @Test
    @DisplayName("获取Date的小时")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testHour() {
        assertEquals(17, DateUtils.hour(date));
    }

    @Test
    @DisplayName("获取Date的分钟")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testMinute() {
        assertEquals(18, DateUtils.minute(date));
    }

    @Test
    @DisplayName("获取Date的秒")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testSecond() {
        assertEquals(19, DateUtils.second(date));
    }

    @Test
    @DisplayName("获取Date的毫秒")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void testMillisecond() {
        assertEquals(20, DateUtils.millisecond(date));
    }


    @Test
    @DisplayName("获取Date的starOfDay")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void starOfDay() {
        final Date reslut = DateUtils.starOfDay(date);
        assertEquals(0, DateUtils.hour(reslut));
        assertEquals(0, DateUtils.minute(reslut));
        assertEquals(0, DateUtils.second(reslut));
        assertEquals(0, DateUtils.millisecond(reslut));
    }

    @Test
    @DisplayName("获取Date的endOfDay")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void endOfDay() {
        final Date reslut = DateUtils.endOfDay(date);
        assertEquals(23, DateUtils.hour(reslut));
        assertEquals(59, DateUtils.minute(reslut));
        assertEquals(59, DateUtils.second(reslut));
    }

    @Test
    @DisplayName("获取Date的firstDayOfYear")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void firstDayOfYear() {
        final Date reslut = DateUtils.firstDayOfYear(date);
        assertEquals(2023, DateUtils.year(reslut));
        assertEquals(1, DateUtils.month(reslut));
        assertEquals(1, DateUtils.day(reslut));
        assertEquals(0, DateUtils.hour(reslut));
        assertEquals(0, DateUtils.minute(reslut));
        assertEquals(0, DateUtils.second(reslut));
    }

    @Test
    @DisplayName("获取Date的firstDayOfYearWithOption")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void firstDayOfYear_with_option() {
        final Date reslut = DateUtils.firstDayOfYear(date, false);
        assertEquals(2023, DateUtils.year(reslut));
        assertEquals(1, DateUtils.month(reslut));
        assertEquals(1, DateUtils.day(reslut));
        assertEquals(23, DateUtils.hour(reslut));
        assertEquals(59, DateUtils.minute(reslut));
        assertEquals(59, DateUtils.second(reslut));
    }

    @Test
    @DisplayName("获取Date的lastDayOfYear")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void lastDayOfYear() {
        final Date reslut = DateUtils.lastDayOfYear(date);
        assertEquals(2023, DateUtils.year(reslut));
        assertEquals(12, DateUtils.month(reslut));
        assertEquals(31, DateUtils.day(reslut));
        assertEquals(0, DateUtils.hour(reslut));
        assertEquals(0, DateUtils.minute(reslut));
        assertEquals(0, DateUtils.second(reslut));
    }

    @Test
    @DisplayName("获取Date的lastDayOfYearWithOption")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void lastDayOfYear_with_option() {
        final Date reslut = DateUtils.lastDayOfYear(date, false);
        assertEquals(2023, DateUtils.year(reslut));
        assertEquals(12, DateUtils.month(reslut));
        assertEquals(31, DateUtils.day(reslut));
        assertEquals(23, DateUtils.hour(reslut));
        assertEquals(59, DateUtils.minute(reslut));
        assertEquals(59, DateUtils.second(reslut));
    }

    @Test
    @DisplayName("获取Date的firstDayOfMonth")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void firstDayOfMonth() {
        final Date reslut = DateUtils.firstDayOfMonth(date);
        assertEquals(2023, DateUtils.year(reslut));
        assertEquals(5, DateUtils.month(reslut));
        assertEquals(1, DateUtils.day(reslut));
        assertEquals(0, DateUtils.hour(reslut));
        assertEquals(0, DateUtils.minute(reslut));
        assertEquals(0, DateUtils.second(reslut));
    }

    @Test
    @DisplayName("获取Date的firstDayOfMonthWithOption")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void firstDayOfMonth_with_option() {
        final Date reslut = DateUtils.firstDayOfMonth(date, false);
        assertEquals(2023, DateUtils.year(reslut));
        assertEquals(5, DateUtils.month(reslut));
        assertEquals(1, DateUtils.day(reslut));
        assertEquals(23, DateUtils.hour(reslut));
        assertEquals(59, DateUtils.minute(reslut));
        assertEquals(59, DateUtils.second(reslut));
    }

    @Test
    @DisplayName("获取Date的lastDayOfMonth")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void lastDayOfMonth() {
        final Date reslut = DateUtils.lastDayOfMonth(date);
        assertEquals(2023, DateUtils.year(reslut));
        assertEquals(5, DateUtils.month(reslut));
        assertEquals(31, DateUtils.day(reslut));
        assertEquals(0, DateUtils.hour(reslut));
        assertEquals(0, DateUtils.minute(reslut));
        assertEquals(0, DateUtils.second(reslut));

    }

    @Test
    @DisplayName("获取Date的lastDayOfMonthWithOption")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void lastDayOfMonth_with_option() {
        final Date reslut = DateUtils.lastDayOfMonth(date, false);
        assertEquals(2023, DateUtils.year(reslut));
        assertEquals(5, DateUtils.month(reslut));
        assertEquals(31, DateUtils.day(reslut));
        assertEquals(23, DateUtils.hour(reslut));
        assertEquals(59, DateUtils.minute(reslut));
        assertEquals(59, DateUtils.second(reslut));
    }

    @Test
    @DisplayName("获取Date的ofDate")
    @Tags({
            @Tag("@id:22"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/5/16")
    })
    void ofDate() {
        final Date result = DateUtils.ofDate(2023, 5, 16, 17, 18, 19, 20000000);
        assertEquals(2023, DateUtils.year(result));
        assertEquals(5, DateUtils.month(result));
        assertEquals(16, DateUtils.day(result));
        assertEquals(17, DateUtils.hour(result));
        assertEquals(18, DateUtils.minute(result));
        assertEquals(19, DateUtils.second(result));
        assertEquals(20, DateUtils.millisecond(result));
    }

    @Test
    @DisplayName("判断时间是否处于start和end时间之间")
    @Tags({
            @Tag("@id:23"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/4/21")
    })
    void test_isEffectiveDate(){
        Date nowDate = DateUtils.ofDate(2023, 5, 16, 17, 18, 19, 20000000);
        Date startDate = DateUtils.ofDate(2023, 5, 16, 16, 18, 19, 20000000);
        Date endDate = DateUtils.ofDate(2023, 5, 16, 19, 18, 19, 20000000);
        assertTrue(DateUtils.isEffectiveDate(nowDate, startDate, endDate));
        startDate = DateUtils.ofDate(2023, 5, 16, 19, 18, 19, 20000000);
        endDate = DateUtils.ofDate(2023, 5, 16, 20, 18, 19, 20000000);
        assertFalse(DateUtils.isEffectiveDate(nowDate,startDate,endDate));
    }

    @Test
    @DisplayName("Date转换为LocalDate")
    @Tags({
            @Tag("@id:20"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/4/21")
    })
    void test_toLocalDate(){
        Date nowDate = DateUtils.ofDate(2023, 5, 16, 17, 18, 19, 20000000);
        LocalDate loacalDate = DateUtils.toLoacalDate(nowDate);
        int year = loacalDate.getYear();
        int monthValue = loacalDate.getMonthValue();
        int dayOfMonth = loacalDate.getDayOfMonth();
        assertEquals(year,2023);
        assertEquals(monthValue,5);
        assertEquals(dayOfMonth,16);
    }

    @Test
    @DisplayName("Date转换为LocalDateTime")
    @Tags({
            @Tag("@id:20"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/4/21")
    })
    void test_toLocalDateTime(){
        Date nowDate = DateUtils.ofDate(2023, 5, 16, 17, 18, 19, 20000000);
        LocalDateTime localDateTime = DateUtils.toLoacalDateTime(nowDate);
        int year = localDateTime.getYear();
        int monthValue = localDateTime.getMonthValue();
        int dayOfMonth = localDateTime.getDayOfMonth();
        int hour = localDateTime.getHour();
        int minute = localDateTime.getMinute();
        int second = localDateTime.getSecond();
        assertEquals(year,2023);
        assertEquals(monthValue,5);
        assertEquals(dayOfMonth,16);
        assertEquals(hour,17);
        assertEquals(minute,18);
        assertEquals(second,19);
    }

    @Test
    @DisplayName("Date转换为ZonedDateTime")
    @Tags({
            @Tag("@id:20"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2023/4/21")
    })
    void test_toZonedDateTime(){
        Date nowDate = DateUtils.ofDate(2023, 5, 16, 17, 18, 19, 20000000);
        ZonedDateTime zonedDateTime = DateUtils.toZonedDateTime(nowDate);
        int year = zonedDateTime.getYear();
        int monthValue = zonedDateTime.getMonthValue();
        int dayOfMonth = zonedDateTime.getDayOfMonth();
        int hour = zonedDateTime.getHour();
        int minute = zonedDateTime.getMinute();
        int second = zonedDateTime.getSecond();
        assertEquals(year,2023);
        assertEquals(monthValue,5);
        assertEquals(dayOfMonth,16);
        assertEquals(hour,17);
        assertEquals(minute,18);
        assertEquals(second,19);
    }

    @Test
    @DisplayName("获取两个时间之间的日期")
    @Tags({
            @Tag("@id:104"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/6/20")
    })
    void testGetBetweenDate_tenDay(){
        Date start = DateUtils.ofDate(2024,6,21,0,0,0,0);
        Date end = DateUtils.ofDate(2024,7,1,0,0,0,0);
        List<Date> betweenDate = DateUtils.getBetweenDate(start, end, ChronoUnit.DAYS, 1);
        assertEquals(betweenDate.size(),11);
    }

    @Test
    @DisplayName("获取两个时间之间的日期,不满足条件测试")
    @Tags({
            @Tag("@id:104"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/6/20")
    })
    void testGetBetweenDate_parmError(){
        Date start = DateUtils.ofDate(2024,6,21,0,0,0,0);
        Date end = DateUtils.ofDate(2024,7,1,0,0,0,0);

        assertThrows(IllegalArgumentException.class,
                () -> DateUtils.getBetweenDate(null, end, ChronoUnit.DAYS, 1));
        assertThrows(IllegalArgumentException.class,
                () -> DateUtils.getBetweenDate(start, null, ChronoUnit.DAYS, 1));
        assertThrows(IllegalArgumentException.class,
                () -> DateUtils.getBetweenDate(start, end, null, 0));
        assertThrows(IllegalArgumentException.class,
                () -> DateUtils.getBetweenDate(start, end, ChronoUnit.DAYS, 0));
    }

    @Test
    @DisplayName("获取某个时间之前或之后的时间")
    @Tags({
            @Tag("@id:104"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/6/20")
    })
    void testGetNextDates_tenDay(){
        Date start = DateUtils.ofDate(2024,6,21,0,0,0,0);
        List<Date> nextAfterDates = DateUtils.getNextDates(start, ChronoUnit.DAYS, 10, false);
        assertEquals(nextAfterDates.size(),10);
        List<Date> nextBeforDates = DateUtils.getNextDates(start, ChronoUnit.DAYS, -10, false);
        assertEquals(nextBeforDates.size(),10);
    }

    @Test
    @DisplayName("获取某个时间之前或之后的时间,错误参数")
    @Tags({
            @Tag("@id:104"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/6/20")
    })
    void testGetNextDates_parmError(){
        Date start = DateUtils.ofDate(2024,6,21,0,0,0,0);
        assertThrows(IllegalArgumentException.class,
                () -> DateUtils.getNextDates(null, ChronoUnit.DAYS, 10, false));
        assertThrows(IllegalArgumentException.class,
                () -> DateUtils.getNextDates(start, null, 10, false));
    }

    @Test
    @DisplayName("获取两个时间之间的日期,不包括开始和结束时间")
    @Tags({
            @Tag("@id:104"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/6/20")
    })
    void testGetBetweenDate_noIncludeStartAndEnd(){
        Date start = DateUtils.ofDate(2024,6,21,0,0,0,0);
        Date end = DateUtils.ofDate(2024,7,1,0,0,0,0);
        List<Date> betweenDate = DateUtils.getBetweenDate(start, end, ChronoUnit.DAYS, 1,false,false);
        assertEquals(betweenDate.size(),9);
        assertNotEquals(betweenDate.get(0),start);
        assertNotEquals(betweenDate.get(betweenDate.size()-1),end);
    }

    @Test
    @DisplayName("获取两个时间之间的日期,倒序列表")
    @Tags({
            @Tag("@id:104"),
            @Tag("@author:wangxiaoji"),
            @Tag("@date:2024/6/20")
    })
    void testGetBetweenDate_reverse(){
        Date start = DateUtils.ofDate(2024,6,21,0,0,0,0);
        Date end = DateUtils.ofDate(2024,7,1,0,0,0,0);
        List<Date> betweenDate = DateUtils.getBetweenDate(start, end, ChronoUnit.DAYS, -1);
        assertEquals(betweenDate.size(),11);
        assertEquals(betweenDate.get(0),end);
        assertEquals(betweenDate.get(betweenDate.size()-1),start);
    }

}