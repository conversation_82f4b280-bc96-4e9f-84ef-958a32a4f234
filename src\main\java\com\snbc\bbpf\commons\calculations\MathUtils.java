/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.calculations;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 【精度计算】加减乘除法精度运算
 * <p>两个double加减乘除进行精度计算</p>
 * <p>两个double加减乘除并指定小数点保留位数，同时指定四舍五入的方式（ROUND_HALF_UP或ROUND_UP）</p>
 * <p>其中对于除法，存在除不尽的情况，则默认保留10位小数点，向上取整</p>
 *
 * <AUTHOR>
 * @module
 * @date 2023/6/9 22:52
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class MathUtils {

    private static final int DEF_SCALE = 10;

    private MathUtils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 加法运算，默认最多保留到小数点后10位，以后的数字四舍五入
     * <p>
     *     MathUtils.add(1, 1.1) = 2.1
     *     MathUtils.add(-1.111, -1.135) = -2.246
     *     MathUtils.add(0, 1999999999) = 1999999999
     *     MathUtils.add(1.00000000000000, 1.55555555555555555) = 2.5555555556
     * </p>
     * @param v1 第一个加数
     * @param v2 第二个加数
     * @return double 加法结果（保留10位小数）
     * @since 1.1.0
     * <AUTHOR>
     * @date 2023/7/5
     */
    public static double add(double v1, double v2) {

        return addSetScale(v1, v2, DEF_SCALE, RoundingMode.HALF_UP);
    }

    /**
     * 加法运算后在进行精度运算
     * <p>
     *     MathUtils.addSetScale(0, 0, 0, RoundingMode.HALF_UP) = 0
     *     MathUtils.addSetScale(-1.11111, -1.13544, 4, RoundingMode.HALF_UP) = -2.2466
     *     MathUtils.addSetScale(321654789.321654789, 0, 6, RoundingMode.UP) = 321654789.321655
     *     MathUtils.addSetScale(321654789.321654789, 0, -6, RoundingMode.UP) >= IllegalArgumentException
     *     MathUtils.addSetScale(321654789.321654789, 0, 0, RoundingMode.DOWN) >= IllegalArgumentException
     * </p>
     * @param v1 第一个加数
     * @param v2 第二个加数
     * @param newScale 结果保留的小数位数（非负）
     * @param roundingMode 舍入模式（支持UP或HALF_UP）
     * @return double 带精度的加法结果
     * @since 1.1.0
     * <AUTHOR>
     * @date 2023/7/5
     */
    public static double addSetScale(double v1, double v2, int newScale, RoundingMode roundingMode) {

        return setScale(BigDecimal.valueOf(v1).add(BigDecimal.valueOf(v2)), newScale, roundingMode);
    }

    /**
     * 减法运算，默认最多保留到小数点后10位，以后的数字四舍五入
     * <p>
     *     MathUtils.sub(0, 0) = 0
     *     MathUtils.sub(-1.111, -1.135) = 0.024
     *     MathUtils.sub(0, 1999999999) = -1999999999
     *     MathUtils.sub(100, 0.00000000001) = 100
     * </p>
     * @param v1 被减数
     * @param v2 减数
     * @return double 减法结果（保留10位小数）
     * @since 1.1.0
     * <AUTHOR>
     * @date 2023/7/5
     */
    public static double sub(double v1, double v2) {

        return subSetScale(v1, v2, DEF_SCALE, RoundingMode.HALF_UP);
    }

    /**
     * 减法运算后在进行精度运算
     * <p>
     *     MathUtils.subSetScale(1.15, 1.1, 2, RoundingMode.HALF_UP) = 0.05
     *     MathUtils.subSetScale(0, 0, 0, RoundingMode.HALF_UP) = 0
     *     MathUtils.subSetScale(-99999, 1999999999, 0, RoundingMode.UP) = -2000099998
     *     MathUtils.subSetScale(321654789.321654789, 0, -6, RoundingMode.UP) >= IllegalArgumentException
     *     MathUtils.subSetScale(321654789.321654789, 0, 0, RoundingMode.DOWN) >= IllegalArgumentException
     * </p>
     * @param v1 被减数
     * @param v2 减数
     * @param newScale 精度
     * @param roundingMode 精确计算模式(支持RoundingMode.UP或RoundingMode.HALF_UP)
     * @return double 带精度的减法结果
     * @since 1.1.0
     * <AUTHOR>
     * @date 2023/7/5
     */
    public static double subSetScale(double v1, double v2, int newScale, RoundingMode roundingMode) {

        return setScale(BigDecimal.valueOf(v1).subtract(BigDecimal.valueOf(v2)), newScale, roundingMode);
    }

    /**
     * 乘法运算，默认最多保留到小数点后10位，以后的数字四舍五入
     * <p>
     *     MathUtils.mul(0, 0) = 0
     *     MathUtils.mul(1, 1.1) = 1.1
     *     MathUtils.mul(-99999, 199) = -19899801
     *     MathUtils.mul(222222.2222222222222222222222, 1) = 222222.2222222222
     * </p>
     * @param v1 第一个乘数
     * @param v2 第二个乘数
     * @return double 乘法结果（保留10位小数）
     * @since 1.1.0
     * <AUTHOR>
     * @date 2023/7/5
     */
    public static double mul(double v1, double v2) {

        return mulSetScale(v1, v2, DEF_SCALE, RoundingMode.HALF_UP);
    }

    /**
     * 乘法运算后在进行精度运算
     * <p>
     *     MathUtils.mulSetScale(1.15, 1.1, 2, RoundingMode.HALF_UP) = 1.27
     *     MathUtils.mulSetScale(0, 0, 0, RoundingMode.HALF_UP) = 0
     *     MathUtils.mulSetScale(-99999, 100000, 0, RoundingMode.UP) = -9.9999E9
     *     MathUtils.mulSetScale(321654789.321654789, 0, -6, RoundingMode.UP) >= IllegalArgumentException
     *     MathUtils.mulSetScale(321654789.321654789, 0, 0, RoundingMode.DOWN) >= IllegalArgumentException
     * </p>
     * @param v1 第一个乘数
     * @param v2 第二个乘数
     * @param newScale 精度
     * @param roundingMode 精确计算模式(支持RoundingMode.UP或RoundingMode.HALF_UP)
     * @return double 带精度的乘法结果
     * @since 1.1.0
     * <AUTHOR>
     * @date 2023/7/5
     */
    public static double mulSetScale(double v1, double v2, int newScale, RoundingMode roundingMode) {

        return setScale(BigDecimal.valueOf(v1).multiply(BigDecimal.valueOf(v2)), newScale, roundingMode);
    }

    /**
     * 除法运算,若除不尽则精确到小数点以后10位，以后的数字四舍五入。
     * <p>
     *     MathUtils.div(1, 1.1) = 0.9090909091
     *     MathUtils.div(-199, 199) = -2.2466
     *     MathUtils.div(0, 1999999999) = 0
     *     MathUtils.div(321654789.321654789, 0, -6, RoundingMode.UP) >= IllegalArgumentException
     *     MathUtils.div(321654789.321654789, 0, 0, RoundingMode.DOWN) >= IllegalArgumentException
     *     MathUtils.div(0, 0) >= ArithmeticException
     * </p>
     * @param v1 被除数
     * @param v2 除数（不能为0）
     * @return double 除法结果（保留10位小数）
     * @since 1.1.0
     * <AUTHOR>
     * @date 2023/7/5
     */
    public static double div(double v1, double v2) {
        return divSetScale(v1, v2, DEF_SCALE, RoundingMode.HALF_UP);
    }

    /**
     * 除法运算后在进行精度运算
     * <p>
     *     MathUtils.divSetScale(1.15, 1.1, 2, RoundingMode.HALF_UP) = 1.05
     *     MathUtils.divSetScale(-8.888888, -2, 4, RoundingMode.HALF_UP) = 4.4444
     *     MathUtils.divSetScale(-99999, 100000, 0, RoundingMode.UP) = -1
     *     MathUtils.divSetScale(321654789.321654789, 0, -6, RoundingMode.UP) >= IllegalArgumentException
     *     MathUtils.divSetScale(321654789.321654789, 0, 0, RoundingMode.DOWN) >= IllegalArgumentException
     *     MathUtils.divSetScale(0, 0, 0, RoundingMode.HALF_UP) >= ArithmeticException
     * </p>
     * @param v1 被除数
     * @param v2 除数（不能为0）
     * @param newScale精度
     * @param roundingMode 精确计算模式(支持RoundingMode.UP上进位制或RoundingMode.HALF_UP四舍五入)
     * @return double 带精度的除法结果
     * @since 1.1.0
     * <AUTHOR>
     * @date 2023/7/5
     */
    public static double divSetScale(double v1, double v2, int newScale, RoundingMode roundingMode) {

        if (newScale < 0) {
            throw new IllegalArgumentException(
                    "The scale must be a positive integer or zero");
        }
        checkRoundingMode(roundingMode);

        return BigDecimal.valueOf(v1).divide(BigDecimal.valueOf(v2), newScale, roundingMode).doubleValue();
    }

    /**
     * 对数值进行精度处理
     * <p>
     *     MathUtils.setScale(1.15, 2, RoundingMode.HALF_UP) = 1.15
     *     MathUtils.setScale(-8.888888, 4, RoundingMode.HALF_UP) = -8.8889
     *     MathUtils.setScale(-99999, 0, RoundingMode.UP) = -99999
     *     MathUtils.setScale(321654789.321654789, -6, RoundingMode.UP) >= IllegalArgumentException
     * </p>
     * @param b 需要处理精度的数值（BigDecimal类型）
     * @param newScale 结果保留的小数位数（非负）
     * @param roundingMode 舍入模式（RoundingMode.UP上进位制或RoundingMode.HALF_UP四舍五入）
     * @return double 处理后的带精度数值
     * @since 1.1.0
     * <AUTHOR>
     * @date 2023/7/5
     */
    public static double setScale(BigDecimal b, int newScale, RoundingMode roundingMode) {

        if (newScale < 0) {
            throw new IllegalArgumentException(
                    "The scale must be a positive integer or zero");
        }
        checkRoundingMode(roundingMode);

        return b.setScale(newScale, roundingMode).doubleValue();
    }

    /**
     * 检查舍入模式是否合法（仅支持UP和HALF_UP）
     * <p>
     *     MathUtils.checkRoundingMode(RoundingMode.UP) = true
     *     MathUtils.checkRoundingMode(RoundingMode.HALF_UP) = true
     *     MathUtils.checkRoundingMode(RoundingMode.DOWN) = false
     * </p>
     * @param roundingMode 待检查的舍入模式
     * @return void 
     * @since 1.1.0
     * <AUTHOR>
     * @date 2023/7/5
     */
    private static void checkRoundingMode(RoundingMode roundingMode) {
        if (null == roundingMode) {
            throw new IllegalArgumentException("null rounding mode");
        }
        switch (roundingMode) {
            case UP:
            case HALF_UP:
                break;
            default:
                throw new IllegalArgumentException("Invalid rounding mode");
        }

    }

}
