# 精确计算 (com.snbc.bbpf.commons.calculations)

## 类概览 

| 类名 | 功能描述 |
|------|----------|
| MathUtils | 提供精确的加减乘除运算，支持指定精度和舍入模式 |
| PrecisionCalculationUtils | 提供计算百分比的功能，支持不同数字类型和舍入方式 |
| AmountTranslateUtils | 提供金额的大小写中文互转功能 |

## MathUtils - 数学运算工具类

### API列表 

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| add(double v1, double v2) | 加法运算 | v1: 被加数<br>v2: 加数 | double: 计算结果(默认精度10位，四舍五入) |
| addSetScale(double v1, double v2, int newScale, RoundingMode roundingMode) | 加法运算后进行精度运算 | v1: 被加数<br>v2: 加数<br>newScale: 精度<br>roundingMode: 舍入模式 | double: 精确计算结果 |
| sub(double v1, double v2) | 减法运算 | v1: 被减数<br>v2: 减数 | double: 计算结果(默认精度10位，四舍五入) |
| subSetScale(double v1, double v2, int newScale, RoundingMode roundingMode) | 减法运算后进行精度运算 | v1: 被减数<br>v2: 减数<br>newScale: 精度<br>roundingMode: 舍入模式 | double: 精确计算结果 |
| mul(double v1, double v2) | 乘法运算 | v1: 被乘数<br>v2: 乘数 | double: 计算结果(默认精度10位，四舍五入) |
| mulSetScale(double v1, double v2, int newScale, RoundingMode roundingMode) | 乘法运算后进行精度运算 | v1: 被乘数<br>v2: 乘数<br>newScale: 精度<br>roundingMode: 舍入模式 | double: 精确计算结果 |
| div(double v1, double v2) | 除法运算 | v1: 被除数<br>v2: 除数 | double: 计算结果(默认精度10位，四舍五入) |
| divSetScale(double v1, double v2, int newScale, RoundingMode roundingMode) | 除法运算后进行精度运算 | v1: 被除数<br>v2: 除数<br>newScale: 精度<br>roundingMode: 舍入模式 | double: 精确计算结果 |
| setScale(BigDecimal b, int newScale, RoundingMode roundingMode) | 精度计算 | b: BigDecimal值<br>newScale: 精度<br>roundingMode: 舍入模式 | double: 精确计算结果 |

### 注意事项

- 所有方法内部都使用BigDecimal进行计算，避免了浮点数精度丢失问题
- 无指定精度的方法默认使用10位精度和四舍五入(RoundingMode.HALF_UP)舍入模式
- 当精度参数(newScale)为负数时，会抛出IllegalArgumentException异常
- 不支持的舍入模式会抛出IllegalArgumentException异常
- 除法运算中，如果除数为0，会抛出ArithmeticException异常
- 计算结果可能与直接使用Java浮点运算的结果不同，因为内部使用了BigDecimal
- 建议在所有需要高精度计算的场景下使用这些方法，特别是财务计算

### 使用示例

```java
// 加法运算示例
double result1 = MathUtils.add(1, 1.1);
System.out.println("1 + 1.1 = " + result1);  // 输出: 1 + 1.1 = 2.1

// 减法运算示例
double result2 = MathUtils.sub(-1.111, -1.135);
System.out.println("-1.111 - (-1.135) = " + result2);  // 输出: -1.111 - (-1.135) = 0.024

// 乘法运算示例
double result3 = MathUtils.mul(1, 1.1);
System.out.println("1 * 1.1 = " + result3);  // 输出: 1 * 1.1 = 1.1

// 除法运算示例
double result4 = MathUtils.div(1, 1.1);
System.out.println("1 / 1.1 = " + result4);  // 输出: 1 / 1.1 = 0.9090909091

// 指定精度和舍入模式的加法运算
double result5 = MathUtils.addSetScale(1.15, 1.1, 2, RoundingMode.HALF_UP);
System.out.println("1.15 + 1.1 (精度2位,四舍五入) = " + result5);  // 输出: 1.15 + 1.1 (精度2位,四舍五入) = 2.25

// 指定精度和舍入模式的减法运算
double result6 = MathUtils.subSetScale(1.15, 1.1, 2, RoundingMode.HALF_UP);
System.out.println("1.15 - 1.1 (精度2位,四舍五入) = " + result6);  // 输出: 1.15 - 1.1 (精度2位,四舍五入) = 0.05

// 指定精度和舍入模式的乘法运算
double result7 = MathUtils.mulSetScale(1.15, 1.1, 2, RoundingMode.HALF_UP);
System.out.println("1.15 * 1.1 (精度2位,四舍五入) = " + result7);  // 输出: 1.15 * 1.1 (精度2位,四舍五入) = 1.27

// 指定精度和舍入模式的除法运算
double result8 = MathUtils.divSetScale(1.15, 1.1, 2, RoundingMode.HALF_UP);
System.out.println("1.15 / 1.1 (精度2位,四舍五入) = " + result8);  // 输出: 1.15 / 1.1 (精度2位,四舍五入) = 1.05

// 异常情况示例
try {
    // 负数精度抛出异常
    MathUtils.addSetScale(321654789.321654789, 0, -6, RoundingMode.UP);
} catch (IllegalArgumentException e) {
    System.out.println("异常: " + e.getMessage());  // 输出: 异常: The scale must be a positive integer or zero
}

try {
    // 不支持的舍入模式抛出异常
    MathUtils.addSetScale(321654789.321654789, 0, 0, RoundingMode.DOWN);
} catch (IllegalArgumentException e) {
    System.out.println("异常: " + e.getMessage());  // 输出: 异常: Invalid rounding mode
}

try {
    // 除数为0抛出异常
    MathUtils.div(1, 0);
} catch (ArithmeticException e) {
    System.out.println("异常: " + e.getMessage());  // 输出: 异常: Division by zero
}
```

## PrecisionCalculationUtils - 百分比计算工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| calculatePercentage(double number, double total, int decimalPlaces, RoundingMode roundingMode) | 计算double类型数字的百分比 | number: 分子<br>total: 分母<br>decimalPlaces: 保留小数位数<br>roundingMode: 舍入模式 | String: 百分比字符串 |
| calculatePercentage(int number, int total, int decimalPlaces, RoundingMode roundingMode) | 计算int类型数字的百分比 | number: 分子<br>total: 分母<br>decimalPlaces: 保留小数位数<br>roundingMode: 舍入模式 | String: 百分比字符串 |
| calculatePercentage(long number, long total, int decimalPlaces, RoundingMode roundingMode) | 计算long类型数字的百分比 | number: 分子<br>total: 分母<br>decimalPlaces: 保留小数位数<br>roundingMode: 舍入模式 | String: 百分比字符串 |

### 注意事项

- 返回的百分比字符串自动包含百分号(%)符号
- 内部使用BigDecimal进行计算，避免浮点数精度问题
- decimalPlaces参数指定了百分比小数点后的位数，不是结果的总精度
- 如果分母(total)为0，将抛出ArithmeticException异常
- 如果保留小数位数(decimalPlaces)为负数，将抛出IllegalArgumentException异常
- 当分子为0时，结果为"0%"或带有指定小数位的"0.00%"
- 结果根据指定的舍入模式(RoundingMode)进行舍入，不同舍入模式可能产生不同结果

### 使用示例

```java
// double类型百分比计算
String percentage1 = PrecisionCalculationUtils.calculatePercentage(25.5, 100.0, 2, RoundingMode.HALF_UP);
System.out.println("25.5/100 = " + percentage1);  // 输出: 25.5/100 = 25.50%

// int类型百分比计算
String percentage2 = PrecisionCalculationUtils.calculatePercentage(25, 100, 0, RoundingMode.HALF_UP);
System.out.println("25/100 = " + percentage2);  // 输出: 25/100 = 25%

// long类型百分比计算
String percentage3 = PrecisionCalculationUtils.calculatePercentage(250L, 1000L, 1, RoundingMode.UP);
System.out.println("250/1000 = " + percentage3);  // 输出: 250/1000 = 25.0%

// 不同精度和舍入模式示例
String percentage4 = PrecisionCalculationUtils.calculatePercentage(33, 100, 2, RoundingMode.HALF_UP);
System.out.println("33/100 (精度2位,四舍五入) = " + percentage4);  // 输出: 33/100 (精度2位,四舍五入) = 33.00%

String percentage5 = PrecisionCalculationUtils.calculatePercentage(33.33, 100, 0, RoundingMode.HALF_UP);
System.out.println("33.33/100 (精度0位,四舍五入) = " + percentage5);  // 输出: 33.33/100 (精度0位,四舍五入) = 33%

String percentage6 = PrecisionCalculationUtils.calculatePercentage(33.33, 100, 2, RoundingMode.UP);
System.out.println("33.33/100 (精度2位,向上舍入) = " + percentage6);  // 输出: 33.33/100 (精度2位,向上舍入) = 33.33%

// 异常情况示例
try {
    // 分母为0抛出异常
    PrecisionCalculationUtils.calculatePercentage(10, 0, 2, RoundingMode.HALF_UP);
} catch (ArithmeticException e) {
    System.out.println("异常: " + e.getMessage());  // 输出: 异常: Division by zero
}
```

## AmountTranslateUtils - 金额中文大小写转换工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| toUpper(int iAmount) | 将int型小写金额转换成大写的金额 | iAmount: 小写金额 | String: 大写金额 |
| toUpper(long lAmount) | 将long型小写金额转换成大写的金额 | lAmount: 小写金额 | String: 大写金额 |
| toLong(String strAmount) | 将大写金额转换为long型小写金额 | strAmount: 大写金额 | long: 小写金额 |
| toInt(String strAmount) | 将大写金额转换为int型小写金额 | strAmount: 大写金额 | int: 小写金额 |

### 注意事项

- 该工具类仅支持整数金额的转换，不支持小数部分
- 工具类只处理非负数金额，负数金额会抛出IllegalArgumentException异常
- 转换的金额单位支持范围从个位到京位(10^16)，完整单位为：个、十、百、千、万、十万、百万、千万、亿、十亿、百亿、千亿、兆、十兆、百兆、千兆、京
- int型整数最大值为2147483647（2的31次方-1），对应中文大写为"贰拾壹亿肆仟柒佰肆拾捌万叁仟陆佰肆拾柒"
- Long.MAX_VALUE为9223372036854775807（2的63次方-1），对应中文大写为"玖佰贰拾贰京叁仟叁佰柒拾贰兆零叁佰陆拾捌亿伍仟肆佰柒拾柒万伍仟捌佰零柒"
- 超过"京"单位的金额将无法正确转换，可能会抛出溢出异常
- 将大写金额转回小写时，如果格式不正确或包含无效字符，会抛出IllegalArgumentException异常
- 将大写金额转为int类型时，如果金额超出int范围，会抛出IllegalArgumentException异常
- 空字符串作为输入将抛出IllegalArgumentException异常，消息为"String is empty!"
- 中文大写金额必须使用标准的中文数字(壹、贰、叁等)和单位(拾、佰、仟等)

### 使用示例

```java
// int型金额转大写
String upper1 = AmountTranslateUtils.toUpper(123);
System.out.println("123转大写: " + upper1);  // 输出: 123转大写: 壹佰贰拾叁

// long型金额转大写
String upper2 = AmountTranslateUtils.toUpper(9876543210L);
System.out.println("9876543210转大写: " + upper2);  // 输出: 9876543210转大写: 玖拾捌亿柒仟陆佰伍拾肆万叁仟贰佰壹拾

// 大写金额转long型
long lower1 = AmountTranslateUtils.toLong("壹佰贰拾叁");
System.out.println("壹佰贰拾叁转小写: " + lower1);  // 输出: 壹佰贰拾叁转小写: 123

// 大写金额转int型
int lower2 = AmountTranslateUtils.toInt("玖仟玖佰玖拾玖");
System.out.println("玖仟玖佰玖拾玖转小写: " + lower2);  // 输出: 玖仟玖佰玖拾玖转小写: 9999

// 带单位的金额转换
String upper3 = AmountTranslateUtils.toUpper(10000);
System.out.println("10000转大写: " + upper3);  // 输出: 10000转大写: 壹万

String upper4 = AmountTranslateUtils.toUpper(100000000);
System.out.println("100000000转大写: " + upper4);  // 输出: 100000000转大写: 壹亿

// 异常情况示例
try {
    // 负数金额转换抛出异常
    AmountTranslateUtils.toUpper(-100);
} catch (IllegalArgumentException e) {
    System.out.println("异常: " + e.getMessage());  // 输出: 异常: Less than 0!
}

try {
    // 无效大写金额转换抛出异常
    AmountTranslateUtils.toLong("ABC");
} catch (IllegalArgumentException e) {
    System.out.println("异常: " + e.getMessage());  // 输出: 异常: String is invalid!
}

try {
    // 空字符串大写金额转换抛出异常
    AmountTranslateUtils.toLong("");
} catch (IllegalArgumentException e) {
    System.out.println("异常: " + e.getMessage());  // 输出: 异常: String is empty!
}

try {
    // 超出int范围的金额转换抛出异常
    AmountTranslateUtils.toInt("壹仟亿");
} catch (IllegalArgumentException e) {
    System.out.println("异常: " + e.getMessage());  // 输出: 异常: String is Overflow!
}
```