/*
 * 版权所有 2009-2023 山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.reflects;

import org.apache.commons.lang3.StringUtils;

import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 反射工具类,提供与反射相关的快捷操作方法
 * <p>1、获取对象指定属性的值</p>
 * <p>2、获取对象所有属性的值</p>
 *
 * <AUTHOR>
 * @date 2023/11/2 10:01
 * @since 1.3.0
 */
public final class ReflectUtils {

    private ReflectUtils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * <p>获取对象指定属性取值</p>
     * <p>  对入参进行非空校验，如果任意一个参数为空，则抛出异常</p>
     * <p>  如果所要求属性名称在目标对象中不存在，则返回Null</p>
     *
     * @param targetObject 对象实体
     * @param propertyName 待获取属性名 如果为空，抛出异常
     * @param clazz 属性值类型 如果为空 抛出异常
     * @return T 属性值类型 如果为空 抛出异常
     * <AUTHOR>
     * @date 2023/11/02 10:19
     * @since 1.3.0
     */
    public static  <T> T getPropertyValue(Object targetObject, String propertyName, Class<T> clazz) {
        validateArgs(targetObject, propertyName, clazz);
        return getValue(targetObject, propertyName);
    }

    /**
     * <p>获取目标对象所有属性取值</p>
     * <p> 返回结果为Map集合 key为属性名称  value为属性值</p>
     *
     * @param targetObject 目标对象
     * @return java.util.Map<java.lang.String,java.lang.Object> 结果集合
     * <AUTHOR>
     * @date 2023/11/02 11:04
     * @since 1.3.0
     */
    public static Map<String,Object> getAllPropertiesValue(Object targetObject) {
        if (null == targetObject) {
            throw new IllegalArgumentException("The Target Object is Null");
        }
        Field[] declaredFields = targetObject.getClass().getDeclaredFields();
        Map<String,Object> resultMap = new HashMap<>();
        Arrays.stream(declaredFields).forEach(field -> {
            String propertyName = field.getName();
            //增加非静态内部类属性参数值处理
            if (!"this$0".equals(propertyName)){
                Object propertyValue = getValue(targetObject, propertyName);
                resultMap.put(propertyName,propertyValue);

            }
        });
        return resultMap;
    }


    /**
     * <p>获取属性值公共方法</p>
     *
     * @param targetObject 目标对象
     * @param propertyName  属性名称
     * @return T 属性值
     * <AUTHOR>
     * @date 2023/11/02 11:05
     * @since 1.3.0
     */
    private static <T> T getValue(Object targetObject, String propertyName) {
        try {
            Field declaredField = targetObject.getClass().getDeclaredField(propertyName);
            if (null == declaredField) {
                throw new IllegalArgumentException("The Property is not Define");
            }
            PropertyDescriptor propertyDescriptor = new PropertyDescriptor(declaredField.getName(),targetObject.getClass());
            Method readMethod = propertyDescriptor.getReadMethod();
            if (null == readMethod) {
                throw new IllegalArgumentException("The Property Get Method Not Define");
            }
            readMethod.setAccessible(true);
            Object invoke = readMethod.invoke(targetObject);
            if (null!= invoke){
                return (T)invoke;
            }
            return null;
        }catch (NoSuchFieldException | IllegalAccessException | IntrospectionException | InvocationTargetException exception) {
            throw new IllegalArgumentException(exception);
        }
    }

    /**
     * <p>参数校验</p>
     *
     * @param targetObject 目标对象
     * @param propertyName 属性名称
     * @param clazz 属性值类型
     * <AUTHOR>
     * @date 2023/11/02 11:06
     * @since 1.3.0
     */
    private static void validateArgs(Object targetObject, String propertyName, Class<? extends Object> clazz) {
        if (null == targetObject) {
            throw new IllegalArgumentException("The Target Object is Null");
        }
        if (StringUtils.isBlank(propertyName)) {
            throw new IllegalArgumentException("PropertyName is Blank");
        }
        if (null == clazz) {
            throw new IllegalArgumentException("Property Value Class is Null");
        }
    }
}
