/*
 * 版权所有 2009-2023 山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.reflects;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertEquals;

 /**
 * 反射调用方法单元测试类
 *
 * <AUTHOR>
 * @module 对象模块
 * @date 2023/11/29 20:20
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
 
class ReflectCallUtilsTest {

     public static class Dog{
        private static String name = "小白";
        private static Integer age = 10;
        private static Color color = new Color("1001","2002","3003");

        public Dog() {
        }
        public Dog(String name, Integer age, Color color) {
            this.name = name;
            this.age = age;
            this.color = color;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Integer getAge() {
            return age;
        }

        public void setAge(Integer age) {
            this.age = age;
        }

        public String setNameAge(String name, Integer age) {
            setName(name);
			setAge(age);
			return "一条名叫" +     name + "狗狗已经" + age + "岁了!";
        }

        public Color getColor() {
            return color;
        }

        public String setColor(Color color) {
            this.color = color;
			return "有颜色的狗狗";
        }
    }

     public static class Color{
        private String red;
        private String green;
        private String blue;

        public Color () {

        }
        public Color(String red, String green, String blue) {
            this.red = red;
            this.green = green;
            this.blue = blue;
        }

        public String getRed() {
            return red;
        }

        public String getGreen() {
            return green;
        }

        public String getBlue() {
            return blue;
        }

        public void setRed(String red) {
            this.red = red;
        }

        public void setGreen(String green) {
            this.green = green;
        }

        public void setBlue(String blue) {
            this.blue = blue;
        }
    }

    private Color color;

    private Dog dog;

    @BeforeEach
    void setup() {
        color = new Color("1001","2002","3003");
        dog = new Dog("大黄",5,color);
    }

    @Test
    @DisplayName("类名参数空应该抛出提示参数是空的异常")
    @Tags({
            @Tag("@id:80"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/11/29")
    })
    void testCall_emptyClassNameShouldThrowException() {
		assertThrows(RuntimeException.class,
				() -> ReflectCallUtils.call("", "getName"),
				"Input parameter is empty");
    }

    @Test
    @DisplayName("方法名参数空应该抛出提示参数是空的异常")
    @Tags({
            @Tag("@id:80"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/11/29")
    })
    void testCall_emptyMethodNameShouldThrowException() {
		assertThrows(RuntimeException.class,
				() -> ReflectCallUtils.call("Dog", ""),
				"Input parameter is empty");
    }

    @Test
    @DisplayName("方法名invalid应该抛出提示参数是invalid的异常")
    @Tags({
            @Tag("@id:80"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/11/29")
    })
    void testCall_invalidMethodNameShouldThrowException() {
		assertThrows(RuntimeException.class,
				() -> ReflectCallUtils.call("Dog", "setSize"),
				"Input parameter is invalid");
    }

    @Test
    @DisplayName("调用指定类setName方法")
    @Tags({
            @Tag("@id:80"),
            @Tag("@author:jiangsheng"),
            @Tag("@date:2023/11/29")
    })
    void testCall_setName() {
        Class<?>[] paramclasses = new Class<?>[1];
        paramclasses[0] = String.class;
        ReflectCallUtils.call("com.snbc.bbpf.commons.reflects.ReflectCallUtilsTest$Dog", "setName", paramclasses, "大白");
        assertEquals("大白", ReflectCallUtils.<String>call("com.snbc.bbpf.commons.reflects.ReflectCallUtilsTest$Dog", "getName"));

    }

     @Test
     @DisplayName("调用指定类getName方法")
     @Tags({
             @Tag("@id:80"),
             @Tag("@author:jiangsheng"),
             @Tag("@date:2023/11/29")
     })
     void testCall_getName() {
         dog.setName("小黑");
         assertEquals("小黑", ReflectCallUtils.<String>call("com.snbc.bbpf.commons.reflects.ReflectCallUtilsTest$Dog", "getName"));
     }

     @Test
     @DisplayName("调用指定类getAge方法")
     @Tags({
             @Tag("@id:80"),
             @Tag("@author:jiangsheng"),
             @Tag("@date:2023/11/29")
     })
     void testCall_getAge() {
         dog.setAge(7);
         assertEquals(7, ReflectCallUtils.<Integer>call("com.snbc.bbpf.commons.reflects.ReflectCallUtilsTest$Dog", "getAge"));
     }

	@Test
	@DisplayName("调用指定类setColor方法")
	@Tags({
			@Tag("@id:80"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/11/29")
	})
	void testCall_setColor() {
        Class<?>[] paramclasses = new Class<?>[1];
        paramclasses[0] = Color.class;
		assertEquals("有颜色的狗狗", ReflectCallUtils.<String>call("com.snbc.bbpf.commons.reflects.ReflectCallUtilsTest$Dog", "setColor", paramclasses, color));
	}

     @Test
     @DisplayName("调用null对象方法应该抛出提示参数是空的异常")
     @Tags({
             @Tag("@id:80"),
             @Tag("@author:jiangsheng"),
             @Tag("@date:2023/11/29")
     })
     void testCallObject_nullObjectShouldThrowException() {
		 assertThrows(RuntimeException.class,
				 () -> ReflectCallUtils.<Integer>call((Object)null, "getAge"),
				 "Input parameter is empty");
     }

	@Test
	@DisplayName("调用指定对象getAge方法")
	@Tags({
			@Tag("@id:80"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/11/29")
	})
	void testCallObject_getAge() {
		dog.setAge(3);
		assertEquals(3, ReflectCallUtils.<Integer>call(dog, "getAge"));
	}
	
	@Test
	@DisplayName("调用指定对象setColor方法")
	@Tags({
			@Tag("@id:80"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/11/29")
	})
	void testCallObject_setColor() {
        Class<?>[] paramclasses = new Class<?>[1];
        paramclasses[0] = Color.class;
		assertEquals("有颜色的狗狗", ReflectCallUtils.<String>call(dog, "setColor", paramclasses, color));
	}

	@Test
	@DisplayName("调用指定对象setNameAge方法")
	@Tags({
			@Tag("@id:80"),
			@Tag("@author:jiangsheng"),
			@Tag("@date:2023/11/29")
	})
	void testCallObject_setNameAge() {
        Class<?>[] paramclasses = new Class<?>[2];
        paramclasses[0] = String.class;
        paramclasses[1] = Integer.class;
		assertEquals("一条名叫花花狗狗已经15岁了!", ReflectCallUtils.<String>call(dog, "setNameAge", 
			paramclasses, "花花", 15));
	}
}
