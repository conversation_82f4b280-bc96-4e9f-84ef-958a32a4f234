/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.bytes.tlv;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.HashMap;
import java.util.Map;

/**
 * TLV数据类型枚举
 * <p>定义Type-Length-Value格式中Type字段的值和对应的数据类型</p>
 * <p>支持基本类型、字符串类型、复杂类型和二进制数据的类型映射</p>
 * <p>提供默认类型映射和扩展定义功能</p>
 * 
 * <AUTHOR>
 * @module 字节处理
 * @date 2025/07/25 10:00
 * @copyright 2024 山东新北洋信息技术股份有限公司. All rights reserved
 * @since 1.7.0
 */
public enum TlvDataType {
    
    // 基本数据类型 - 整数类型
    /** 8位有符号整数 */
    BYTE(0x01, Byte.class, byte.class),
    /** 16位有符号整数 */
    SHORT(0x02, Short.class, short.class),
    /** 32位有符号整数 */
    INTEGER(0x03, Integer.class, int.class),
    /** 64位有符号整数 */
    LONG(0x04, Long.class, long.class),
    /** 大整数 */
    BIG_INTEGER(0x05, BigInteger.class),
    
    // 基本数据类型 - 浮点数类型
    /** 32位浮点数 */
    FLOAT(0x10, Float.class, float.class),
    /** 64位浮点数 */
    DOUBLE(0x11, Double.class, double.class),
    /** 高精度小数 */
    BIG_DECIMAL(0x12, BigDecimal.class),
    
    // 基本数据类型 - 布尔类型
    /** 布尔值 */
    BOOLEAN(0x20, Boolean.class, boolean.class),
    
    // 字符串类型
    /** UTF-8编码字符串 */
    STRING_UTF8(0x30, String.class),
    /** ASCII编码字符串 */
    STRING_ASCII(0x31, String.class),
    
    // 复杂类型
    /** 对象类型(Map) */
    MAP(0x40, Map.class),
    /** 数组类型(Iterable) */
    ARRAY(0x41, Iterable.class),
    
    // 二进制数据
    /** 字节数组 */
    BYTE_ARRAY(0x50, byte[].class),
    
    // 特殊类型
    /** 空值 */
    NULL(0x00, Void.class);
    
    /** Type字段的值 */
    private final int typeValue;
    /** 对应的Java类型 */
    private final Class<?>[] javaTypes;
    
    /** 类型映射缓存 */
    private static final Map<Class<?>, TlvDataType> TYPE_MAPPING = new HashMap<>();
    /** 自定义类型映射 */
    private static final Map<Class<?>, TlvDataType> CUSTOM_TYPE_MAPPING = new HashMap<>();
    /** Type值映射 */
    private static final Map<Integer, TlvDataType> VALUE_MAPPING = new HashMap<>();
    
    static {
        // 初始化类型映射
        for (TlvDataType dataType : values()) {
            VALUE_MAPPING.put(dataType.typeValue, dataType);
            for (Class<?> javaType : dataType.javaTypes) {
                TYPE_MAPPING.put(javaType, dataType);
            }
        }
        // 确保String类型默认映射到UTF8
        TYPE_MAPPING.put(String.class, STRING_UTF8);
    }
    
    /**
     * 构造函数
     * @param typeValue Type字段的值
     * @param javaTypes 对应的Java类型
     */
    TlvDataType(int typeValue, Class<?>... javaTypes) {
        this.typeValue = typeValue;
        this.javaTypes = javaTypes;
    }
    
    /**
     * 获取Type字段的值

     * @return Type字段的值
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public int getTypeValue() {
        return typeValue;
    }
    
    /**
     * 获取对应的Java类型
     * @return Java类型数组
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public Class<?>[] getJavaTypes() {
        return javaTypes.clone();
    }
    
    /**
     * 根据Java类型获取对应的TLV数据类型
     * @param javaType Java类型
     * @return 对应的TLV数据类型，如果未找到返回null
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public static TlvDataType fromJavaType(Class<?> javaType) {
        // 先查找自定义映射
        TlvDataType customType = CUSTOM_TYPE_MAPPING.get(javaType);
        if (customType != null) {
            return customType;
        }
        
        // 查找默认映射
        TlvDataType defaultType = TYPE_MAPPING.get(javaType);
        if (defaultType != null) {
            return defaultType;
        }
        
        // 检查是否为数组类型
        if (javaType.isArray() && javaType.getComponentType() == byte.class) {
            return BYTE_ARRAY;
        }
        
        // 检查是否实现了Map接口
        if (Map.class.isAssignableFrom(javaType)) {
            return MAP;
        }
        
        // 检查是否实现了Iterable接口
        if (Iterable.class.isAssignableFrom(javaType)) {
            return ARRAY;
        }
        
        return null;
    }
    
    /**
     * 根据Type值获取对应的TLV数据类型
     * @param typeValue Type字段的值
     * @return 对应的TLV数据类型，如果未找到返回null
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public static TlvDataType fromTypeValue(int typeValue) {
        return VALUE_MAPPING.get(typeValue);
    }
    
    /**
     * 注册自定义类型映射
     * @param javaType Java类型
     * @param tlvDataType TLV数据类型
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public static void registerCustomType(Class<?> javaType, TlvDataType tlvDataType) {
        if (javaType == null || tlvDataType == null) {
            throw new IllegalArgumentException("Java type and TLV data type cannot be null");
        }
        CUSTOM_TYPE_MAPPING.put(javaType, tlvDataType);
    }
    
    /**
     * 移除自定义类型映射
     * @param javaType Java类型
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public static void unregisterCustomType(Class<?> javaType) {
        if (javaType != null) {
            CUSTOM_TYPE_MAPPING.remove(javaType);
        }
    }
    
    /**
     * 清除所有自定义类型映射
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public static void clearCustomTypes() {
        CUSTOM_TYPE_MAPPING.clear();
    }
    
    /**
     * 检查是否支持指定的Java类型
     * @param javaType Java类型
     * @return 如果支持返回true，否则返回false
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public static boolean isSupported(Class<?> javaType) {
        return fromJavaType(javaType) != null;
    }
    
    /**
     * 获取所有支持的Type值
     * @return Type值数组
     * @since 1.7.0
     * <AUTHOR>
     * @date 2025/07/25
     */
    public static int[] getSupportedTypeValues() {
        return VALUE_MAPPING.keySet().stream().mapToInt(Integer::intValue).toArray();
    }
}
