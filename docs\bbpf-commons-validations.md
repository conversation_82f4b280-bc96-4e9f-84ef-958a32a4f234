# 校验处理 (com.snbc.bbpf.commons.validations)

## 工具类与注解概览  

| 工具类 | 对应注解 | 功能描述 |
|------|------|----------|
| ValidStringUtil | @ValidString | 提供字符串校验功能，支持多种内置验证类型 |
| ValidNotEmptyUtil | @ValidNotEmpty | 提供非空校验功能 |
| ValidStringLengthUtil | @ValidStringLength | 提供字符串长度校验功能 |
| ValidStringNumberUtil | @ValidStringNumber | 提供字符串数字格式校验功能 |
| ValidStringContainsUtil | @ValidStringContains | 提供字符串包含关系校验功能 |

### 各工具类详细API

#### ValidStringUtil - 字符串校验工具类

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| validateString(String str, String message, String regex, ValidStringType validStringType) | 验证字符串是否符合指定类型 | str: 待验证字符串<br>message: 错误信息<br>regex: 正则表达式(REGEX类型时必填)<br>validStringType: 验证类型 | void |
| validateObject(Object obj) | 验证对象中带有@ValidString注解的字段 | obj: 待验证对象 | void |

##### 使用注意事项

1. 当验证类型为 `ValidStringType.REGEX` 时，必须提供非空的正则表达式参数，否则将抛出 `IllegalArgumentException` 异常
2. `validateObject` 方法会递归查找对象及其父类中所有带有 `@ValidString` 注解的字段
3. 验证失败时会抛出 `IllegalArgumentException` 异常，包含错误信息
4. 方法对 null 值也进行验证，不会自动跳过空值
5. 要验证的字段必须是 String 类型，其他类型将导致类型转换异常

#### ValidNotEmptyUtil - 非空校验工具类

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| emptyFieldValidate(Object object) | 校验对象中带有@ValidNotEmpty注解的字段是否为空 | object: 待验证的对象 | void |

##### 使用注意事项

1. 方法只检查对象直接声明的字段，不会检查父类中的字段
2. 被检查的字段必须有对应的 getter 方法，遵循 JavaBean 规范
3. 方法使用 `ObjectEmptyCheck.isEmpty()` 判断空值，会检查 null、空字符串、空集合等情况
4. 校验失败时抛出的异常信息优先使用注解中定义的 message，如果 message 为空，则使用默认错误信息
5. 如果传入的对象为 null，将抛出 "object can't be null" 异常

#### ValidStringLengthUtil - 字符串长度校验工具类

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| stringFieldsValidate(Object object) | 校验对象中带有@ValidStringLength注解的字段长度是否符合要求 | object: 待验证的对象 | void |

##### 使用注意事项

1. 仅校验 String 类型字段，其他类型字段即使有注解也会被忽略
2. 字段必须有对应的 getter 方法，遵循 JavaBean 规范
3. 如果字段值为 null，且 min > 0，则验证失败
4. 字符串长度校验采用 Java 的 length() 方法，按字符数量计算，而非字节数
5. 当字段值长度不在 [min, max] 范围内时，验证失败并抛出异常

#### ValidStringNumberUtil - 字符串数字格式校验工具类

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| stringFieldsValidate(Object object) | 校验对象中带有@ValidStringNumber注解的字段是否为数字格式 | object: 待验证的对象 | void |

##### 使用注意事项

1. 使用 Apache Commons Lang3 的 NumberUtils.isCreatable() 方法验证数字格式
2. 支持整数、小数、科学计数法等数字格式，如 "123"、"123.45"、"1.23E2"
3. 字段值为 null 时会验证失败，必须是非空字符串
4. 字段必须具有符合 JavaBean 规范的 getter 方法，否则会抛出 "get method not found" 异常
5. 方法只检查对象直接声明的方法，不会检查继承的方法

#### ValidStringContainsUtil - 字符串包含关系校验工具类

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| stringFieldsValidate(Object object) | 校验对象中带有@ValidStringContains注解的字段是否包含指定字符串 | object: 待验证的对象 | void |

##### 使用注意事项

1. 使用 Java 的 String.contains() 方法进行包含关系判断，区分大小写
2. 字段值为 null 时会验证失败，必须是非空字符串
3. 字段必须具有符合 JavaBean 规范的 getter 方法
4. 注解的 value 属性定义了字段值必须包含的字符串
5. 包含关系判断是精确匹配，不支持正则表达式或模糊匹配

### 工具类与注解使用示例

```java
// =========== 1. ValidStringUtil 与 @ValidString 使用示例 ===========

// 1.1 直接验证字符串
try {
    // 验证邮箱格式
    ValidStringUtil.validateString("<EMAIL>", "邮箱格式不正确", "", ValidStringType.EMAIL);
    
    // 验证手机号格式
    ValidStringUtil.validateString("***********", "手机号格式不正确", "", ValidStringType.PHONE_NUMBER);
    
    // 使用自定义正则表达式
    ValidStringUtil.validateString("abc123", "只允许字母和数字", "[a-zA-Z0-9]+", ValidStringType.REGEX);
} catch (IllegalArgumentException e) {
    System.out.println("验证失败: " + e.getMessage());
}

// 1.2 使用注解验证对象
public class User {
    @ValidString(validStringType = ValidStringType.EMAIL, message = "邮箱格式不正确")
    private String email;
    
    @ValidString(validStringType = ValidStringType.PHONE_NUMBER, message = "手机号格式不正确")
    private String phone;
    
    @ValidString(validStringType = ValidStringType.REGEX, regex = "[a-zA-Z0-9]+", message = "用户名只能包含字母和数字")
    private String username;
    
    // getter和setter方法省略
}

// 验证对象
User user = new User();
user.setEmail("invalid-email");
user.setPhone("***********");
user.setUsername("abc123");

try {
    ValidStringUtil.validateObject(user);
} catch (IllegalArgumentException | IllegalAccessException e) {
    System.out.println("验证失败: " + e.getMessage());
}

// =========== 2. ValidNotEmptyUtil 与 @ValidNotEmpty 使用示例 ===========

// 定义带有非空验证注解的类
public class Product {
    @ValidNotEmpty(message = "产品名称不能为空")
    private String name;
    
    @ValidNotEmpty(message = "产品描述不能为空")
    private String description;
    
    @ValidNotEmpty(message = "产品分类不能为空")
    private List<String> categories;
    
    // getter和setter方法省略
}

// 创建对象并验证
Product product = new Product();
product.setName("智能手机");
product.setDescription("");  // 空字符串，会触发验证失败
product.setCategories(Arrays.asList("电子产品", "通讯设备"));

try {
    ValidNotEmptyUtil.emptyFieldValidate(product);
} catch (IllegalArgumentException e) {
    System.out.println("验证失败: " + e.getMessage());  // 输出：验证失败: 产品描述不能为空
}

// =========== 3. ValidStringLengthUtil 与 @ValidStringLength 使用示例 ===========

// 定义带有字符串长度验证注解的类
public class Article {
    @ValidStringLength(min = 5, max = 50, message = "标题长度必须在5-50个字符之间")
    private String title;
    
    @ValidStringLength(min = 10, max = 2000, message = "内容长度必须在10-2000个字符之间")
    private String content;
    
    // getter和setter方法省略
}

// 创建对象并验证
Article article = new Article();
article.setTitle("Test");  // 长度小于5，会触发验证失败
article.setContent("这是文章内容，长度超过10个字符");

try {
    ValidStringLengthUtil.stringFieldsValidate(article);
} catch (IllegalArgumentException e) {
    System.out.println("验证失败: " + e.getMessage());  // 输出：验证失败: 标题长度必须在5-50个字符之间
}

// =========== 4. ValidStringNumberUtil 与 @ValidStringNumber 使用示例 ===========

// 定义带有字符串数字验证注解的类
public class Order {
    @ValidStringNumber(message = "订单ID必须为数字")
    private String orderId;
    
    @ValidStringNumber(message = "价格必须为数字")
    private String price;
    
    // getter和setter方法省略
}

// 创建对象并验证
Order order = new Order();
order.setOrderId("ORD12345");  // 包含非数字字符，会触发验证失败
order.setPrice("99.99");

try {
    ValidStringNumberUtil.stringFieldsValidate(order);
} catch (IllegalArgumentException e) {
    System.out.println("验证失败: " + e.getMessage());  // 输出：验证失败: 订单ID必须为数字
}

// =========== 5. ValidStringContainsUtil 与 @ValidStringContains 使用示例 ===========

// 定义带有字符串包含验证注解的类
public class WebPage {
    @ValidStringContains(value = "http", message = "URL必须包含http")
    private String url;
    
    @ValidStringContains(value = ".html", message = "页面地址必须以.html结尾")
    private String pagePath;
    
    // getter和setter方法省略
}

// 创建对象并验证
WebPage webPage = new WebPage();
webPage.setUrl("ftp://example.com");  // 不包含http，会触发验证失败
webPage.setPagePath("index.html");

try {
    ValidStringContainsUtil.stringFieldsValidate(webPage);
} catch (IllegalArgumentException e) {
    System.out.println("验证失败: " + e.getMessage());  // 输出：验证失败: URL必须包含http
}
```

## 常用验证器

### 验证器接口

| 接口名 | 功能描述 |
|------|----------|
| IStringValidator | 字符串验证接口，定义validate方法 |

### 验证器工厂

| 类名 | 功能描述 |
|------|----------|
| StringValidationStrategyFactory | 验证器工厂类，根据验证类型返回对应的验证器实例 |

### 验证器实现类

| 类名 | 功能描述 | 验证类型 |
|------|----------|----------|
| EmailValidator | 邮箱格式验证器 | ValidStringType.EMAIL |
| IdentityCardValidator | 身份证号码验证器 | ValidStringType.IDENTITY_CARD |
| ChineseCharacterValidator | 中文字符验证器 | ValidStringType.CHINESE_CHARACTER |
| BankCardValidator | 银行卡号验证器 | ValidStringType.BANK_CARD |
| IpAddressValidator | IP地址验证器 | ValidStringType.IP_ADDRESS |
| PhoneNumberValidator | 电话号码验证器 | ValidStringType.PHONE_NUMBER |
| UrlValidator | URL格式验证器 | ValidStringType.URL |
| RegexValidator | 正则表达式验证器 | ValidStringType.REGEX |

### 验证类型枚举

```java
public enum ValidStringType {
    // 身份证号码验证
    IDENTITY_CARD,
    // 邮箱验证
    EMAIL,
    // 汉字验证
    CHINESE_CHARACTER,
    // 银行卡号验证
    BANK_CARD,
    // IP地址验证
    IP_ADDRESS,
    //电话号码
    PHONE_NUMBER,
    //网址
    URL,
    //正则表达式
    REGEX
}
```

### 验证器使用注意事项

1. 所有验证器均通过工厂类 `StringValidationStrategyFactory` 获取，确保了验证器的单例使用
2. 验证失败时统一抛出 `IllegalArgumentException` 异常，而非返回布尔值
3. 验证器不会对 null 值进行特殊处理，null 值一般会导致验证失败
4. RegexValidator 需要在构建时提供正则表达式，而其他验证器使用内置的正则表达式
5. 每种验证器都针对特定场景优化，例如身份证验证不仅检查格式还会校验内部编码规则

### 验证器使用示例

```java
// 1. 使用验证器工厂获取验证器
// 邮箱验证器
IStringValidator emailValidator = StringValidationStrategyFactory.getValidator(ValidStringType.EMAIL);
try {
    emailValidator.validate("<EMAIL>", "邮箱格式不正确");
    System.out.println("邮箱格式正确");
} catch (IllegalArgumentException e) {
    System.out.println("验证失败: " + e.getMessage());
}

// 身份证验证器
IStringValidator idCardValidator = StringValidationStrategyFactory.getValidator(ValidStringType.IDENTITY_CARD);
try {
    idCardValidator.validate("110101199001011234", "身份证格式不正确");
    System.out.println("身份证格式正确");
} catch (IllegalArgumentException e) {
    System.out.println("验证失败: " + e.getMessage());
}

// 中文字符验证器
IStringValidator chineseValidator = StringValidationStrategyFactory.getValidator(ValidStringType.CHINESE_CHARACTER);
try {
    chineseValidator.validate("中文字符", "必须是中文字符");
    System.out.println("中文字符格式正确");
} catch (IllegalArgumentException e) {
    System.out.println("验证失败: " + e.getMessage());
}

// 银行卡验证器
IStringValidator bankCardValidator = StringValidationStrategyFactory.getValidator(ValidStringType.BANK_CARD);
try {
    bankCardValidator.validate("6222021234567890123", "银行卡号格式不正确");
    System.out.println("银行卡号格式正确");
} catch (IllegalArgumentException e) {
    System.out.println("验证失败: " + e.getMessage());
}

// IP地址验证器
IStringValidator ipValidator = StringValidationStrategyFactory.getValidator(ValidStringType.IP_ADDRESS);
try {
    ipValidator.validate("***********", "IP地址格式不正确");
    System.out.println("IP地址格式正确");
} catch (IllegalArgumentException e) {
    System.out.println("验证失败: " + e.getMessage());
}

// 电话号码验证器
IStringValidator phoneValidator = StringValidationStrategyFactory.getValidator(ValidStringType.PHONE_NUMBER);
try {
    phoneValidator.validate("***********", "手机号格式不正确");
    System.out.println("手机号格式正确");
} catch (IllegalArgumentException e) {
    System.out.println("验证失败: " + e.getMessage());
}

// URL验证器
IStringValidator urlValidator = StringValidationStrategyFactory.getValidator(ValidStringType.URL);
try {
    urlValidator.validate("https://www.example.com", "URL格式不正确");
    System.out.println("URL格式正确");
} catch (IllegalArgumentException e) {
    System.out.println("验证失败: " + e.getMessage());
}

// 正则表达式验证器
IStringValidator regexValidator = new RegexValidator("[a-zA-Z0-9]+");
try {
    regexValidator.validate("abc123", "只允许字母和数字");
    System.out.println("格式正确");
    
    regexValidator.validate("abc!123", "只允许字母和数字");
} catch (IllegalArgumentException e) {
    System.out.println("验证失败: " + e.getMessage());  // 输出: 验证失败: 只允许字母和数字
}
``` 