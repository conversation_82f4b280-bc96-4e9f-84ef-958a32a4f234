/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.dates;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.time.temporal.Temporal;
import java.time.temporal.TemporalAdjusters;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @module Date日期处理
 * 对LocalDateTime进行时间计算。
 * <p>计算当前时间一定时间（天，小时，分钟，秒，毫秒）之前或之后的时间</p>
 * <p>计算两个时间之间间隔时差（天，小时，分钟，秒，毫秒）<p>
 * <p>计算当前时间一定天数之前或之后的00点00分00秒或23点59分59秒的时间</p>
 * <p>计算当前时间所在当前年/季/月/周第一天的起始时间和最后一天的起始时间，可区分00点00分00秒或23点59分59秒</p>
 * <p>计算当前时间的（天，小时，分钟，秒，毫秒）数</p>
 * <p>根据（天，小时，分钟，秒，毫秒）数转换成时间</p>
 * <p>判断某个时间是否在start时间和end时间之间</p>
 * <p>将LocalDateTime转换为Date,LocalDate，ZonedDateTime</p>
 * <p>返回两个日期间的 每一分钟/小时/天/月/年的时间对象的列表</p>
 * <p>返回指定时间或当前时间前或后N分钟/小时/天/月/年的时间对象的列表</p>
 * @date 2023/4/23 9:58
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class LocalDateTimeUtils extends BaseDateDiffUtil {

    public static final String LOCAL_DATE_TIME_IS_NULL = "localDateTime is null";

    /**
     * #LocalDateTime 加任意年月日时分秒毫秒
     *
     * @param localDateTime 制定日期
     * @param years         年
     * @param months        月
     * @param days          日
     * @param hours         时
     * @param minutes       分
     * @param seconds       秒
     * @param milliseconds  毫秒
     * @return LocalDateTime
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static LocalDateTime plus(LocalDateTime localDateTime, int years, int months, int days, int hours, long minutes, long seconds, long milliseconds) {
        final Temporal temporal = commonPlus(localDateTime, years, months, days, hours, minutes, seconds, milliseconds);
        return (LocalDateTime) temporal;
    }


    /**
     * 计算时间差
     *
     * @param start    开始时间
     * @param end      结束时间
     * @param timeUnit 时间单位
     * @return
     * @throws <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static long diff(LocalDateTime start, LocalDateTime end, TimeUnit timeUnit) {
        return commondDiff(Date.from(start.atZone(ZoneId.systemDefault()).toInstant()),
                Date.from(end.atZone(ZoneId.systemDefault()).toInstant()),
                timeUnit);
    }

    /**
     * # 增加年
     *
     * @param localDateTime 日期
     * @param years         年
     * @return LocalDateTime
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static LocalDateTime plusYears(LocalDateTime localDateTime, int years) {
        return plus(localDateTime, years, 0, 0, 0, 0, 0, 0);
    }

    /**
     * # 增加月数
     *
     * @param localDateTime 日期
     * @param months        月
     * @return localDateTime
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static LocalDateTime plusMonths(LocalDateTime localDateTime, int months) {
        return plus(localDateTime, 0, months, 0, 0, 0, 0, 0);
    }

    /**
     * # 增加天数
     *
     * @param localDateTime 日期
     * @param days          天数
     * @return localDateTime
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static LocalDateTime plusDays(LocalDateTime localDateTime, int days) {
        return plus(localDateTime, 0, 0, days, 0, 0, 0, 0);
    }

    /**
     * # 增加小时
     *
     * @param localDateTime 日期
     * @param hours         小时
     * @return localDateTime
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static LocalDateTime plusHours(LocalDateTime localDateTime, int hours) {
        return plus(localDateTime, 0, 0, 0, hours, 0, 0, 0);
    }

    /**
     * # 增加分钟
     *
     * @param localDateTime 日期
     * @param minutes       分钟
     * @return localDateTime
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static LocalDateTime plusMinutes(LocalDateTime localDateTime, long minutes) {
        return plus(localDateTime, 0, 0, 0, 0, minutes, 0, 0);
    }


    /**
     * # 增加秒
     *
     * @param localDateTime 日期
     * @param seconds       秒
     * @return localDateTime
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static LocalDateTime plusSeconds(LocalDateTime localDateTime, long seconds) {
        return plus(localDateTime, 0, 0, 0, 0, 0, seconds, 0);
    }

    /**
     * # 增加毫秒
     *
     * @param localDateTime 日期
     * @param milliseconds  毫秒
     * @return localDateTime
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static LocalDateTime plusMilliseconds(LocalDateTime localDateTime, long milliseconds) {
        return plus(localDateTime, 0, 0, 0, 0, 0, 0, milliseconds);
    }


    /**
     * # 获取制定时间的年
     *
     * @param localDateTime 日期
     * @return 年数
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static int year(LocalDateTime localDateTime) {
        return yearTemporal(localDateTime);
    }

    /**
     * # 获取制定时间的月数
     *
     * @param localDateTime 日期
     * @return 月数
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static int month(LocalDateTime localDateTime) {
        return monthTemporal(localDateTime);
    }

    /**
     * # 获取制定时间的天数
     *
     * @param localDateTime 日期
     * @return 天数
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static int day(LocalDateTime localDateTime) {
        return dayTemporal(localDateTime);
    }

    /**
     * # 获取制定时间的小时数
     *
     * @param localDateTime 日期
     * @return 小时数
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static int hour(LocalDateTime localDateTime) {
        return hourTemporal(localDateTime);
    }

    /**
     * # 获取制定时间的分钟数
     *
     * @param localDateTime 日期
     * @return 分钟数
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static int minute(LocalDateTime localDateTime) {
        return minuteTemporal(localDateTime);
    }

    /**
     * # 获取制定时间的秒数
     *
     * @param localDateTime 日期
     * @return 秒数
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static int second(LocalDateTime localDateTime) {
        return secondTemporal(localDateTime);
    }

    /**
     * # 获取制定时间的毫秒数
     *
     * @param localDateTime 日期
     * @return 毫秒数
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static int millisecond(LocalDateTime localDateTime) {
        return millisecondTemporal(localDateTime);
    }

    /**
     * # 获取制定时间的默认凌晨时间 00点00分00秒
     *
     * @param localDateTime 日期
     * @return LocalDateTime
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static LocalDateTime starOfDay(LocalDateTime localDateTime) {
        return localDateTime.withHour(0).withMinute(0).withSecond(0).withNano(0);
    }

    /**
     * # 获取制定时间的夜间时间，23点59分59秒
     *
     * @param localDateTime 日期
     * @return LocalDateTime
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static LocalDateTime endOfDay(LocalDateTime localDateTime) {
        return localDateTime.withHour(INT_23).withMinute(INT_59).withSecond(INT_59);
    }

    /**
     * # 获取制定时间当年的第一日，默认00点00分00秒
     *
     * @param localDateTime 日期
     * @return LocalDateTime
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static LocalDateTime firstDayOfYear(LocalDateTime localDateTime) {
        return firstDayOfYear(localDateTime, true);
    }

    /**
     * # 获取制定时间当年的第一日，可区分00点00分00秒或23点59分59秒
     *
     * @param localDateTime 日期
     * @param isMorning     true 00点00分00秒 ,false 23点59分59秒
     * @return LocalDateTime
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static LocalDateTime firstDayOfYear(LocalDateTime localDateTime, boolean isMorning) {
        LocalDate localDate = localDateTime.toLocalDate().with(TemporalAdjusters.firstDayOfYear());
        return isMorning ? morning(localDate) : evening(localDate);
    }

    /**
     * # 获取制定时间当年的最后一日，默认00点00分00秒
     *
     * @param localDateTime 日期
     * @return LocalDateTime
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static LocalDateTime lastDayOfYear(LocalDateTime localDateTime) {
        return lastDayOfYear(localDateTime, true);
    }

    /**
     * # 获取制定时间当年的最后一日，可区分00点00分00秒或23点59分59秒
     *
     * @param localDateTime 日期
     * @param isMorning     true 00点00分00秒 ,false 23点59分59秒
     * @return LocalDateTime
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static LocalDateTime lastDayOfYear(LocalDateTime localDateTime, boolean isMorning) {
        LocalDate localDate = localDateTime.toLocalDate().with(TemporalAdjusters.lastDayOfYear());
        return isMorning ? morning(localDate) : evening(localDate);
    }


    /**
     * # 获取制定时间当月的第一日，默认00点00分00秒
     *
     * @param localDateTime 日期
     * @return LocalDateTime
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static LocalDateTime firstDayOfMonth(LocalDateTime localDateTime) {
        return firstDayOfMonth(localDateTime, true);
    }

    /**
     * # 获取制定时间当月的第一日，可区分00点00分00秒或23点59分59秒
     *
     * @param localDateTime 日期
     * @param isMorning     true 00点00分00秒 ,false 23点59分59秒
     * @return LocalDateTime
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static LocalDateTime firstDayOfMonth(LocalDateTime localDateTime, boolean isMorning) {
        LocalDate localDate = localDateTime.toLocalDate().with(TemporalAdjusters.firstDayOfMonth());
        return isMorning ? morning(localDate) : evening(localDate);
    }

    /**
     * # 获取制定时间当月的最后一日，默认00点00分00秒
     *
     * @param localDate 日期
     * @return LocalDateTime
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static LocalDateTime lastDayOfMonth(LocalDateTime localDate) {
        return lastDayOfMonth(localDate, true);
    }

    /**
     * # 获取制定时间当月的最后一日，可区分00点00分00秒或23点59分59秒
     *
     * @param localDateTime 日期
     * @param isMorning     true 00点00分00秒 ,false 23点59分59秒
     * @return LocalDateTime
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static LocalDateTime lastDayOfMonth(LocalDateTime localDateTime, boolean isMorning) {
        LocalDate localDate = localDateTime.toLocalDate().with(TemporalAdjusters.lastDayOfMonth());
        return isMorning ? morning(localDate) : evening(localDate);
    }

    /**
     * # 根据年月日时分秒毫秒构造LocalDateTime
     *
     * @param years        年
     * @param months       月
     * @param days         日
     * @param hours        时
     * @param minutes      分
     * @param seconds      秒
     * @param milliseconds 毫秒
     * @return LocalDateTime
     * @throws
     * <AUTHOR>
     * @date 2023/5/16
     * @since 1.0.0
     */
    public static LocalDateTime ofLocalDateTime(int years, int months, int days, int hours, int minutes, int seconds, int milliseconds) {
        return LocalDateTime.of(years, months, days, hours, minutes, seconds, milliseconds);
    }

    /**
     * 判断now是否在start和end之间
     * @param now 时间
     * @param start 开始时间
     * @param end 结束时间
     * @return 是否在start和end之间
     * <AUTHOR>
     * @since 1.1.0
     * @date 2023/4/21
     */
    public static boolean isEffectiveDate(LocalDateTime now, LocalDateTime start, LocalDateTime end){
        if(now==null || start==null || end==null){
            throw new IllegalArgumentException("argument is null");
        }

        if(start.isAfter(end)){
            throw new IllegalArgumentException("end_time is less than start_time");
        }
        if(now.isBefore(start)){
            return false;
        }
        return now.isBefore(end);
    }

    /**
     * 将LocalDateTime转换为Date
     * @param localDateTime {@link java.time.LocalDateTime}
     * @return {@link java.util.Date}
     * <AUTHOR>
     * @since 1.1.0
     * @date 2023/4/21
     */
    public static Date toDate(LocalDateTime localDateTime){
        return toDate(localDateTime,ZoneId.systemDefault());
    }

    /**
     * 将LocalDateTime转换为Date
     * @param localDateTime {@link java.time.LocalDate}
     * @param zoneId {@link java.time.ZoneId}
     * @return {@link java.util.Date}
     * <AUTHOR>
     * @since 1.1.0
     * @date 2023/4/21
     */
    public static Date toDate(LocalDateTime localDateTime, ZoneId zoneId){
        if(localDateTime==null){
            throw new IllegalArgumentException(LOCAL_DATE_TIME_IS_NULL);
        }
        return Date.from(localDateTime.atZone(Optional.ofNullable(zoneId).orElse(ZoneId.systemDefault())).toInstant());
    }

    /**
     * 将LocalDateTime转换为Date
     * @param localDateTime {@link java.time.LocalDateTime}
     * @return {@link java.time.LocalDate}
     * <AUTHOR>
     * @since 1.1.0
     * @date 2023/4/21
     */
    public static LocalDate toLocalDate(LocalDateTime localDateTime){
        if(localDateTime==null){
            throw new IllegalArgumentException(LOCAL_DATE_TIME_IS_NULL);
        }
        return localDateTime.toLocalDate();
    }

    /**
     * 将LocalDateTime转换为Date
     * @param localDateTime {@link java.time.LocalDateTime}
     * @return {@link java.time.LocalDate}
     * <AUTHOR>
     * @since 1.1.0
     * @date 2023/4/21
     */
    public static ZonedDateTime toZonedDateTime(LocalDateTime localDateTime){
        if(localDateTime==null){
            throw new IllegalArgumentException(LOCAL_DATE_TIME_IS_NULL);
        }
        return localDateTime.atZone(ZoneId.systemDefault());
    }

    /**
     * 将LocalDateTime转换为Date
     * @param localDateTime {@link java.time.LocalDateTime}
     * @param zoneId {@link java.time.ZoneId}
     * @return {@link java.time.ZonedDateTime}
     * <AUTHOR>
     * @since 1.1.0
     * @date 2023/4/21
     */
    public static ZonedDateTime toZonedDateTime(LocalDateTime localDateTime,ZoneId zoneId){
        if(localDateTime==null){
            throw new IllegalArgumentException(LOCAL_DATE_TIME_IS_NULL);
        }
        return localDateTime.atZone(Optional.ofNullable(zoneId).orElse(ZoneId.systemDefault()));
    }

    /**
     * 获取两个时间之间相隔年的列表
     * 包含开始时间start和结束时间end
     * 当start或者end为空时,或者start大于end时，抛出异常
     * @param start {@link java.time.LocalDateTime} 时间1
     * @param end {@link java.time.LocalDateTime}  时间2
     * @param val 间隔
     * @return {@link java.time.LocalDateTime}
     * <AUTHOR>
     * @since 1.5.0
     * @date 2024/5/28
     */
    public static List<LocalDateTime> getBetweenDateByYear(LocalDateTime start, LocalDateTime end, int val){
        return getBetweenDate(start, end, ChronoUnit.YEARS, val);
    }

    /**
     * 获取两个时间之间相隔季度的列表
     * 包含开始时间start和结束时间end
     * 当start或者end为空时,或者start大于end时，抛出异常
     * @param start {@link java.time.LocalDateTime} 时间1
     * @param end {@link java.time.LocalDateTime}  时间2
     * @param val 间隔
     * @return {@link java.time.LocalDateTime}
     * <AUTHOR>
     * @since 1.5.0
     * @date 2024/5/28
     */
    public static List<LocalDateTime> getBetweenDateByQuarter(LocalDateTime start, LocalDateTime end, int val){
        return getBetweenDate(start, end, ChronoUnit.MONTHS, val*QUARTER_MONTH);
    }

    /**
     * 获取两个时间之间相隔月份的列表
     * 包含开始时间start和结束时间end
     * 当start或者end为空时,或者start大于end时，抛出异常
     * @param start {@link java.time.LocalDateTime} 时间1
     * @param end {@link java.time.LocalDateTime}  时间2
     * @param val 间隔
     * @return {@link java.time.LocalDateTime}
     * <AUTHOR>
     * @since 1.5.0
     * @date 2024/5/28
     */
    public static List<LocalDateTime> getBetweenDateByMonths(LocalDateTime start, LocalDateTime end, int val){
        return getBetweenDate(start, end, ChronoUnit.MONTHS, val);
    }

    /**
     * 获取两个时间之间相隔天数的列表
     * 包含开始时间start和结束时间end
     * 当start或者end为空时,或者start大于end时，抛出异常
     * @param start {@link java.time.LocalDateTime} 时间1
     * @param end {@link java.time.LocalDateTime}  时间2
     * @param val 间隔
     * @return {@link java.time.LocalDateTime}
     * <AUTHOR>
     * @since 1.5.0
     * @date 2024/5/28
     */
    public static List<LocalDateTime> getBetweenDateByDays(LocalDateTime start, LocalDateTime end, int val){
        return getBetweenDate(start, end, ChronoUnit.DAYS, val);
    }

    /**
     * 获取两个时间之间相隔小时的列表
     * 包含开始时间start和结束时间end
     * 当start或者end为空时,或者start大于end时，抛出异常
     * @param start {@link java.time.LocalDateTime} 时间1
     * @param end {@link java.time.LocalDateTime}  时间2
     * @param val 间隔
     * @return {@link java.time.LocalDateTime}
     * <AUTHOR>
     * @since 1.5.0
     * @date 2024/5/28
     */
    public static List<LocalDateTime> getBetweenDateByHours(LocalDateTime start, LocalDateTime end, int val){
        return getBetweenDate(start, end, ChronoUnit.HOURS, val);
    }

    /**
     * 获取两个时间之间相隔分钟数的列表
     * 包含开始时间start和结束时间end
     * 当start或者end为空时,或者start大于end时，抛出异常
     * @param start {@link java.time.LocalDateTime} 时间1
     * @param end {@link java.time.LocalDateTime}  时间2
     * @param val 间隔
     * @return {@link java.time.LocalDateTime}
     * <AUTHOR>
     * @since 1.5.0
     * @date 2024/5/28
     */
    public static List<LocalDateTime> getBetweenDateByMinutes(LocalDateTime start, LocalDateTime end, int val){
        return getBetweenDate(start, end, ChronoUnit.MINUTES, val);
    }

    /**
     * 获取两个时间之间的日期
     * 包含开始时间start和结束时间end
     * 当start或者end为空时,或者start大于end时，抛出异常
     * @param start {@link java.time.LocalDateTime} 时间1
     * @param end {@link java.time.LocalDateTime}  时间2
     * @param field {@link java.time.temporal.ChronoUnit} 时间类型，支持年，月，日，时，分
     * @param val 间隔值 ，当负数时，表示从end开始，往前推
     * @return {@link java.time.LocalDateTime}
     * <AUTHOR>
     * @since 1.5.0
     * @date 2024/5/28
     */
    public static List<LocalDateTime> getBetweenDate(LocalDateTime start, LocalDateTime end, ChronoUnit field, int val){
        return getBetweenDate(start, end, field, val, true, true);
    }

    /**
     * 获取两个时间之间的日期
     * 当start或者end为空时,或者start大于end时，抛出异常
     * @param start {@link java.time.LocalDateTime} 时间1
     * @param end {@link java.time.LocalDateTime}  时间2
     * @param field {@link java.time.temporal.ChronoUnit} 时间类型，支持年，月，日，时，分
     * @param val 间隔值 ，当负数时，表示从end开始，往前推
     * @param isIncludeStart 是否包含开始时间
     * @param isIncludeEnd 是否包含结束时间
     * @return {@link java.time.LocalDateTime}
     * <AUTHOR>
     * @since 1.5.0
     * @date 2024/5/28
     */
    public static List<LocalDateTime> getBetweenDate(LocalDateTime start, LocalDateTime end, ChronoUnit field,
                                                     int val, boolean isIncludeStart, boolean isIncludeEnd){
        if(start==null || end == null){
            throw new IllegalArgumentException("argument is null");
        }
        if(val==0){
            throw new IllegalArgumentException("val cannot be 0");
        }
        if(start.isAfter(end)){
            throw new IllegalArgumentException("start must be before end");
        }
        // 获取start和end之间的时间

        List<LocalDateTime> dates = new LinkedList<>();
        if (isIncludeStart){
            dates.add(start);
        }
        int addVal = Math.abs(val);

        LocalDateTime current = start;
        while (current.isBefore(end)) {
            // 增加val个单位
            current = current.plus(addVal, field);
            // 添加当前时间到列表
            dates.add(current);
        }
        if (!isIncludeEnd){
            dates.remove(dates.size()-1);
        }
        if (val < 0) {
            Collections.reverse(dates);
        }
        return dates;
    }

    /**
     * 返回指定时间后N分钟/小时/天/月/年的时间对象的列表
     * @param start 开始时间
     * @param field 单位
     * @param val 值
     * @return list
     * <AUTHOR>
     * @since 1.5.0
     * @date 2024/6/20
     */
    public List<LocalDateTime> getAfterDates(LocalDateTime start, ChronoUnit field, int val){
        if(val < 0){
            throw new IllegalArgumentException("val must be positive");
        }
        return getNextDates(start,field, val, false);
    }

    /**
     * 返回指定时间前N分钟/小时/天/月/年的时间对象的列表
     * @param start 开始时间
     * @param field 单位
     * @param val 值
     * @return list
     * <AUTHOR>
     * @since 1.5.0
     * @date 2024/6/20
     */
    public List<LocalDateTime> getBeforeDates(LocalDateTime start, ChronoUnit field, int val){
        if(val < 0){
            throw new IllegalArgumentException("val must be positive");
        }
        return getNextDates(start,field, -val, false);
    }

    /**
     * 返回指定时间前或后N分钟/小时/天/月/年的时间对象的列表
     * @param start 开始时间
     * @param field 单位
     * @param val 值
     * @param isIncludeStart 是否包含开始时间
     * @return list
     * <AUTHOR>
     * @since 1.5.0
     * @date 2024/6/20
     */
    public static List<LocalDateTime> getNextDates(LocalDateTime start, ChronoUnit field, int val, boolean isIncludeStart){
        if(start==null){
            throw new IllegalArgumentException("start is null");
        }
        if(field==null){
            throw new IllegalArgumentException("field is null");
        }
        if(val == 0){
            return Collections.singletonList(start);
        }
        List<LocalDateTime> dates = new LinkedList<>();
        if(isIncludeStart){
            dates.add(start);
        }

        LocalDateTime current = start;
        int addVal = val/Math.abs(val);
        while(val!=0){
            // 增加val个单位
            current = current.plus(addVal, field);
            // 添加当前时间到列表
            dates.add(current);
            val += -addVal;
        }
        return dates;
    }
}
