/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.validations.aspect;

import com.snbc.bbpf.commons.validations.annotation.ValidString;
import com.snbc.bbpf.commons.validations.annotation.ValidStringType;
import com.snbc.bbpf.commons.validations.validator.IStringValidator;
import com.snbc.bbpf.commons.validations.validator.RegexValidator;
import com.snbc.bbpf.commons.validations.validator.StringValidationStrategyFactory;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;

/**
 * 字符串格式验证注解的方法参数的切面类
 *
 * <AUTHOR>
 * @module
 * @date 2023/10/30 13:57
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
@Aspect
public class ValidParameterStringFormatAspect {
    //扫描所有配置@ValidString注解的方法
    @Pointcut("execution(@com.snbc.bbpf.commons.validations.annotation.ValidString *  *(..))")
    public void myPointcut() {
    }

    /*
     * 方法的环绕切面（对配置字符串格式验证注解的方法参数进行非空校验）
     *
     * @param joinPoint
     * @return Object
     * @since 1.3.0
     * <AUTHOR>
     * @date 2023/10/30
     */
    @Around("myPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {

        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Parameter[] parameters = method.getParameters();
        // 所有的参数
        Object[] args = joinPoint.getArgs();
        String jpName = joinPoint.getSignature().getName();
        // 校验每个参数
        for (int i = 0; i < parameters.length; i++) {
            Parameter parameter = parameters[i];
            // 获取参数注解
            ValidString anno = parameter.getAnnotation(ValidString.class);
            // 存在@ValidString，忽略
            if (null == anno) {
                continue;
            }
            try {
                if (anno.validStringType() == ValidStringType.REGEX) {
                    if ("".equals(anno.regex())) {
                        throw new IllegalArgumentException("No regular expression defined");
                    }
                    IStringValidator stringValidator = new RegexValidator(anno.regex());
                    stringValidator.validate((String) args[i], anno.message());
                } else {
                    IStringValidator stringValidator = StringValidationStrategyFactory
                            .getValidator(anno.validStringType());
                    stringValidator.validate((String) args[i], anno.message());
                }
            } catch (IllegalArgumentException e) {
                throw new IllegalArgumentException("method[" + jpName + "]parameter[" + parameter.getName() + "] " + e.getMessage(), e);

            }
        }
        return joinPoint.proceed();
    }

}
