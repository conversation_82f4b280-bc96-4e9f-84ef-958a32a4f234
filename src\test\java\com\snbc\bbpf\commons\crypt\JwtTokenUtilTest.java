/*
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.commons.crypt;

import io.jsonwebtoken.Claims;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * token生成类测试类
 *
 * <AUTHOR>
 * @module 脱敏
 * @date 2023/12/05 18:18
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class JwtTokenUtilTest {
    private JwtTokenUtil jwtTokenUtil;
    private Map<String, Object> claims;
    private String privateKeyString;
    private String publicKeyString;

    @BeforeEach
    void setUp() {
        jwtTokenUtil = new JwtTokenUtil();
        claims = new HashMap<>();
        claims.put("userId", "1");
        claims.put("username", "username");
        privateKeyString="MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCJ+bo+0vwtr8bGuaRtmzGSNtZJxJZuKh2di3sYMe4isj01ixz+1CQPI278EHFeW1jUuEPDc+pW9BBWjsP+iugNvezJbJlvhAHv4GQ31Y1BsEqhgYhJltzwjy20G2rgHzm9pa0AqrM5kDgFUuPfDP/+rs6o3T1mNj+tM/3nOtA6DuEKhcKX3AQIWzDzpnNUI4HbW37PgerM8hCOPmoMRTuVsw6wZ1aOjnDVugmrj+Z1BQpU6ONaKpradU4vyONd5cWde4Xz28MAPGw55QVUCVflQtXHGGOjA1NEgPby1InG6QIdyxBEhsUoVw7LufB6Smk1xAm03UCFRxi4fsakCXFFAgMBAAECggEACAggfl27zlEIKjNOTg8VsKQ+jybBdwI+nRgsfzkQ2rYzd/V0J6Vjv/nZERVH9C0vG5rqXutVfeBRPyiqYhvl7D7dFNCckfBGSJdwXguxYFyJy6MFsvr0O5CqIWzjKM7IhrCpRHurWTN6/1xW5XoVQNceMphGZbfMtyFqq1YyHt7+656215k4BdDkFuJtt1wW+/dTzbPNZykbMem26RVPniJ5JuTE2cbq6QiCrtzdAaE1NEFTnZHdQ5ZqY7kuy+wd52BJHlQnZCdX/SzSevu8aWR/hXXbISSI6vMSOt9g3IliOIJOzdS84EnLYCLBvuY3vjcjtazOXOIV3FTwsObeNQKBgQDKdL2mTVJBHPjDkVs39Hc6+KkOKOSYMoGG6Z+/eQcDrruaXIVuC+V3/d2dG4BcraukuggUykgo5R/eaJnwkBq1PNZU7FstEo71IZXivnE/ITby1ZLCyJ/sm7y/JjpL37hgYcJk/3S+p8dXGw3qIFWXOMQoAskFe/EHIpO1uFiddwKBgQCud1dYdkkZWLOyLh6ihqxSJ7nFEKoh43su6vNLtE9qvJ5BK478RPC6CZ+ltbx5CW6LsFDPtbLhQLZnHMTj1S4wBG6rlhQ7c8SsaE8ogKSvifDZfBDC8ReAuuKalFQ0HJaC38O7kQ/2/+EAougoVDAYwOH1XpFPHww0c5MXgeXmIwKBgFeiULU7ou8sa0G8GZAO8c1E9Wh7qvd2ZTrQxaVL7g1aBTq78cXAwINAED2BOf7j7fhPzk+xf6q0Aydyf6/xsJ3ix6Pa61yHO/o/n3GWnc6FwhM6/1vxG8h/YSQcl/9fx59wsYSmTxJ37YF25H8DoAjlOYNYMUt+asZ74RQ3x6PhAoGBAJNZmfzN0klFAGfxyc/svGeaw6xrubVrgmOK7jc3L4fvjB1wo4/uzf3iGOMFDgyP6byzCl2TkWPrmuKVirj+GMdXRv1GgQoBac9PPePwWqcjcrbMsP9kTjxcGv0BX+ivaHNad97X0ssDK860yC0fhYuyhGUPHNzdVRqLrmTBQiFPAoGAeDig34zSzC7O/NMOwUE7Q2ZHuBIWnEZ5oUE8CEzn3mVd+UAN7JjnIPF90MXOtNwqcPRdUECdD4/sRw8rUN/MmmPMbOtSKdPc0A0hwz5aIMeRf3jValCEn7dOl0Gh+y4MfoOi0Ct47bXEjKI3k/IaVs9VaQjUi1iAx5sAITJ9UOE=";
        publicKeyString="MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAifm6PtL8La/GxrmkbZsxkjbWScSWbiodnYt7GDHuIrI9NYsc/tQkDyNu/BBxXltY1LhDw3PqVvQQVo7D/oroDb3syWyZb4QB7+BkN9WNQbBKoYGISZbc8I8ttBtq4B85vaWtAKqzOZA4BVLj3wz//q7OqN09ZjY/rTP95zrQOg7hCoXCl9wECFsw86ZzVCOB21t+z4HqzPIQjj5qDEU7lbMOsGdWjo5w1boJq4/mdQUKVOjjWiqa2nVOL8jjXeXFnXuF89vDADxsOeUFVAlX5ULVxxhjowNTRID28tSJxukCHcsQRIbFKFcOy7nwekppNcQJtN1AhUcYuH7GpAlxRQIDAQAB";
    }

    @Test
    @DisplayName("输入非空")
    @Tags({
            @Tag("@id:89"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/12/6")
    })
    void testGenerateToken_ShouldReturnValidToken() {
        String token = jwtTokenUtil.generateToken(claims, privateKeyString, 1000L);
        assertNotNull(token);
    }

    @Test
    @DisplayName("输入为空")
    @Tags({
            @Tag("@id:89"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/12/6")
    })
    void testGenerateToken_ShouldThrowException() {
        assertThrows(IllegalArgumentException.class, () -> jwtTokenUtil.generateToken(new HashMap<>(), privateKeyString, 1000L));
        assertThrows(IllegalArgumentException.class, () -> jwtTokenUtil.generateToken(claims, "", 1000L));
        assertThrows(IllegalArgumentException.class, () -> jwtTokenUtil.generateToken(claims, privateKeyString, 0));
    }

    @Test
    @DisplayName("输入非空")
    @Tags({
            @Tag("@id:89"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/12/6")
    })
    void testGetClaimsFromToken_ShouldReturnValidClaims() throws UnsupportedEncodingException {
        String token = jwtTokenUtil.generateToken(claims, privateKeyString, 1000L);
        Claims result = jwtTokenUtil.getClaimsFromToken(token, publicKeyString);
        assertNotNull(result);
        assertEquals("username", result.get("username"));
    }

    @Test
    @DisplayName("输入为空")
    @Tags({
            @Tag("@id:89"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/12/6")
    })
    void testGetClaimsFromToken_ShouldThrowException() {
        String token = jwtTokenUtil.generateToken(claims, privateKeyString, 1000L);
        assertThrows(IllegalArgumentException.class, () -> jwtTokenUtil.getClaimsFromToken("", publicKeyString));
        assertThrows(IllegalArgumentException.class, () -> jwtTokenUtil.getClaimsFromToken(token, ""));
    }
}