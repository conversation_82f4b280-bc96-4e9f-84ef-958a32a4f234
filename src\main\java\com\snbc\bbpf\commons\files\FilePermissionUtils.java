/*
 * 版权所有 2009-2024山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.files;

import com.snbc.bbpf.commons.system.OSType;
import com.snbc.bbpf.commons.system.SystemInfoUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.attribute.AclEntry;
import java.nio.file.attribute.AclEntryPermission;
import java.nio.file.attribute.AclEntryType;
import java.nio.file.attribute.AclFileAttributeView;
import java.nio.file.attribute.PosixFilePermission;
import java.nio.file.attribute.PosixFilePermissions;
import java.nio.file.attribute.UserPrincipal;
import java.util.ArrayList;
import java.util.EnumSet;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

/**
 * 文件权限操作帮助类
 * 1. 可以对文件，角色和读写权限进行设置
 * 2. 需要支持对符合条件的文件进行批量设置权限。
 * 3. 支持windows和linux
 * <p>
 * 使用示例：<br>
 *  1.获取文件的权限列表：<br>
 *      FilePermissionUtils.getFilePermissions(filePath); ==> List:{FilePermissionType.READ, FilePermissionType.WRITE} <br>
 *  2.设置文件权限(不确定文件系统) <br>
 *      FilePermissionUtils.setFilePermissions(filePath, widowsPermissions,"rw-r--r--"); <br>
 *  3.设置windows下多用户或组的权限 <br>
 *      FilePermissionUtils.setFilePermissions(filePath, widowsPermissions,null); <br>
 * </p>
 *
 * <AUTHOR>
 * @module 文件处理
 * @date 2024/8/9 9:38
 * @copyright 2024 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class FilePermissionUtils {

    public static final String EVERYONE_NAME = "everyone";

    public static final String USER_NAME = "user.name";

    private FilePermissionUtils() {
        throw new IllegalStateException("Utility class");
    }
    /**
     * 获取指定文件的文件权限列表 <br>
     * 该方法会返回对应文件的读写执行权限列表。<br>
     * 注意：windows下everyone或当前登录用户如果设置了ACL权限，且权限只有数据读取权限才认为是具有读权限。其他3种权限不认为有读权限 <br>
     *     只有写入数据和追加数据有权限才认为是写入权限。其他3种写权限不认为有写权限。<br>
     *     linux下只是当前用户的权限。
     *
     * @param filePath  文件路径
     * @return java.util.List<com.snbc.bbpf.commons.files.FilePermissionType>
     * @throws IllegalArgumentException 文件路径不存在
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/8/9
     */
    public static List<FilePermissionType> getFilePermissions(String filePath) throws IOException  {
        Path path = FilePathUtils.getFilePath(filePath);
        List<FilePermissionType> list = new ArrayList<>();

        if(SystemInfoUtil.getSystemType() == OSType.WINDOWS){
            getFilePermissionsByWindows(path, list);
        }
        if(SystemInfoUtil.getSystemType() != OSType.WINDOWS){
            getFilePermissionByLinux(path, list);
        }
        return list;
    }

    /**
     * linux系统下获取文件权限列表
     *
     * @param path  文件路径
     * @param list  权限列表
     * @return void
     * @throws
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/8/12
     */
    private static void getFilePermissionByLinux(Path path, List<FilePermissionType> list) throws IOException {
        Set<PosixFilePermission> posixFilePermissions = Files.getPosixFilePermissions(path);
        if(posixFilePermissions.contains(PosixFilePermission.OWNER_READ)){
            list.add(FilePermissionType.READ);
        }
        if(posixFilePermissions.contains(PosixFilePermission.OWNER_WRITE)){
            list.add(FilePermissionType.WRITE);
        }
        if(posixFilePermissions.contains(PosixFilePermission.OTHERS_EXECUTE)){
            list.add(FilePermissionType.EXECUTE);
        }
    }
    /**
     * windows下获取文件的权限列表
     *
     * @param path  文件路径
     * @param list  权限列表
     * @return void
     * @throws
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/8/12
     */
    private static void getFilePermissionsByWindows(Path path, List<FilePermissionType> list) throws IOException {
        AclFileAttributeView aclView = Files.getFileAttributeView(path, AclFileAttributeView.class);
        if (aclView == null) {
            throw new RuntimeException("ACL view is not supported on this file system.");
        }
        List<AclEntry> aclEntries = aclView.getAcl();
        for (AclEntry aclEntry : aclEntries) {
            if(aclEntry.principal().getName().toLowerCase(Locale.getDefault()).contains(EVERYONE_NAME)||
                    aclEntry.principal().getName().contains(System.getProperty(USER_NAME))){
                if(aclEntry.permissions().contains(AclEntryPermission.READ_DATA)){
                    list.add(FilePermissionType.READ);
                }
                if(aclEntry.permissions().contains(AclEntryPermission.WRITE_DATA)||
                        aclEntry.permissions().contains(AclEntryPermission.APPEND_DATA)){
                    list.add(FilePermissionType.WRITE);
                }
                if(aclEntry.permissions().contains(AclEntryPermission.EXECUTE)){
                    list.add(FilePermissionType.EXECUTE);
                }
            }

        }
    }


    /**
     * 对指定文件进行权限设置，支持windows或linux，默认不会清空原有权限设置。
     * <p>
     * 如果不确定操作系统，建议windows和linux权限设置都要进行设置 <br>
     * windowsPermissions的map参数的key是windows的用户或用户组名称 <br>
     * linuxPermissions格式为linux下的格式 <br>
     * 注意如果当前用户无法进行权限操作，可能会抛出异常或修改权限不生效的情况。<br>
     * 使用时务必确保当前用户有对应文件修改权限的权限。<br>
     * </p>
     *
     * @param filePath  文件路径
     * @param windowsPermissions    windows下权限设置 key为用户和用户组名称 确认不是windows可传NULL
     * @param linuxPermissions  linux/macos下权限设置
     *                          三个为一组分别是group owner others
     *                          r：读 w：写 x:执行 -:不设置 格式 rwxrwxrwx
     *                          确认不是linux/macos可以传null
     * @return void
     * @throws
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/8/9
     */
    public static void setFilePermissions(String filePath,
                                          Map<String,List<FilePermissionType>> windowsPermissions,
                                          String linuxPermissions) {
        setFilePermissions(filePath, windowsPermissions, linuxPermissions,false);
    }

    /**
     * 对指定文件进行权限设置，支持windows或linux。
     * <p>
     * 如果不确定操作系统，建议windows和linux权限设置都要进行设置 <br>
     * windowsPermissions的map参数的key是windows的用户或用户组名称 <br>
     * linuxPermissions格式为linux下的格式 <br>
     * 注意如果当前用户无法进行权限操作，可能会抛出异常或修改权限不生效的情况。<br>
     * 使用时务必确保当前用户有对应文件修改权限的权限。<br>
     * </p>
     *
     * @param filePath  文件路径
     * @param windowsPermissions    windows下权限设置 key为用户和用户组名称 确认不是windows可传NULL
     * @param linuxPermissions  linux/macos下权限设置
     *                          三个为一组分别是group owner others
     *                          r：读 w：写 x:执行 -:不设置 格式 rwxrwxrwx
     *                          确认不是linux/macos可以传null
     * @param isClear   是否情空原有权限设置
     * @return void
     * @throws
     * @since 1.0.0
     * <AUTHOR>
     * @date 2024/8/12
     */
    public static void setFilePermissions(String filePath,
                                          Map<String,List<FilePermissionType>> windowsPermissions,
                                          String linuxPermissions,
                                          boolean isClear) {
        Path path = FilePathUtils.getFilePath(filePath);
        if(!ObjectUtils.isEmpty(windowsPermissions) && SystemInfoUtil.getSystemType() == OSType.WINDOWS){
            setFilePermissionsByWindows(path, windowsPermissions,isClear);
        }
        if(StringUtils.isNotBlank(linuxPermissions) && SystemInfoUtil.getSystemType() != OSType.WINDOWS){
            setFilePermissionsByLinux(path, linuxPermissions);
        }
    }

    /**
     * 对linux下文件权限设置
     *
     * @param path  文件路径
     * @param linuxPermission linux/macos下权限设置
     *                        三个为一组分别是group owner others
     *                        r：读 w：写 x:执行 -:不设置 格式 rwxrwxrwx，
     *                        确认不是linux/macos可以传null
     * @return void
     * @throws
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/8/9
     */
    private static void setFilePermissionsByLinux(Path path, String linuxPermission) {
        // 对于linux/unix/macos权限设置使用PosixFilePermission
        Set<PosixFilePermission> posixPermissions = PosixFilePermissions.fromString(linuxPermission);
        // 设置POSIX权限
        try {
            Files.setPosixFilePermissions(path, posixPermissions);
        } catch (IOException e) {
            throw new RuntimeException("set posix permission fail.",e);
        }
    }

    /**
     * 对windows下文件进行权限设置，可对多个用户和用户组进行设置
     *
     * @param path 文件路径
     * @param permissions  windows下当前用户的权限设置 确认不是windows可传NULL
     * @param isClear  是否清空已有权限
     * @return void
     * @throws
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/8/9
     */
    private static void setFilePermissionsByWindows(Path path, Map<String,List<FilePermissionType>> permissions, boolean isClear) {
        permissions.forEach((k,v) -> {
            // 对于windows权限设置使用AclFileAttributeView
            AclFileAttributeView aclView = Files.getFileAttributeView(path, AclFileAttributeView.class);
            if (aclView == null) {
                throw new RuntimeException("ACL view is not supported on this file system.");
            }

            // 清除已经存在的ACL 实体
            try {
                if(isClear) {
                    aclView.setAcl(new ArrayList<>());
                }
                // 创建新的ACL 基于当前用户增加权限
                UserPrincipal userPrincipal = null;
                userPrincipal = path.getFileSystem().getUserPrincipalLookupService().lookupPrincipalByName(k);
                AclEntry.Builder entryBuilder = AclEntry.newBuilder()
                    .setPrincipal(userPrincipal)
                    .setType(AclEntryType.ALLOW);
                Set<AclEntryPermission> permissionSet = EnumSet.noneOf(AclEntryPermission.class);
                if (v.contains(FilePermissionType.READ)) {
                    permissionSet.add(AclEntryPermission.READ_DATA);
                }
                if (v.contains(FilePermissionType.WRITE)) {
                    permissionSet.add(AclEntryPermission.WRITE_DATA);
                    permissionSet.add(AclEntryPermission.APPEND_DATA);
                }
                if (v.contains(FilePermissionType.EXECUTE)) {
                    permissionSet.add(AclEntryPermission.EXECUTE);
                }
                entryBuilder.setPermissions(permissionSet);
                List<AclEntry> aclEntries = aclView.getAcl();
                aclEntries.add(entryBuilder.build());

                //按照新的权限进行设置
                aclView.setAcl(aclEntries);
            } catch (IOException e) {
                throw new RuntimeException("setAcl failed.", e);
            }
        });

    }
}
