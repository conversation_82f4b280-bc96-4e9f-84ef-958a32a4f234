package com.snbc.bbpf.commons.bytes;


import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.function.Consumer;

/**
 * byte数组根据大小端转换
 * <p>将char类型根据大小端转换为字节数组</p>
 * <p>将short类型根据大小端转换为字节数组</p>
 * <p>将int类型根据大小端转换为字节数组</p>
 * <p>将long类型根据大小端转换为字节数组</p>
 * <p>将字节数组根据大小端转换为char数据</p>
 * <p>将字节数组根据大小端转换为short数据</p>
 * <p>将字节数组根据大小端转换为int类型的ushort数据</p>
 * <p>将字节数组根据大小端转换为int数据</p>
 * <p>将字节数组根据大小端转换为long类型的uint数据</p>
 * <p>将字节数组根据大小端转换为long数据</p>
 * <p>将字节数组根据大小端转换为String类型的ulong数据</p>
 * 示例：1.将int转换为小端 ByteOrderUtils.toByteArray(1,ByteOrder.LITTLE_ENDIAN)
 *      2.将int转换为大端 ByteOrderUtils.toByteArray(1,ByteOrder.BIG_ENDIAN)
 *      3.将大端字节转换为int类型 ByteOrderUtils.toInt(new byte[]{0x00, 0x00, 0x04, 0x18},ByteOrder.BIG_ENDIAN)
 *      4.将小端字节转换为int类型 ByteOrderUtils.toInt(new byte[]{0x00, 0x00, 0x04, 0x18},ByteOrder.LITTLE_ENDIAN)
 * <AUTHOR>
 * @module 字节处理
 * @date 2024/8/15 19:18
 * @copyright 2024 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class ByteOrderUtils {

    /**
     * 将char类型根据大小端转换为字节数组
     * 转换的字节大小与 Character.BYTES 有关
     * @param val 需要转换的值
     * @param order   ByteOrder常量，区分大小端
     * @return 转换后的字节数组
     * <AUTHOR>
     * @date 2024/8/15
     * @since 1.5.0
     */
    public static byte[] toByteArray(char val, ByteOrder order){
        return toByteArray(Character.BYTES,order,buffer -> buffer.putChar(val));
    }

    /**
     * 将short类型根据大小端转换为字节数组
     * 转换的字节大小与 Short.BYTES 有关
     * @param val 需要转换的值
     * @param order   ByteOrder常量，区分大小端
     * @return 转换后的字节数组
     * <AUTHOR>
     * @date 2024/8/15
     * @since 1.5.0
     */
    public static byte[] toByteArray(short val, ByteOrder order){
        return toByteArray(Short.BYTES,order,buffer -> buffer.putShort(val));
    }

    /**
     * 将int类型根据大小端转换为字节数组
     * 转换的字节大小与 Integer.BYTES 有关
     * @param val 需要转换的值
     * @param order   ByteOrder常量，区分大小端
     * @return 转换后的字节数组
     * <AUTHOR>
     * @date 2024/8/15
     * @since 1.5.0
     */
    public static byte[] toByteArray(int val, ByteOrder order){
        return toByteArray(Integer.BYTES,order,buffer -> buffer.putInt(val));
    }

    /**
     * 将long类型根据大小端转换为字节数组
     * 转换的字节大小与 Long.BYTES 有关
     * @param val 需要转换的值
     * @param order   ByteOrder常量，区分大小端
     * @return 转换后的字节数组
     * <AUTHOR>
     * @date 2024/8/15
     * @since 1.5.0
     */
    public static byte[] toByteArray(long val, ByteOrder order){
        return toByteArray(Long.BYTES,order,buffer -> buffer.putLong(val));
    }


    /**
     * 将byte数组根据大小端转换为char类型
     * 转换的字节大小与 Character.BYTES 有关
     * @param bytes 需要转换的字节数组
     * @param order   ByteOrder常量，区分大小端
     * @return 转换后的值
     * <AUTHOR>
     * @date 2024/8/15
     * @since 1.5.0
     */
    public static char toChar(byte[] bytes,ByteOrder order){
        return createByteBuffer(bytes, 0, Character.BYTES, order, Character.BYTES).getChar();
    }

    /**
     * 将byte数组根据大小端转换为short类型
     * 转换的字节大小与 Short.BYTES 有关
     * @param bytes 需要转换的字节数组
     * @param order   ByteOrder常量，区分大小端
     * @return 转换后的值
     * <AUTHOR>
     * @date 2024/8/15
     * @since 1.5.0
     */
    public static short toShort(byte[] bytes, ByteOrder order) {
        return createByteBuffer(bytes, 0, Short.BYTES, order, Short.BYTES).getShort();
    }

    /**
     * 将byte数组根据大小端转换为int类型的ushort类型
     * 转换的字节大小与 Short.BYTES 有关
     * @param bytes 需要转换的字节数组
     * @param order   ByteOrder常量，区分大小端
     * @return 转换后的值
     * <AUTHOR>
     * @date 2024/8/15
     * @since 1.5.0
     */
    public static int toUnsignedShortInt(byte[] bytes, ByteOrder order) {
        return Short.toUnsignedInt(createByteBuffer(bytes, 0, Short.BYTES, order, Short.BYTES).getShort());
    }

    /**
     * 将byte数组根据大小端转换为int类型
     * 转换的字节大小与 Integer.BYTES 有关
     * @param bytes 需要转换的字节数组
     * @param order   ByteOrder常量，区分大小端
     * @return 转换后的值
     * <AUTHOR>
     * @date 2024/8/15
     * @since 1.5.0
     */
    public static int toInt(byte[] bytes,ByteOrder order){
        return createByteBuffer(bytes, 0, Integer.BYTES, order, Integer.BYTES).getInt();
    }

    /**
     * 将byte数组根据大小端转换为long类型的uint类型
     * 转换的字节大小与 Integer.BYTES 有关
     * @param bytes 需要转换的字节数组
     * @param order   ByteOrder常量，区分大小端
     * @return 转换后的值
     * <AUTHOR>
     * @date 2024/8/15
     * @since 1.5.0
     */
    public static long toUnsignedIntLong(byte[] bytes, ByteOrder order){
        return Integer.toUnsignedLong(createByteBuffer(bytes, 0, Integer.BYTES, order, Integer.BYTES).getInt());
    }

    /**
     * 将byte数组根据大小端转换为long类型
     * 转换的字节大小与 Long.BYTES 有关
     * @param bytes 需要转换的字节数组
     * @param order   ByteOrder常量，区分大小端
     * @return 转换后的值
     * <AUTHOR>
     * @date 2024/8/15
     * @since 1.5.0
     */
    public static long toLong(byte[] bytes, ByteOrder order) {
        return createByteBuffer(bytes, 0, Long.BYTES, order, Long.BYTES).getLong();
    }

    /**
     * 将byte数组根据大小端转换为String类型的ulong类型
     * 转换的字节大小与 Long.BYTES 有关
     * @param bytes 需要转换的字节数组
     * @param order   ByteOrder常量，区分大小端
     * @return 转换后的值
     * <AUTHOR>
     * @date 2024/8/15
     * @since 1.5.0
     */
    public static String toUnsignedLongString(byte[] bytes, ByteOrder order) {
        return Long.toUnsignedString(createByteBuffer(bytes, 0, Long.BYTES, order, Long.BYTES).getLong());
    }

    /**
     * 将long类型根据大小端转换为字节数组
     * 转换的字节大小与 Long.BYTES 有关,当length超过context.length-start时，length=context.length-start
     * @param context 要拷贝的字节数组
     * @param start   开始位置
     * @param length   要获取的长度
     * @return 转换后的字节数组
     * <AUTHOR>
     * @date 2024/8/15
     * @since 1.5.0
     */
    public static byte[] getByteSection(byte[] context, int start, int length){
        if(length > context.length-start ){
            length = context.length-start;
        }
        byte[] res = new byte[length];
        System.arraycopy(context,start,res,0,length);
        return res;
    }

    /**
     * 创建一个ByteBuffer
     * @param bytes 字节数组
     * @param start 开始位置
     * @param length 长度
     * @param order 大小端
     * @param size  大小
     * @return ByteBuffer
     * <AUTHOR>
     * @date 2024/8/15
     * @since 1.5.0
     */
    private static ByteBuffer createByteBuffer(byte[] bytes, int start, int length, ByteOrder order, int size){
        byte[] byteSection = getByteSection(bytes, start, length);
        ByteBuffer buffer = ByteBuffer.allocate(size);
        buffer.order(order);
        buffer.put(byteSection);
        buffer.flip();
        return buffer;
    }

    /**
     * 创建一个ByteBuffer，并且传入值由外部调用者决定，返回字节
     * @param byteSize 长度
     * @param order 大小端
     * @param toByteArray 执行的操作
     * @return byte数组
     * <AUTHOR>
     * @date 2024/8/15
     * @since 1.5.0
     */
    private static byte[] toByteArray(int byteSize, ByteOrder order, Consumer<ByteBuffer> toByteArray){
        ByteBuffer allocate = ByteBuffer.allocate(byteSize).order(order);
        toByteArray.accept(allocate);
        allocate.flip();
        byte[] bytes = new byte[byteSize];
        allocate.get(bytes);
        return bytes;
    }



}
