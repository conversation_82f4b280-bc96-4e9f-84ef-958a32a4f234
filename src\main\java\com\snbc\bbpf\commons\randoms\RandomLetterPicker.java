/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.randoms;


import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 正则随机辅助类（正则来自github）
 *
 * <AUTHOR>
 * @module 字符串处理
 * @date 2023/4/28 1:46
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class RandomLetterPicker {

    private static final int BUFFER_STEP = 2;
    private final List<String> letters;
    private final int size;
    private final Random random;


    public static class Builder {
        private List<String> letters;
        private Random random;

        public Builder() {
            letters = new ArrayList<>();
            random = null;
        }

        public <E extends Enum<E> & Letter> Builder addAllByEnum(Class<E> enumClass) {
            letters.addAll(Arrays.stream(enumClass.getEnumConstants())
                    .map(e -> e.getLetter())
                    .collect(Collectors.toList()));
            return this;
        }

        public Builder addAll(List<String> list) {
            letters.addAll(list);
            return this;
        }

        public Builder add(String letter) {
            letters.add(letter);
            return this;
        }

        public Builder remove(String remove) {
            letters = letters.stream()
                    .filter(l -> !l.equals(remove))
                    .collect(Collectors.toList());
            return this;
        }

        public Builder setRandom(Random random) {
            this.random = random;
            return this;
        }

        public RandomLetterPicker build() {
            return new RandomLetterPicker(this);
        }

        public List<String> getLetters() {
            return letters;
        }

        public Random getRandom() {
            return random;
        }
    }

    public static Builder builder() {
        return new Builder();
    }

    /**
     * 构造RandomLetterPicker实例
     * @param b 构建器对象
     * <AUTHOR>
     * @date 2023/4/28
     * @since 1.0.0
     */
    private RandomLetterPicker(Builder b) {
        letters = Collections.unmodifiableList(b.getLetters());
        if (b.getRandom() == null) {
            random = new Random();
        } else {
            random = b.getRandom();
        }

        size = letters.size();
    }

    /**
     * 随机选取一个字符
     * @return 随机字符
     * <AUTHOR>
     * @date 2023/4/28
     * @since 1.0.0
     */
    public String pickRandomLetter() {
        return letters.get(random.nextInt(size));
    }

    public static RandomLetterPicker constructByCharacterRange(final List<String> bounds) {
        Builder definedPickerBuilder = builder();
        int bufferSize = bounds.size();

        for (int i = 0; i < bufferSize; i += BUFFER_STEP) {
            int beginCode = bounds.get(i).charAt(0);
            int endCode = bounds.get(i + 1).charAt(0);

            if (beginCode > endCode) {
                throw new IllegalArgumentException("Detected invalid character range: ["
                        + (char) beginCode + "-" + (char) endCode + "]");
            }

            for (int code = beginCode; code <= endCode; code++) {
                definedPickerBuilder.add(String.valueOf((char) code));
            }
        }

        return definedPickerBuilder.build();
    }
}

interface Letter {
    String getLetter();
}

/**
 * 数字常量
 */
enum DigitLetter implements Letter {
    ZERO("0"), ONE("1"), TWO("2"), THREE("3"), FOUR("4"),
    FIVE("5"), SIX("6"), SEVEN("7"), EIGHT("8"), NINE("9");

    private final String letter;

    DigitLetter(String letter) {
        this.letter = letter;
    }

    @Override
    public String getLetter() {
        return letter;
    }
}

/**
 * 小写字母常量
 */
enum LowerCaseLetter implements Letter {
    A("a"), B("b"), C("c"), D("d"), E("e"), F("f"), G("g"), H("h"),
    I("i"), J("j"), K("k"), L("l"), M("m"), N("n"), O("o"), P("p"),
    Q("q"), R("r"), S("s"), T("t"), U("u"), V("v"), W("w"), X("x"),
    Y("y"), Z("z");

    private final String letter;

    LowerCaseLetter(String letter) {
        this.letter = letter;
    }

    @Override
    public String getLetter() {
        return letter;
    }
}

/**
 * Symbol常量
 */
enum SymbolLetter implements Letter {
    TILDE("~"), BACKTICK("`"), EXCLAMATION("!"), AT("@"), DOLLER("$"), PERCENT("%"), CAP("^"),
    AND("&"), ASTERISK("*"), LPAREN("("), RPAREN(")"), MINUS("-"), UNDERBAR("_"), PLUS("+"),
    EQUAL("="), LBRACE("{"), RBRACE("}"), LBRACKET("["), RBRACKET("]"), PIPE("|"), COLON(":"),
    SEMICOLON(";"), SINGLEQUOTE("'"), DOT("."), LANGLE("<"), RANGLE(">"), QUESTION("?"),
    SLASH("/"), SHARP("#"), COMMA(","), BACKSLASH("\\"), DOUBLEQUOTE("\"");

    private final String letter;

    SymbolLetter(String letter) {
        this.letter = letter;
    }

    @Override
    public String getLetter() {
        return letter;
    }
}

/**
 * 大写字母常量
 */
enum UpperCaseLetter implements Letter {
    A("A"), B("B"), C("C"), D("D"), E("E"), F("F"), G("G"), H("H"),
    I("I"), J("J"), K("K"), L("L"), M("M"), N("N"), O("O"), P("P"),
    Q("Q"), R("R"), S("S"), T("T"), U("U"), V("V"), W("W"), X("X"),
    Y("Y"), Z("Z");

    private final String letter;

    UpperCaseLetter(String letter) {
        this.letter = letter;
    }


    @Override
    public String getLetter() {
        return letter;
    }
}

class RegexNormalizer {

    private static final int GROUP_TWO = 2;
    private static final int GROUP_THREE = 3;

    private static final int RADIX = 10;

    private static final Pattern REPETITION_QUANTIFIER_RE = Pattern
            .compile("([^\\\\])\\{([0-9]+),([0-9]+)?\\}");
    private static final Pattern ASTERISK_QUANTIFIER_RE = Pattern.compile("([^\\\\])\\*");
    private static final Pattern PLUS_QUANTIFIER_RE = Pattern.compile("([^\\\\])\\+");
    private static final Pattern QUESTION_QUANTIFIER_RE = Pattern.compile("([^\\\\])\\?");

    private final int numOfUpperLimit;
    private final Random random;

    public RegexNormalizer(int numOfUpperLimit, Random random) {
        this.numOfUpperLimit = numOfUpperLimit;
        this.random = random;
    }

    public String normalizeQuantifiers(final String regex) {
        String expanded = regex;

        Matcher repetitionMatcher = REPETITION_QUANTIFIER_RE.matcher(expanded);
        while (repetitionMatcher.find()) {
            int start = Integer.parseInt(repetitionMatcher.group(GROUP_TWO), RADIX);
            int end;
            if (repetitionMatcher.group(GROUP_THREE) == null) {
                end = numOfUpperLimit;
            } else {
                end = Integer.parseInt(repetitionMatcher.group(GROUP_THREE), RADIX);
            }
            expanded =
                    repetitionMatcher.replaceFirst(repetitionMatcher.group(1) + "{"
                            + getRandomNumAsString(start, end) + "}");
            repetitionMatcher = REPETITION_QUANTIFIER_RE.matcher(expanded);
        }

        Matcher asteriskMatcher = ASTERISK_QUANTIFIER_RE.matcher(expanded);
        while (asteriskMatcher.find()) {
            expanded =
                    asteriskMatcher.replaceFirst(asteriskMatcher.group(1) + "{"
                            + getRandomNumAsString(0, numOfUpperLimit) + "}");
            asteriskMatcher = ASTERISK_QUANTIFIER_RE.matcher(expanded);
        }

        Matcher plusMatcher = PLUS_QUANTIFIER_RE.matcher(expanded);
        while (plusMatcher.find()) {
            expanded =
                    plusMatcher.replaceFirst(plusMatcher.group(1) + "{"
                            + getRandomNumAsString(1, numOfUpperLimit) + "}");
            plusMatcher = PLUS_QUANTIFIER_RE.matcher(expanded);
        }

        Matcher questionMatcher = QUESTION_QUANTIFIER_RE.matcher(expanded);
        while (questionMatcher.find()) {
            expanded =
                    questionMatcher.replaceFirst(questionMatcher.group(1) + "{"
                            + getRandomNumAsString(0, 1) + "}");
            questionMatcher = QUESTION_QUANTIFIER_RE.matcher(expanded);
        }

        return expanded;
    }

    private String getRandomNumAsString(final int start, final int end) {
        int bound = end - start;
        if (bound < 0) {
            throw new IllegalArgumentException("Detected invalid quantifier: " + "{" + start + "," + end + "}");
        }
        return Integer.toString(random.nextInt(bound + 1) + start, RADIX);
    }
}


class RandomLetterPickers {
    private static final int MAX_RANGE = 255;
    private final RandomLetterPicker upperCase;
    private final RandomLetterPicker lowerCase;
    private final RandomLetterPicker digit;
    private final RandomLetterPicker symbol;
    private final RandomLetterPicker any;
    private final RandomLetterPicker salt;
    private final RandomLetterPicker binary;
    private final RandomLetterPicker word;
    private final RandomLetterPicker notWord;
    private final RandomLetterPicker notDigit;
    private final RandomLetterPicker space;

    private static final Pattern characterRegex = Pattern.compile("\\W");

    public RandomLetterPickers(Random random) {
        upperCase = RandomLetterPicker.builder()
                .setRandom(random)
                .addAllByEnum(UpperCaseLetter.class)
                .build();

        lowerCase = RandomLetterPicker.builder()
                .setRandom(random)
                .addAllByEnum(LowerCaseLetter.class)
                .build();

        digit = RandomLetterPicker.builder()
                .setRandom(random)
                .addAllByEnum(DigitLetter.class)
                .build();

        symbol = RandomLetterPicker.builder()
                .setRandom(random)
                .addAllByEnum(SymbolLetter.class)
                .build();

        any = RandomLetterPicker.builder()
                .setRandom(random)
                .addAllByEnum(UpperCaseLetter.class)
                .addAllByEnum(LowerCaseLetter.class)
                .addAllByEnum(DigitLetter.class)
                .addAllByEnum(SymbolLetter.class)
                .build();

        salt = RandomLetterPicker.builder()
                .setRandom(random)
                .addAllByEnum(UpperCaseLetter.class)
                .addAllByEnum(LowerCaseLetter.class)
                .addAllByEnum(DigitLetter.class)
                .add(".")
                .add("/")
                .build();

        binary = RandomLetterPicker.builder()
                .setRandom(random)
                .addAll(IntStream.range(0, MAX_RANGE)
                        .mapToObj(i -> Character.toString((char) i))
                        .collect(Collectors.toList()))
                .build();

        word = RandomLetterPicker.builder()
                .setRandom(random)
                .addAllByEnum(UpperCaseLetter.class)
                .addAllByEnum(LowerCaseLetter.class)
                .addAllByEnum(DigitLetter.class)
                .add("_")
                .build();

        notWord = RandomLetterPicker.builder()
                .setRandom(random)
                .addAllByEnum(SymbolLetter.class)
                .remove("_")
                .build();

        notDigit = RandomLetterPicker.builder()
                .setRandom(random)
                .addAllByEnum(UpperCaseLetter.class)
                .addAllByEnum(LowerCaseLetter.class)
                .addAllByEnum(SymbolLetter.class)
                .build();

        space = RandomLetterPicker.builder()
                .setRandom(random)
                .add(" ")
                .add("\t")
                .build();
    }


    public RandomLetterPicker getDigit() {
        return digit;
    }


    public RandomLetterPicker getAny() {
        return any;
    }


    public RandomLetterPicker getWord() {
        return word;
    }

    public RandomLetterPicker getNotWord() {
        return notWord;
    }

    public RandomLetterPicker getNotDigit() {
        return notDigit;
    }

    public RandomLetterPicker getSpace() {
        return space;
    }

    public static class ScannedUserDefinedPicker {
        private final int cursor;
        private final String key;
        private final List<String> bounds;


        public ScannedUserDefinedPicker(
                final int cursorForScanning, final String key, final List<String> bounds) {
            this.cursor = cursorForScanning;
            this.key = key;
            this.bounds = bounds;
        }

        public int getCursor() {
            return cursor;
        }

        public String getKey() {
            return key;
        }

        public List<String> getBounds() {
            return bounds;
        }
    }

    public static ScannedUserDefinedPicker scan(final String[] regexCharacters, final int index) {
        StringBuilder key = new StringBuilder("");
        String character;
        List<String> bounds = new ArrayList<>();

        int i = index;
        while (!"]".equals(character = regexCharacters[++i])) {
            if ("-".equals(character) && !bounds.isEmpty()) {
                String beginCharacter = bounds.get(bounds.size() - 1);
                String endCharacter = regexCharacters[++i];
                key.append(beginCharacter);
                key.append("-");
                key.append(endCharacter);
                bounds.add(endCharacter);
            } else {
                if (characterRegex.matcher(character).matches()) {
                    throw new IllegalArgumentException("'" + character + "'" + "will be treated literally inside []");
                }
                bounds.add(character);
            }
        }

        return new ScannedUserDefinedPicker(i, key.toString(), bounds);
    }
}
