package com.snbc.bbpf.commons.files;

/**
 * 类说明 ini的对象类
 * <p>功能列表</p>
 * <p>1.</p>
 *
 * <AUTHOR>
 * @module 模块
 * @date 2023/11/24 11:21
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class IniSectionDomain {
    /**
     * 分组
     */
    private String section;
    /**
     * 键
     */
    private String key;
    /**
     * 值
     */
    private String value;

    public IniSectionDomain() {
    }

    public IniSectionDomain(String section, String key, String value) {
        this.section = section;
        this.key = key;
        this.value = value;
    }

    @Override
    public String toString() {
        return "IniSectionDomain{" +
                "section='" + section + '\'' +
                ", key='" + key + '\'' +
                ", value='" + value + '\'' +
                '}';
    }

    public String getSection() {
        return section;
    }

    public void setSection(String section) {
        this.section = section;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
