/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.strings;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * 类描述:    .
 *
 * <AUTHOR>
 * 创建时间:  [2023/6/6 16:41]
 */
class HexUtilsTest {


    @Test
    @DisplayName("将字节数组转换为十六进制字符串")
    @Tags({
            @Tag("@id:4"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/6/6")
    })
    void testByteArrayToHexString() {
        byte[] byteArray = {10, 11, 12, 13, 14, 15};
        String hexString = HexUtils.byteArrayToHexString(byteArray);

        assertEquals("0a0b0c0d0e0f", hexString);
    }

    @Test
    @DisplayName("将十六进制字符串转换为字节数组")
    @Tags({
            @Tag("@id:4"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/6/6")
    })
    void testHexStringToByteArray() {
        String hexString = "0a0b0c0d0e0f";
        byte[] byteArray = HexUtils.hexStringToByteArray(hexString);

        byte[] expectedArray = {10, 11, 12, 13, 14, 15};
        assertArrayEquals(expectedArray, byteArray);
    }

    @Test
    @DisplayName("将十六进制字符串转换为字节数组_判断入参空")
    @Tags({
            @Tag("@id:4"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/6/6")
    })
    void testHexStringToByteArrayNull() {
        assertThrows(IllegalArgumentException.class, () -> {
            HexUtils.hexStringToByteArray(null);
        });
    }

    @Test
    @DisplayName("将十六进制字符串转换为字节数组_判断入参空")
    @Tags({
            @Tag("@id:4"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/6/6")
    })
    void testHexStringToByteArrayEmpty() {
        assertThrows(IllegalArgumentException.class, () -> {
            HexUtils.hexStringToByteArray("");
        });
    }

    @Test
    @DisplayName("将十六进制字符串转换为字节数组_判断入参空")
    @Tags({
            @Tag("@id:4"),
            @Tag("@author:xuping2"),
            @Tag("@date:2023/6/6")
    })
    void testHexStringToByteArrayNull2() {
        assertThrows(IllegalArgumentException.class, () -> {
            HexUtils.byteArrayToHexString(null);
        });
    }
}
