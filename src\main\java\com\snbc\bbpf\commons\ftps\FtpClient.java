/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.ftps;

import com.snbc.bbpf.commons.files.FileUtils;
import com.snbc.bbpf.commons.ftps.config.ConnectConfig;
import com.snbc.bbpf.commons.ftps.config.FileTransferType;
import com.snbc.bbpf.commons.ftps.config.FtpMode;
import com.snbc.bbpf.commons.objs.ObjectEmptyCheck;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 【数据传输】 ftp 客户端
 *
 * <AUTHOR>
 * @module 数据传输
 * @date 2023/9/24 17:50
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public class FtpClient extends BaseFtpClient implements ITransferClient {

    //FTP客户端
    private FTPClient client;

    /**
     * 连接FTP服务端
     * @return boolean 是否连接成功
     * @throws IllegalStateException 连接配置无效异常
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/8/23
     */
    @Override
    public synchronized void connect() {
        if (!isConnectParamValid(this.connectConfig)){
            throw new IllegalStateException("connectParam == null || connectParam 参数校验错误！");
        }
        if (isConnected()){
            return ;
        }
        try {
            client = new org.apache.commons.net.ftp.FTPClient();
            client.setControlEncoding(this.connectConfig.getEncoding());
            client.setConnectTimeout((int)this.connectConfig.getConnectTimeout());
            client.setDataTimeout(Duration.ofMillis(this.connectConfig.getTransferTimeout()));

            client.connect(this.connectConfig.getHost(),this.connectConfig.getPort());
            client.login(this.connectConfig.getUserName(),this.connectConfig.getPassword());
            client.setFileType(getFileType(this.connectConfig.getTransferFileType()));
            if (FtpMode.ACTIVE_MODE.getMode().equals(this.connectConfig.getMode())) {
                client.enterLocalActiveMode();
            } else {
                client.enterLocalPassiveMode();
            }
            if (!FTPReply.isPositiveCompletion(client.getReplyCode())){
                disconnect();
            }
            isConnect.set(true);
        } catch (Exception e){
            throw new RuntimeException(e);
        }

    }

    /**
     * 获取文件传送类型
     * @param type 类型名称
     * @return int 类型编号
     * @throws
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/8/23
     */
    private static int getFileType(String type){
        if (FileTransferType.FILE_TRANSFER_TYPE_ASCII.getType().equals(type)){
            return FTP.ASCII_FILE_TYPE;
        } else {
            return FTP.BINARY_FILE_TYPE;
        }
    }

    /**
     * 主动断开FTP连接
     * @return void
     * @throws RuntimeException 断开连接时运行时异常
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/8/23
     */
    @Override
    public synchronized void disconnect() {
        isConnect.set(false);
        if (this.client != null){
            try {
                this.client.disconnect();
            } catch (IOException e) {
                throw new RuntimeException(e);
            } finally {
                this.client = null;
            }

        }
    }
    /**
     * 将本地文件上传到FTP下的指定目录
     * 默认上传成功不删除本地文件，如果远端文件已存在默认覆盖。
     * @param localFilePath  本地文件路径
     * @param remoteDirPath  远端文件路径
     * @return boolean 是否上传成功
     * @throws
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/8/23
     */
    @Override
    public boolean upload(String localFilePath, String remoteDirPath) {
        return this.upload(localFilePath,
                remoteDirPath,
                false,
                true);
    }

    /**
     * 将本地文件上传到FTP下的指定目录
     * @param localFilePath 本地文件路径
     * @param remoteDirPath 远端文件路径
     * @param deleteLocalFileIfUploadSuccess 上传成功是否删除本地文件
     * @param overwriteIfFileExist 如果远端文件已存在是否覆盖
     * @return boolean 上传是否成功
     * @throws
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/8/23
     */
    @Override
    public synchronized boolean upload(String localFilePath,
                                       String remoteDirPath,
                                       boolean deleteLocalFileIfUploadSuccess,
                                       boolean overwriteIfFileExist) {
        checkConnectStatus();

        File localFile = new File(localFilePath);
        if (!localFile.exists()||!localFile.isFile()){
            throw new IllegalArgumentException("local localFile is not exist or is not a localFile!");
        }
        String fileName = localFile.getName();

        String remoteDir = getDirPathWithoutLastSeparator(remoteDirPath);
        String remoteFilePath = remoteDir+File.separator+fileName;
        if (!overwriteIfFileExist && existFileInRemote(remoteFilePath)){
            throw new IllegalArgumentException("remote file is exist!");
        }

        try (FileInputStream fileInputStream = new FileInputStream(localFile)){
            if (!client.changeWorkingDirectory(remoteDir)){
                client.makeDirectory(remoteDir);
                client.changeWorkingDirectory(remoteDir);
            }

            if (client.storeFile(new String(fileName.getBytes(StandardCharsets.UTF_8),this.connectConfig.getEncoding()),fileInputStream)){

                if (deleteLocalFileIfUploadSuccess){
                    Files.delete(localFile.toPath());
                }
                return true;
            }
        } catch (Exception e){
            throw new RuntimeException(e);
        }
        return false;
    }

    /**
     * 判断远程目录或文件是否存在
     * @param remoteFilePath 远程文件路径
     * @return 是否存在
     * <AUTHOR>
     * @date 2023/11/05
     * @since 1.4.0
     */
    public boolean existFileInRemote(String remoteFilePath){
        try {
            FTPFile[] files = this.client.listFiles(remoteFilePath);
            if (files!=null && files.length>0){
                return true;
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return false;
    }

    /**
     * 从远端下载文件到指定目录
     *
     * @param remoteFilePath  远端文件路径
     * @param localDirPath  本地文件目录路径
     * @return boolean 是否下载成功
     * @throws
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/8/23
     */
    @Override
    public boolean download(String remoteFilePath, String localDirPath) {
        return this.download(remoteFilePath,
                localDirPath,
                false,
                true);
    }

    /**
     * 从远端下载文件到指定目录
     * @param remoteFilePath  远端文件路径
     * @param localDirPath  本地文件目录路径
     * @param deleteRemoteFileIfDownloadSuccess  下载成功是否删除远端文件
     * @param overwriteIfFileExist 本地文件已存在是否覆盖
     * @return boolean 是否下载成功
     * @throws
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/8/23
     */
    @Override
    public synchronized boolean download(String remoteFilePath,
                                                String localDirPath,
                                                boolean deleteRemoteFileIfDownloadSuccess,
                                                boolean overwriteIfFileExist){
        checkConnectStatus();
        if (!existFileInRemote(remoteFilePath)){
            throw new IllegalArgumentException("remote file not exist!");
        }

        String localFileName = getFileNameByFilePath(remoteFilePath);
        String localDir = getDirPathWithoutLastSeparator(localDirPath);
        File localFile = new File(localDir+File.separator+localFileName);
        if (!overwriteIfFileExist && localFile.exists()){
            throw new IllegalArgumentException("local file is exist!");
        }

        File localDirFile = localFile.getParentFile();
        if (!localDirFile.exists()||!localDirFile.isDirectory()){
            localDirFile.mkdirs();
        }
        try(InputStream in =this.client.retrieveFileStream(
                new String(remoteFilePath.getBytes(StandardCharsets.UTF_8),
                        this.connectConfig.getEncoding()))) {
            Files.copy(in,localFile.toPath(), StandardCopyOption.REPLACE_EXISTING);

            if (deleteRemoteFileIfDownloadSuccess){
                deleteRemoteFile(remoteFilePath);
            }
            return true;
        }catch (Exception e){
            throw new RuntimeException(e);
        }

    }


    /**
     * 删除远程文件
     *
     * @param remoteFilePath 远程文件路径
     * @return 结果 由于没有权限或文件不存在等原因会导致返回false
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/9/26
     */
    @Override
    public synchronized boolean deleteRemoteFile(String remoteFilePath){
        checkConnectStatus();
        // 文件不存在时，返回 ture
        try {
            return this.client.deleteFile(remoteFilePath);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 批量删除文件名符合条件的远端文件
     *
     * @param remoteDirPath 远端文件目录
     * @param predicate     文件名过滤器
     * @return java.util.Map<java.lang.String, java.lang.Boolean>  符合条件的远程文件是否删除成功列表
     * @throws
     * <AUTHOR>
     * @date 2024/8/23
     * @since 1.5.0
     */
    @Override
    public Map<String, Boolean> batchDeleteRemoteFiles(String remoteDirPath, Predicate<String> predicate) {
        Map<String, Boolean> map = new HashMap<>();
        List<String> list = listFiles(remoteDirPath)
                .stream()
                .filter(predicate)
                .collect(Collectors.toList());
        for (String remoteFilePath : list) {
            String path = remoteDirPath+ LINUX_SEPARATOR +remoteFilePath;
            Boolean isSuccess = Boolean.FALSE;
            try {
                isSuccess = deleteRemoteFile(path);
            }catch(Exception e) {
                isSuccess = Boolean.FALSE;
            }
            map.put(path,isSuccess);
        }
        return map;
    }


    /**
     * 删除远程目录
     * 包含目录和文件
     *
     * @param remoteDir 远程文件路径
     * @return 结果
     * @throws RuntimeException 异常信息
     * @since 1.2.0
     * <AUTHOR>
     * @date 2023/9/26
     */
    @Override
    public synchronized boolean deleteRemoteDir(String remoteDir) {
        checkConnectStatus();
        try {
            return this.delRemoteDir(remoteDir);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 删除远程目录
     * @param remoteDirPath  远程目录路径
     * @return boolean
     * @throws IOException
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/8/23
     */
    private boolean delRemoteDir(String remoteDirPath) throws IOException {
        String remoteDir =  getDirPathWithoutLastSeparator(remoteDirPath);

        FTPFile[] files = this.client.listFiles(remoteDir);
        if (files==null || files.length==0){
            // 目录不存在时，返回false,暂时没有解决方案
            return this.client.removeDirectory(remoteDir);
        }

        boolean deleteResult = true;
        for (FTPFile ftpFile:files){
            String fileOrDirPath = remoteDir + LINUX_SEPARATOR + ftpFile.getName();
            if (ftpFile.isFile()){
                deleteResult = this.client.deleteFile(fileOrDirPath);
                if (!deleteResult){
                    break;
                }
            } else {
                boolean deleteDirResult = delRemoteDir(fileOrDirPath);
                if (!deleteDirResult){
                    deleteResult = false;
                    break;
                }
            }
        }
        if (deleteResult){
            deleteResult = this.client.removeDirectory(remoteDir);
        }
        return deleteResult;
    }

    /**
     * 检查FTP连接状态
     * @param
     * @return void
     * @throws IllegalArgumentException 抛出无效连接的异常。
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/8/23
     */
    private void checkConnectStatus() {
        if (this.client == null || !this.isConnected()){
            throw new IllegalArgumentException("invoke connect first!");
        }
    }

    /**
     * 检查连接参数是否有效
     * @param connectConfig 连接参数
     * @return boolean 是否有效
     * @throws
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/8/23
     */
    private static boolean isConnectParamValid(ConnectConfig connectConfig) {
        return connectConfig !=null && connectConfig.isValid();
    }

    /**
     * 列出远程默认目录的所有文件
     * @return 文件数组
     * <AUTHOR>
     * @date 2023/11/05
     * @since 1.4.0
     */
    @Override
    public List<String> listFiles(){
        try {
            FTPFile[] ftpFiles = this.client.listFiles();
            List<String>  fileNames = new ArrayList<>();
            if (ftpFiles != null){
                for (FTPFile ftpFile : ftpFiles){
                    fileNames.add(ftpFile.getName());
                }
            }
            return fileNames;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 列出指定远程目录的所有文件，目录不存在或目录下没有文件返回空数组
     * @param remoteDirPath 远程目录
     * @return 文件数组
     * <AUTHOR>
     * @date 2023/11/05
     * @since 1.4.0
     */
    @Override
    public List<String> listFiles(String remoteDirPath){
        return listFiles(remoteDirPath,null);
    }

    /**
     * 列出指定远程目录的符合条件的文件，目录不存在或目录下没有文件返回空数组
     *
     * @param remoteDirPath 远程目录
     * @param fileNamePredicate     文件名过滤器
     * @return java.util.List<java.lang.String> 返回文件名列表
     * @throws
     * <AUTHOR>
     * @date 2024/8/23
     * @since 1.5.0
     */
    @Override
    public List<String> listFiles(String remoteDirPath, Predicate<String> fileNamePredicate) {
        List<String>  fileNames = new ArrayList<>();
        try {
            FTPFile[] ftpFiles = this.client.listFiles(remoteDirPath);

            if (ftpFiles != null){
                for (FTPFile ftpFile : ftpFiles){
                    if (fileNamePredicate == null || fileNamePredicate.test(ftpFile.getName())){
                        fileNames.add(ftpFile.getName());
                    }
                }
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return fileNames;
    }

    /**
     * 批量上传文件操作
     * 默认不删除本地文件，会覆盖远端重名文件
     *
     * @param localPath     本地文件路径
     * @param predicate     文件过滤器
     * @param remoteDirPath 远端目录
     * @return java.util.Map<java.lang.String, java.lang.Boolean> 返回文件上传成功列表
     * @throws
     * <AUTHOR>
     * @date 2024/8/23
     * @since 1.5.0
     */
    @Override
    public Map<String, Boolean> batchUpload(String localPath, Predicate<Path> predicate, String remoteDirPath) {
        return batchUpload(localPath, predicate, remoteDirPath, false, true);
    }

    /**
     * 批量上传文件操作
     *
     * @param localPath                      本地文件路径
     * @param predicate                      文件过滤器
     * @param remoteDirPath                  远端目录
     * @param deleteLocalFileIfUploadSuccess 上传成功后是否删除本地文件
     * @param overwriteIfFileExist           是否覆盖已有文件
     * @return java.util.Map<java.lang.String, java.lang.Boolean> 返回文件上传成功列表
     * @throws
     * <AUTHOR>
     * @date 2024/8/23
     * @since 1.5.0
     */
    @Override
    public Map<String, Boolean> batchUpload(String localPath,
                                            Predicate<Path> predicate,
                                            String remoteDirPath,
                                            boolean deleteLocalFileIfUploadSuccess,
                                            boolean overwriteIfFileExist) {
        List<Path> list = FileUtils.filter(localPath, predicate);
        Map<String, Boolean> result = new HashMap<>();
        for (Path path : list) {
            Boolean isSuccess = upload(path.toString(), remoteDirPath, deleteLocalFileIfUploadSuccess, overwriteIfFileExist);
            result.put(path.toAbsolutePath().toString(), isSuccess);
        }
        return result;
    }

    /**
     * 批量下载文件名符合条件的文件
     * 默认不删除远端文件，会覆盖本地重名文件
     *
     * @param remoteDirPath 远端文件路径
     * @param predicate      文件过滤器
     * @param localDirPath   本地文件路径
     * @return java.util.Map<java.lang.String, java.lang.Boolean> 返回文件下载成功列表
     * @throws
     * <AUTHOR>
     * @date 2024/8/23
     * @since 1.5.0
     */
    @Override
    public Map<String, Boolean> batchDownload(String remoteDirPath, Predicate<String> predicate, String localDirPath) {
        return batchDownload(remoteDirPath, predicate, localDirPath, false, true);
    }

    /**
     * 批量下载文件名符合条件的文件
     *
     * @param remoteDirPath    远端文件路径
     * @param predicate        文件过滤器
     * @param localPath        本地文件路径
     * @param deleteRemoteFileIfDownloadSuccess  下载成功是否删除远端文件
     * @param overwriteIfFileExist           是否覆盖已有文件
     * @return java.util.Map<java.lang.String, java.lang.Boolean> 返回文件下载成功列表
     * @throws
     * <AUTHOR>
     * @date 2024/8/23
     * @since 1.5.0
     */
    @Override
    public Map<String, Boolean> batchDownload(String remoteDirPath,
                                              Predicate<String> predicate,
                                              String localPath,
                                              boolean deleteRemoteFileIfDownloadSuccess,
                                              boolean overwriteIfFileExist) {
        List<String> list = listFiles(remoteDirPath, predicate);
        Map<String, Boolean> result = new HashMap<>();
        if (!ObjectEmptyCheck.isEmpty(list)) {
            for (String fileName : list) {
                String filePath = remoteDirPath+ LINUX_SEPARATOR +fileName;
                Boolean isSuccess = download(filePath, localPath, deleteRemoteFileIfDownloadSuccess, overwriteIfFileExist);
                result.put(filePath, isSuccess);
            }
        }
        return result;
    }

    /**
     * 在远端创建目录
     *
     * @param remoteDirPath 远端目录
     * @param dirName       需要创建的目录名称
     * @return boolean 是否创建成功
     * @throws
     * <AUTHOR>
     * @date 2024/8/23
     * @since 1.5.0
     */
    @Override
    public boolean createRemoteDir(String remoteDirPath, String dirName)  {
        try {
            return client.makeDirectory(remoteDirPath+ LINUX_SEPARATOR +dirName);
        } catch (IOException e) {
            return false;
        }
    }


}
