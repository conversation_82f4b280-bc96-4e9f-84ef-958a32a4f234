/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.collects;

import com.snbc.bbpf.commons.objs.ObjectEmptyCheck;

import java.lang.reflect.Array;
import java.util.Arrays;
import java.util.Collection;
import java.util.Map;

/**
 * 【集合处理】对象数组是否为空
 * <p>需要判断对象数组是否为空。 需要针对不同情况判断是否为空（可以有不同的实现方法）：</p>
 * <p> 1.对象数组本身为空。</p>
 * <p> 2.对象数组本身不为空，但对象数组长度为0。</p>
 * <p> 3.对象数组本身不为空，但对象数组长度大于0，但对象数组中对象都为空对象。</p>
 * <p> 4.对象数组本身不为空，但对象数组长度大于0，但对象数组中对象类型也是集合类型，则需要递归判断。</p>
 * <p> 5.对象数组本身不为空，但对象数组长度大于0，但对象数组中对象类型也是字符类型，则需要判断字符类型是否为空。 针对数组中对象判空可以单独调用对象判断的方法进行（需要其他模块先完成后进行开发） 注意处理效率。</p>
 *
 * <AUTHOR>
 * @module 集合处理
 * @date 2023/4/23 9:58
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class ObjectArrayCheck {

    private ObjectArrayCheck() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 对象集合本身为空
     * 例如：
     * <p>
     *     ObjectArrayCheck.isNull(new Object[]{"objs"}) => false
     *     ObjectArrayCheck.isNull(null)) => true
     *     ObjectArrayCheck.isNull(new Object[]{}) => false
     * </p>
     * @param objs  集合
     * @return boolean 如果对象集合为空则为true
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/23
     */
    public static boolean isNull(Object[] objs) {
        return objs == null;
    }

    /**
     * 本身不为空，但集合中没有对象
     * 例如：
     * <p>
     *     ObjectArrayCheck.isEmpty(null)) => true
     *     ObjectArrayCheck.isEmpty(new Object[]{}) => true
     *     ObjectArrayCheck.isEmpty(new Object[]{"object"}) => false
     *
     * </p>
     * @param objs 集合
     * @return boolean 如果集合为空或集合中没有对象
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/23
     */
    public static boolean isEmpty(Object[] objs) {
        return objs == null || objs.length == 0;
    }

    /**
     * 非空的集合对象中包括Null的对象（Map的话包含key或value）
     * 例如：
     * <p>
     *     ObjectArrayCheck.hasNullItem(new Object[]{"objs"}) => false
     *     ObjectArrayCheck.hasNullItem(null) => true
     *     ObjectArrayCheck.hasNullItem(new Object[]{null}) => true
     *     ObjectArrayCheck.hasNullItem(new Object[]{""}) => false
     *     ObjectArrayCheck.hasNullItem(new Object[]{"objs",""}) = false
     *     ObjectArrayCheck.hasNullItem(new Object[]{"objs",null}) => true
     *     ObjectArrayCheck.hasNullItem(new Object[]{"objs",new Object[]{}}) => false
     *     ObjectArrayCheck.hasNullItem(new Object[]{"objs",new Object[]{null}}) => true
     *     ObjectArrayCheck.hasNullItem(new Object[]{"objs",new Object[]{"abc",null}}) => true
     *     ObjectArrayCheck.hasNullItem(new Object[]{"objs",new Object[]{"abc",""}}) => false
     *     ObjectArrayCheck.hasNullItem(new Object[]{"objs",new Object[]{1,""}}) = false
     *     ObjectArrayCheck.hasNullItem(new Object[]{"objs",new Object[]{1, Arrays.asList("12","23")}}) => false
     *     ObjectArrayCheck.hasNullItem(new Object[]{"objs",new Object[]{1, Arrays.asList("12",null)}}) => true
     *     ObjectArrayCheck.hasNullItem(new Object[]{"objs",new Object[]{1, Arrays.asList("12","")}}) => false
     * </p>
     * @param objs 非空的集合对象
     * @return boolean 如果集合对象中包含空对象则返回true
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/23
     */
    public static boolean hasNullItem(Object[] objs) {
        return isNull(objs) || Arrays.stream(objs).anyMatch(ObjectArrayCheck::isNull);
    }

    /**
     * 非空的集合对象中包括Empyt的对象（Map的话包含key或value）
     * 例如：
     * <p>
     *     ObjectArrayCheck.hasEmptyItem(new Object[]{"objs"}) => false
     *     ObjectArrayCheck.hasEmptyItem(null) => true
     *     ObjectArrayCheck.hasEmptyItem(new Object[]{null}) => true
     *     ObjectArrayCheck.hasEmptyItem(new Object[]{""}) => true
     *     ObjectArrayCheck.hasEmptyItem(new Object[]{"objs",""}) => true
     *     ObjectArrayCheck.hasEmptyItem(new Object[]{"objs",null}) => true
     *     ObjectArrayCheck.hasEmptyItem(new Object[]{"objs",new Object[]{}}) => true
     *     ObjectArrayCheck.hasEmptyItem(new Object[]{"objs",new Object[]{null}}) => true
     *     ObjectArrayCheck.hasEmptyItem(new Object[]{"objs",new Object[]{"abc",null}}) => true
     *     ObjectArrayCheck.hasEmptyItem(new Object[]{"objs",new Object[]{"abc",""}})) => true
     *     ObjectArrayCheck.hasEmptyItem(new Object[]{"objs",new Object[]{1,""}}) => true
     *     ObjectArrayCheck.hasEmptyItem(new Object[]{"objs",new Object[]{1, Arrays.asList("12","23")}}) => false
     *     ObjectArrayCheck.hasEmptyItem(new Object[]{"objs",new Object[]{1, Arrays.asList("12",null)}}) => true
     *     ObjectArrayCheck.hasEmptyItem(new Object[]{"objs",new Object[]{1, Arrays.asList("12","")}}) => true
     * </p>
     * @param objs 非空的集合对象
     * @return boolean 如果集合对象中包含空对象则返回true
     * @throws 
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/4/23
     */
    public static boolean hasEmptyItem(Object[] objs) {
        return isEmpty(objs) || Arrays.stream(objs).anyMatch(ObjectEmptyCheck::isEmpty);
    }
    public static boolean isNull(Object obj) {
        if (obj == null) {
            return true;
        } else if (obj.getClass().isArray()) {
            return hasNullItem((Object[])obj);
        } else if (obj instanceof Collection) {
            return hasNullItem(((Collection) obj).toArray()) ;
        } else if (obj instanceof Map) {
            return hasNullItem(((Map)obj).keySet().toArray()) ||
                            hasNullItem(((Map)obj).values().toArray());
        } else {
            return false;
        }
    }

}
