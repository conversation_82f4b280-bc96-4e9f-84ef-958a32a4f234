/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.strings;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

/**
 * 字符串补全处理测试类
 *
 * <AUTHOR>
 * @module
 * @date 2023/5/8 13:08
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class StringPadUtilsTest {

    @Test
    @DisplayName("左右填充，填充字符串为2位长，填充长度不是2的整数倍")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testCenterPadTotalLength_true() {

        assertEquals("tsrtc", StringPadUtils.centerPadTotalLength("sr", 5, "tc"));
    }

    @Test
    @DisplayName("按总长度左右填充，原内容为null，填充长度10")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testcenterPadTotalLength_strnull_10length() {

        assertNull(StringPadUtils.centerPadTotalLength(null, 10, ""));
    }

    @Test
    @DisplayName("按总长度左右填充，填充为null，填充长度10")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testcenterPadTotalLength_null_10length() {

        assertEquals("    sr    ", StringPadUtils.centerPadTotalLength("sr", 10, null));
    }

    @Test
    @DisplayName("按总长度左右填充，填充字符串为空，填充长度1")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testcenterPadTotalLength_empty_10length() {
        assertEquals("sr", StringPadUtils.centerPadTotalLength("sr", 1, ""));
    }

    @Test
    @DisplayName("按总长度左右填充，填充字符串为空格，填充长度11")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testcenterPadTotalLength_blank_11length() {

        assertEquals("    sr     ", StringPadUtils.centerPadTotalLength("sr", 11, " "));
    }

    @Test
    @DisplayName("按总长度左右填充，填充字符串为3位长，填充长度为3的倍数")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testcenterPadTotalLength_asd_9length() {

        assertEquals("asdasdasd", StringPadUtils.centerPadTotalLength("asd", 9, "asd"));
    }

    @Test
    @DisplayName("按总长度左右填充，填充字符串为3位长，填充长度不为3的倍数")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testcenterPadTotalLength_asd_13length() {

        assertEquals("asdas中文asdasd", StringPadUtils.centerPadTotalLength("中文", 13, "asd"));
    }

    @Test
    @DisplayName("按总长度左右填充，填充字符串为“汉字”，填充长度5")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testcenterPadTotalLength_chinese_5length() {

        assertEquals("汉中文汉字", StringPadUtils.centerPadTotalLength("中文", 5, "汉字"));
    }

    @Test
    @DisplayName("按总长度左右填充，填充字符串为3位长，填充长度为0")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testcenterPadTotalLength_asd_0length() {

        assertEquals("asd", StringPadUtils.centerPadTotalLength("asd", 0, "asd"));
    }

    @Test
    @DisplayName("按总长度左右填充，填充字符串为2位长，填充长度小于0")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testcenterPadTotalLength_asd_negativelength() {

        assertEquals("-0", StringPadUtils.centerPadTotalLength("-0", -4, "ad"));
    }

    @Test
    @DisplayName("按总长度左右填充，填充字符串为10位长，填充长度7")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testcenterPadTotalLength_1234567890_7length() {

        assertEquals("12-0123", StringPadUtils.centerPadTotalLength("-0", 7, "1234567890"));
    }

    @Test
    @DisplayName("按总长度左填充，原内容为null，填充长度10")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testleftPadTotalLength_strnull_10length() {

        assertNull(StringPadUtils.leftPadTotalLength(null, 10, ""));
    }

    @Test
    @DisplayName("按总长度左填充，填充为null，填充长度10")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testleftPadTotalLength_null_10length() {

        assertEquals("        sr", StringPadUtils.leftPadTotalLength("sr", 10, null));
    }

    @Test
    @DisplayName("按总长度左填充，填充字符串为空，填充长度1")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testleftPadTotalLength_empty_10length() {
        assertEquals("sr", StringPadUtils.leftPadTotalLength("sr", 1, ""));
    }

    @Test
    @DisplayName("按总长度左填充，填充字符串为空格，填充长度10")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testleftPadTotalLength_blank_10length() {

        assertEquals("        sr", StringPadUtils.leftPadTotalLength("sr", 10, " "));
    }

    @Test
    @DisplayName("按总长度左填充，填充字符串为3位长，填充长度为3的倍数")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testleftPadTotalLength_asd_9length() {

        assertEquals("asdasdasd", StringPadUtils.leftPadTotalLength("asd", 9, "asd"));
    }

    @Test
    @DisplayName("按总长度左填充，填充字符串为3位长，填充长度不为3的倍数")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testleftPadTotalLength_asd_13length() {

        assertEquals("asdasdasdas中文", StringPadUtils.leftPadTotalLength("中文", 13, "asd"));
    }

    @Test
    @DisplayName("按总长度左填充，填充字符串为“汉字”，填充长度5")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testleftPadTotalLength_chinese_5length() {

        assertEquals("汉字汉中文", StringPadUtils.leftPadTotalLength("中文", 5, "汉字"));
    }

    @Test
    @DisplayName("按总长度左填充，填充字符串为3位长，填充长度为0")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testleftPadTotalLength_asd_0length() {

        assertEquals("asd", StringPadUtils.leftPadTotalLength("asd", 0, "asd"));
    }

    @Test
    @DisplayName("按总长度左填充，填充字符串为2位长，填充长度小于0")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testleftPadTotalLength_asd_negativelength() {

        assertEquals("-0", StringPadUtils.leftPadTotalLength("-0", -4, "ad"));
    }

    @Test
    @DisplayName("按总长度左填充，填充字符串为10位长，填充长度7")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testleftPadTotalLength_1234567890_7length() {

        assertEquals("12345-0", StringPadUtils.leftPadTotalLength("-0", 7, "1234567890"));
    }

    @Test
    @DisplayName("按总长度右填充，原内容为null，填充长度10")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testrightPadTotalLength_strnull_10length() {

        assertNull(StringPadUtils.rightPadTotalLength(null, 10, ""));
    }

    @Test
    @DisplayName("按总长度右填充，填充为null，填充长度10")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testrightPadTotalLength_null_10length() {

        assertEquals("sr        ", StringPadUtils.rightPadTotalLength("sr", 10, null));
    }

    @Test
    @DisplayName("按总长度右填充，填充字符串为空，填充长度1")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testrightPadTotalLength_empty_10length() {
        assertEquals("sr", StringPadUtils.rightPadTotalLength("sr", 1, ""));
    }

    @Test
    @DisplayName("按总长度右填充，填充字符串为空格，填充长度10")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testrightPadTotalLength_blank_10length() {

        assertEquals("sr        ", StringPadUtils.rightPadTotalLength("sr", 10, " "));
    }

    @Test
    @DisplayName("按总长度右填充，填充字符串为3位长，填充长度为3的倍数")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testrightPadTotalLength_asd_9length() {

        assertEquals("asdasdasd", StringPadUtils.rightPadTotalLength("asd", 9, "asd"));
    }

    @Test
    @DisplayName("按总长度右填充，填充字符串为3位长，填充长度不为3的倍数")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testrightPadTotalLength_asd_13length() {

        assertEquals("中文asdasdasdas", StringPadUtils.rightPadTotalLength("中文", 13, "asd"));
    }

    @Test
    @DisplayName("按总长度右填充，填充字符串为“汉字”，填充长度5")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testrightPadTotalLength_chinese_5length() {

        assertEquals("中文汉字汉", StringPadUtils.rightPadTotalLength("中文", 5, "汉字"));
    }

    @Test
    @DisplayName("按总长度右填充，填充字符串为3位长，填充长度为0")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testrightPadTotalLength_zzz_0length() {

        assertEquals("asd", StringPadUtils.rightPadTotalLength("asd", 0, "zzz"));
    }

    @Test
    @DisplayName("按总长度右填充，填充字符串为2位长，填充长度小于0")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testrightPadTotalLength_asd_negativelength() {

        assertEquals("-0", StringPadUtils.rightPadTotalLength("-0", -4, "ad"));
    }

    @Test
    @DisplayName("按总长度右填充，填充字符串为10位长，填充长度7")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testrightPadTotalLength_1234567890_7length() {

        assertEquals("-012345", StringPadUtils.rightPadTotalLength("-0", 7, "1234567890"));
    }

    @Test
    @DisplayName("按增加长度左填充，原内容为null，填充长度10")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testleftPadAddLength_strnull_10length() {

        assertNull(StringPadUtils.leftPadAddLength(null, 10, ""));
    }

    @Test
    @DisplayName("按增加长度左填充，填充为null，填充长度10")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testleftPadAddLength_null_10length() {

        assertEquals("          sr", StringPadUtils.leftPadAddLength("sr", 10, null));
    }

    @Test
    @DisplayName("按增加长度左填充，填充字符串为空，填充长度10")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testleftPadAddLength_empty_10length() {

        assertEquals("          sr", StringPadUtils.leftPadAddLength("sr", 10, ""));
    }

    @Test
    @DisplayName("按增加长度左填充，填充字符串为空格，填充长度10")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testleftPadAddLength_blank_10length() {

        assertEquals("          sr", StringPadUtils.leftPadAddLength("sr", 10, " "));
    }

    @Test
    @DisplayName("按增加长度左填充，填充字符串为3位长，填充长度为3的倍数")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testleftPadAddLength_asd_9length() {

        assertEquals("asdasdasdasd", StringPadUtils.leftPadAddLength("asd", 9, "asd"));
    }

    @Test
    @DisplayName("按增加长度左填充，填充字符串为3位长，填充长度为0")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testleftPadAddLength_asd_0length() {

        assertEquals("asd", StringPadUtils.leftPadAddLength("asd", 0, "asd"));
    }

    @Test
    @DisplayName("按增加长度左填充，填充字符串为2位长，填充长度小于0")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testleftPadAddLength_asd_negativelength() {

        assertEquals("-0", StringPadUtils.leftPadAddLength("-0", -4, "ad"));
    }

    @Test
    @DisplayName("按增加长度左填充，填充字符串为“汉字”，填充长度5")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testleftPadAddLength_chinese_5length() {

        assertEquals("汉字汉字汉中文", StringPadUtils.leftPadAddLength("中文", 5, "汉字"));
    }

    @Test
    @DisplayName("按增加长度左填充，填充字符串为10位长，填充长度小于字符串长度")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testleftPadAddLength_1234567890_7length() {

        assertEquals("1234567-0", StringPadUtils.leftPadAddLength("-0", 7, "1234567890"));
    }

    @Test
    @DisplayName("按增加长度右填充，原内容为null，填充长度10")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testrightPadAddLength_strnull_10length() {

        assertNull(StringPadUtils.rightPadAddLength(null, 10, ""));
    }

    @Test
    @DisplayName("按增加长度右填充，填充为null，填充长度10")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testrightPadAddLength_null_10length() {

        assertEquals("sr          ", StringPadUtils.rightPadAddLength("sr", 10, null));
    }

    @Test
    @DisplayName("按增加长度右填充，填充字符串为空，填充长度1")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testrightPadAddLength_empty_10length() {
        assertEquals("sr ", StringPadUtils.rightPadAddLength("sr", 1, ""));
    }

    @Test
    @DisplayName("按增加长度右填充，填充字符串为空格，填充长度10")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testrightPadAddLength_blank_10length() {

        assertEquals("sr          ", StringPadUtils.rightPadAddLength("sr", 10, " "));
    }

    @Test
    @DisplayName("按增加长度右填充，填充字符串为3位长，填充长度为3的倍数")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testrightPadAddLength_asd_9length() {

        assertEquals("asdasdasdasd", StringPadUtils.rightPadAddLength("asd", 9, "asd"));
    }

    @Test
    @DisplayName("按增加长度右填充，填充字符串为3位长，填充长度不为3的倍数")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testrightPadAddLength_asd_13length() {

        assertEquals("中文asdasdasdasda", StringPadUtils.rightPadAddLength("中文", 13, "asd"));
    }

    @Test
    @DisplayName("按增加长度右填充，填充字符串为“汉字”，填充长度5")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testrightPadAddLength_chinese_5length() {

        assertEquals("中文汉字汉字汉", StringPadUtils.rightPadAddLength("中文", 5, "汉字"));
    }

    @Test
    @DisplayName("按增加长度右填充，填充字符串为3位长，填充长度为0")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testrightPadAddLength_asd_0length() {

        assertEquals("asd", StringPadUtils.rightPadAddLength("asd", 0, "asd"));
    }

    @Test
    @DisplayName("按增加长度右填充，填充字符串为2位长，填充长度小于0")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testrightPadAddLength_asd_negativelength() {

        assertEquals("-0", StringPadUtils.rightPadAddLength("-0", -4, "ad"));
    }

    @Test
    @DisplayName("按增加长度右填充，填充字符串为10位长，填充长度7")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testrightPadAddLength_1234567890_7length() {

        assertEquals("-01234567", StringPadUtils.rightPadAddLength("-0", 7, "1234567890"));
    }


    @Test
    @DisplayName("左侧和右侧都按增加的长度填充，原内容为null")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testleftAndRightPadAddLength_strnull_10length() {

        assertNull(StringPadUtils.leftAndRightPadAddLength(null, 10, "", 10, ""));
    }

    @Test
    @DisplayName("左侧和右侧都按增加的长度填充，左侧填充为null长度0,右侧填充为null长度10")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testleftAndRightPadAddLength_null_10length() {

        assertEquals("sr          ", StringPadUtils.leftAndRightPadAddLength("sr", 0, null, 10, null));
    }

    @Test
    @DisplayName("左侧和右侧都按增加的长度填充，左侧填充字符串为空长度1,右侧填字符串为空长度1")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testleftAndRightPadAddLength_empty_1length() {
        assertEquals(" sr ", StringPadUtils.leftAndRightPadAddLength("sr", 1, "", 1, ""));
    }

    @Test
    @DisplayName("左侧和右侧都按增加的长度填充，左侧填充字符串为空格长度2,右侧填字符串为1长度3")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testleftAndRightPadAddLength_leftblank_rigth1() {

        assertEquals("  sr111", StringPadUtils.leftAndRightPadAddLength("sr", 2, " ", 3, "1"));
    }

    @Test
    @DisplayName("左侧和右侧都按增加的长度填充，左侧填充字符串为3位长填充长度为3的倍数,右侧填字符串为2位长填充长度为2的倍数")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testleftAndRightPadAddLength_asd_9length() {

        assertEquals("asdasdasdasdwewe", StringPadUtils.leftAndRightPadAddLength("asd", 9, "asd", 4, "we"));
    }

    @Test
    @DisplayName("左侧和右侧都按增加的长度填充，左侧填充字符串为3位长填充长度不为3的倍数,右侧填字符串为2位长填充长度不为2的倍数")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testleftAndRightPadAddLength_asd_13length() {

        assertEquals("asdasdasdasda中文wewewewewewew", StringPadUtils.leftAndRightPadAddLength("中文", 13, "asd", 13, "we"));
    }

    @Test
    @DisplayName("左侧和右侧都按增加的长度填充，左右测填充字符串为“汉字”，填充长度5")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testleftAndRightPadAddLength_chinese_5length() {

        assertEquals("汉字汉字汉中文汉字汉字汉", StringPadUtils.leftAndRightPadAddLength("中文", 5, "汉字", 5, "汉字"));
    }

    @Test
    @DisplayName("左侧和右侧都按增加的长度填充，左右侧填充字符串为4位长，填充长度为0")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testleftAndRightPadAddLength_0length() {

        assertEquals("asd", StringPadUtils.leftAndRightPadAddLength("asd", 0, "asda", 0, "asd"));
    }

    @Test
    @DisplayName("左侧和右侧都按增加的长度填充，左侧填充字符串为2位长填充长度小于0,右侧填字符串为1位长填充长度5")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testleftAndRightPadAddLength_negativelength() {

        assertEquals("-0$$$$$", StringPadUtils.leftAndRightPadAddLength("-0", -4, "ad", 5, "$"));
    }

    @Test
    @DisplayName("左侧和右侧都按增加的长度填充，左右侧填充字符串为10位长，填充长度7")
    @Tags({
            @Tag("@id:9"),
            @Tag("@author:taoxin"),
            @Tag("@date:2023/5/5")
    })
    void testleftAndRightPadAddLength_1234567890_7length() {

        assertEquals("1234567-09876543", StringPadUtils.leftAndRightPadAddLength("-0", 7, "1234567890", 7, "9876543210"));
    }
}
