package com.snbc.bbpf.commons.validations.validator;

import com.snbc.bbpf.commons.validations.ValidException;
import org.junit.jupiter.api.Test;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;

import static org.junit.jupiter.api.Assertions.*;
class PhoneNumberValidatorTest {
    private final PhoneNumberValidator validator = new PhoneNumberValidator();
    @Test
    @DisplayName("输入非空")
    @Tags({
            @Tag("@id:36"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/6/28")
    })
    void testValidateWithValidPhoneNumber() {
        String validPhoneNumber = "13812345678";
        String validMessage = "Valid phone number";
        assertDoesNotThrow(() -> validator.validate(validPhoneNumber, validMessage));
    }
    @Test
    @DisplayName("输入为空")
    @Tags({
            @Tag("@id:36"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/6/28")
    })
    void testValidateWithNullPhoneNumber() {
        String nullPhoneNumber = null;
        String validMessage = "Valid phone number";
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                validator.validate(nullPhoneNumber, validMessage));
        assertEquals("Mobile number is empty or null", exception.getMessage());
    }
    @Test
    @DisplayName("手机号码格式错误")
    @Tags({
            @Tag("@id:36"),
            @Tag("@author:yangweipeng"),
            @Tag("@date:2023/6/28")
    })
    void testValidateWithInvalidPhoneNumberFormat() {
        String invalidPhoneNumber = "12345678901";
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                validator.validate(invalidPhoneNumber, null));
        assertEquals("The phone number format is incorrect", exception.getMessage());
    }
}
