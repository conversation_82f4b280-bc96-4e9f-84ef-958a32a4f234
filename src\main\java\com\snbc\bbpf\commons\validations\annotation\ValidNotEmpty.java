/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.validations.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 非空验证注解
 * 支持对类的属性及方法参数进行非空验证
 * 若需要对方法参数进行非空验证，需在方法与方法参数上均配置@ValidNotEmpty才生效，参考ValidParameterNotEmptyAspectTest
 *
 * <AUTHOR>
 * @module
 * @date 2023/7/26 21:34
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */

@Target({ElementType.FIELD,ElementType.METHOD,ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidNotEmpty {
    // 错误信息
    String message() default "";
}
