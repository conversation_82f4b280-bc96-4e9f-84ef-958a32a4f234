# 系统工具 (com.snbc.bbpf.commons.system)

## 类概览 

| 类名 | 功能描述 |
|------|----------|
| SystemInfoUtil | 提供系统信息获取功能，包括操作系统、JVM、CPU、内存、网络接口等信息 |
| ConnectCheckUtils | 提供网络连接检测功能，包括检查主机可访问性、端口开放情况等 |
| NetworkInterfaceInfo | 网络接口信息数据类，用于封装网络接口的名称、MAC地址和IP地址列表 |
| HttpConnectionChecker | 提供基于HTTP协议的网络连接检测功能，使用HEAD请求验证URL的可访问性 |

## SystemInfoUtil - 系统信息工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| getOsName() | 获取操作系统名称 | 无 | String: 操作系统名称 |
| getOsVersion() | 获取操作系统版本 | 无 | String: 操作系统版本 |
| getOsArchitecture() | 获取操作系统架构 | 无 | String: 操作系统架构 |
| getTotalMemoryInMB() | 获取Java虚拟机总内存(MB) | 无 | long: 总内存 |
| getFreeMemoryInMB() | 获取Java虚拟机空闲内存(MB) | 无 | long: 空闲内存 |
| getMaxMemoryInMB() | 获取Java虚拟机最大可分配内存(MB) | 无 | long: 最大内存 |
| getCpuCoreCount() | 获取CPU核心数 | 无 | int: CPU核心数 |
| getCpuUsage() | 获取CPU使用率 | 无 | double: CPU使用率(0.0到1.0) |
| getHostName() | 获取主机名 | 无 | String: 主机名 |
| getNetworkInterfaces() | 获取所有网络接口的MAC地址和IP地址 | 无 | List<NetworkInterfaceInfo>: 网络接口信息列表 |
| getSystemInfo() | 获取系统信息 | 无 | String: 系统信息字符串 |
| getUptime() | 获取系统运行时间(毫秒) | 无 | long: 系统运行时间 |
| getBootTime() | 获取系统启动时间 | 无 | LocalDateTime: 系统启动时间 |
| getSystemType() | 获取操作系统类型 | 无 | OSType: 操作系统类型枚举 |
| getEnvironmentVariable(String envVar) | 获取指定环境变量 | envVar: 环境变量名称 | String: 环境变量值 |

### 注意事项

- 方法返回值可能因操作系统类型而异，在不同平台上结果可能不同
- getCpuUsage() 需要 JVM 支持 com.sun.management.OperatingSystemMXBean 接口
- 系统资源测量（如CPU使用率、内存）是瞬时值，可能波动较大
- 某些方法在虚拟化环境或容器中可能无法获得准确的物理主机信息
- getNetworkInterfaces() 方法可能需要管理员权限才能获取完整MAC地址信息
- getSystemType() 方法返回的枚举值有限，不能识别所有操作系统类型
- 获取环境变量可能受系统安全策略影响，某些敏感变量可能不可读取

### 使用示例

```java
// 获取操作系统信息
String osName = SystemInfoUtil.getOsName();          // 例如："Windows 10"
String osVersion = SystemInfoUtil.getOsVersion();    // 例如："10.0"
String osArch = SystemInfoUtil.getOsArchitecture();  // 例如："amd64"

// 获取JVM内存信息
long totalMemory = SystemInfoUtil.getTotalMemoryInMB();  // 例如：252（MB）
long freeMemory = SystemInfoUtil.getFreeMemoryInMB();    // 例如：192（MB）
long maxMemory = SystemInfoUtil.getMaxMemoryInMB();      // 例如：4096（MB）

// 获取CPU信息
int cpuCount = SystemInfoUtil.getCpuCoreCount();     // 例如：8
double cpuUsage = SystemInfoUtil.getCpuUsage();      // 例如：0.25 (表示25%的CPU使用率)

// 获取主机信息
String hostName = SystemInfoUtil.getHostName();     // 例如："DESKTOP-AB123CD"

// 获取网络接口信息
List<NetworkInterfaceInfo> networkInterfaces = SystemInfoUtil.getNetworkInterfaces();
for (NetworkInterfaceInfo info : networkInterfaces) {
    System.out.println("接口名称: " + info.getName());
    System.out.println("MAC地址: " + info.getMacAddress());
    System.out.println("IP地址列表: " + info.getIpAddresses());
}

// 获取系统运行信息
long uptime = SystemInfoUtil.getUptime();           // 例如：3600000（表示系统已运行1小时）
LocalDateTime bootTime = SystemInfoUtil.getBootTime();  // 例如：2023-06-01T08:30:00

// 获取系统类型
OSType systemType = SystemInfoUtil.getSystemType();  // 例如：OSType.WINDOWS

// 获取完整系统信息
String systemInfo = SystemInfoUtil.getSystemInfo();
System.out.println(systemInfo);

// 获取环境变量
String javaHome = SystemInfoUtil.getEnvironmentVariable("JAVA_HOME");
System.out.println("JAVA_HOME: " + javaHome);
```

## NetworkInterfaceInfo - 网络接口信息类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| NetworkInterfaceInfo(String name, String macAddress, List<String> ipAddresses) | 构造函数 | name: 网络接口名称<br>macAddress: MAC地址<br>ipAddresses: IP地址列表 | - |
| getName() | 获取网络接口名称 | 无 | String: 网络接口名称 |
| getMacAddress() | 获取MAC地址 | 无 | String: MAC地址 |
| getIpAddresses() | 获取IP地址列表 | 无 | List<String>: IP地址列表 |

### 注意事项

- 网络接口名称在不同操作系统上格式不同（如Windows上可能是"本地连接"，Linux上可能是"eth0"）
- MAC地址可能因权限问题返回null或默认值，特别是在非管理员运行的应用中
- IP地址列表可能包含IPv4和IPv6地址，使用时需注意区分
- 某些虚拟或禁用的网络接口也可能被列出，需要进行额外过滤
- MAC地址格式可能因系统而异，有些系统返回带冒号分隔(AA:BB:CC)，有些返回带连字符(AA-BB-CC)

### 使用示例

```java
// 创建一个NetworkInterfaceInfo对象
List<String> ipAddresses = new ArrayList<>();
ipAddresses.add("***********00");
ipAddresses.add("fe80::1234:5678:abcd:ef01");

NetworkInterfaceInfo interfaceInfo = new NetworkInterfaceInfo(
    "eth0", 
    "00-11-22-33-44-55", 
    ipAddresses
);

// 获取网络接口信息
System.out.println("接口名称: " + interfaceInfo.getName());
System.out.println("MAC地址: " + interfaceInfo.getMacAddress());
System.out.println("IP地址列表: " + interfaceInfo.getIpAddresses());

// 通常通过SystemInfoUtil.getNetworkInterfaces()获取所有网络接口信息
List<NetworkInterfaceInfo> networkInterfaces = SystemInfoUtil.getNetworkInterfaces();
for (NetworkInterfaceInfo info : networkInterfaces) {
    System.out.println("接口: " + info.getName() + ", MAC: " + info.getMacAddress());
    for (String ip : info.getIpAddresses()) {
        System.out.println("  IP: " + ip);
    }
}
```

## ConnectCheckUtils - 连接检测工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| hostIsAlive(String addr, Integer time, TimeUnit timeUnit) | 检查IPv4主机是否可达 | addr: 主机名或IP地址<br>time: 超时时间<br>timeUnit: 超时时间单位 | boolean: 是否可达 |
| hostIsAliveIpv6(String addr, Integer time, TimeUnit timeUnit) | 检查IPv6主机是否可达 | addr: 主机名或IPv6地址<br>time: 超时时间<br>timeUnit: 超时时间单位 | boolean: 是否可达 |
| hostPortIsAlive(String addr, Integer port, Integer time, TimeUnit timeUnit) | 检查指定端口是否开放 | addr: 主机名或IP地址<br>port: 端口号<br>time: 超时时间<br>timeUnit: 超时时间单位 | boolean: 是否开放 |

### 注意事项

- 网络连接检测可能会受到防火墙和网络配置的影响，如果主机或防火墙禁用了ICMP，则hostIsAlive方法可能会测试通信失败
- **重要**：ConnectCheckUtils类的hostIsAlive和hostIsAliveIpv6方法底层使用ping命令，许多生产环境服务器出于安全考虑会禁用ping(ICMP)响应，这种情况下即使服务器在线也会返回false
- 建议在关键系统中使用hostPortIsAlive方法（基于TCP连接）作为替代，或结合多种检测方法使用
- 请确保命令执行的超时设置合理，避免长时间阻塞
- 在某些受限环境中可能无法执行底层命令，导致功能不可用
- 名称解析依赖于本地DNS配置，可能影响结果准确性
- 方法都是静态的，方便直接调用，但无法模拟测试

### 使用示例

```java
import java.util.concurrent.TimeUnit;

// 检查IPv4主机是否可达
boolean reachable = ConnectCheckUtils.hostIsAlive("www.example.com", 5, TimeUnit.SECONDS);
System.out.println("主机可达: " + reachable);

boolean localReachable = ConnectCheckUtils.hostIsAlive("127.0.0.1", 2, TimeUnit.SECONDS);
System.out.println("本地主机可达: " + localReachable);

// 检查IPv6主机是否可达
boolean ipv6Reachable = ConnectCheckUtils.hostIsAliveIpv6("::1", 2, TimeUnit.SECONDS);
System.out.println("IPv6本地主机可达: " + ipv6Reachable);

// 检查指定端口是否开放
boolean portOpen = ConnectCheckUtils.hostPortIsAlive("www.example.com", 80, 5, TimeUnit.SECONDS);
System.out.println("80端口开放: " + portOpen);

boolean sshPortOpen = ConnectCheckUtils.hostPortIsAlive("***********", 22, 3, TimeUnit.SECONDS);
System.out.println("SSH端口开放: " + sshPortOpen);

// 检查本地端口
int randomPort = 8080; // 替换为你想测试的端口
boolean localPortOpen = ConnectCheckUtils.hostPortIsAlive("127.0.0.1", randomPort, 2, TimeUnit.SECONDS);
System.out.println("本地" + randomPort + "端口开放: " + localPortOpen);
```

## HttpConnectionChecker - HTTP连接检测工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| HttpConnectionChecker() | 默认构造函数，创建一个使用固定线程池和配置好的HttpClient的实例 | 无 | - |
| HttpConnectionChecker(ExecutorService executorService, CloseableHttpClient httpClient) | 使用自定义组件的构造函数 | executorService: 用于异步执行请求的线程池<br>httpClient: 用于发送HTTP请求的客户端 | - |
| checkConnection(String url) | 检查单个URL的连通性 | url: 要检查的URL | boolean: URL是否可访问 |
| checkConnections(List<String> urls) | 依次检查多个URL，找到第一个可访问的即返回 | urls: 要检查的URL列表 | boolean: 是否有可访问的URL |
| close() | 关闭资源，实现AutoCloseable接口 | 无 | void |

### 注意事项

- HttpConnectionChecker使用HEAD请求而非GET请求，只获取响应头信息，减少网络流量
- **重要**：某些网站可能禁用了HEAD请求，这种情况下即使网站正常也会返回false
- 此检测方式可能会存在中间页或劫持问题，例如WIFI有可能会需要授权访问的中间页，实际并未连通互联网
- 这种方式只代表本机有网络连接，不一定代表真的能连接到互联网
- 建议将HttpConnectionChecker与ConnectCheckUtils结合使用，通过多种方式验证网络连接状态
- HttpConnectionChecker实现了AutoCloseable接口，推荐使用try-with-resources语句确保资源正确关闭
- 如果不使用try-with-resources，必须手动调用close()方法释放资源，否则可能导致线程池泄漏
- 在高并发环境中，建议使用自定义的线程池以便更好地控制资源使用

### 使用示例

```java
import com.snbc.bbpf.commons.system.HttpConnectionChecker;
import java.util.Arrays;
import java.util.List;

// 创建连接检查器
try (HttpConnectionChecker checker = new HttpConnectionChecker()) {
    // 检查单个URL
    boolean isGoogleAccessible = checker.checkConnection("https://www.google.com");
    System.out.println("Google可访问: " + isGoogleAccessible);
    
    // 检查多个URL，找到第一个可访问的
    List<String> urlsToCheck = Arrays.asList(
        "https://www.baidu.com",
        "https://www.bing.com",
        "https://www.yahoo.com"
    );
    boolean anyAccessible = checker.checkConnections(urlsToCheck);
    System.out.println("至少有一个URL可访问: " + anyAccessible);
}
// HttpConnectionChecker实现了AutoCloseable接口，使用try-with-resources可自动关闭资源
```

### 高级用法

```java
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

// 创建自定义的ExecutorService
ExecutorService executorService = Executors.newFixedThreadPool(5);

// 创建自定义的HttpClient
CloseableHttpClient httpClient = HttpClientBuilder.create()
    .setUserAgent("MyCustomAgent/1.0")
    .build();

// 使用自定义组件创建HttpConnectionChecker
try (HttpConnectionChecker checker = new HttpConnectionChecker(executorService, httpClient)) {
    boolean isAccessible = checker.checkConnection("https://example.com");
    System.out.println("Example.com可访问: " + isAccessible);
}
// 记得在不需要的时候关闭ExecutorService
executorService.shutdown();
``` 