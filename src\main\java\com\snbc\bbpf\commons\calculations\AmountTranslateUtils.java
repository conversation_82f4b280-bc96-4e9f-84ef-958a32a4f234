/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.calculations;

import com.snbc.bbpf.commons.strings.StringEmptyCheck;



/**
 * 【精度计算】大小写金额互相转换
 *
 * 计数单位，只解析到'京'，理由：int型整数最大值为Integer.MAX_VALUE=2147483647（2的31次方-1）：贰拾壹亿肆仟柒佰肆拾捌万叁仟陆佰肆拾柒
 * Long.MAX_VALUE=9223372036854775807（2的63次方-1）：玖佰贰拾贰京叁仟叁佰柒拾贰兆零叁佰陆拾捌亿伍仟肆佰柒拾柒万伍仟捌佰零柒
 * Long.MAX最大值只用到'京'，并且一般金额数不会超过'京'，后续有需求超过'京'的金额则需转换为更大的整数类型
 *
 *
 * <p>将金额进行大小写互相转换</p>
 * <p>功能列表</p>
 * <p>1.对int或long转换成大写的金额 </p>
 * <p>2.将大写金额转换为int或long类型 </p>
 *
 * <AUTHOR>
 * @module
 * @date 2023/7/20 19:30
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class AmountTranslateUtils {

	
    /**
     * 抛出参数异常描述
     */
    public static final String PARAMETER_EMPTY = "String is empty!";
    public static final String PARAMETER_INVALID = "String is invalid!";
    public static final String PARAMETER_LESSTHAN0 = "Less than 0!";
    public static final String PARAMETER_OVERFLOW = "String is Overflow!";

    /**
     * 数字
     */
    private static final int NUMBER_0 = 0;
    private static final int NUMBER_1 = 1;
    private static final int NUMBER_4 = 4;
    private static final int NUMBER_10 = 10;
    private static final int NUMBER_10000 = 10000;

	
    /**
     * 查找到无效值
     */
    private static final int INVALID_VALUE = -1;

	
    /**
     * 金额大写值
     */
	private static final char[] number = new char[]{'零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'};
	private static final char[] level = new char[]{'拾', '佰', '仟'};
    /**
     * 计数单位
     */
	private static final char[] layer = new char[]{'万', '亿', '兆', '京'};
	
    /**
     * 数组长度
     */
    private static final int NUMBER_SIZE = 10;
    private static final int LAYER_SIZE = 4;
    private static final int LEVEL_SIZE = 3;
	

    private AmountTranslateUtils() {
        throw new IllegalStateException("Utility class");
    }

	
    /**
     * 中文数转换数字
     *
     * @param cValue 中文数
     * @return 数字
     * @since 1.1.0
     * <AUTHOR>
     * @date 2023/7/20
     */
    private static int getNumber(char cValue) {
		for (int i = NUMBER_0; i < NUMBER_SIZE; ++i) {
			if (cValue == number[i]) {
				return i;
			}
		}
		return INVALID_VALUE;
    }

    /**
     * 查找'万', '亿', '兆', '京'计数单位数组索引
     *
     * @param cValue 计数单位
     * @return int 数组索引
     * @since 1.1.0
     * <AUTHOR>
     * @date 2023/7/20
     */
    private static int getLayer(char cValue) {
		for (int i = NUMBER_0; i < LAYER_SIZE; ++i) {
			if (cValue == layer[i]) {
				return i;
			}
		}
		return INVALID_VALUE;
    }

    /**
     * 查找'拾', '佰', '仟'计数单位数组索引
     * @param cValue 计数单位
     * @return int 匹配的数组索引（0-2对应拾/佰/仟），未找到返回-1
     * @since 1.1.0
     * <AUTHOR>
     * @date 2023/7/20
     */
    private static int getLevel(char cValue) {
		for (int i = NUMBER_0; i < LEVEL_SIZE; ++i) {
			if (cValue == level[i]) {
				return i;
			}
		}
		return INVALID_VALUE;
    }

	/**
	 * 相加
	 *
	 * @param lAmount 原金额
	 * @param lValue 相加金额
     * @return long 相加后的总金额
     * @throws IllegalArgumentException 当任一参数为负，或相加结果超过Long.MAX_VALUE时抛出
     * @since 1.1.0
     * <AUTHOR>
     * @date 2023/7/20
     */
	private static long add(long lAmount, long lValue) {
		if (lAmount < NUMBER_0 || lValue < NUMBER_0) {
			throw new IllegalArgumentException(PARAMETER_OVERFLOW);
		}

		if (lAmount > Long.MAX_VALUE 
			|| lValue > Long.MAX_VALUE 
			|| lAmount > (Long.MAX_VALUE - lValue)) {
			throw new IllegalArgumentException(PARAMETER_OVERFLOW);	
		}

		return lAmount + lValue;
	}

	/**
	 * 相乘
	 *
	 * @param lAmount 金额
	 * @param lValue 计数
     * @return long 相乘后的总金额
     * @throws IllegalArgumentException 当lAmount为负、lValue≤0，或相乘结果超过Long.MAX_VALUE时抛出
     * @since 1.1.0
     * <AUTHOR>
     * @date 2023/7/20
     */
	private static long multiply(long lAmount, long lValue) {
		if (lAmount < NUMBER_0 || lValue <= NUMBER_0) {
			throw new IllegalArgumentException(PARAMETER_OVERFLOW);
		}

		if (lAmount > Long.MAX_VALUE 
			|| lValue > Long.MAX_VALUE 
			|| lAmount > (Long.MAX_VALUE / lValue)) {
			throw new IllegalArgumentException(PARAMETER_OVERFLOW);	
		}

		return lAmount * lValue;
	}
	

    /**
     * 对int型小写金额转换成大写的金额
     *
     * <p>例如：输入123，返回"壹百贰拾叁"</p>  
     *
     * @param iAmount 小写金额
     * @return 大写金额
     * @throws IllegalArgumentException 如果参数小于0
     * @since 1.1.0
     * <AUTHOR>
     * @date 2023/7/20
     */
    public static String toUpper(int iAmount) {
    	return toUpper((long)iAmount);
    }

    /**
     * 对long型小写金额转换成大写的金额
     *
     * <p>例如：输入123，返回"壹百贰拾叁"</p>  
     *
     * @param lAmount 小写金额
     * @return 大写金额
     * @throws IllegalArgumentException 如果参数小于0
     * @since 1.1.0
     * <AUTHOR>
     * @date 2023/7/20
     */
    public static String toUpper(long lAmount) {
		if (lAmount < NUMBER_0) {
			throw new IllegalArgumentException(PARAMETER_LESSTHAN0);
		}

		if (lAmount == NUMBER_0) {
			return "零";
		}

    	StringBuilder sbf = new StringBuilder();
		int iDigits = INVALID_VALUE;
		int iLevel = -1;
		int iNumber = 0;
		boolean bZero = false;
		boolean bBegin = false;
		
    	while (lAmount != NUMBER_0) {
    		iNumber = (int)(lAmount % NUMBER_SIZE);
    		lAmount /= NUMBER_SIZE;
			++iDigits;
			
			if (iNumber != NUMBER_0 && !bBegin) {
				bBegin = true;
			}
			// 为0，先记着
			bZero = appendNumber(sbf, iNumber, bZero, bBegin);

			iLevel = iDigits % NUMBER_4 - NUMBER_1;
			if (0 < iDigits && INVALID_VALUE == iLevel) {
				bBegin = iNumber != NUMBER_0;
				bZero = appendLevel(sbf, iDigits, bZero);
			}

			//当前为0，下一位
			if (iNumber == NUMBER_0) {
				continue;
			}

			if (iLevel > INVALID_VALUE) {
				sbf.insert(NUMBER_0, level[iLevel]);
			}

    		sbf.insert(NUMBER_0, number[iNumber]);


    	}
        return sbf.toString();
    }

	/**
	 * 追加计数单位层级
	 * @param sbf 转换后字符
	 * @param iDigits 是否数字
	 * @param bZero 是否零
	 * @return boolean 新的零标记状态（处理完当前层级后是否仍为零）
	 * @since 1.1.0
	 * <AUTHOR>
	 * @date 2023/7/20      
	 */
	private static boolean appendLevel(StringBuilder sbf, int iDigits, boolean bZero) {

		if (bZero) {
			bZero = false;
			sbf.insert(NUMBER_0, number[NUMBER_0]);
		}

		int iLayer = iDigits / NUMBER_4 - NUMBER_1;
		sbf.insert(NUMBER_0, layer[iLayer]);
		return bZero;
	}

	/**
	 * 追加数字的处理
	 * @param sbf 转换后字符
	 * @param iNumber 是否数字
	 * @param bZero 是否零
	 * @param bBegin 开始位置
	 * @return boolean 新的零标记状态（当前数字处理后是否仍为零）
	 * @since 1.1.0
	 * <AUTHOR>
	 * @date 2023/7/20   
	 */
	private static boolean appendNumber(StringBuilder sbf, int iNumber, boolean bZero, boolean bBegin) {
		if (iNumber == NUMBER_0 && bBegin) {
			bZero = true;
		}
		if (iNumber != NUMBER_0 && bZero) {
			bZero = false;
			sbf.insert(NUMBER_0, number[NUMBER_0]);
		}
		return bZero;
	}

	/**
     * 将大写金额转换为小写金额
     *
     * <p>例如：输入"壹百贰拾叁"，返回123</p>  
     *
     * @param strAmount 大写金额
     * @return long小写金额
     * @throws IllegalArgumentException 如果输入字符串为空、溢出、不符合转换规则
     * @since 1.1.0
     * <AUTHOR>
     * @date 2023/7/20
     */
    private static long toLower(String strAmount) {
		if (StringEmptyCheck.isBlank(strAmount)) {
			throw new IllegalArgumentException(PARAMETER_EMPTY);			
		}
		long lAmount = 0;
		long lNumber = 0;
		int iLength = strAmount.length();
		boolean bNumber = true;
		long lLevel = -1;
		long lLayer = -1;
		char cValue = 0;
		long lValue = 0;
		for (int i = NUMBER_0; i < iLength; ++i) {
			cValue = strAmount.charAt(i);
			if (bNumber) {
				lValue = validNumberValue(cValue);

				//为0则跳过
				if (lValue != NUMBER_0) {
					lAmount = add(lAmount, lValue);
					bNumber = false;				
				}
			} else if (INVALID_VALUE != (lValue = getLayer(cValue))) {
				lLevel = INVALID_VALUE;
				++lValue;
				lValue = (long) Math.pow(NUMBER_10000,lValue);
				validLayerValue(lLayer, lValue);
				lLayer = lValue;
				lNumber = lAmount % NUMBER_10000;
                lAmount -= lNumber;
				lNumber = multiply(lNumber, lLayer);
				lAmount = add(lAmount, lNumber);
				bNumber = true;	
			} else if (INVALID_VALUE == (lValue = getLevel(cValue))) {
				throw new IllegalArgumentException(PARAMETER_INVALID);							
			} else {
				++lValue;
				lValue = (long) Math.pow(NUMBER_10,lValue);
				validLayerValue(lLevel, lValue);
				lLevel = lValue;
				lNumber = lAmount % NUMBER_SIZE;
                lAmount -= lNumber;
				lNumber = multiply(lNumber, lLevel);
				lAmount = add(lAmount, lNumber);
				if (i < (iLength - 1)) {				
					bNumber = INVALID_VALUE == getLayer(strAmount.charAt(i + 1));
				}
			}
		}
	
        return lAmount;
    }
/**
	 * 校验计数单位层级的有效性（确保层级顺序正确，避免小单位在大单位之后）
	 * @param lLayer 上一个已处理的层级值（初始为-1表示未处理）
	 * @param lValue 当前层级的数值（如万=10000，亿=100000000等）
	 * @throws IllegalArgumentException 当当前层级值小于等于上一个层级值时抛出（如先出现亿后出现万）
	 * @since 1.1.0
	 * <AUTHOR>
	 * @date 2023/7/20
	 */
	private static void validLayerValue(long lLayer, long lValue) {
		if (lLayer != INVALID_VALUE && lLayer <= lValue) {
			throw new IllegalArgumentException(PARAMETER_INVALID);
		}
	}
/**
	 * 校验字符是否为有效的大写数字字符（零-玖）
	 * @param cValue 待校验的字符
	 * @return long 对应的数字值（0-9）
	 * @throws IllegalArgumentException 当字符不是有效的大写数字时抛出
	 * @since 1.1.0
	 * <AUTHOR>
	 * @date 2023/7/20
	 */
	private static long validNumberValue(char cValue) {
		long lValue;
		if (INVALID_VALUE == (lValue = getNumber(cValue))) {
			throw new IllegalArgumentException(PARAMETER_INVALID);							
		 }
		return lValue;
	}

	/**
     * 将大写金额转换为long类型
     *
     * <p>例如：输入"壹百贰拾叁"，返回123</p>  
     *
     * @param strAmount 大写金额
     * @return long小写金额
     * @throws IllegalArgumentException 如果输入字符串为空、溢出、不符合转换规则
     * @since 1.1.0
     * <AUTHOR>
     * @date 2023/7/20
     */
    public static long toLong(String strAmount) {
    	return toLower(strAmount);
    }
	
    /**
     * 将大写金额转换为int类型
     *
     * <p>例如：输入"壹百贰拾叁"，返回123</p>  
     *
     * @param strAmount 大写金额
     * @return int小写金额
     * @throws IllegalArgumentException 如果输入字符串为空、溢出、不符合转换规则
     * @since 1.1.0
     * <AUTHOR>
     * @date 2023/7/20
     */
    public static int toInt(String strAmount) {
    	long lAmount = toLower(strAmount);
		if (Integer.MAX_VALUE < lAmount) {
			throw new IllegalArgumentException(PARAMETER_OVERFLOW);									
		}
    	return (int)lAmount;
    }

}


