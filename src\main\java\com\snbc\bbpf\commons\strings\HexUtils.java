/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.strings;


/**
 * 将byte数组和十六进制字符串互相转换。同时需要保证大byte数组的转换效率。
 * <p>
 *
 * <AUTHOR>
 * @module 版本号比较工具类
 * @date 2023/4/29 9:58
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class HexUtils {
    public static final String PARAMETER_EMPTY = "Input parameter is empty";

    private static final int HALF_SIZE = 2;
    private static final int SETP = 2;
    private static final int LEFT_OFFSET = 4;
    private static final int RADIX_SIXTEEN = 16;

    private HexUtils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 将字节数组转换为十六进制字符串
     *
     * @param byteArray 字节数组
     * @return 转换后的十六进制字符串
     * <AUTHOR>
     * @date 2023/6/6
     * @since 1.1.0
     */
    public static String byteArrayToHexString(byte[] byteArray) {
        if (byteArray == null) {
            throw new IllegalArgumentException(PARAMETER_EMPTY);
        }
        StringBuilder hexString = new StringBuilder();
        for (byte b : byteArray) {
            String hex = Integer.toHexString(0xFF & b);
            if (hex.length() == 1) {
                hexString.append("0");
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }

    /**
     * 将十六进制字符串转换为字节数组
     *
     * @param hexString 十六进制字符串
     * @return 转换后的字节数组
     * <AUTHOR>
     * @date 2023/6/6
     * @since 1.1.0
     */
    public static byte[] hexStringToByteArray(String hexString) {
        if (StringEmptyCheck.isEmpty(hexString)) {
            throw new IllegalArgumentException(PARAMETER_EMPTY);
        }
        int length = hexString.length();
        byte[] byteArray = new byte[length / HALF_SIZE];
        for (int i = 0; i < length; i += SETP) {
            byteArray[i / HALF_SIZE] = (byte) ((Character.digit(hexString.charAt(i), RADIX_SIXTEEN) << LEFT_OFFSET)
                    + Character.digit(hexString.charAt(i + 1), RADIX_SIXTEEN));
        }
        return byteArray;
    }
}
