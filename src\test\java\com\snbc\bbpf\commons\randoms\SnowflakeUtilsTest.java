/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.commons.randoms;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import java.net.UnknownHostException;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;


/**
 * 雪花算法测试类
 *
 * <AUTHOR>
 * @module 随机类
 * @date 2023/6/13 3:43
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
class SnowflakeUtilsTest {
    private SnowflakeUtils snowflake;

    private AtomicLong lastId;


    @DisplayName("生成雪花算法数字字符串,默认长度18位")
    @Tags({
            @Tag("@id:32"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/6/13")
    })
    @Test
    void testRandom_snow_flake_fix_length() {
        Long snowFlakeNumric = SnowflakeUtils.genId();
        assertEquals(18, snowFlakeNumric.toString().length());

    }

    @DisplayName("生成雪花算法数字字符串,验证生成100个不重复")
    @Tags({
            @Tag("@id:32"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/8/31")
    })
    @Test
    void testRandom_snow_flake_muilt() {
        Set<Long> snowIds = new HashSet<>();
        IntStream.range(0, 100).forEach(s -> snowIds.add(SnowflakeUtils.genId()));

        assertEquals(100, snowIds.size());

    }

    @DisplayName("生成雪花算法数字字符串，可以指定长度")
    @Tags({
            @Tag("@id:32"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/6/13")
    })
    @Test
    void testRandom_snow_flake_numeric() {
        Long snowFlakeNumric = SnowflakeUtils.genId(16);
        assertEquals(16, snowFlakeNumric.toString().length());

    }

    @DisplayName("生成雪花算法数字字符串，入参错误")
    @Tags({
            @Tag("@id:32"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/6/13")
    })
    @Test
    void testRandom_snow_flake_numeric_input_error() {
        IllegalArgumentException thrown = Assertions.assertThrows(IllegalArgumentException.class,
                () -> SnowflakeUtils.genId(-1));
        Assertions.assertEquals("Requested random string length -1 is less than 0.", thrown.getMessage());
        thrown = Assertions.assertThrows(IllegalArgumentException.class,
                () -> SnowflakeUtils.genId(20));
        Assertions.assertEquals("Requested random string length 20 is more than 18.", thrown.getMessage());

    }

    @DisplayName("生成雪花算法数字字符串，0长度")
    @Tags({
            @Tag("@id:32"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/6/13")
    })
    @Test
    void testRandom_snow_flake_numeric_input_zero() {
        Long snowFlakeNumric = SnowflakeUtils.genId(0);
        assertEquals(0L, snowFlakeNumric);

    }

    @DisplayName("生成雪花算法数字字符串,指定数据中心")
    @Tags({
            @Tag("@id:32"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/6/25")
    })
    @Test
    void genId() {
        Long snowFlakeNumric = SnowflakeUtils.genIdByCustomerDataId(18, 20);
        assertEquals(18, snowFlakeNumric.toString().length());

        IllegalArgumentException thrown = Assertions.assertThrows(IllegalArgumentException.class,
                () -> SnowflakeUtils.genIdByCustomerDataId(18, 40));
        Assertions.assertEquals("dataId区间在1～31位", thrown.getMessage());
    }

    @DisplayName("生成雪花算法数字字符串,指定workerId")
    @Tags({
            @Tag("@id:32"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/6/25")
    })
    @Test
    void genIdByCustomerWorkId() {

        Long snowFlakeNumric = SnowflakeUtils.genIdByCustomerWorkId(18, 20);
        assertEquals(18, snowFlakeNumric.toString().length());

        IllegalArgumentException thrown = Assertions.assertThrows(IllegalArgumentException.class,
                () -> SnowflakeUtils.genIdByCustomerWorkId(18, 40));
        Assertions.assertEquals("workId区间在1～31位", thrown.getMessage());
    }

    @DisplayName("生成雪花算法数字字符串,指定workerId,dataId")
    @Tags({
            @Tag("@id:32"),
            @Tag("@author:xuyunfeng"),
            @Tag("@date:2023/6/25")
    })
    @Test
    void genIdByCustomerDataId() {
        Long snowFlakeNumric = SnowflakeUtils.genId(18, 20, 20);
        assertEquals(18, snowFlakeNumric.toString().length());

    }

    @DisplayName(value = "Data center ID should be within the range of 0 to 31")
    @Tags({
            @Tag("@id:93"),
            @Tag("@author:xuping2"),
    })
    @Test
    public void testGetDataCenterId() {
        long dataCenterId = SnowflakeUtils.getDataCenterId(31);
        Assertions.assertTrue(
                dataCenterId >= 0 && dataCenterId <= 31,
                "Data center ID should be within the range of 0 to 31");
    }

    @DisplayName(value = "invalid_host_address")
    @Tags({
            @Tag("@id:93"),
            @Tag("@author:xuping2"),
    })
    @Test
    public void testGetDataCenterIdWithException() {
        try {
            // Simulate an UnknownHostException by passing an invalid host address
            SnowflakeUtils.getDataCenterId(SnowflakeUtils.getHostId("invalid_host_address", 31));
        } catch (RuntimeException e) {
            // Expecting an UnknownHostException wrapped in a RuntimeException
            Assertions.assertTrue(e.getCause() instanceof java.net.UnknownHostException);
        }
    }

    @DisplayName(value = "Data center ID should be between 0 and 1")
    @Tags({
            @Tag("@id:93"),
            @Tag("@author:xuping2"),
    })
    @Test
    void testGetDataCenterId1() {
        int maxDatacenterId = 31; // 根据Snowflake的DATA_CENTER_ID_BITS，最大值为31
        long dataCenterId = SnowflakeUtils.getDataCenterId(maxDatacenterId);
        assertTrue(
                dataCenterId >= 0 && dataCenterId <= maxDatacenterId,
                "Data center ID should be between 0 and " + maxDatacenterId);
    }

    @DisplayName(value = "Exception for UnknownHostException should be caught and handled")
    @Tags({
            @Tag("@id:93"),
            @Tag("@author:xuping2"),
    })
    @Test
    void testGetDataCenterIdWithException1() {
        try {
            // Mock a situation where InetAddress.getLocalHost().getHostAddress() throws
            // UnknownHostException
            // Note: This is not a real test case as it would require actually breaking the network
            // or mocking the InetAddress class.
            // For demonstration purposes, we are assuming that the exception is thrown.
            // In a real-world scenario, you would use a mocking framework like Mockito to mock the
            // InetAddress class.
            throw new RuntimeException(new UnknownHostException());
        } catch (RuntimeException e) {
            if (e.getCause() instanceof UnknownHostException) {
                // Exception handling for UnknownHostException can go here
                // In this case, we are just verifying that the exception is caught and can be
                // handled appropriately
                assertTrue(true, "Exception for UnknownHostException should be caught and handled");
            } else {
                // Unexpected exception, fail the test
                throw e;
            }
        }
    }

    @DisplayName(value = "Generated ID should be greater than last ID")
    @Tags({
            @Tag("@id:93"),
            @Tag("@author:xuping2"),
    })
    @Test
    public void testNextId_ShouldGenerateUniqueIds() {
        for (int i = 0; i < 1000; i++) {
            long newId = snowflake.nextId();
            Assertions.assertTrue(
                    newId > lastId.get(), "Generated ID should be greater than last ID");
            lastId.set(newId);
        }
    }

    @DisplayName(value = "IDs should be the same for the same workerId and dataCenterId")
    @Tags({
            @Tag("@id:93"),
            @Tag("@author:xuping2"),
    })
    @Test
    public void testNextId_ShouldGenerateConsistentIdsForSameInputs() {
        SnowflakeUtils snowflake1 = new SnowflakeUtils(1, 1);
        SnowflakeUtils snowflake2 = new SnowflakeUtils(1, 1);

        long id1 = snowflake1.nextId();
        long id2 = snowflake2.nextId();

        Assertions.assertEquals(
                id1, id2, "IDs should be the same for the same workerId and dataCenterId");
    }

    @DisplayName(value = "IDs should not be the same for the different workerId and dataCenterId")
    @Tags({
            @Tag("@id:93"),
            @Tag("@author:xuping2"),
    })
    @Test
    // IDs should not be the same for the different workerId and dataCenterId
    public void testNextId_ShouldGenerateDifferentIdsForDifferentInputs() {
        SnowflakeUtils snowflake1 = new SnowflakeUtils(1, 1);
        SnowflakeUtils snowflake2 = new SnowflakeUtils(2, 2);
        SnowflakeUtils.snowflakeId();
        long id1 = snowflake1.nextId();
        long id2 = snowflake2.nextId();

        Assertions.assertNotEquals(
                id1, id2, "IDs should not be the same for the different workerId and dataCenterId");
    }

    @DisplayName(
            value =
                    "IDs should not be the same for the same workerId and dataCenterId in one"
                            + " object")
    @Tags({
            @Tag("@id:93"),
            @Tag("@author:xuping2"),
    })
    @Test
    // IDs should not be the same for the same workerId and dataCenterId  in one object
    public void testNextId_ShouldGenerateDifferentIdsForSameInputs() {
        SnowflakeUtils snowflake1 = new SnowflakeUtils(1, 1);
        long id1 = snowflake1.nextId();
        long id2 = snowflake1.nextId();
        Assertions.assertNotEquals(
                id1,
                id2,
                "IDs should not be the same for the same workerId and dataCenterId in one object");
    }

    @DisplayName(value = "  make 1000 calls to nextId() and check that the have different")
    @Tags({
            @Tag("@id:93"),
            @Tag("@author:xuping2"),
    })
    @Test
    // make 1000 calls to nextId() and check that the have different
    public void testNextId_ShouldGenerateDifferentIds() {
        for (int i = 0; i < 1000; i++) {
            long newId = snowflake.nextId();
            // Generated ID should be greater than last ID everytime
            Assertions.assertTrue(
                    newId > lastId.get(), "Generated ID should be greater than last ID");
            // make 1000 calls to nextId() and check that the have different  ，so the 1000 times
            // call nextId() should have different ID
            Assertions.assertNotEquals(
                    newId,
                    lastId.get(),
                    "make 1000 calls to nextId() and check that the have different");
            lastId.set(newId);
        }
    }

    @BeforeEach
    public void setUp() {
        snowflake = new SnowflakeUtils();
        lastId = new AtomicLong();
    }

    @DisplayName(value = "Get getWorkerId")
    @Tags({
            @Tag("@id:93"),
            @Tag("@author:xuping2"),
    })
    @Test
    public void testGetWorkerId() {
        SnowflakeUtils snowflake = new SnowflakeUtils(1, 1);
        long re = snowflake.nextId();
        long workerId = snowflake.getWorkerId(re);
        assertEquals(1L, workerId);
    }

    @DisplayName(value = "Get getWorkerId with max worker id")
    @Tags({
            @Tag("@id:93"),
            @Tag("@author:xuping2"),
    })
    @Test
    public void testGetWorkerIdWithMaxWorkerId() {
        SnowflakeUtils snowflake = new SnowflakeUtils(-1L ^ (-1L << 5L), 1);
        long re = snowflake.nextId();
        long workerId = snowflake.getWorkerId(re);
        assertEquals(-1L ^ (-1L << 5L), workerId);
    }

    @DisplayName(value = "Get getWorkerId with min worker id")
    @Tags({
            @Tag("@id:93"),
            @Tag("@author:xuping2"),
    })
    @Test
    public void testGetWorkerIdWithMinWorkerId() {
        SnowflakeUtils snowflake = new SnowflakeUtils(0, 1);

        long re = snowflake.nextId();
        long workerId = snowflake.getWorkerId(re);
        assertEquals(0L, workerId);
    }

    @DisplayName(value = "Get getWorkerId with random worker id")
    @Tags({
            @Tag("@id:93"),
            @Tag("@author:xuping2"),
    })
    @Test
    public void testGetDataCenterId12() {
        long workerId = 1L;
        long dataCenterId = 2L;
        SnowflakeUtils snowflake = new SnowflakeUtils(workerId, dataCenterId);

        long id = snowflake.nextId();
        long extractedDataCenterId = snowflake.getDataCenterId(id);
        Assertions.assertEquals(dataCenterId, extractedDataCenterId);
    }

    @DisplayName(value = "Get getWorkerId with max worker id")
    @Tags({
            @Tag("@id:93"),
            @Tag("@author:xuping2"),
    })
    @Test
    public void testGetDataCenterIdWithMaxValues() {
        long workerId = 31L; // Max worker ID
        long dataCenterId = 31L; // Max data center ID
        SnowflakeUtils snowflake = new SnowflakeUtils(workerId, dataCenterId);

        long id = snowflake.nextId();
        long extractedDataCenterId = snowflake.getDataCenterId(id);
        Assertions.assertEquals(dataCenterId, extractedDataCenterId);
    }

    @DisplayName(value = "Get getWorkerId with min worker id")
    @Tags({
            @Tag("@id:93"),
            @Tag("@author:xuping2"),
    })
    @Test
    public void testGetDataCenterIdWithMinValues() {
        long workerId = 0L; // Min worker ID
        long dataCenterId = 0L; // Min data center ID
        SnowflakeUtils snowflake = new SnowflakeUtils(workerId, dataCenterId);

        long id = snowflake.nextId();
        long extractedDataCenterId = snowflake.getDataCenterId(id);
        Assertions.assertEquals(dataCenterId, extractedDataCenterId);
    }

    @DisplayName(value = "Get getWorkerId with random worker id")
    @Tags({
            @Tag("@id:93"),
            @Tag("@author:xuping2"),
    })
    @Test
    public void testGetDataCenterIdWithRandomValues() {
        long workerId = 17L; // Random worker ID
        long dataCenterId = 7L; // Random data center ID
        SnowflakeUtils snowflake = new SnowflakeUtils(workerId, dataCenterId);

        long id = snowflake.nextId();
        long extractedDataCenterId = snowflake.getDataCenterId(id);
        Assertions.assertEquals(dataCenterId, extractedDataCenterId);
    }

    @DisplayName(value = "Get getWorkerId with random worker id")
    @Tags({
            @Tag("@id:93"),
            @Tag("@author:xuping2"),
    })
    @Test
    public void testGetDataCenterIdDoesNotAffectOriginalId() {
        long workerId = 1L;
        long dataCenterId = 2L;
        SnowflakeUtils snowflake = new SnowflakeUtils(workerId, dataCenterId);

        long id = snowflake.nextId();
        long originalId = id;
        snowflake.getDataCenterId(id);
        Assertions.assertEquals(originalId, id);
    }
    @DisplayName(value = "Get getWorkerId with random worker id")
    @Tags({
            @Tag("@id:93"),
            @Tag("@author:xuping2"),
    })
    @Test
    public void testGetGenerateDateTimeWithLargeId() {
        SnowflakeUtils snowflake = new SnowflakeUtils(1, 1);
        // Generate a large ID manually to simulate a long time ago
        long largeId = 1L << 63; // Shifting 1 to the left by 63 positions gives a large number
        long generateDateTime = snowflake.getGenerateDateTime(largeId);
        Date date = new Date(generateDateTime);
        // Since we manually created a large ID, the date should be very far in the past
        Assertions.assertTrue(date.before(new Date()));
        System.out.println(date);
    }
    @DisplayName(value = "Get getWorkerId with random worker id")
    @Tags({
            @Tag("@id:93"),
            @Tag("@author:xuping2"),
    })
    @Test
    public void testGetGenerateDateTime() {
        SnowflakeUtils snowflake = new SnowflakeUtils(1, 1);
        long id = snowflake.nextId();
        long generateDateTime = snowflake.getGenerateDateTime(id);
        Date date = new Date(generateDateTime);
        Assertions.assertNotNull(date);
        System.out.println(date);
    }
    @DisplayName(value = "Get getWorkerId with random worker id")
    @Tags({
            @Tag("@id:93"),
            @Tag("@author:xuping2"),
    })
    @Test
    public void testGetGenerateDateTimeWithCustomEpoch() {
        Date customEpoch = new Date();
        SnowflakeUtils snowflake = new SnowflakeUtils(customEpoch, 1, 1,true);
        long id = snowflake.nextId();
        long generateDateTime = snowflake.getGenerateDateTime(id);
        Date date = new Date(generateDateTime);
        Assertions.assertTrue(date.after(customEpoch) || date.equals(customEpoch));
        System.out.println(date);
    }

    @DisplayName(value = "Get getWorkerId with static method")
    @Tags({
            @Tag("@id:93"),
            @Tag("@author:xuping2"),
    })
    @Test
    void snowflakeId() {
        Long aLong = SnowflakeUtils.snowflakeId();
        Assertions.assertNotNull(aLong);


    }
}
