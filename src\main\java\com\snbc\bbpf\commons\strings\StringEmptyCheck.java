/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */

package com.snbc.bbpf.commons.strings;

import org.apache.commons.lang3.StringUtils;

/**
 * 字符串空、空白、“null”字符串判断类
 *
 * <AUTHOR>
 * @module 字符串非空判断
 * @date 2023/4/25 19:09
 * @copyright 2023 山东新北洋信息技术股份有限公司. All rights reserved
 */
public final class StringEmptyCheck {

    private StringEmptyCheck() {
        throw new IllegalStateException("Utility class");
    }

    private static final String NULLSTR = "null";
    /**
     * 判断字符串是否为空
     *
     * @param value 需要判断的字符串
     * @return boolean 若为null或空返回true 非空返回false
     * @since 1.5.0
     * <AUTHOR>
     * @date 2023/4/25
     */
    public static boolean isEmpty(final String value) {
        return StringUtils.isEmpty(value);
    }

    /**
     * 判断字符串是否为空或"null"字符串
     *<P>
     *     StringEmptyCheck.isEmptyOrNull("")     = true
     *     StringEmptyCheck.isEmptyOrNull(null)     = true
     *     StringEmptyCheck.isEmptyOrNull("null")   = true
     *     StringEmptyCheck.isEmptyOrNull("NULL")   = true
     *     StringEmptyCheck.isEmptyOrNull(" NULL ") = true
     *</P>
     * @param value 需要判断的字符串
     * @return Boolean 若为null、空、"null"字符串返回true 非空返回false
     * @throws
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/5/31
     */
    public static Boolean isEmptyOrNull(final String value){
        return isEmpty(value) || isNullStr(value);
    }
    /**
     * 判断多个字符串中任意一个是否为空
     * @param values 多个字符串
     * @return boolean 空返回true
     * @throws
     * @since 1.4.0
     * <AUTHOR>
     * @date 2023/12/12
     */
    public static boolean isAnyEmpty(final String... values){
        return StringUtils.isAnyEmpty(values);
    }

    /**
     * 判断多个字符串中任意一个是否为空或"null"字符串
     *
     *<P>
     *     StringEmptyCheck.isAnyEmptyOrNull("","b","c")     = true
     *     StringEmptyCheck.isAnyEmptyOrNull(null,"b","c")     = true
     *     StringEmptyCheck.isAnyEmptyOrNull("b","null","c")   = true
     *     StringEmptyCheck.isAnyEmptyOrNull("b","Null","Null")   = true
     *     StringEmptyCheck.isAnyEmptyOrNull("b","Null  ","c") = true
     *</P>
     * @param values 多个字符串
     * @return Boolean 若为null、空、"null"字符串返回true 非空返回false
     * @throws
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/5/31
     */
    public static Boolean isAnyEmptyOrNull(final String... values){
        return isAnyEmpty(values) || isAnyNullStr(values);
    }
    /**
     * 判断字符串是否为空或空白
     *
     * @param value 需要判断的字符串
     * @return boolean 若为null或空或空白返回true 否则返回false
     * <AUTHOR>
     * @date 2023/4/25
     */
    public static boolean isBlank(final String value) {
        return StringUtils.isBlank(value);
    }

    /**
     * 判断字符串是否为空、空白或"null"字符串
     *<P>
     *     StringEmptyCheck.isBlankOrNull("")     = true
     *     StringEmptyCheck.isBlankOrNull(null)     = true
     *     StringEmptyCheck.isBlankOrNull("null")   = true
     *     StringEmptyCheck.isBlankOrNull("NULL")   = true
     *     StringEmptyCheck.isBlankOrNull(" NULL ") = true
     *</P>
     * @param value 需要判断的字符串
     * @return Boolean 若为null、空、"null"字符串返回true 非空返回false
     * @throws
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/5/31
     */
    public static Boolean isBlankOrNull(final String value){
        return isBlank(value) || isNullStr(value);
    }


    /**
     * 判断多个字符串中任意一个是否为空或空白
     * @param values
     * @return java.lang.Boolean
     * @throws
     * @since 1.4.0
     * <AUTHOR>
     * @date 2023/12/12
     */
    public static Boolean isAnyBlank(final String... values){
        return StringUtils.isAnyBlank(values);
    }

    /**
     * 判断多个字符串中任意一个是否为空、空白或"null"字符串
     *<P>
     *     StringEmptyCheck.isAnyBlankOrNull(" ","b","c")     = true
     *     StringEmptyCheck.isAnyBlankOrNull(null,"b","c")     = true
     *     StringEmptyCheck.isAnyBlankOrNull("b","null","c")   = true
     *     StringEmptyCheck.isAnyBlankOrNull("b","Null","Null")   = true
     *     StringEmptyCheck.isAnyBlankOrNull("b"," Null","c") = true
     *</P>
     * @param values 多个字符串
     * @return Boolean 若为null、空、"null"字符串返回true 非空返回false
     * @throws
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/5/31
     */
    public static Boolean isAnyBlankOrNull(final String... values){
        return isAnyBlank(values) || isAnyNullStr(values);
    }
    /**
     * 判断字符串是否为”null“字符串，不区分大小写，自动去除首尾空格判断
     *
     * @param value
     * @return Boolean
     * @throws
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/5/31
     */
    private static Boolean isNullStr(String value){
        return NULLSTR.equalsIgnoreCase(value.trim());
    }

    /**
     * 判断任意字符串中是否存在”null“字符串，不区分大小写，自动去除首尾空格判断
     *
     * @param value
     * @return Boolean
     * @throws
     * @since 1.5.0
     * <AUTHOR>
     * @date 2024/5/31
     */
    private static Boolean isAnyNullStr(String... value){
        for(String str:value){
            if(Boolean.TRUE.equals(isNullStr(str))){
                return true;
            }
        }
        return false;
    }
}
