# 加密解密 (com.snbc.bbpf.commons.crypt)

## 类概览 

| 类名 | 功能描述 |
|------|----------|
| AesUtils | 提供AES加密算法功能，支持CBC和ECB加密模式和多种填充方式 |
| RSAUtils | 提供RSA加密算法功能，支持公钥加密私钥解密，私钥签名公钥验签 |
| Base64Utils | 提供Base64编解码功能 |
| CipherUtils | 提供对称加密算法通用实现，被其他加密工具类调用 |
| DesUtils | 提供3DES加密算法功能，支持CBC和ECB加密模式和多种填充方式 |
| JasyptUtil | 提供基于Jasypt库的PBE(Password Based Encryption)加解密功能 |
| JwtTokenUtil | 提供JWT令牌生成和解析功能 |
| MD5Utils | 提供MD5摘要算法功能 |
| SHAUtils | 提供SHA系列哈希算法功能，包括SHA-256和SHA-512 |
| SM3Utils | 提供国密SM3摘要算法功能 |
| Sm4Utils | 提供国密SM4对称加密算法功能，支持CBC和ECB加密模式和多种填充方式 |

## AesUtils - AES加密工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| encryptCbc(byte[] key, byte[] keyIv, byte[] data, Padding padding) | AES加密CBC模式 | key: 加密密钥(128/192/256位)<br>keyIv: 初始向量(16字节)<br>data: 加密数据<br>padding: 填充方式枚举 | byte[]: 加密后的字节数组 |
| decryptCbc(byte[] key, byte[] keyIv, byte[] data, Padding padding) | AES解密CBC模式 | key: 加密密钥(128/192/256位)<br>keyIv: 初始向量(16字节)<br>data: 解密数据<br>padding: 填充方式枚举 | byte[]: 解密后的字节数组 |
| encryptEcb(byte[] key, byte[] data, Padding padding) | AES加密ECB模式 | key: 加密密钥(128/192/256位)<br>data: 加密数据<br>padding: 填充方式枚举 | byte[]: 加密后的字节数组 |
| decryptEcb(byte[] key, byte[] data, Padding padding) | AES解密ECB模式 | key: 加密密钥(128/192/256位)<br>data: 解密数据<br>padding: 填充方式枚举 | byte[]: 解密后的字节数组 |

### 注意事项

- 密钥长度必须是16字节(128位)、24字节(192位)或32字节(256位)，否则会抛出InvalidKeyException异常
- 初始向量(keyIv)必须是16字节长度，否则会抛出InvalidAlgorithmParameterException异常
- ECB模式安全性较低，不推荐用于敏感数据加密，推荐使用CBC模式
- 在CBC模式下，相同的明文使用相同的密钥和IV会产生相同的密文，建议对安全要求高的场景使用不同的IV
- 加解密过程中可能抛出BadPaddingException、IllegalBlockSizeException等异常，建议做好异常处理
- Padding参数使用枚举值，最常用的是PKCS5PADDING

### 使用示例

```java
// AES加密CBC模式
String plainText = "Hello, World!";
byte[] key = "1234567890abcdef".getBytes(StandardCharsets.UTF_8); // 16字节的密钥
byte[] iv = "abcdef1234567890".getBytes(StandardCharsets.UTF_8);  // 16字节的初始向量
byte[] data = plainText.getBytes(StandardCharsets.UTF_8);
byte[] encrypted = AesUtils.encryptCbc(key, iv, data, Padding.PKCS5PADDING);
System.out.println("CBC模式加密结果: " + Base64Utils.encode(encrypted));

// AES解密CBC模式
byte[] decrypted = AesUtils.decryptCbc(key, iv, encrypted, Padding.PKCS5PADDING);
String decryptedText = new String(decrypted, StandardCharsets.UTF_8);
System.out.println("CBC模式解密结果: " + decryptedText);  // 输出: Hello, World!

// AES加密ECB模式
byte[] encryptedEcb = AesUtils.encryptEcb(key, data, Padding.PKCS5PADDING);
System.out.println("ECB模式加密结果: " + Base64Utils.encode(encryptedEcb));

// AES解密ECB模式
byte[] decryptedEcb = AesUtils.decryptEcb(key, encryptedEcb, Padding.PKCS5PADDING);
String decryptedEcbText = new String(decryptedEcb, StandardCharsets.UTF_8);
System.out.println("ECB模式解密结果: " + decryptedEcbText);  // 输出: Hello, World!
```

## RSAUtils - RSA加密工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| rsaEncrypt(String content, String publicKey) | 使用公钥加密内容 | content: 待加密内容<br>publicKey: 公钥字符串 | String: Base64编码的加密结果 |
| rsaDecrypt(String content, String privateKey) | 使用私钥解密内容 | content: 加密内容<br>privateKey: 私钥字符串 | String: 解密后的内容 |
| rsaEncrypt(String content, String strKey, boolean keyType, SymmetricAlgorithm algorithm) | 使用指定密钥和算法加密内容 | content: 待加密内容<br>strKey: 密钥字符串<br>keyType: true为公钥，false为私钥<br>algorithm: 算法枚举 | String: Base64编码的加密结果 |
| rsaDecrypt(String content, String strKey, boolean keyType, SymmetricAlgorithm algorithm, Boolean bras) | 使用指定密钥和算法解密内容 | content: 加密内容<br>strKey: 密钥字符串<br>keyType: true为公钥，false为私钥<br>algorithm: 算法枚举<br>bras: true为2048位密钥，false为1024位密钥 | String: 解密后的内容 |
| createKeys() | 生成RSA密钥对(2048位) | 无 | Map<String, String>: 包含公钥和私钥的映射 |
| createKeys(int len, String providerName) | 生成指定长度和提供者的RSA密钥对 | len: 密钥长度<br>providerName: 提供者名称 | Map<String, String>: 包含公钥和私钥的映射 |
| sign(String content, String privateKey) | 使用私钥对内容进行签名 | content: 待签名内容<br>privateKey: 私钥字符串 | String: Base64编码的签名结果 |
| sign(String content, String privateKey, SymmetricAlgorithm algorithm, Padding padding) | 使用指定算法和填充方式进行签名 | content: 待签名内容<br>privateKey: 私钥字符串<br>algorithm: 算法枚举<br>padding: 填充方式枚举 | String: Base64编码的签名结果 |
| verifySign(String content, String sign, String publicKey) | 使用公钥验证签名 | content: 原始内容<br>sign: 签名<br>publicKey: 公钥字符串 | boolean: 验证是否通过 |
| verifySign(String content, String sign, String publicKey, SymmetricAlgorithm algorithm, Padding padding) | 使用指定算法和填充方式验证签名 | content: 原始内容<br>sign: 签名<br>publicKey: 公钥字符串<br>algorithm: 算法枚举<br>padding: 填充方式枚举 | boolean: 验证是否通过 |

### 注意事项

- 公钥和私钥必须是有效的RSA密钥对，否则会抛出异常
- 公钥和私钥字符串需要是Base64编码格式
- 默认密钥长度为2048位，安全性较高，但加解密速度较慢
- RSA加密的数据长度有限制，受密钥长度影响，1024位密钥大约可加密117字节，2048位密钥大约可加密245字节
- 如果需要加密大量数据，建议使用对称加密算法(如AES)加密数据，然后使用RSA加密对称密钥
- 签名验证失败时会返回false而不是抛出异常，调用方需要判断返回值
- 当使用BC作为提供者时，需要确保项目中已添加BouncyCastle依赖
- RSA加解密过程中可能抛出多种异常，如NoSuchAlgorithmException, InvalidKeyException等，建议做好异常处理

### 使用示例

```java
// 生成RSA密钥对
Map<String, String> keyMap = RSAUtils.createKeys();
String publicKey = keyMap.get("publicKey");
String privateKey = keyMap.get("privateKey");

System.out.println("公钥: " + publicKey);
System.out.println("私钥: " + privateKey);

// 使用公钥加密内容
String content = "Hello, RSA!";
String encrypted = RSAUtils.rsaEncrypt(content, publicKey);
System.out.println("加密结果: " + encrypted);

// 使用私钥解密内容
String decrypted = RSAUtils.rsaDecrypt(encrypted, privateKey);
System.out.println("解密结果: " + decrypted);  // 输出: Hello, RSA!

// 使用私钥对内容进行签名
String sign = RSAUtils.sign(content, privateKey);
System.out.println("签名结果: " + sign);

// 使用公钥验证签名
boolean verifyResult = RSAUtils.verifySign(content, sign, publicKey);
System.out.println("验证结果: " + verifyResult);  // 输出: true

// 使用指定长度创建密钥对
Map<String, String> keyMap2048 = RSAUtils.createKeys(2048, "BC");
String publicKey2048 = keyMap2048.get("publicKey");
String privateKey2048 = keyMap2048.get("privateKey");
```

## Base64Utils - Base64编解码工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| encode(byte[] plainTextByteArray) | Base64编码字节数组 | plainTextByteArray: 明文字节数组 | String: Base64编码字符串 |
| decode(String cipherTextStr) | Base64解码为字节数组 | cipherTextStr: Base64编码字符串 | byte[]: 解码后的字节数组 |

### 注意事项

- Base64编码会增加原始数据大小约33%，需要考虑存储空间
- 输入为null时会抛出NullPointerException，使用前应进行null检查
- 解码非法Base64字符串时会抛出IllegalArgumentException，应做好异常处理
- Base64编码不是加密算法，不提供安全性，只是一种数据表示方法
- 该工具类使用Java 8内置的Base64实现，性能较好

### 使用示例

```java
// Base64编码字节数组
String text = "Hello, Base64!";
byte[] bytes = text.getBytes(StandardCharsets.UTF_8);
String encoded = Base64Utils.encode(bytes);
System.out.println("Base64编码: " + encoded);  // 例如："SGVsbG8sIEJhc2U2NCE="

// Base64解码为字节数组
byte[] decoded = Base64Utils.decode(encoded);
String decodedText = new String(decoded, StandardCharsets.UTF_8);
System.out.println("Base64解码: " + decodedText);  // 输出: Hello, Base64!
```

## CipherUtils - 加密公共工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| doFinal(int opMode, SymmetricAlgorithm symmetricAlgorithm, WorkingMode workingMode, Padding padding, byte[] key, byte[] keyIv, byte[] data) | 对称加解密操作(有初始化向量) | opMode: 加解密模式<br>symmetricAlgorithm: 算法枚举<br>workingMode: 工作模式枚举<br>padding: 填充方式枚举<br>key: 密钥<br>keyIv: 初始向量<br>data: 加解密数据 | byte[]: 加解密结果 |
| doFinal(int opMode, SymmetricAlgorithm symmetricAlgorithm, WorkingMode workingMode, Padding padding, byte[] key, byte[] data) | 对称加解密操作(无初始化向量) | opMode: 加解密模式<br>symmetricAlgorithm: 算法枚举<br>workingMode: 工作模式枚举<br>padding: 填充方式枚举<br>key: 密钥<br>data: 加解密数据 | byte[]: 加解密结果 |

### 注意事项

- 该工具类一般不直接使用，而是通过AesUtils、DesUtils等具体加密工具类调用
- 加密模式使用Cipher.ENCRYPT_MODE，解密模式使用Cipher.DECRYPT_MODE
- 当使用CBC模式时必须提供初始向量(keyIv)，否则会抛出InvalidAlgorithmParameterException异常
- 不同算法对密钥长度和初始向量长度有不同要求，使用前应查阅相关文档
- 加解密过程中可能抛出多种异常，如BadPaddingException、NoSuchAlgorithmException等，需要做好异常处理
- 对于BouncyCastle提供的算法，需要确保项目中已添加BouncyCastle依赖库

### 使用示例

```java
// 使用CipherUtils进行AES-CBC加密(一般通过AesUtils调用，而不是直接使用)
byte[] key = "1234567890abcdef".getBytes(StandardCharsets.UTF_8);
byte[] iv = "abcdef1234567890".getBytes(StandardCharsets.UTF_8);
byte[] data = "Hello, CipherUtils!".getBytes(StandardCharsets.UTF_8);

byte[] encrypted = CipherUtils.doFinal(
    Cipher.ENCRYPT_MODE, 
    SymmetricAlgorithm.AES, 
    WorkingMode.CBC, 
    Padding.PKCS5PADDING, 
    key, 
    iv, 
    data
);

System.out.println("加密结果: " + Base64Utils.encode(encrypted));

// 使用CipherUtils进行AES-CBC解密
byte[] decrypted = CipherUtils.doFinal(
    Cipher.DECRYPT_MODE, 
    SymmetricAlgorithm.AES, 
    WorkingMode.CBC, 
    Padding.PKCS5PADDING, 
    key, 
    iv, 
    encrypted
);

System.out.println("解密结果: " + new String(decrypted, StandardCharsets.UTF_8));
```

## DesUtils - 3DES加密工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| encryptCbc(byte[] key, byte[] keyIv, byte[] data, Padding padding) | 3DES加密CBC模式 | key: 加密密钥(24位以上,8的倍数)<br>keyIv: 初始向量(8字节)<br>data: 加密数据<br>padding: 填充方式枚举 | byte[]: 加密后的字节数组 |
| decryptCbc(byte[] key, byte[] keyIv, byte[] data, Padding padding) | 3DES解密CBC模式 | key: 加密密钥(24位以上,8的倍数)<br>keyIv: 初始向量(8字节)<br>data: 解密数据<br>padding: 填充方式枚举 | byte[]: 解密后的字节数组 |
| encryptEcb(byte[] key, byte[] data, Padding padding) | 3DES加密ECB模式 | key: 加密密钥(24位以上,8的倍数)<br>data: 加密数据<br>padding: 填充方式枚举 | byte[]: 加密后的字节数组 |
| decryptEcb(byte[] key, byte[] data, Padding padding) | 3DES解密ECB模式 | key: 加密密钥(24位以上,8的倍数)<br>data: 解密数据<br>padding: 填充方式枚举 | byte[]: 解密后的字节数组 |

### 注意事项

- 密钥(key)长度必须是24字节或以上且是8的倍数，否则会抛出InvalidKeyException异常
- 初始向量(keyIv)必须是8字节长度，否则会抛出InvalidAlgorithmParameterException异常
- 3DES算法速度较慢，不适合大量数据加密，考虑使用AES替代
- ECB模式安全性较低，不推荐用于敏感数据加密，推荐使用CBC模式
- 3DES已被认为是不够安全的算法，在安全要求高的场景建议使用AES或SM4
- 解密错误的数据或使用错误的密钥会抛出BadPaddingException异常
- Padding参数支持多种填充方式，最常用的是PKCS5PADDING

### 使用示例

```java
// 3DES加密CBC模式
String plainText = "Hello, World!";
byte[] key = "123456789012345678901234".getBytes(StandardCharsets.UTF_8); // 24字节的密钥
byte[] iv = "12345678".getBytes(StandardCharsets.UTF_8);  // 8字节的初始向量
byte[] data = plainText.getBytes(StandardCharsets.UTF_8);
byte[] encrypted = DesUtils.encryptCbc(key, iv, data, Padding.PKCS5PADDING);
System.out.println("CBC模式加密结果: " + Base64Utils.encode(encrypted));

// 3DES解密CBC模式
byte[] decrypted = DesUtils.decryptCbc(key, iv, encrypted, Padding.PKCS5PADDING);
String decryptedText = new String(decrypted, StandardCharsets.UTF_8);
System.out.println("CBC模式解密结果: " + decryptedText);  // 输出: Hello, World!

// 3DES加密ECB模式
byte[] encryptedEcb = DesUtils.encryptEcb(key, data, Padding.PKCS5PADDING);
System.out.println("ECB模式加密结果: " + Base64Utils.encode(encryptedEcb));

// 3DES解密ECB模式
byte[] decryptedEcb = DesUtils.decryptEcb(key, encryptedEcb, Padding.PKCS5PADDING);
String decryptedEcbText = new String(decryptedEcb, StandardCharsets.UTF_8);
System.out.println("ECB模式解密结果: " + decryptedEcbText);  // 输出: Hello, World!
```

## JasyptUtil - Jasypt加解密工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| encrypt(String plainText) | 使用默认参数加密字符串 | plainText: 待加密的字符串 | String: 加密后的字符串 |
| encrypt(String plainText, String password, String algorithm, int iterations) | 使用指定参数加密字符串 | plainText: 待加密的字符串<br>password: 加密密钥<br>algorithm: 加密算法<br>iterations: 迭代次数 | String: 加密后的字符串 |
| decrypt(String encryptedText) | 使用默认参数解密字符串 | encryptedText: 加密的字符串 | String: 解密后的字符串 |
| decrypt(String encryptedText, String password, String algorithm, int iterations) | 使用指定参数解密字符串 | encryptedText: 加密的字符串<br>password: 加密密钥<br>algorithm: 加密算法<br>iterations: 迭代次数 | String: 解密后的字符串 |

### 注意事项

- 默认使用的加密算法是PBEWithMD5AndDES，默认迭代次数为1000
- 加密和解密必须使用相同的密钥、算法和迭代次数，否则无法正确解密
- 输入为null时会抛出IllegalArgumentException异常，使用前应进行null检查
- 使用错误的密钥或算法解密会导致解密失败并抛出异常
- 算法必须是系统支持的PBE算法，否则会抛出NoSuchAlgorithmException异常
- 常用的PBE算法包括PBEWithMD5AndDES、PBEWithMD5AndTripleDES、PBEWithSHA1AndDESede、PBEWithHMACSHA512AndAES_128等
- 迭代次数越高安全性越高，但性能越低，建议根据实际需求选择合适的迭代次数，当前默认迭代次数为1000，这个迭代次数是一个经验最优值，可以修改，但是不建议修改。
- 该工具类依赖于第三方库jasypt，使用前需确保项目中已添加相关依赖
- PBE加密强度取决于密码的复杂度，建议使用强密码
- 加密结果每次都不同，即使使用相同的密钥和明文，这是因为内部使用了随机盐值

### 使用示例

```java
// 使用默认参数加密
String plainText = "Hello, Jasypt!";
String encryptedText = JasyptUtil.encrypt(plainText);
System.out.println("默认参数加密结果: " + encryptedText);

// 使用默认参数解密
String decryptedText = JasyptUtil.decrypt(encryptedText);
System.out.println("默认参数解密结果: " + decryptedText);  // 输出: Hello, Jasypt!

// 使用自定义参数加密
String customPassword = "myStrongPassword";
String algorithm = "PBEWithMD5AndTripleDES";
int iterations = 2000;
String customEncrypted = JasyptUtil.encrypt(plainText, customPassword, algorithm, iterations);
System.out.println("自定义参数加密结果: " + customEncrypted);

// 使用自定义参数解密
String customDecrypted = JasyptUtil.decrypt(customEncrypted, customPassword, algorithm, iterations);
System.out.println("自定义参数解密结果: " + customDecrypted);  // 输出: Hello, Jasypt!

// 注意：使用错误的密钥解密会抛出异常
try {
    String wrongDecrypted = JasyptUtil.decrypt(customEncrypted, "wrongPassword", algorithm, iterations);
} catch (Exception e) {
    System.out.println("使用错误密钥解密失败: " + e.getMessage());
}
```

## JwtTokenUtil - JWT令牌工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| generateToken(Map<String, Object> claims, String privateKeyString, long expiration) | 生成JWT令牌 | claims: 令牌声明<br>privateKeyString: 私钥字符串<br>expiration: 过期时间(分钟) | String: JWT令牌字符串 |
| getClaimsFromToken(String token, String publicKeyString) | 从令牌中获取声明 | token: JWT令牌<br>publicKeyString: 公钥字符串 | Claims: 令牌中的声明 |

### 注意事项

- 私钥必须是有效的RSA私钥，公钥必须是对应的RSA公钥，否则会抛出异常
- JWT令牌一旦生成无法撤销，只能等待其过期，因此过期时间设置很重要
- 对于高安全性要求的场景，令牌过期时间应尽量短（如30分钟以内）
- 当验证已过期的令牌时会抛出ExpiredJwtException异常
- 当令牌被篡改时会抛出SignatureException异常
- 当令牌格式错误时会抛出MalformedJwtException异常
- 令牌中不应包含敏感信息，因为JWT的Payload部分只是Base64编码而非加密
- 此工具类依赖于第三方库jjwt，使用前需确保项目中已添加相关依赖

### 使用示例

```java
// 生成RSA密钥对用于JWT签名
Map<String, String> keyMap = RSAUtils.createKeys();
String publicKey = keyMap.get("publicKey");
String privateKey = keyMap.get("privateKey");

// 创建JWT令牌
JwtTokenUtil jwtTokenUtil = new JwtTokenUtil();
Map<String, Object> claims = new HashMap<>();
claims.put("username", "testUser");
claims.put("userId", 12345);
claims.put("role", "admin");

// 生成令牌(60分钟过期)
String token = jwtTokenUtil.generateToken(claims, privateKey, 60);
System.out.println("JWT令牌: " + token);

// 解析令牌
Claims tokenClaims = jwtTokenUtil.getClaimsFromToken(token, publicKey);
System.out.println("用户名: " + tokenClaims.get("username"));
System.out.println("用户ID: " + tokenClaims.get("userId"));
System.out.println("角色: " + tokenClaims.get("role"));
System.out.println("过期时间: " + tokenClaims.getExpiration());
```

## MD5Utils - MD5摘要工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| md5(String rawStr) | 原生JDK实现MD5计算 | rawStr: 待计算字符串 | String: MD5摘要(32位十六进制) |
| md5(byte[] rawBytes) | 原生JDK实现MD5计算 | rawBytes: 待计算字节数组 | String: MD5摘要(32位十六进制) |
| md5WithBouncyCastle(String rawStr) | BouncyCastle实现MD5计算 | rawStr: 待计算字符串 | String: MD5摘要(32位十六进制) |
| md5WithBouncyCastle(byte[] rawBytes) | BouncyCastle实现MD5计算 | rawBytes: 待计算字节数组 | String: MD5摘要(32位十六进制) |

### 注意事项

- MD5算法已不再被视为安全的哈希算法，存在碰撞风险，不应用于安全敏感场景
- 对于安全级别要求高的应用，建议使用SHA-256或更高级的哈希算法
- MD5只能用作数据完整性校验，不应用于密码存储或安全加密
- 使用BouncyCastle实现的方法需要确保项目中已添加BouncyCastle依赖库
- 输入为null时会抛出NullPointerException，使用前应进行null检查
- MD5算法的输出固定为32位十六进制字符串(128位)，不受输入长度影响
- 相同的输入总是产生相同的输出，这是MD5等哈希算法的基本特性

### 使用示例

```java
// 使用原生JDK计算字符串的MD5摘要
String data = "Hello, World!";
String md5 = MD5Utils.md5(data);
System.out.println("MD5(JDK): " + md5);

// 使用BouncyCastle计算字符串的MD5摘要
String md5Bc = MD5Utils.md5WithBouncyCastle(data);
System.out.println("MD5(BC): " + md5Bc);

// 计算字节数组的MD5摘要
byte[] bytes = data.getBytes(StandardCharsets.UTF_8);
String md5Bytes = MD5Utils.md5(bytes);
System.out.println("字节数组MD5: " + md5Bytes);

// 使用BouncyCastle计算字节数组的MD5摘要
String md5BytesBc = MD5Utils.md5WithBouncyCastle(bytes);
System.out.println("字节数组MD5(BC): " + md5BytesBc);
```

## SHAUtils - SHA哈希算法工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| encrypt(String msg) | 使用SHA-256加密 | msg: 待加密内容 | String: SHA-256摘要 |
| encryptFive(String msg) | 使用SHA-512加密 | msg: 待加密内容 | String: SHA-512摘要 |
| encrypt(String msg, String algorithm) | 使用指定算法加密 | msg: 待加密内容<br>algorithm: 加密算法(SHA-256、SHA-512等) | String: 对应算法的摘要 |

### 注意事项

- SHA-256和SHA-512都是被广泛接受的安全哈希算法，比MD5更安全
- SHA-512产生的摘要更长(128个十六进制字符)，计算速度略慢于SHA-256但安全性更高
- 默认encrypt方法使用SHA-256算法，输出长度为64个十六进制字符
- 使用不支持的算法名称会抛出NoSuchAlgorithmException异常
- 输入为null时会抛出NullPointerException，使用前应进行null检查
- SHA算法是单向的，无法从摘要还原原始数据
- 相同的输入总是产生相同的输出，这是哈希算法的基本特性
- 对于密码存储等高安全性场景，应该结合加盐技术使用SHA算法

### 使用示例

```java
// 使用SHA-256加密
String data = "Hello, SHA!";
String sha256 = SHAUtils.encrypt(data);
System.out.println("SHA-256: " + sha256);

// 使用SHA-512加密
String sha512 = SHAUtils.encryptFive(data);
System.out.println("SHA-512: " + sha512);

// 使用指定算法加密
String sha384 = SHAUtils.encrypt(data, "SHA-384");
System.out.println("SHA-384: " + sha384);
```

## SM3Utils - 国密SM3摘要工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| sm3(String rawStr) | 计算字符串的SM3摘要 | rawStr: 待计算字符串 | String: SM3摘要(十六进制) |
| sm3(byte[] rawBytes) | 计算字节数组的SM3摘要 | rawBytes: 待计算字节数组 | String: SM3摘要(十六进制) |

### 注意事项

- SM3是国家密码管理局发布的密码杂凑算法标准，适用于商用密码应用中的数字签名和验证
- SM3算法的安全性与SHA-256相当，但在国内应用场景更为广泛
- 使用SM3算法需要确保项目中已添加BouncyCastle依赖库，该库提供了SM3算法的实现
- SM3摘要输出长度固定为64位十六进制字符(256位)，不受输入长度影响
- 输入为null时会抛出NullPointerException，使用前应进行null检查
- 在需要符合国密标准的应用场景中，应优先考虑使用SM3算法
- 与其他哈希算法一样，SM3也是单向的，无法从摘要还原原始数据

### 使用示例

```java
// 计算字符串的SM3摘要
String data = "Hello, SM3!";
String sm3 = SM3Utils.sm3(data);
System.out.println("SM3摘要: " + sm3);

// 计算字节数组的SM3摘要
byte[] bytes = data.getBytes(StandardCharsets.UTF_8);
String sm3Bytes = SM3Utils.sm3(bytes);
System.out.println("字节数组SM3摘要: " + sm3Bytes);
```

## Sm4Utils - 国密SM4加密工具类

### API列表

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| encryptCbc(byte[] key, byte[] keyIv, byte[] data, Padding padding) | SM4加密CBC模式 | key: 加密密钥(16字节)<br>keyIv: 初始向量(16字节)<br>data: 加密数据<br>padding: 填充方式枚举 | byte[]: 加密后的字节数组 |
| decryptCbc(byte[] key, byte[] keyIv, byte[] data, Padding padding) | SM4解密CBC模式 | key: 加密密钥(16字节)<br>keyIv: 初始向量(16字节)<br>data: 解密数据<br>padding: 填充方式枚举 | byte[]: 解密后的字节数组 |
| encryptEcb(byte[] key, byte[] data, Padding padding) | SM4加密ECB模式 | key: 加密密钥(16字节)<br>data: 加密数据<br>padding: 填充方式枚举 | byte[]: 加密后的字节数组 |
| decryptEcb(byte[] key, byte[] data, Padding padding) | SM4解密ECB模式 | key: 加密密钥(16字节)<br>data: 解密数据<br>padding: 填充方式枚举 | byte[]: 解密后的字节数组 |

### 注意事项

- SM4是国家密码管理局发布的分组密码算法标准，适用于商用密码应用中的数据加密
- 密钥(key)长度必须是16字节(128位)，否则会抛出InvalidKeyException异常
- 初始向量(keyIv)必须是16字节长度，否则会抛出InvalidAlgorithmParameterException异常
- 使用SM4算法需要确保项目中已添加BouncyCastle依赖库，该库提供了SM4算法的实现
- ECB模式安全性较低，不推荐用于敏感数据加密，推荐使用CBC模式
- SM4算法的性能与AES相当，在需要符合国密标准的场景下可替代AES使用
- 与AES类似，SM4也支持多种填充方式，最常用的是PKCS5PADDING和PKCS7PADDING
- 加解密过程中可能抛出多种异常，如BadPaddingException、IllegalBlockSizeException等，建议做好异常处理

### 使用示例

```java
// SM4加密CBC模式
String plainText = "Hello, SM4!";
byte[] key = "1234567890abcdef".getBytes(StandardCharsets.UTF_8); // 16字节的密钥
byte[] iv = "abcdef1234567890".getBytes(StandardCharsets.UTF_8);  // 16字节的初始向量
byte[] data = plainText.getBytes(StandardCharsets.UTF_8);
byte[] encrypted = Sm4Utils.encryptCbc(key, iv, data, Padding.PKCS5PADDING);
System.out.println("CBC模式加密结果: " + Base64Utils.encode(encrypted));

// SM4解密CBC模式
byte[] decrypted = Sm4Utils.decryptCbc(key, iv, encrypted, Padding.PKCS5PADDING);
String decryptedText = new String(decrypted, StandardCharsets.UTF_8);
System.out.println("CBC模式解密结果: " + decryptedText);  // 输出: Hello, SM4!

// SM4加密ECB模式
byte[] encryptedEcb = Sm4Utils.encryptEcb(key, data, Padding.PKCS5PADDING);
System.out.println("ECB模式加密结果: " + Base64Utils.encode(encryptedEcb));

// SM4解密ECB模式
byte[] decryptedEcb = Sm4Utils.decryptEcb(key, encryptedEcb, Padding.PKCS5PADDING);
String decryptedEcbText = new String(decryptedEcb, StandardCharsets.UTF_8);
System.out.println("ECB模式解密结果: " + decryptedEcbText);  // 输出: Hello, SM4!
``` 